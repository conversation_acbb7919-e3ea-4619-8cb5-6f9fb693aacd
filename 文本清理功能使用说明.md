# 文本清理功能使用说明

## 概述

为了解决复制粘贴文本时可能包含的隐藏特殊字符问题，我们开发了一套完整的文本清理解决方案。该功能可以自动去除零宽字符、方向控制字符、不可见Unicode字符等，确保输入框中的文本内容干净整洁。

## 功能特性

### 清理的字符类型
- **零宽字符**: `\u200B-\u200D\uFEFF` (零宽空格、零宽连接符等)
- **方向控制字符**: `\u202A-\u202E` (从右到左标记、从左到右标记等)
- **其他方向控制字符**: `\u2066-\u2069`
- **对象替换字符**: `\uFFFC`
- **字节顺序标记**: `\uFEFF` (BOM)
- **控制字符**: `\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F`
- **不可见字符**: `\u2028\u2029`
- **特殊空白字符**: `\u00A0\u1680\u2000-\u200A\u202F\u205F\u3000`

## 使用方式

### 方式1: Vue指令（推荐）

最简单的使用方式是直接在输入框上添加 `v-text-cleaner` 指令：

```vue
<template>
  <!-- 基础用法：自动清理粘贴内容 -->
  <input v-text-cleaner v-model="inputValue" placeholder="输入内容" />
  
  <!-- 高级用法：配置清理选项 -->
  <input 
    v-text-cleaner="{ onInput: true, onPaste: true }" 
    v-model="inputValue" 
    placeholder="输入内容" 
  />
  
  <!-- 文本域 -->
  <textarea 
    v-text-cleaner 
    v-model="textValue" 
    placeholder="多行文本"
    rows="4"
  ></textarea>
</template>
```

#### 指令配置选项

```javascript
{
  onPaste: true,   // 是否在粘贴时清理（默认: true）
  onInput: false,  // 是否在输入时清理（默认: false）
  callback: null   // 清理后的回调函数（可选）
}
```

### 方式2: 手动调用方法

如果需要更灵活的控制，可以直接调用清理方法：

```vue
<script>
import { removeHiddenCharacters, cleanPastedText } from '@/utils/textCleaner'

export default {
  methods: {
    handlePaste(event) {
      // 获取粘贴的文本
      const pastedText = event.clipboardData.getData('text')
      // 清理文本
      const cleanedText = removeHiddenCharacters(pastedText)
      // 使用清理后的文本
      this.inputValue = cleanedText
    },
    
    cleanCurrentText() {
      // 清理当前输入框的内容
      this.inputValue = removeHiddenCharacters(this.inputValue)
    }
  }
}
</script>
```

### 方式3: 程序化设置

对于动态创建的输入框，可以使用程序化方式：

```javascript
import { setupInputCleaner } from '@/utils/textCleaner'

// 获取输入框元素
const inputElement = document.querySelector('#myInput')

// 设置自动清理
setupInputCleaner(inputElement, {
  onPaste: true,
  onInput: false,
  callback: (cleanedValue) => {
    console.log('文本已清理:', cleanedValue)
  }
})
```

## 实际应用示例

### 1. 用户注册表单

```vue
<template>
  <div class="register-form">
    <el-form :model="form" :rules="rules" ref="form">
      <el-form-item label="用户名" prop="username">
        <el-input 
          v-text-cleaner 
          v-model="form.username" 
          placeholder="请输入用户名"
        />
      </el-form-item>
      
      <el-form-item label="邮箱" prop="email">
        <el-input 
          v-text-cleaner="{ onInput: true }" 
          v-model="form.email" 
          placeholder="请输入邮箱"
        />
      </el-form-item>
      
      <el-form-item label="个人简介" prop="bio">
        <el-input 
          v-text-cleaner 
          type="textarea" 
          v-model="form.bio" 
          placeholder="请输入个人简介"
          rows="4"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
```

### 2. 搜索框

```vue
<template>
  <div class="search-box">
    <el-input
      v-text-cleaner="{ onInput: true, onPaste: true }"
      v-model="searchKeyword"
      placeholder="请输入搜索关键词"
      @keyup.enter="handleSearch"
      clearable
    >
      <el-button slot="append" @click="handleSearch">搜索</el-button>
    </el-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: ''
    }
  },
  methods: {
    handleSearch() {
      if (this.searchKeyword.trim()) {
        // 执行搜索逻辑
        console.log('搜索关键词:', this.searchKeyword)
      }
    }
  }
}
</script>
```

### 3. 富文本编辑器内容清理

```vue
<script>
import { removeHiddenCharacters } from '@/utils/textCleaner'

export default {
  methods: {
    handleEditorPaste(event) {
      // 阻止默认粘贴行为
      event.preventDefault()
      
      // 获取粘贴的文本
      const pastedText = event.clipboardData.getData('text/plain')
      
      // 清理文本
      const cleanedText = removeHiddenCharacters(pastedText)
      
      // 插入到编辑器中
      this.insertTextToEditor(cleanedText)
    }
  }
}
</script>
```

## 文件结构

```
src/
├── utils/
│   └── textCleaner.js          # 核心清理方法
├── directives/
│   └── textCleaner.js          # Vue指令
├── components/
│   └── TextCleanerExample.vue  # 使用示例组件
└── main.js                     # 指令注册
```

## API 参考

### removeHiddenCharacters(text)

核心清理方法，去除文本中的隐藏特殊字符。

**参数:**
- `text` (string): 需要清理的文本

**返回值:**
- `string`: 清理后的文本

**示例:**
```javascript
import { removeHiddenCharacters } from '@/utils/textCleaner'

const dirtyText = 'Hello​World‌Test‍'  // 包含零宽字符
const cleanText = removeHiddenCharacters(dirtyText)
console.log(cleanText) // 'HelloWorldTest'
```

### cleanPastedText(text)

清理粘贴的文本内容，是 `removeHiddenCharacters` 的别名。

### setupInputCleaner(element, options)

为DOM元素设置自动清理功能。

**参数:**
- `element` (HTMLElement): 输入框元素
- `options` (Object): 配置选项
  - `onPaste` (boolean): 是否在粘贴时清理，默认 `true`
  - `onInput` (boolean): 是否在输入时清理，默认 `false`
  - `callback` (function): 清理后的回调函数，可选

## 注意事项

1. **性能考虑**: 建议只在粘贴时清理（`onPaste: true`），避免在每次输入时都进行清理，除非有特殊需求。

2. **兼容性**: 该功能依赖现代浏览器的 Unicode 支持，在 IE 浏览器中可能存在兼容性问题。

3. **表单验证**: 清理操作会触发 `input` 事件，确保与现有的表单验证逻辑兼容。

4. **数据绑定**: 使用指令时，清理后的值会自动更新到 Vue 的数据绑定中。

## 测试

项目中包含了完整的测试用例，可以使用以下测试数据验证功能：

```javascript
// 包含各种隐藏字符的测试文本
const testText = 'Hello\u200BWorld\u200CTest\u200D\uFEFF\u202A测试文本\u202C'
```

## 扩展功能

如需添加更多字符清理规则，可以修改 `src/utils/textCleaner.js` 中的正则表达式：

```javascript
export function removeHiddenCharacters(text) {
  // ... 现有代码 ...
  
  // 添加自定义清理规则
  .replace(/your-custom-regex/g, '')
  
  // ... 现有代码 ...
}
``` 