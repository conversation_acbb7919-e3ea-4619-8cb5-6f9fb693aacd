# SMS短信管理系统接口读写分类文档

## 文档说明
本文档将SMS短信管理系统的所有API接口按照读（READ）/写（WRITE）操作进行分类，并按照项目文件结构进行组织。

## 接口分类标准
- **读操作（READ）**：获取数据、查询信息、验证状态等不会改变系统数据的操作
- **写操作（WRITE）**：创建、更新、删除等会改变系统数据的操作

## 项目结构与接口映射

<!-- ### 1. 登录认证相关 (`src/components/page/login1.vue`, `src/components/page/Login.vue`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/gateway/api/code` | GET | 获取验证码图片 |
| `/upms/user/info` | GET | 获取用户信息 |
| `/upms/online/existUser/{username}` | GET | 检查用户是否存在 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/auth/oauth/token` | POST | 账号密码登录 |
| `/auth/mobile/token` | POST | 手机号登录 |
| `/auth/loginOut` | POST | 退出登录 |
| `/upms/online/register` | POST | 用户注册 |
| `/upms/code/register/smsCode` | POST | 发送注册验证码 | -->

### 2. 短信发送模块 (`src/components/page/client/SMS/sendMessages/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumertasksms/page` | POST | 获取短信任务列表（查询） |
| `/cpus/v3/selectConsumerWebTaskPage` | POST | 获取Web任务列表（查询） |
| `/cpus/v3/queryConsumerWebTaskById/{id}` | GET | 根据ID查询任务 |
| `/cpus/v3/web-task/info/{id}` | GET | 获取任务详情 |
| `/cpus/consumertimingsms/page` | POST | 获取定时短信列表（查询） |
| `/cpus/v3/consumersms/hasVariable` | POST | 检查是否包含变量（验证） |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumersmsinfo/submitSms` | POST | 提交短信（验证） |
| `/cpus/consumersmsinfo/customSendSms` | POST | 自定义内容发送短信 |
| `/cpus/consumersmsinfo/templateSendSms` | POST | 模板发送短信 |
| `/cpus/v3/consumersms/send/customize` | POST | 自定义短信发送V3 |
| `/cpus/v3/consumersms/send/template` | POST | 模板短信发送V3 |
| `/cpus/v3/consumersms/send/customize/file` | POST | 文件方式自定义短信发送 |
| `/cpus/v3/updateConsumerWebTask` | POST | 更新任务 |
| `/cpus/v3/web-task/cancel` | POST | 取消任务 |
| `/cpus/consumertimingsms/batchCancel/{ids}` | GET | 批量取消定时短信 |

### 3. 短信模板管理 (`src/components/page/client/SMS/SMSContentConfiguration/components/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v3/consumersmstemplate/page` | POST | 获取模板列表（查询） |
| `/cpus/v3/consumersmstemplate/selectClientTemplate` | POST | 获取客户模板列表（查询） |
| `/cpus/consumersmstemplate/get/{id}` | GET | 获取模板详情 |
| `/cpus/v3/consumersmstemplate/validateUserTempExist/` | POST | 验证模板是否存在 |
| `/cpus/v3/consumersmstemplate/longUrl` | POST | 获取长链接 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v3/consumersmstemplate/template/add` | POST | 添加模板 |
| `/cpus/v3/consumersmstemplate/template/update` | POST | 更新模板 |
| `/cpus/v3/consumersmstemplate/template/copy` | POST | 复制模板 |
| `/cpus/v3/consumersmstemplate/delete/{id}` | DELETE | 删除模板 |
| `/cpus/v3/consumersmstemplate/upload` | POST | 批量上传模板 |

### 4. 签名管理 (`src/components/page/client/SMS/SMSContentConfiguration/components/SignatureManagement.vue`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/signature/signatureList` | POST | 获取签名列表（查询） |
| `/cpus/signature/findModelBySignature` | GET | 根据签名查找模型 |
| `/cpus/signature/findModel/realName` | GET | 查找实名信息 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/signature/saveInfo` | POST | 保存签名信息 |
| `/cpus/signature/update` | PUT | 更新签名 |
| `/cpus/signature/delete` | DELETE | 删除签名 |
| `/cpus/signature/realName/edit` | POST | 编辑实名信息 |
| `/cpus/signature/realName/export` | POST | 导出实名信息 |
| `/cpus/signature/realName/import` | POST | 导入实名信息 |
| `/cpus/signature/upload` | POST | 上传签名文件 |
| `/cpus/signature/batchUploadAttachment` | POST | 批量上传附件 |

### 5. 短信记录与备案管理 (`src/components/page/client/SMS/SMSRecordManagement/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumersmstemplatereportmobile/page` | POST | 获取号码备案列表（查询） |
| `/cpus/consumersmstemplatereportlink/page` | POST | 获取链接备案列表（查询） |
| `/cpus/consumersmstemplatereportlink/extractDomain` | GET | 提取域名 |
| `/cpus/consumersmstemplatereportlinkextension/page` | POST | 获取链接扩展列表（查询） |
| `/cpus/consumersmstemplatereportlinkproof/page` | POST | 获取链接权威列表（查询） |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumersmstemplatereportmobile` | POST | 新增号码备案 |
| `/cpus/consumersmstemplatereportmobile` | PUT | 更新号码备案 |
| `/cpus/consumersmstemplatereportmobile/{id}` | DELETE | 删除号码备案 |
| `/cpus/consumersmstemplatereportmobile/batchUpload` | POST | 批量上传号码备案 |
| `/cpus/consumersmstemplatereportlink` | POST | 新增链接备案 |
| `/cpus/consumersmstemplatereportlink` | PUT | 更新链接备案 |
| `/cpus/consumersmstemplatereportlink/{id}` | DELETE | 删除链接备案 |
| `/cpus/consumersmstemplatereportlinkextension` | POST | 新增链接扩展 |
| `/cpus/consumersmstemplatereportlinkextension` | PUT | 更新链接扩展 |
| `/cpus/consumersmstemplatereportlinkextension/{id}` | DELETE | 删除链接扩展 |
| `/cpus/consumersmstemplatereportlinkproof` | POST | 新增链接权威 |
| `/cpus/consumersmstemplatereportlinkproof` | PUT | 更新链接权威 |
| `/cpus/consumersmstemplatereportlinkproof/{id}` | DELETE | 删除链接权威 |

### 6. 统计分析模块 (`src/components/page/client/SMS/statisticalAnalysis/`)

#### 读操作接口（全部为查询统计数据）
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/statistics/page` | POST | 获取统计数据（查询） |
| `/cpus/statistics/smsPage` | POST | 获取短信统计数据（查询） |
| `/cpus/statistics/esSmsPage` | POST | 获取ES短信统计数据（查询） |
| `/cpus/statistics/replyPage` | POST | 获取回复统计数据（查询） |
| `/cpus/statistics/sendCharts` | GET | 获取发送图表数据 |
| `/cpus/statistics/statusReport` | GET | 获取状态报告 |
| `/cpus/statistics/exportDecode/check` | GET | 检查导出解密 |
| `/cpus/consumerdataoverviewday/businessOverview` | GET | 获取业务概览 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/statistics/export` | POST | 导出统计数据 |
| `/cpus/statistics/exportData` | POST | 导出数据 |

<!-- ### 7. 彩信管理模块 (`src/components/page/client/MMS/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumermmstemplate/page` | POST | 获取彩信模板列表（查询） |
| `/cpus/consumermmstemplate/{id}` | GET | 获取彩信模板详情 |
| `/cpus/v1/consumermms/list` | POST | 获取彩信草稿列表（查询） |
| `/cpus/consumertimingmms/selectTimingPage` | POST | 获取定时彩信列表（查询） |
| `/cpus/v1/consumermms/replyPage` | POST | 获取彩信回复列表（查询） |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v1/mms/consumerWebTask` | POST | 彩信Web任务 |
| `/cpus/v1/mms/cancel` | POST | 取消彩信发送 |
| `/cpus/consumermmstemplate` | POST | 新增彩信模板 |
| `/cpus/consumermmstemplate` | PUT | 更新彩信模板 |
| `/cpus/consumermmstemplate/{id}` | DELETE | 删除彩信模板 |
| `/cpus/consumertimingmms/cancelTimingMms` | POST | 取消定时彩信 | -->

### 8. 视频短信管理模块 (`src/components/page/client/VideoRMS/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v1/consumervideo` | POST | 获取视频模板列表（查询） |
| `/cpus/consumertimingvideo/selectTimingPage` | POST | 获取定时视频短信列表（查询） |
| `/cpus/statistics/export` | POST | 导出 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v1/video/consumerWebTask` | POST | 视频短信Web任务 |
| `/cpus/v1/video/cancel` | POST | 取消视频短信 |
| `/cpus/v1/consumervideo/{id}` | DELETE | 删除视频模板 |
| `/cpus/v1/consumervideo/copy` | POST | 复制视频模板 |
| `/cpus/consumertimingvideo/cancelTimingMms` | POST | 取消定时视频短信 |

### 9. 语音服务模块 (`src/components/page/client/voice/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumertimingvoice/selectTimingPage` | POST | 获取定时语音列表（查询） |
| `/cpus/consumervoicemessage/messages` | POST | 获取语音消息列表（查询） |
| `/cpus/voice/consumerWebTask` | POST | 获取语音任务列表（查询） |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v1/consumervoice/captcha/send` | POST | 发送语音验证码 |
| `/cpus/v1/consumervoice/notice/send` | POST | 发送语音通知 |
| `/cpus/v1/consumervoice/customize/send` | POST | 发送个性化语音 |
| `/cpus/consumertimingvoice/cancelTimingVoice` | POST | 取消定时语音 |
| `/cpus/voice/cancel` | POST | 取消语音任务 |

### 10. 国际短信管理模块 (`src/components/page/client/IMS/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumerimsmessage/messages` | POST | 获取国际短信消息（查询） |
| `/cpus/consumertimingims/selectTimingPage` | POST | 获取定时国际短信列表（查询） |
| `/recharge/client/gjPrice` | GET | 获取国际价格 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumertimingims/cancelTimingIms` | POST | 取消定时国际短信 |

### 11. 账户信息管理模块 (`src/components/page/client/accountInformation/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumerclientinfo/getClientInfo` | GET | 获取客户信息 |
| `/cpus/consumerclientinfo/info` | GET | 获取详细信息 |
| `/cpus/consumerclientinfo/checkRealStatus` | GET | 检查实名状态 |
| `/cpus/consumerclientinfo/validatePassword/{password}` | GET | 验证密码 |
| `/cpus/consumerclientinfo/generatePassword` | GET | 生成密码 |
| `/cpus/consumerclientinfo/loginTelephoneManager/list` | POST | 获取登录手机列表（查询） |
| `/cpus/consumerclientinfo/homePage` | POST | 获取首页数据（查询） |
| `/cpus/consumerclientsms/overrun` | GET | 获取超限信息 |
| `/cpus/userLoginAdmin/loginPhoneInfo` | GET | 获取登录手机信息 |
| `/cpus/userLoginAdmin/sendVerificationCode` | GET | 发送验证码 |
| `/cpus/userLoginAdmin/verifiedStatus` | GET | 获取验证状态 |
| `/cpus/code/checkVerificationCode` | GET | 检查验证码 |
| `/cpus/code/sendVerificationCode` | GET | 发送验证码 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumerclientinfo/setting` | POST | 设置客户信息 |
| `/cpus/consumerclientinfo/updateLoginPassword` | GET | 更新登录密码 |
| `/cpus/consumerclientinfo/updPasswordV2` | PUT | 更新接口密码V2 |
| `/cpus/consumerclientinfo/saltV2` | PUT | 更新加密盐V2 |
| `/cpus/userLoginAdmin/phoneDecryptUnfreeze` | POST | 手机解密解冻 |
| `/cpus/userLoginAdmin/addLoginPhoneV2` | POST | 添加登录手机V2 |
| `/cpus/userLoginAdmin/deleteLoginPhoneV2` | DELETE | 删除登录手机V2 |
| `/cpus/userLoginAdmin/updateLoginPhone` | PUT | 更新登录手机 |
| `/cpus/userLoginAdmin/transferAdmin` | POST | 转移管理员 |

### 12. 充值费用中心 (`src/components/page/client/CostCenter/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/recharge/client/recharge/page` | GET | 获取充值记录列表 |
| `/recharge/client/balance/{productId}` | GET | 获取产品余额 |
| `/recharge/client/balance/notice/info` | GET | 获取余额提醒信息 |
| `/recharge/products` | GET | 获取产品列表 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/recharge/client/balance/notice/open` | POST | 开启余额提醒 |
| `/recharge/client/balance/notice/close` | POST | 关闭余额提醒 |

### 13. 黑白名单管理 (`src/components/page/client/accountInformation/BlacklistManagement/`, `src/components/page/client/accountInformation/whiteList/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| 无特定读接口，列表查询通过POST实现 | - | - |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/omcs/clientBlacklist/add` | POST | 添加黑名单 |
| `/omcs/clientBlacklist/batchDelete` | POST | 批量删除黑名单 |
| `/omcs/clientBlacklist/deleteById/{id}` | GET | 根据ID删除黑名单 |
| `/cpus/consumerClientIp` | POST | 新增IP白名单 |
| `/cpus/consumerClientIp` | PUT | 更新IP白名单 |
| `/cpus/consumerClientIp/{id}` | DELETE | 删除IP白名单 |

### 14. 文件管理 (`src/components/publicComponents/FileUpload.vue`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v3/file/{fileId}` | GET | 获取文件信息 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v3/file/upload` | POST | 文件上传 |
| `/cpus/v3/file/uploadAndCompress` | POST | 上传并压缩文件 |
| `/cpus/v3/file/discernMobile` | POST | 识别手机号文件 |

### 15. AI模板助手 (`src/components/common/AiTemplateAssistant/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v3/ai/template/remain/times` | GET | 查询剩余次数 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/v3/ai/template/generate` | POST | AI生成模板 |
| `/cpus/v3/ai/template/rewrite` | POST | AI改写模板 |

### 16. 闪信服务 (`src/components/page/client/flashTest/`)

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumerflashhourstatistics/list` | POST | 获取闪信统计列表（查询） |
| `/cpus/consumeronelogin/page` | POST | 获取一键登录列表（查询） |
| `/cpus/consumeronelogin/app/all` | GET | 获取所有应用 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/cpus/consumeronelogin` | POST | 新增一键登录 |
| `/cpus/consumeronelogin` | PUT | 更新一键登录 |
| `/cpus/consumeronelogin/{id}` | DELETE | 删除一键登录 |

### 17. 其他公共接口

#### 读操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/shops/shopgoods/bestSell` | GET | 获取热销商品 |
| `/contact/contact/open` | GET | 打开联系人 |
| `/omcs/operatingchannelprovincial/list` | GET | 获取省份通道列表 |
| `/omcs/operatingchannelinfo/operator` | GET | 获取运营商信息 |
| `/omcs/operatingchannelinfo/info` | GET | 获取通道信息 |
| `/cpus/consumerlabel/list` | GET | 获取标签列表 |
| `/cpus/consumerclientmessage/page` | POST | 获取消息列表（查询） |
| `/cpus/consumerclientmessage/markAsRead/{ids}` | GET | 标记已读 |
| `/cpus/consumerclientmessage/delete/{ids}` | GET | 删除消息 |
| `/cpus/consumerclient/sysLog/page` | POST | 获取系统日志（查询） |
| `/cpus/consumerCertificate/info` | GET | 获取证书信息 |
| `/cpus/consumerCertificate/idCardInfo` | GET | 获取身份证信息 |
| `/cpus/consumerCertificate/entLicenseInfo` | GET | 获取营业执照信息 |
| `/fiveWeb/compress/result` | GET | 获取压缩结果 |
| `/upms/generatekey/decryptMobile` | POST | 解密手机号 |

#### 写操作接口
| 接口路径 | 方法 | 功能说明 |
|---------|------|----------|
| `/slms/v3/shortLink/add` | POST | 添加短链接 |
| `/slms/shortLink/domain/update` | POST | 更新短链域名 |
| `/slms/shortLink/domainApply` | POST | 申请短链域名 |
| `/cpus/consumerlabel` | POST | 新增标签 |
| `/cpus/consumerlabel/{id}` | DELETE | 删除标签 |
| `/cpus/consumerclientlinkman/add` | POST | 添加联系人 |
| `/cpus/consumerclientlinkman/update` | PUT | 更新联系人 |
| `/cpus/consumerpartnerlinkman/add` | POST | 添加合作伙伴联系人 |
| `/cpus/consumerpartnerlinkman/update` | PUT | 更新合作伙伴联系人 |
| `/cpus/consumerCertificate` | POST | 提交证书信息 |
| `/cpus/consumerCertificate/verify` | POST | 验证证书 |
| `/cpus/consumerCertificate/signContract` | POST | 签署合同 |
| `/fiveWeb/compress/upload` | POST | 上传压缩文件 |
| `/fiveWeb/compress/asynVideo` | POST | 异步处理视频 |


| `/usls-slms/shortLink/domain/delete` | del | 删除短链白名单 |
| `/usls-slms/v3/shortLink/delete/` | del | 删除短链统计 |
| `/consumer-recharge/client/balance/notice/close` | post | 关闭余额提醒 |
| `consumer-recharge/client/balance/notice/open` | post | 开启余额提醒 |
| `cpus/consumersmsreplyrule` | post、put、DELETE | 短信自动回复｜
| `/cpus/v1/consumervideo/send` | POST | 发送视频短信 |
| `/cpus/v1/consumervideo/custom/create` | POST | 创建视频短信模版 |
| `/cpus/consumertimingvideo/cancelTimingMm` | POST | 取消接口定时 |
| `/cpus/consumerimsmessage/messages` | POST | 国际短信发送 |
| `/client-cpus/consumerclientlinkman/` | DELETE | 删除警告联系人 |



## GLS模块概述
GLS（General Logic Service）管理模块是SMS系统的核心管理平台，主要用于：
- 子用户管理
- 统计分析
- 费用管理
- 业务配置管理

## 接口分类标准
- **读操作（READ）**：获取数据、查询信息、验证状态等不会改变系统数据的操作
- **写操作（WRITE）**：创建、更新、删除等会改变系统数据的操作

---

## 1. 用户管理模块 (`/src/components/page/gls/UserManagement/`)

### 读操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/v3/consumer/manager/user/list` | POST | 获取用户列表（查询） | UserManagement.vue |
| `/cpus/v3/consumer/manager/user/info/{userId}` | GET | 获取用户详细信息 | UserDetails.vue |
v3/consumer/manager/realName
v3/consumer/manager/user/pwd
v3/consumer/manager/updateLoginPhone
v3/consumer/manager/addLoginPhoneV2
v3/consumer/manager/user/signature/saveInfo
v3/consumer/manager/overrun
v3/consumersmstemplate/manager/template/add
v3/consumersmstemplate/manager/template/update
/v3/consumersmstemplate/manager/delete/
v3/consumer/manager/user/signature/saveInfo
| `/cpus/v3/consumer/manager/user/{id}` | GET | 根据ID获取用户信息 | UserEditing.vue |
| `/cpus/v3/consumer/manager/user/ext` | GET | 获取用户扩展信息 | UserEditing.vue |
| `/cpus/v3/consumer/manager/user/signature/list` | GET | 获取用户签名列表 | UserManagement.vue |
| `/cpus/v3/consumer/manager/selectAllIndustry` | GET | 获取所有行业分类 | UserEditing.vue |
| `/cpus/v3/consumer/manager/realName/{userId}` | GET | 获取实名信息 | UserManagement.vue |
| `/upms/online/existUser/{username}` | GET | 检查用户名是否存在 | UserEditing.vue |
| `/cpus/v3/consumersmstemplate/selectClientTemplate` | POST | 获取客户模板列表（查询） | TemplateManagement.vue |

### 写操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/v3/consumer/manager/user/create` | POST | 创建用户 | UserEditing.vue |
| `/cpus/v3/consumer/manager/user/info` | PUT | 更新用户信息 | UserEditing.vue |
| `/cpus/v3/consumer/manager/user/del/{username}` | DELETE | 删除用户 | UserManagement.vue |
| `/cpus/v3/consumer/manager/user/status` | POST | 用户启用/停用 | UserManagement.vue |
| `/cpus/v3/consumer/manager/user/isState` | POST | 用户数据开启/关闭 | UserManagement.vue |
| `/cpus/v3/consumer/manager/user/signature/default` | PUT | 设置默认签名 | UserManagement.vue |
| `/cpus/v3/consumer/manager/user/export` | POST | 导出用户列表 | UserManagement.vue |
| `/cpus/userLoginAdmin/addLoginPhoneV2` | POST | 添加登录手机V2 | settingPhone.vue |
| `/cpus/userLoginAdmin/deleteLoginPhoneV2` | DELETE | 删除登录手机V2 | settingPhone.vue |
| `/cpus/userLoginAdmin/updateLoginPhone` | PUT | 更新登录手机 | settingPhone.vue |
| `/cpus/userLoginAdmin/transferAdmin` | POST | 转移管理员 | settingPhone.vue |
| `/cpus/userLoginAdmin/phoneDecryptUnfreeze` | POST | 手机解密解冻 | settingPhone.vue |

---

## 2. 短信统计分析模块 (`/src/components/page/gls/SMS/statisticalAnalysis/`)

### 读操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/v3/consumer/statistics/user/statistics` | POST | 获取用户发送统计（查询） | UserRecord.vue |
| `/cpus/v3/consumer/statistics/month/statistics` | POST | 获取月统计数据（查询） | MonthRecord.vue |
| `/cpus/v3/consumer/manager/sms/sub-accounts/message` | POST | 获取子用户短信发送明细（查询） | subSendDetails.vue |
| `/cpus/consumerpartner/reply/page` | POST | 获取子用户短信回复明细（查询） | subSmsRecord.vue |
| `/cpus/statistics/smsPage` | POST | 获取短信发送记录（查询） | ShortMessageRecording.vue |
| `/cpus/statistics/replyPage` | POST | 获取短信回复记录（查询） | ShortMessageRecording.vue |
| `/cpus/statistics/page` | POST | 获取统计分析数据（查询） | DataScreening.vue |
| `/cpus/statistics/sendCharts` | GET | 获取发送图表数据 | DataScreening.vue |
| `/cpus/statistics/statusReport` | GET | 获取状态报告 | DataScreening.vue |
| `/cpus/consumerdataoverviewday/businessOverview` | GET | 获取业务概览数据 | DataScreening.vue |
| `/cpus/consumerclientinfo/getClientInfo` | GET | 获取客户信息 | DataScreening.vue |
| `/cpus/shortlink/getdatalist` | POST | 获取短链统计数据（查询） | reportForm.vue |
| `/cpus/shortlink/getsldetaillist` | POST | 获取短链详情数据（查询） | detailShort.vue |
| `/cpus/statistics/selectTemNameByClient` | GET | 获取客户模板名称 | TemplateQuery.vue |
| `/cpus/statistics/templatePage` | POST | 获取模板统计数据（查询） | TemplateQuery.vue |

### 写操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/v3/consumer/statistics/user/statistics/export` | POST | 导出用户统计数据 | UserRecord.vue |
| `/cpus/v3/consumer/statistics/month/statistics/export` | POST | 导出月统计数据 | MonthRecord.vue |
| `/cpus/statistics/export` | POST | 导出统计数据 | TemplateManagement.vue |
| `/upms/generatekey/decryptMobile` | POST | 解密手机号 | 多个文件 |

---

## 3. 费用中心模块 (`/src/components/page/gls/CostCenter/`)

### 读操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/v3/consumer/record/user/manager/list` | POST | 获取用户余额管理列表（查询） | BalanceManagement.vue |
| `/recharge/partner/recharge/page` | GET | 获取合作伙伴充值记录（查询） | GlsRecord.vue |
| `/recharge/client/recharge/page` | GET | 获取客户充值记录（查询） | MindGlsRecord.vue |
| `/recharge/partner/balance/notice/info` | GET | 获取余额提醒信息 | BalanceManagement.vue |
| `/recharge/client/balance/{productId}` | GET | 获取产品余额 | DataScreening.vue |

### 写操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/recharge/partner/recharge` | POST | 合作伙伴充值/扣款 | BalanceManagement.vue |
| `/recharge/partner/balance/notice/open` | POST | 开启余额提醒 | BalanceManagement.vue |
| `/recharge/partner/balance/notice/close` | POST | 关闭余额提醒 | BalanceManagement.vue |

---

## 4. 彩信管理模块 (`/src/components/page/gls/MMS/`)

### 读操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/v3/consumer/statistics/user/statistics` | POST | 获取彩信用户统计（查询） | MMSuserRecord.vue |
| `/cpus/v3/consumer/statistics/month/statistics` | POST | 获取彩信月统计（查询） | MMSmonthRecord.vue |
| `/cpus/v3/consumer/manager/mms/sub-accounts/message` | POST | 获取子用户彩信发送明细（查询） | subMMSDetails.vue |

### 写操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/upms/generatekey/decryptMobile` | POST | 解密手机号 | subMMSDetails.vue |

---

## 5. 视频短信管理模块 (`/src/components/page/gls/VideoRMS/`)

### 读操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/v3/consumer/statistics/user/statistics` | POST | 获取视频用户统计（查询） | videoUserRecord.vue |
| `/cpus/v3/consumer/statistics/month/statistics` | POST | 获取视频月统计（查询） | videoMonthRecord.vue |
| `/cpus/v3/consumer/manager/rms/sub-accounts/message` | POST | 获取子用户视频发送明细（查询） | subVideoDetails.vue |

### 写操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/upms/generatekey/decryptMobile` | POST | 解密手机号 | subVideoDetails.vue |

---

## 6. 国际短信管理模块 (`/src/components/page/gls/IMS/`)

### 读操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/v3/consumer/statistics/user/statistics` | POST | 获取国际短信用户统计（查询） | ImsUserRecord.vue |
| `/cpus/v3/consumer/statistics/month/statistics` | POST | 获取国际短信月统计（查询） | ImsMonthRecord.vue |
| `/cpus/v3/consumer/manager/ims/sub-accounts/message` | POST | 获取子用户国际短信发送明细（查询） | subImsDetails.vue |
| `/recharge/partner/gjPrice` | GET | 获取国际价格表 | ImsPirce.vue |

### 写操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/recharge/partner/gjPrice/batch` | POST | 批量调整国际价格 | ImsPirce.vue |
| `/recharge/partner/gjPrice/reset` | POST | 恢复默认价格 | ImsPirce.vue |
| `/upms/generatekey/decryptMobile` | POST | 解密手机号 | subImsDetails.vue |

---

## 7. 签名管理模块 (`/src/components/page/gls/SMS/signature/`)

### 读操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/signature/signatureList` | POST | 获取签名列表（查询） | subSignatureManagement.vue |
| `/cpus/signature/findModel/realName` | GET | 获取实名信息 | subSignatureManagement.vue |

### 写操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| 无特定的签名管理写操作接口 | - | - | - |

---

## 8. 账户信息管理模块 (`/src/components/page/gls/accountInformation/`)

### 读操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/consumerclientinfo/getClientInfo` | GET | 获取客户信息 | account.vue |
| `/cpus/consumerclientinfo/info` | GET | 获取详细信息 | account.vue |
| `/cpus/consumerclientinfo/validatePassword/{password}` | GET | 验证密码 | account.vue |
| `/cpus/consumerclientinfo/generatePassword` | GET | 生成密码 | account.vue |
| `/cpus/consumerclientinfo/loginTelephoneManager/list` | POST | 获取登录手机列表（查询） | account.vue |
| `/cpus/userLoginAdmin/loginPhoneInfo` | GET | 获取登录手机信息 | account.vue |

### 写操作接口

| 接口路径 | 方法 | 功能说明 | 文件位置 |
|---------|------|----------|----------|
| `/cpus/consumerclientinfo/setting` | POST | 设置客户信息 | account.vue |
| `/cpus/consumerclientinfo/updateLoginPassword` | GET | 更新登录密码 | account.vue |
| `/cpus/consumerclientinfo/updPasswordV2` | PUT | 更新接口密码V2 | account.vue |
| `/cpus/consumerclientinfo/saltV2` | PUT | 更新加密盐V2 | account.vue |



---


## 文档更新记录

- **创建日期**: 2025-7-16
- **最后更新**: 2025-7-16
- **版本**: v1.0
- **说明**: 基于项目文件结构对所有API接口进行读写分类 
