{"name": "jl-mis", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "local": "vue-cli-service serve --open --mode test", "build": "vue-cli-service build --mode dev", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint", "show:config": "vue-cli-service inspect"}, "dependencies": {"@tensorflow-models/mobilenet": "^2.1.1", "@tensorflow/tfjs": "^4.21.0", "axios": "^0.18.0", "clipboard": "^2.0.8", "echarts": "^5.0.2", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.14", "mavon-editor": "^2.6.17", "moment": "^2.29.1", "vant": "^2.13.2", "vue": "^2.5.17", "vue-cropperjs": "^2.2.0", "vue-quill-editor": "3.0.6", "vue-router": "^3.0.1", "vue-schart": "^1.0.0", "vuedraggable": "^2.16.0", "vuex": "^3.0.1", "vuex-along": "^1.2.13"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.2.0", "@vue/cli-plugin-eslint": "^3.2.1", "@vue/cli-service": "^3.2.0", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-remove-console": "^6.9.4", "crypto-js": "^4.2.0", "eslint": "^5.10.0", "eslint-plugin-vue": "^5.0.0", "less": "^3.9.0", "less-loader": "^4.1.0", "mammoth": "^1.9.0", "vue-cli-plugin-element": "^1.0.0", "vue-template-compiler": "^2.5.17"}}