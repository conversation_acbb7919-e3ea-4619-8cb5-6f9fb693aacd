const productionGzipExtensions = ['js', 'css'] // 需要gzip压缩的文件后缀
const path = require('path')
const IS_PROD = ['production'].includes(process.env.NODE_ENV)
const filenameHashing = true
const productionSourceMap = !IS_PROD
const assetsDir = 'static'
let timeStamp = new Date().getTime();
function resolve (dir) {
  return path.join(__dirname, dir)
}
function getAssetPath (assetsDir, filePath) {
  return assetsDir
    ? path.posix.join(assetsDir, filePath)
    : filePath
}
const option = {
  /** 区分打包环境与开发环境
   * process.env.NODE_ENV==='production'  (打包环境)
   * process.env.NODE_ENV==='development' (开发环境)
   * baseUrl: process.env.NODE_ENV==='production'?"https://cdn.didabisai.com/front/":'front/',
   */
  // 基本路径
  baseUrl: './',
  // 输出文件目录
  outputDir: 'dist',
  assetsDir: "static",
  // eslint-loader 是否在保存的时候检查
  lintOnSave: false,
  // use the full build with in-browser compiler?
  // https://vuejs.org/v2/guide/installation.html#Runtime-Compiler-vs-Runtime-only
  runtimeCompiler: true,
  
  chainWebpack: (config) => {
    // 指定环境打包js路径
    if (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'test') {
      const isLegacyBundle = process.env.VUE_CLI_MODERN_MODE && !process.env.VUE_CLI_MODERN_BUILD
      const filename = getAssetPath(
        assetsDir,
        `js/[name]${isLegacyBundle ? `-legacy` : ``}${filenameHashing ? '.[contenthash:8]' : ''}.js`
      )
      config.mode('production').devtool(productionSourceMap ? 'source-map' : false).output.filename(filename).chunkFilename(filename)
    }
    // 修改图片输出路径
    // config.module.rule('images').test(/\.(png|jpe?g|gif|ico)(\?.*)?$/).use('url-loader').loader('url-loader').options({
    //   name: path.join('../assets/', 'img/[name].[ext]')
    // })
  },
  // webpack配置
  // see https://github.com/vuejs/vue-cli/blob/dev/docs/webpack.md
  //  chainWebpack: () => {},
  //  configureWebpack: () => {},
  //如果想要引入babel-polyfill可以这样写
  // configureWebpack: (config) => {
  //   config.entry = ["babel-polyfill", "./src/main.js"]
  // },
  // vue-loader 配置项
  // https://vue-loader.vuejs.org/en/options.html
  //  vueLoader: {},
  // 生产环境是否生成 sourceMap 文件

  productionSourceMap: false,
  // css相关配置
  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    extract: {
      filename: `static/css/[name].${timeStamp}.css`,
      chunkFilename: `static/css/chunk.[id].${timeStamp}.css`,
    },
    // 开启 CSS source maps?
    sourceMap: false,
    // css预设器配置项
    loaderOptions: {},
    // 启用 CSS modules for all css / pre-processor files.
    modules: false
  },
  
  // use thread-loader for babel & TS in production build
  // enabled by default if the machine has more than 1 cores
  //  parallel: require('os').cpus().length > 1,
  // 是否启用dll
  // See https://github.com/vuejs/vue-cli/blob/dev/docs/cli-service.md#dll-mode
  //  dll: false,
  // PWA 插件相关配置
  // see https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-pwa
  //  pwa: {},
  // webpack-dev-server 相关配置
  configureWebpack:{
    performance: {
      hints: 'warning',
      // 入口起点的最大体积
      maxEntrypointSize: 50000000,
      // 生成文件的最大体积
      maxAssetSize: 50000000,
      // 只给出 js 文件的性能提示
      assetFilter: function (assetFilename) {
        return assetFilename.endsWith('.js') || assetFilename.endsWith('.css') 
      },
      
    },
    output: { // 输出重构 打包编译后的js文件名称,添加时间戳.
      filename: `static/js/js[name].${timeStamp}.js`,
      chunkFilename: `static/js/chunk.[id].${timeStamp}.js`,
    }
  },
  devServer: {
    // open: process.platform === 'darwin',
    host: 'clients.zthysms.com',
    // port: 8080,
    // https: false,
    // hotOnly: false,
    // proxy: null, // 设置代理
    // before: app => {}
    // proxy: 'http://***********'
    proxy: {
      // '/api': {
      //   target: 'http://**********',
      //   changeOrigin: true
      // },
      '/gateway': {
        target: 'http://client.zthysms.cn',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/gateway/, '/gateway')
      }
    }
  },
  //    // 第三方插件配置
  //    pluginOptions: {
  //     // ...
  //    }
}

module.exports = option