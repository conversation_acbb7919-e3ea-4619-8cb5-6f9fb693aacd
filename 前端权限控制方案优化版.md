# SMS短信管理系统前端权限控制方案 (优化版)

## 方案概述

根据后端 `/cpus/consumerclientinfo/getClientInfo` 接口返回的 `writePermission` 字段，对前端所有写操作进行UI层面的权限控制，包括按钮禁用、表单禁用、操作提示等功能。

## 权限控制标识设计

### 后端接口返回格式
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "userInfo": {
      // 其他用户信息...
    },
    "writePermission": true  // 写权限控制字段：true=有写权限，false=只读权限
  }
}
```

## 前端权限控制实现方案

### 1. 权限管理 Store (Vuex) - 简化版

```javascript
// store/modules/permissions.js
const state = {
  writePermission: false,          // 写权限标识
  permissionsLoaded: false         // 权限是否已加载
}

const mutations = {
  SET_WRITE_PERMISSION(state, hasPermission) {
    state.writePermission = !!hasPermission
    state.permissionsLoaded = true
  },
  CLEAR_PERMISSIONS(state) {
    state.writePermission = false
    state.permissionsLoaded = false
  }
}

const actions = {
  // 获取用户权限
  async fetchUserPermissions({ commit }) {
    try {
      const response = await this.$api.get('/cpus/consumerclientinfo/getClientInfo')
      if (response.code === 200) {
        const writePermission = response.data.writePermission || false
        commit('SET_WRITE_PERMISSION', writePermission)
        return writePermission
      }
    } catch (error) {
      console.error('获取用户权限失败:', error)
      commit('CLEAR_PERMISSIONS')
    }
  },
  
  // 清除权限（退出登录时）
  clearPermissions({ commit }) {
    commit('CLEAR_PERMISSIONS')
  },

  // 刷新权限
  async refreshPermissions({ dispatch }) {
    return await dispatch('fetchUserPermissions')
  }
}

const getters = {
  // 检查是否有写权限
  hasWritePermission: (state) => {
    return state.writePermission && state.permissionsLoaded
  },
  
  // 检查权限是否已加载
  isPermissionsLoaded: (state) => {
    return state.permissionsLoaded
  },

  // 获取权限状态文本
  permissionStatusText: (state) => {
    if (!state.permissionsLoaded) return '权限加载中...'
    return state.writePermission ? '读写权限' : '只读权限'
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
```

### 2. 权限控制指令 (Vue Directive) - 简化版

```javascript
// directives/permission.js
import store from '@/store'

// 权限控制指令
export const permission = {
  bind(el, binding, vnode) {
    checkAndUpdatePermission(el, binding)
  },
  
  update(el, binding) {
    checkAndUpdatePermission(el, binding)
  }
}

// 检查并更新元素权限状态
function checkAndUpdatePermission(el, binding) {
  const { value } = binding
  
  // 如果没有传值或传值为true，表示需要写权限
  const needWritePermission = value === undefined || value === true
  
  if (needWritePermission) {
    const hasPermission = checkWritePermission()
    
    if (hasPermission) {
      // 有权限：恢复正常状态
      el.disabled = false
      el.classList.remove('permission-disabled')
      el.removeAttribute('title')
    } else {
      // 无权限：禁用状态
      el.disabled = true
      el.classList.add('permission-disabled')
      el.setAttribute('title', '您当前为只读权限，无法执行此操作')
    }
  }
}

// 检查写权限
function checkWritePermission() {
  const permissionsLoaded = store.getters['permissions/isPermissionsLoaded']
  const hasWritePermission = store.getters['permissions/hasWritePermission']
  
  // 如果权限还未加载，默认允许（避免页面闪烁）
  if (!permissionsLoaded) {
    return true
  }
  
  return hasWritePermission
}

// 权限检查函数导出，供组件内使用
export const hasWritePermission = checkWritePermission
```

### 3. 混入 (Mixin) 方式 - 简化版

```javascript
// mixins/permissionMixin.js
import { mapGetters } from 'vuex'

export const permissionMixin = {
  computed: {
    ...mapGetters('permissions', [
      'hasWritePermission',
      'isPermissionsLoaded',
      'permissionStatusText'
    ])
  },
  
  methods: {
    // 检查写权限
    checkWritePermission() {
      return this.hasWritePermission
    },
    
    // 显示权限不足提示
    showPermissionDenied() {
      this.$message.warning('您当前为只读权限，无法执行此操作')
    },

    // 权限检查并执行操作
    executeWithPermission(callback, errorCallback = null) {
      if (this.checkWritePermission()) {
        if (typeof callback === 'function') {
          callback()
        }
      } else {
        if (typeof errorCallback === 'function') {
          errorCallback()
        } else {
          this.showPermissionDenied()
        }
      }
    }
  }
}
```

## 组件中的使用示例

### 1. 使用指令方式 (推荐)

```vue
<template>
  <div>
    <!-- 简单使用：需要写权限 -->
    <el-button 
      type="primary" 
      v-permission
      @click="sendSms"
    >
      发送短信
    </el-button>
    
    <!-- 明确指定需要写权限 -->
    <el-button 
      type="danger" 
      v-permission="true"
      @click="deleteTemplate"
    >
      删除模板
    </el-button>
    
    <!-- 不需要权限控制的按钮 -->
    <el-button 
      type="info"
      @click="viewDetails"
    >
      查看详情
    </el-button>
  </div>
</template>
```

### 2. 使用混入方式

```vue
<template>
  <div>
    <!-- 权限状态显示 -->
    <div class="permission-status">
      <el-tag :type="hasWritePermission ? 'success' : 'warning'">
        {{ permissionStatusText }}
      </el-tag>
    </div>
    
    <!-- 按钮权限控制 -->
    <el-button 
      type="primary"
      :disabled="!hasWritePermission"
      @click="handleSendSms"
    >
      发送短信
    </el-button>
    
    <el-button 
      type="danger"
      :disabled="!hasWritePermission"
      @click="handleDeleteTemplate"
    >
      删除模板
    </el-button>
  </div>
</template>

<script>
import { permissionMixin } from '@/mixins/permissionMixin'

export default {
  name: 'SmsTemplate',
  mixins: [permissionMixin],
  
  methods: {
    handleSendSms() {
      this.executeWithPermission(() => {
        // 执行发送逻辑
        this.sendSms()
      })
    },
    
    handleDeleteTemplate() {
      this.executeWithPermission(() => {
        // 确认删除
        this.$confirm('确定要删除这个模板吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.deleteTemplate()
        })
      })
    },

    sendSms() {
      // 发送短信的具体逻辑
      console.log('发送短信')
    },

    deleteTemplate() {
      // 删除模板的具体逻辑
      console.log('删除模板')
    }
  }
}
</script>
```

### 3. 表格操作列权限控制

```vue
<template>
  <div>
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button 
        type="primary" 
        v-permission
        @click="addTemplate"
      >
        新增模板
      </el-button>
      
      <el-button 
        type="danger" 
        v-permission
        :disabled="!selectedIds.length"
        @click="batchDelete"
      >
        批量删除
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table 
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="name" label="模板名称"></el-table-column>
      <el-table-column prop="content" label="模板内容"></el-table-column>
      
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="viewTemplate(scope.row)"
          >
            查看
          </el-button>
          
          <el-button
            size="mini"
            v-permission
            @click="editTemplate(scope.row)"
          >
            编辑
          </el-button>
          
          <el-button
            size="mini"
            type="danger"
            v-permission
            @click="deleteTemplate(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { permissionMixin } from '@/mixins/permissionMixin'

export default {
  name: 'TemplateList',
  mixins: [permissionMixin],
  
  data() {
    return {
      tableData: [],
      selectedIds: []
    }
  },
  
  methods: {
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    
    addTemplate() {
      this.$router.push('/template/add')
    },
    
    editTemplate(row) {
      this.$router.push(`/template/edit/${row.id}`)
    },
    
    deleteTemplate(row) {
      this.executeWithPermission(() => {
        this.$confirm('确定要删除这个模板吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 删除逻辑
          this.performDelete(row.id)
        })
      })
    },
    
    batchDelete() {
      this.executeWithPermission(() => {
        this.$confirm(`确定要删除选中的 ${this.selectedIds.length} 个模板吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 批量删除逻辑
          this.performBatchDelete(this.selectedIds)
        })
      })
    }
  }
}
</script>
```

### 4. 表单权限控制

```vue
<template>
  <div>
    <el-form 
      :model="formData" 
      :rules="formRules"
      ref="form"
      label-width="120px"
    >
      <el-form-item label="模板名称" prop="name">
        <el-input 
          v-model="formData.name"
          :disabled="!hasWritePermission"
        />
      </el-form-item>
      
      <el-form-item label="模板内容" prop="content">
        <el-input 
          type="textarea"
          v-model="formData.content"
          :disabled="!hasWritePermission"
          :rows="4"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button 
          type="primary" 
          v-permission
          @click="saveTemplate"
        >
          保存
        </el-button>
        
        <el-button @click="cancel">
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
```

## 权限初始化流程

### 1. 应用启动时

```javascript
// main.js
import store from './store'
import { permission } from './directives/permission'

// 注册权限指令
Vue.directive('permission', permission)

// 应用启动后获取权限
new Vue({
  router,
  store,
  async created() {
    // 如果已登录，获取权限信息
    if (this.$common.getCookie('ZTGlS_TOKEN')) {
      try {
        await this.$store.dispatch('permissions/fetchUserPermissions')
      } catch (error) {
        console.error('权限初始化失败:', error)
      }
    }
  },
  render: h => h(App)
}).$mount('#app')
```

### 2. 登录成功后

```javascript
// 登录组件中
async submitForm() {
  try {
    // 执行登录
    const response = await this.$api.post('/auth/oauth/token', this.loginForm)
    
    if (response.code === 200) {
      // 保存token
      this.$common.setCookie('ZTGlS_TOKEN', response.data.access_token)
      
      // 获取用户权限
      await this.$store.dispatch('permissions/fetchUserPermissions')
      
      this.$message.success('登录成功')
      this.$router.push('/dashboard')
    }
  } catch (error) {
    this.$message.error('登录失败')
    console.error('登录错误:', error)
  }
}
```

### 3. 退出登录时

```javascript
// 退出登录
async logout() {
  try {
    await this.$api.post('/auth/loginOut')
  } catch (error) {
    console.error('退出登录请求失败:', error)
  } finally {
    // 清除权限
    this.$store.dispatch('permissions/clearPermissions')
    
    // 清除token
    this.$common.clearCookie('ZTGlS_TOKEN')
    
    // 跳转登录页
    this.$router.push('/login')
  }
}
```

### 4. 权限变更后刷新

```javascript
// 某些场景下可能需要刷新权限
async refreshUserPermissions() {
  try {
    const hasPermission = await this.$store.dispatch('permissions/refreshPermissions')
    this.$message.success(`权限已更新：${hasPermission ? '读写权限' : '只读权限'}`)
  } catch (error) {
    this.$message.error('权限刷新失败')
  }
}
```

## CSS样式

```css
/* 权限不足时的样式 */
.permission-disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.permission-disabled:hover {
  opacity: 0.5 !important;
}

/* Element UI 按钮权限样式 */
.el-button.permission-disabled {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

.el-button.permission-disabled:hover {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

/* 输入框权限样式 */
.el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

/* 权限状态标签样式 */
.permission-status {
  margin-bottom: 16px;
  text-align: right;
}

.permission-status .el-tag {
  font-size: 12px;
}
```

## 工具类和辅助方法

### 权限检查工具

```javascript
// utils/permission.js
import store from '@/store'

/**
 * 检查是否有写权限
 * @returns {boolean}
 */
export function hasWritePermission() {
  return store.getters['permissions/hasWritePermission']
}

/**
 * 检查权限是否已加载
 * @returns {boolean}
 */
export function isPermissionsLoaded() {
  return store.getters['permissions/isPermissionsLoaded']
}

/**
 * 获取权限状态文本
 * @returns {string}
 */
export function getPermissionStatusText() {
  return store.getters['permissions/permissionStatusText']
}

/**
 * 执行需要权限的操作
 * @param {Function} callback - 有权限时执行的回调
 * @param {Function} errorCallback - 无权限时执行的回调
 */
export function executeWithPermission(callback, errorCallback = null) {
  if (hasWritePermission()) {
    if (typeof callback === 'function') {
      callback()
    }
  } else {
    if (typeof errorCallback === 'function') {
      errorCallback()
    } else {
      // 默认提示
      if (window.Vue && window.Vue.$message) {
        window.Vue.$message.warning('您当前为只读权限，无法执行此操作')
      }
    }
  }
}
```

## 优势和特点

1. **简洁高效**: 基于单一权限字段，逻辑简单清晰
2. **易于维护**: 减少了复杂的权限映射和判断逻辑
3. **用户友好**: 提供清晰的权限状态提示和操作反馈
4. **响应迅速**: 纯前端UI控制，无需等待接口响应
5. **性能优良**: 权限检查开销最小
6. **扩展性好**: 未来如需细化权限，可在此基础上扩展

## 注意事项

1. **后端安全**: 前端权限控制仅为用户体验优化，后端必须实现真正的权限验证和接口拦截
2. **权限及时性**: 权限变更后需要重新获取用户信息或刷新页面
3. **错误处理**: 妥善处理权限获取失败的情况
4. **用户提示**: 无权限时给用户清晰的提示信息
5. **兼容性**: 确保权限未加载时页面能正常显示
6. **UI一致性**: 确保所有写操作按钮和表单元素都添加了权限控制

## 实施步骤

### 第一阶段：基础实施
1. 创建 permissions Store 模块
2. 实现权限指令和混入
3. 在关键页面添加权限控制

### 第二阶段：全面覆盖
1. 为所有写操作按钮添加权限控制
2. 对表单输入框添加禁用状态控制
3. 完善样式和用户提示

### 第三阶段：优化完善
1. 添加权限状态显示组件
2. 优化用户体验和交互反馈
3. 完善测试覆盖和文档

这个优化版本基于单一 `writePermission` 字段，专注于前端UI层面的权限控制，大大简化了权限控制逻辑，同时保持了良好的用户体验。后端的接口安全由服务器端负责实现。 