<template>
  <div class="enhanced-template-demo">
    <div class="demo-header">
      <h1>增强模版变量管理演示</h1>
      <p>展示现代化的模版变量提取、管理和配置功能</p>
    </div>

    <div class="demo-content">
      <!-- 模版输入区域 -->
      <el-card shadow="hover" class="input-card">
        <div slot="header">
          <span class="card-title">
            <i class="el-icon-edit"></i> 模版内容编辑
          </span>
        </div>
        
        <div class="input-section">
          <el-input
            type="textarea"
            placeholder="请输入包含变量的模版内容，例如：【某某公司】尊敬的{userName}，您的验证码是{validCode}，请在{expireTime}分钟内使用。"
            v-model="templateContent"
            :rows="4"
            maxlength="800"
            show-word-limit
            @input="onTemplateContentChange"
          />
          
          <div class="quick-examples">
            <span class="examples-label">快速示例：</span>
            <el-button 
              v-for="(example, index) in quickExamples" 
              :key="index"
              size="mini" 
              type="text" 
              @click="useExample(example)"
              class="example-btn"
            >
              {{ example.name }}
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 变量管理区域 -->
      <div class="variables-section" v-if="extractedVariables.length > 0">
        <el-card shadow="hover" class="variables-card">
          <div slot="header" class="variables-header">
            <span class="variables-title">
              <i class="el-icon-setting"></i> 模版变量管理
            </span>
            <el-badge :value="extractedVariables.length" class="variables-count">
              <span class="variables-count-text">已识别变量</span>
            </el-badge>
          </div>
          
          <div class="variables-content">
            <div class="variables-description">
              <el-alert
                title="变量配置说明"
                type="info"
                :closable="false"
                show-icon>
                <template slot="default">
                  <p>系统已自动识别模版中的变量，请为每个变量配置正确的类型。变量类型决定了该变量在实际使用时可以接受的数据格式。</p>
                </template>
              </el-alert>
            </div>
            
            <div class="variables-list">
              <div 
                v-for="(variable, index) in extractedVariables" 
                :key="variable.name"
                class="variable-item"
                :class="{ 'variable-item-error': !variable.type }"
              >
                <div class="variable-card">
                  <div class="variable-header">
                    <div class="variable-name-section">
                      <el-tag 
                        :type="variable.type ? 'success' : 'warning'"
                        size="medium"
                        class="variable-tag"
                      >
                        {{ '{' + variable.name + '}' }}
                      </el-tag>
                      <span class="variable-description">变量名称</span>
                    </div>
                    <div class="variable-actions">
                      <el-button 
                        type="text" 
                        size="mini" 
                        @click="editVariableName(index)"
                        class="edit-btn"
                      >
                        <i class="el-icon-edit"></i> 重命名
                      </el-button>
                      <el-button 
                        type="text" 
                        size="mini" 
                        @click="removeVariable(index)"
                        class="remove-btn"
                      >
                        <i class="el-icon-delete"></i> 删除
                      </el-button>
                    </div>
                  </div>
                  
                  <div class="variable-body">
                    <div class="variable-type-section">
                      <label class="variable-label">变量类型：</label>
                      <el-select 
                        v-model="variable.type" 
                        placeholder="请选择变量类型"
                        class="variable-type-select"
                        @change="onVariableTypeChange(variable, $event)"
                      >
                        <el-option 
                          v-for="type in variableTypes" 
                          :key="type.value"
                          :label="type.label" 
                          :value="type.value"
                        >
                          <div class="type-option">
                            <span class="type-name">{{ type.label }}</span>
                            <span class="type-format">{{ type.format }}</span>
                          </div>
                        </el-option>
                      </el-select>
                    </div>
                    
                    <div class="variable-info" v-if="variable.type">
                      <div class="variable-rule">
                        <i class="el-icon-info"></i>
                        <span>{{ getVariableRule(variable.type) }}</span>
                      </div>
                      <div class="variable-examples" v-if="getVariableExamples(variable.type)">
                        <span class="examples-label">示例：</span>
                        <el-tag 
                          v-for="example in getVariableExamples(variable.type)" 
                          :key="example"
                          size="mini" 
                          type="info"
                          class="example-tag"
                        >
                          {{ example }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="variables-summary" v-if="extractedVariables.length > 0">
              <el-divider content-position="left">
                <span class="summary-title">配置摘要</span>
              </el-divider>
              <div class="summary-content">
                <div class="summary-stats">
                  <div class="stat-item">
                    <span class="stat-number">{{ extractedVariables.length }}</span>
                    <span class="stat-label">总变量数</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-number">{{ configuredVariablesCount }}</span>
                    <span class="stat-label">已配置</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-number">{{ unconfiguredVariablesCount }}</span>
                    <span class="stat-label">未配置</span>
                  </div>
                </div>
                <div class="summary-actions">
                  <el-button 
                    type="primary" 
                    size="small"
                    @click="autoConfigureVariables"
                    :disabled="configuredVariablesCount === extractedVariables.length"
                  >
                    <i class="el-icon-magic-stick"></i> 智能配置
                  </el-button>
                  <el-button 
                    type="default" 
                    size="small"
                    @click="resetVariableTypes"
                  >
                    <i class="el-icon-refresh"></i> 重置配置
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 结果展示区域 -->
      <el-card shadow="hover" class="result-card" v-if="extractedVariables.length > 0">
        <div slot="header">
          <span class="card-title">
            <i class="el-icon-view"></i> 配置结果
          </span>
        </div>
        
        <div class="result-content">
          <div class="result-item">
            <label>模版内容：</label>
            <div class="result-value template-content">{{ templateContent }}</div>
          </div>
          
          <div class="result-item">
            <label>变量配置（JSON格式）：</label>
            <div class="result-value json-content">
              <pre>{{ JSON.stringify(variableParams, null, 2) }}</pre>
            </div>
          </div>
          
          <div class="result-actions">
            <el-button type="primary" @click="copyResult">
              <i class="el-icon-document-copy"></i> 复制配置
            </el-button>
            <el-button type="success" @click="exportResult">
              <i class="el-icon-download"></i> 导出配置
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 使用说明 -->
      <el-card shadow="never" class="guide-card">
        <div slot="header">
          <span class="card-title">
            <i class="el-icon-question"></i> 使用说明
          </span>
        </div>
        
        <div class="guide-content">
          <h4>支持的变量类型：</h4>
          <div class="type-list">
            <div v-for="type in variableTypes" :key="type.value" class="type-item">
              <div class="type-header">
                <span class="type-name">{{ type.label }}</span>
                <span class="type-format">{{ type.format }}</span>
              </div>
              <div class="type-rule">{{ type.rule }}</div>
              <div class="type-examples">
                <span>示例：</span>
                <span v-for="(example, index) in type.examples" :key="index" class="example">
                  {{ example }}{{ index < type.examples.length - 1 ? '、' : '' }}
                </span>
              </div>
            </div>
          </div>
          
          <h4>使用步骤：</h4>
          <ol>
            <li>在模版内容中输入包含变量的文本，变量格式为 <code>{变量名}</code></li>
            <li>系统会自动识别并提取所有变量</li>
            <li>为每个变量选择合适的类型</li>
            <li>可以使用"智能配置"功能自动推荐变量类型</li>
            <li>查看配置结果并复制或导出</li>
          </ol>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: "EnhancedTemplateDemo",
  data() {
    return {
      templateContent: "",
      extractedVariables: [],
      
      // 快速示例
      quickExamples: [
        {
          name: "验证码短信",
          content: "【某某公司】您的验证码是{validCode}，请在{expireTime}分钟内使用，如非本人操作请忽略。"
        },
        {
          name: "订单通知",
          content: "【购物平台】尊敬的{userName}，您的订单{orderNumber}已发货，预计{deliveryDate}送达，快递单号：{trackingNumber}。"
        },
        {
          name: "余额提醒",
          content: "【银行】尊敬的{customerName}，您的账户在{transactionDate}发生消费，金额{amount}，余额{balance}。"
        },
        {
          name: "会员营销",
          content: "【商城】尊敬的{memberName}，您有一张{couponAmount}优惠券即将过期，请于{expireDate}前使用，拒收请回复R。"
        }
      ],
      
      // 变量类型定义
      variableTypes: [
        {
          value: 'valid_code',
          label: '验证码',
          format: '{valid_code}',
          rule: '4-6位数字英文混合，支持英文大小写',
          examples: ['123456', 'A1B2C3', 'abc123']
        },
        {
          value: 'mobile_number',
          label: '电话号码',
          format: '{mobile_number}',
          rule: '1-15位纯数字',
          examples: ['13812345678', '02012345678', '4001234567']
        },
        {
          value: 'other_number',
          label: '其他号码',
          format: '{other_number}',
          rule: '1-32位字母+数字组合，支持中划线-',
          examples: ['ORDER-123456', 'SF1234567890', 'ID-ABC123']
        },
        {
          value: 'amount',
          label: '金额',
          format: '{amount}',
          rule: '支持数字（含英文小数点.）或数字的中文（壹贰叁肆伍陆柒捌玖拾佰仟万亿 圆元整角分厘毫）',
          examples: ['100.50', '99元', '壹佰元整']
        },
        {
          value: 'date',
          label: '时间',
          format: '{date}',
          rule: '符合时间的表达方式，也支持中文：2019年9月3日16时24分35秒',
          examples: ['2023-12-25', '2023年12月25日', '14:30:00']
        },
        {
          value: 'chinese',
          label: '中文汉字',
          format: '{chinese}',
          rule: '1-32中文，支持中文圆括号（）',
          examples: ['张三', '北京市（朝阳区）', '优惠活动']
        },
        {
          value: 'others',
          label: '其他',
          format: '{others}',
          rule: '1-35个中文数字字母组合，支持中文符号和空格',
          examples: ['Hello 世界', '产品名称 V2.0', '特殊符号！@#']
        }
      ]
    };
  },
  computed: {
    // 已配置变量数量
    configuredVariablesCount() {
      return this.extractedVariables.filter(v => v.type).length;
    },

    // 未配置变量数量
    unconfiguredVariablesCount() {
      return this.extractedVariables.filter(v => !v.type).length;
    },

    // 变量参数对象
    variableParams() {
      const params = {};
      this.extractedVariables.forEach(variable => {
        if (variable.type) {
          params[variable.name] = variable.type;
        }
      });
      return params;
    }
  },
  methods: {
    // 使用示例
    useExample(example) {
      this.templateContent = example.content;
      this.onTemplateContentChange(example.content);
    },

    // 模版内容变化
    onTemplateContentChange(content) {
      this.extractVariablesFromContent(content);
    },

    // 从模版内容中提取变量
    extractVariablesFromContent(content) {
      if (!content) {
        this.extractedVariables = [];
        return;
      }

      const variableRegex = /{([^}]+)}/g;
      const matches = [];
      let match;

      while ((match = variableRegex.exec(content)) !== null) {
        const variableName = match[1].trim();
        if (variableName && !matches.find(m => m.name === variableName)) {
          matches.push({
            name: variableName,
            type: '',
            originalValue: match[0]
          });
        }
      }

      // 保留已有的类型配置
      matches.forEach(newVar => {
        const existingVar = this.extractedVariables.find(v => v.name === newVar.name);
        if (existingVar) {
          newVar.type = existingVar.type;
        }
      });

      this.extractedVariables = matches;
    },

    // 获取变量规则说明
    getVariableRule(typeValue) {
      const type = this.variableTypes.find(t => t.value === typeValue);
      return type ? type.rule : '';
    },

    // 获取变量示例
    getVariableExamples(typeValue) {
      const type = this.variableTypes.find(t => t.value === typeValue);
      return type ? type.examples : [];
    },

    // 变量类型变更事件
    onVariableTypeChange(variable, newType) {
      variable.type = newType;
    },

    // 编辑变量名
    editVariableName(index) {
      const variable = this.extractedVariables[index];
      this.$prompt('请输入新的变量名', '重命名变量', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: variable.name,
        inputValidator: (value) => {
          if (!value) return '变量名不能为空';
          if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
            return '变量名只能包含字母、数字和下划线，且必须以字母开头';
          }
          const isDuplicate = this.extractedVariables.some((v, i) => 
            i !== index && v.name === value
          );
          if (isDuplicate) return '变量名不能重复';
          return true;
        }
      }).then(({ value }) => {
        const oldName = variable.name;
        variable.name = value;
        
        // 更新模版内容中的变量名
        this.templateContent = this.templateContent.replace(
          new RegExp(`{${oldName}}`, 'g'),
          `{${value}}`
        );
        
        this.$message({
          type: 'success',
          message: '变量名修改成功'
        });
      }).catch(() => {});
    },

    // 删除变量
    removeVariable(index) {
      const variable = this.extractedVariables[index];
      
      this.$confirm(`确定要删除变量 {${variable.name}} 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从模版内容中移除该变量
        this.templateContent = this.templateContent.replace(
          new RegExp(`{${variable.name}}`, 'g'),
          `[${variable.name}]`
        );
        
        // 从变量列表中移除
        this.extractedVariables.splice(index, 1);
        
        this.$message({
          type: 'success',
          message: '变量删除成功'
        });
      }).catch(() => {});
    },

    // 智能配置变量类型
    autoConfigureVariables() {
      this.extractedVariables.forEach(variable => {
        if (!variable.type) {
          const name = variable.name.toLowerCase();
          
          if (name.includes('code') || name.includes('验证码')) {
            variable.type = 'valid_code';
          } else if (name.includes('phone') || name.includes('mobile') || name.includes('电话')) {
            variable.type = 'mobile_number';
          } else if (name.includes('amount') || name.includes('money') || name.includes('金额') || name.includes('价格')) {
            variable.type = 'amount';
          } else if (name.includes('date') || name.includes('time') || name.includes('时间') || name.includes('日期')) {
            variable.type = 'date';
          } else if (name.includes('order') || name.includes('number') || name.includes('id') || name.includes('编号')) {
            variable.type = 'other_number';
          } else if (name.includes('name') || name.includes('姓名') || name.includes('用户')) {
            variable.type = 'chinese';
          } else {
            variable.type = 'others';
          }
        }
      });
      
      this.$message({
        type: 'success',
        message: '智能配置完成'
      });
    },

    // 重置变量类型
    resetVariableTypes() {
      this.$confirm('确定要重置所有变量的类型配置吗？', '重置确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.extractedVariables.forEach(variable => {
          variable.type = '';
        });
        
        this.$message({
          type: 'success',
          message: '变量类型已重置'
        });
      }).catch(() => {});
    },

    // 复制结果
    copyResult() {
      const result = {
        template: this.templateContent,
        variables: this.variableParams
      };
      
      const textArea = document.createElement('textarea');
      textArea.value = JSON.stringify(result, null, 2);
      document.body.appendChild(textArea);
      textArea.select();
      
      try {
        document.execCommand('copy');
        this.$message({
          type: 'success',
          message: '配置已复制到剪贴板'
        });
      } catch (err) {
        this.$message({
          type: 'error',
          message: '复制失败，请手动复制'
        });
      }
      
      document.body.removeChild(textArea);
    },

    // 导出结果
    exportResult() {
      const result = {
        template: this.templateContent,
        variables: this.variableParams,
        exportTime: new Date().toISOString()
      };
      
      const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `template-config-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      this.$message({
        type: 'success',
        message: '配置已导出'
      });
    }
  }
};
</script>

<style lang="less" scoped>
.enhanced-template-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: #303133;
    margin-bottom: 10px;
  }
  
  p {
    color: #666;
    font-size: 14px;
  }
}

.demo-content {
  .input-card, .variables-card, .result-card, .guide-card {
    margin-bottom: 20px;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 8px;
        color: #1890ff;
      }
    }
  }

  .input-section {
    .quick-examples {
      margin-top: 15px;
      
      .examples-label {
        font-size: 12px;
        color: #666;
        margin-right: 10px;
      }
      
      .example-btn {
        margin-right: 10px;
        margin-bottom: 5px;
        font-size: 12px;
      }
    }
  }

  .result-content {
    .result-item {
      margin-bottom: 20px;
      
      label {
        display: block;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .result-value {
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        background: #fafafa;
        
        &.template-content {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
          line-height: 1.5;
        }
        
        &.json-content {
          pre {
            margin: 0;
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
    
    .result-actions {
      text-align: center;
      
      .el-button {
        margin: 0 8px;
      }
    }
  }

  .guide-content {
    h4 {
      color: #303133;
      margin: 20px 0 15px 0;
    }
    
    .type-list {
      .type-item {
        margin-bottom: 15px;
        padding: 12px;
        border: 1px solid #e6e6e6;
        border-radius: 6px;
        background: #fafafa;
        
        .type-header {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 5px;
          
          .type-name {
            font-weight: 600;
            color: #1890ff;
          }
          
          .type-format {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            color: #666;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
          }
        }
        
        .type-rule {
          font-size: 13px;
          color: #666;
          margin-bottom: 5px;
          line-height: 1.4;
        }
        
        .type-examples {
          font-size: 12px;
          color: #999;
          
          .example {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          }
        }
      }
    }
    
    ol {
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        line-height: 1.5;
        color: #666;
        
        code {
          background: #f0f0f0;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
      }
    }
  }
}

// 引入变量管理样式（与template.vue中的样式保持一致）
@import './template-variables-styles.less';
</style>
