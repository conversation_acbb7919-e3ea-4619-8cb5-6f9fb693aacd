// 增强的模版变量管理样式
.variables-section {
  margin: 20px 0;

  .variables-card {
    border: 1px solid #e6f7ff;
    background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
    
    &:hover {
      box-shadow: 0 8px 25px rgba(24, 144, 255, 0.12);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
  }

  .variables-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .variables-title {
      font-size: 16px;
      font-weight: 600;
      color: #1890ff;
      
      i {
        margin-right: 8px;
        font-size: 18px;
      }
    }

    .variables-count {
      .variables-count-text {
        font-size: 12px;
        color: #666;
        margin-left: 8px;
      }
    }
  }

  .variables-content {
    .variables-description {
      margin-bottom: 20px;
      
      /deep/ .el-alert {
        border-radius: 8px;
        border: 1px solid #e6f7ff;
        
        .el-alert__content {
          p {
            margin: 0;
            line-height: 1.6;
            color: #666;
          }
        }
      }
    }

    .variables-list {
      .variable-item {
        margin-bottom: 16px;
        
        &.variable-item-error {
          .variable-card {
            border-color: #ff7875;
            background: linear-gradient(135deg, #fff2f0 0%, #fff1f0 100%);
          }
        }

        .variable-card {
          border: 1px solid #d9d9d9;
          border-radius: 12px;
          padding: 16px;
          background: #ffffff;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
          }

          .variable-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .variable-name-section {
              display: flex;
              align-items: center;
              gap: 12px;

              .variable-tag {
                font-size: 14px;
                font-weight: 600;
                padding: 6px 12px;
                border-radius: 6px;
              }

              .variable-description {
                font-size: 12px;
                color: #8c8c8c;
              }
            }

            .variable-actions {
              display: flex;
              gap: 8px;

              .edit-btn {
                color: #1890ff;
                
                &:hover {
                  color: #40a9ff;
                  background: #e6f7ff;
                }
              }

              .remove-btn {
                color: #ff4d4f;
                
                &:hover {
                  color: #ff7875;
                  background: #fff2f0;
                }
              }
            }
          }

          .variable-body {
            .variable-type-section {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 12px;

              .variable-label {
                font-size: 14px;
                font-weight: 500;
                color: #262626;
                min-width: 80px;
              }

              .variable-type-select {
                flex: 1;
                max-width: 300px;
                
                /deep/ .el-input__inner {
                  border-radius: 6px;
                  border: 1px solid #d9d9d9;
                  
                  &:focus {
                    border-color: #40a9ff;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                  }
                }
              }
            }

            .variable-info {
              background: #fafafa;
              border-radius: 8px;
              padding: 12px;
              border-left: 3px solid #1890ff;

              .variable-rule {
                display: flex;
                align-items: flex-start;
                gap: 8px;
                margin-bottom: 8px;
                font-size: 13px;
                color: #595959;
                line-height: 1.5;

                i {
                  color: #1890ff;
                  margin-top: 2px;
                }
              }

              .variable-examples {
                display: flex;
                align-items: center;
                gap: 8px;
                flex-wrap: wrap;

                .examples-label {
                  font-size: 12px;
                  color: #8c8c8c;
                  font-weight: 500;
                }

                .example-tag {
                  font-size: 11px;
                  border-radius: 4px;
                  background: #f0f0f0;
                  border: 1px solid #d9d9d9;
                }
              }
            }
          }
        }
      }
    }

    .variables-summary {
      margin-top: 24px;
      
      .summary-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }

      .summary-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
        border-radius: 12px;
        padding: 16px;
        border: 1px solid #b7eb8f;

        .summary-stats {
          display: flex;
          gap: 24px;

          .stat-item {
            text-align: center;

            .stat-number {
              display: block;
              font-size: 24px;
              font-weight: 700;
              color: #1890ff;
              line-height: 1;
            }

            .stat-label {
              display: block;
              font-size: 12px;
              color: #8c8c8c;
              margin-top: 4px;
            }
          }
        }

        .summary-actions {
          display: flex;
          gap: 8px;

          .el-button {
            border-radius: 6px;
            font-weight: 500;
            
            &.el-button--primary {
              background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
              border: none;
              
              &:hover {
                background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
              }
            }
          }
        }
      }
    }
  }
}

// 变量类型选择器样式
/deep/ .type-option {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .type-name {
    font-weight: 500;
    color: #262626;
  }

  .type-format {
    font-size: 11px;
    color: #8c8c8c;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
}
