<template>
  <div class="smart-template-demo">
    <div class="demo-header">
      <h1>智能模版生成演示</h1>
      <p>输入包含具体内容的短信示例，系统将自动识别变量并生成模版</p>
    </div>

    <div class="demo-content">
      <!-- 智能识别区域 -->
      <el-card shadow="hover" style="margin-bottom: 20px;">
        <div slot="header" class="clearfix">
          <span style="font-weight: bold; color: #409EFF;">
            <i class="el-icon-magic-stick"></i> 智能模版生成
          </span>
        </div>
        
        <div style="margin-bottom: 15px;">
          <span style="font-size: 14px; color: #666;">
            请输入包含具体内容的短信示例：
          </span>
        </div>
        
        <el-input
          type="textarea"
          placeholder="例如：【某某公司】您的验证码是123456，请在5分钟内使用，如非本人操作请忽略。"
          v-model="smartExample"
          :rows="4"
          maxlength="800"
          show-word-limit
          style="margin-bottom: 15px;">
        </el-input>
        
        <div style="text-align: right; margin-bottom: 15px;">
          <el-button @click="clearExample">清空</el-button>
          <el-button type="primary" @click="analyzeExample" :loading="analyzing">
            <i class="el-icon-magic-stick"></i> 智能识别
          </el-button>
        </div>

        <!-- 预设示例 -->
        <div style="margin-bottom: 15px;">
          <span style="font-size: 12px; color: #666;">快速试用示例：</span>
          <div style="margin-top: 8px;">
            <el-button 
              v-for="(example, index) in presetExamples" 
              :key="index"
              size="mini" 
              type="text" 
              @click="usePresetExample(example)"
              style="margin-right: 10px; margin-bottom: 5px;"
            >
              {{ example.name }}
            </el-button>
          </div>
        </div>

        <!-- 识别结果展示 -->
        <div v-if="recognitionResults.length > 0">
          <el-divider content-position="left">识别结果</el-divider>
          
          <div style="background: #f5f7fa; padding: 15px; border-radius: 4px; margin-bottom: 15px;">
            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">生成的模版：</div>
            <div style="font-weight: bold; color: #303133; font-size: 16px; line-height: 1.5;">
              {{ generatedTemplate }}
            </div>
          </div>
          
          <div style="margin-bottom: 15px;">
            <span style="font-size: 14px; color: #666;">识别到的变量（点击可修改变量名）：</span>
          </div>
          
          <div class="recognition-variables" style="margin-bottom: 15px;">
            <el-tag
              v-for="(result, index) in recognitionResults"
              :key="index"
              :type="result.confirmed ? 'success' : 'info'"
              closable
              @close="removeResult(index)"
              @click="editVariableName(index)"
              style="margin: 3px 8px 3px 0; cursor: pointer; font-size: 13px;"
            >
              <span style="color: #666;">{{ result.originalValue }}</span>
              <i class="el-icon-arrow-right" style="margin: 0 5px;"></i>
              <span style="font-weight: bold;">{{ '{' + result.variableName + '}' }}</span>
              <span style="margin-left: 5px; font-size: 11px; opacity: 0.8;">
                ({{ getVariableTypeLabel(result.suggestedType) }})
              </span>
            </el-tag>
          </div>
          
          <div style="text-align: center;">
            <el-button type="success" @click="copyTemplate">
              <i class="el-icon-document-copy"></i> 复制模版
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 使用说明 -->
      <el-card shadow="never">
        <div slot="header">
          <span style="font-weight: bold;">使用说明</span>
        </div>
        <div class="usage-guide">
          <h4>支持的变量类型：</h4>
          <ul>
            <li><strong>验证码：</strong> 4-6位数字或字母数字组合，如：123456、A1B2C3</li>
            <li><strong>手机号：</strong> 11位手机号码，如：13812345678</li>
            <li><strong>金额：</strong> 数字金额，如：100.50、99元</li>
            <li><strong>时间：</strong> 各种时间格式，如：2023年12月25日、14:30、2023-12-25</li>
            <li><strong>订单号：</strong> 字母数字组合，如：ORDER123456</li>
            <li><strong>其他数字：</strong> 其他数字内容，如：房间号、编号等</li>
          </ul>
          
          <h4>使用技巧：</h4>
          <ul>
            <li>输入真实的短信内容示例，包含具体的数值</li>
            <li>系统会自动识别可变的部分并生成对应的变量</li>
            <li>可以点击变量标签修改变量名称</li>
            <li>生成的模版可以直接复制使用</li>
          </ul>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: "SmartTemplateDemo",
  data() {
    return {
      smartExample: "",
      analyzing: false,
      recognitionResults: [],
      generatedTemplate: "",
      
      // 预设示例
      presetExamples: [
        {
          name: "验证码短信",
          content: "【测试公司】您的验证码是123456，请在5分钟内使用，如非本人操作请忽略。"
        },
        {
          name: "订单通知",
          content: "【购物平台】您的订单ORDER20231225001已发货，预计2023年12月27日送达，快递单号：SF1234567890。"
        },
        {
          name: "余额提醒",
          content: "【银行】您的账户在12月25日14:30发生消费，金额99.50元，余额1000.00元。"
        },
        {
          name: "会员营销",
          content: "【商城】尊敬的会员张先生，您有一张100元优惠券即将过期，请于2023年12月31日前使用，拒收请回复R。"
        }
      ]
    };
  },
  methods: {
    // 使用预设示例
    usePresetExample(example) {
      this.smartExample = example.content;
    },

    // 清空示例
    clearExample() {
      this.smartExample = "";
      this.recognitionResults = [];
      this.generatedTemplate = "";
    },

    // 分析示例
    analyzeExample() {
      if (!this.smartExample.trim()) {
        this.$message({
          message: "请输入示例文本",
          type: "warning"
        });
        return;
      }

      this.analyzing = true;
      
      setTimeout(() => {
        this.recognitionResults = this.performTextAnalysis(this.smartExample);
        this.generatedTemplate = this.generateTemplate();
        this.analyzing = false;
        
        if (this.recognitionResults.length === 0) {
          this.$message({
            message: "未识别到可变内容，请检查示例文本",
            type: "info"
          });
        } else {
          this.$message({
            message: `成功识别到 ${this.recognitionResults.length} 个变量`,
            type: "success"
          });
        }
      }, 800);
    },

    // 执行文本分析
    performTextAnalysis(text) {
      const results = [];
      
      const patterns = [
        {
          regex: /\b\d{4,6}\b/g,
          type: 'valid_code',
          namePrefix: 'code'
        },
        {
          regex: /1[3-9]\d{9}/g,
          type: 'mobile_number',
          namePrefix: 'phone'
        },
        {
          regex: /\d+\.?\d*元?/g,
          type: 'amount',
          namePrefix: 'amount'
        },
        {
          regex: /\d{4}年\d{1,2}月\d{1,2}日/g,
          type: 'date',
          namePrefix: 'date'
        },
        {
          regex: /\d{1,2}:\d{1,2}/g,
          type: 'date',
          namePrefix: 'time'
        },
        {
          regex: /[A-Z][A-Z0-9]{5,}/g,
          type: 'other_number',
          namePrefix: 'orderNo'
        },
        {
          regex: /[A-Z]{2}\d{8,}/g,
          type: 'other_number',
          namePrefix: 'trackingNo'
        }
      ];

      let usedNames = new Set();
      let nameCounters = {};

      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.regex.exec(text)) !== null) {
          const originalValue = match[0];
          
          let baseName = pattern.namePrefix;
          if (!nameCounters[baseName]) {
            nameCounters[baseName] = 1;
          }
          
          let variableName = baseName;
          if (nameCounters[baseName] > 1) {
            variableName = `${baseName}${nameCounters[baseName]}`;
          }
          
          while (usedNames.has(variableName)) {
            nameCounters[baseName]++;
            variableName = `${baseName}${nameCounters[baseName]}`;
          }
          
          usedNames.add(variableName);
          nameCounters[baseName]++;

          const existingResult = results.find(r => r.originalValue === originalValue);
          if (!existingResult) {
            results.push({
              originalValue,
              variableName,
              suggestedType: pattern.type,
              confirmed: false,
              startIndex: match.index,
              endIndex: match.index + originalValue.length
            });
          }
        }
      });

      return results.sort((a, b) => a.startIndex - b.startIndex);
    },

    // 生成模版
    generateTemplate() {
      if (this.recognitionResults.length === 0) {
        return this.smartExample;
      }

      let template = this.smartExample;
      const sortedResults = [...this.recognitionResults].sort((a, b) => b.startIndex - a.startIndex);
      
      sortedResults.forEach(result => {
        const before = template.substring(0, result.startIndex);
        const after = template.substring(result.endIndex);
        template = before + `{${result.variableName}}` + after;
      });

      return template;
    },

    // 获取变量类型标签
    getVariableTypeLabel(type) {
      const typeLabels = {
        'valid_code': '验证码',
        'mobile_number': '手机号',
        'amount': '金额',
        'date': '时间',
        'other_number': '其他号码',
        'chinese': '中文',
        'others': '其他'
      };
      return typeLabels[type] || '其他';
    },

    // 移除识别结果
    removeResult(index) {
      this.recognitionResults.splice(index, 1);
      this.generatedTemplate = this.generateTemplate();
    },

    // 编辑变量名
    editVariableName(index) {
      const result = this.recognitionResults[index];
      this.$prompt('请输入新的变量名', '编辑变量名', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: result.variableName,
        inputValidator: (value) => {
          if (!value) return '变量名不能为空';
          if (!/^[a-zA-Z][a-zA-Z0-9]*$/.test(value)) {
            return '变量名只能包含字母和数字，且必须以字母开头';
          }
          const isDuplicate = this.recognitionResults.some((r, i) => 
            i !== index && r.variableName === value
          );
          if (isDuplicate) return '变量名不能重复';
          return true;
        }
      }).then(({ value }) => {
        this.recognitionResults[index].variableName = value;
        this.generatedTemplate = this.generateTemplate();
        this.$message({
          type: 'success',
          message: '变量名修改成功'
        });
      }).catch(() => {});
    },

    // 复制模版
    copyTemplate() {
      if (!this.generatedTemplate) {
        this.$message({
          message: "没有可复制的模版",
          type: "warning"
        });
        return;
      }

      // 创建临时文本区域来复制文本
      const textArea = document.createElement('textarea');
      textArea.value = this.generatedTemplate;
      document.body.appendChild(textArea);
      textArea.select();
      
      try {
        document.execCommand('copy');
        this.$message({
          type: 'success',
          message: '模版已复制到剪贴板'
        });
      } catch (err) {
        this.$message({
          type: 'error',
          message: '复制失败，请手动复制'
        });
      }
      
      document.body.removeChild(textArea);
    }
  }
};
</script>

<style lang="less" scoped>
.smart-template-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: #303133;
    margin-bottom: 10px;
  }
  
  p {
    color: #666;
    font-size: 14px;
  }
}

.recognition-variables {
  .el-tag {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

.usage-guide {
  h4 {
    color: #303133;
    margin: 15px 0 10px 0;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
      color: #666;
      
      strong {
        color: #303133;
      }
    }
  }
}
</style>
