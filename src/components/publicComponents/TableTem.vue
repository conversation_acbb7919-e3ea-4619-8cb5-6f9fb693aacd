<!-- // 数据说明如props所示
// 表格数据转换显示如orderManagement页面显示：在表头数据中新增一个属性 formatData: function(val) { return val == '1' ? '启用' : '停用' } 
// 如果要转换时间戳 要在引用表格页面中的操作：1、引入date.js   2、formatData:function(val) {
//  return formatDate(new Date(val*1000), 'yyyy-MM-dd hh:mm:ss');
// } -->
<template>
<div class="tableTem">
 <el-table
    v-loading="tableDataObj.loading2"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.6)"
    element-loading-customClass='loadingStyle'
    :data="tableDataObj.tableData"
    :max-height="tableDataObj.tableStyle.height"
    :border="tableDataObj.tableStyle.border"
    :stripe="tableDataObj.tableStyle.stripe"
    :style="tableDataObj.tableStyle.style"
    :default-expand-all=tableDataObj.tableStyle.isDefaultExpand
    @selection-change="handleSelectionChange"
    >
    <!-- 复选框一栏 -->
    <el-table-column
      type="selection"
      align="center"
      width="55" v-if="tableDataObj.tableStyle.isSelection">
    </el-table-column>
    <!-- 折叠栏 -->
     <el-table-column type="expand" label="展开项" width="60px" v-if="tableDataObj.tableStyle.isExpand">
      <template slot-scope="props">
        <el-form label-position="left" inline class="demo-table-expand">
          <el-form-item :class="[tableDataObj.tableStyle.expandLine2 ? 'expand2Line' : '']"    :label="item.showName" v-for="(item,index) in tableDataObj.tableLabelExpand" :key="index">
            <!-- 有数据变化和条件的颜色变化 -->
            <span v-if="item.showCondition && item.formatData"  :class="[item.showCondition.condition==props.row[item.prop] ? 'red':'']" >{{ props.row[item.prop] | formatters(item.formatData) }}</span>
            <!-- 只有条件颜色变化 -->
            <span v-else-if="item.showCondition" :class="[item.showCondition.condition.indexOf(props.row[item.prop])>-1 ? 'red':'']" >{{ props.row[item.prop] | formatters(item.formatData) }}</span>
            <!-- 只有表格数据整理 -->
            <span v-else-if="item.formatData" :style="{color:item.showColorTag ? item.showColorTag.color :'' }"> {{ props.row[item.prop] | formatters(item.formatData) }}</span>
            <!-- 正常 -->
           <span v-else :style="{color:item.showColorTag ? item.showColorTag.color :'' }">{{ props.row[item.prop] }}</span>  
          </el-form-item>
        </el-form>
      </template>
    </el-table-column>
    <!-- 数据表格 -->
     <el-table-column v-for="(item,index) in tableDataObj.tableLabel"
     :prop="item.prop"
      :key="index"
      :label="item.showName"
      :fixed="item.fixed"
      :sortable="item.sortable"
      :width="item.width">
      <template slot-scope="scope">
        <!-- 有数据变化和条件的颜色变化 -->
         <span v-if="item.showCondition && item.formatData"  :class="[item.showCondition.condition==scope.row[item.prop] ? 'red':'']" >{{ scope.row[item.prop] | formatters(item.formatData) }}</span>
         <!-- 只有条件颜色变化 -->
         <span v-else-if="item.showCondition" :class="[item.showCondition.condition.indexOf(scope.row[item.prop])>-1 ? 'red':'']" >{{ scope.row[item.prop] | formatters(item.formatData) }}</span>
         <!-- 只有表格数据整理 -->
         <span v-else-if="item.formatData" :style="{color:item.showColorTag ? item.showColorTag.color :'' }"> {{ scope.row[item.prop] | formatters(item.formatData) }}</span>
         <!-- 不同颜色标签 -->
         <div v-else-if="item.showTags">
           <el-tag class="comTableTag" :style="{background:item.showTags.bgColor[tagIndex]}"   v-for="(tag,tagIndex) in scope.row[item.prop]" :key="tagIndex">{{tag}}</el-tag>
         </div>
         <!-- 当有通道方法时 -->
         <div v-else-if="item.Channel">
          <span v-for="(item,Index) in (scope.row[item.prop])?(scope.row[item.prop]).split(','):(scope.row[item.prop])" :key="Index" @click="ChannelRef(item)" style="color:rgb(22, 165, 137);cursor: pointer;">{{ item+" "}}</span>  
         </div>
         <!-- <span v-else-if="item.Channel" @click="ChannelRef(scope.row[item.prop])" style="color:rgb(22, 165, 137);cursor: pointer;">{{ scope.row[item.prop] }}</span>   -->
         <!-- 正常 -->
         <div
         class="item-prop"
            v-else
            :style="{ color: item.showColorTag ? item.showColorTag.color : '' }"
          >
            <div v-if="item.showName == '标签'">
              <el-tag v-if="scope.row[item.prop]">{{
                scope.row[item.prop]
              }}</el-tag>
            </div>
            <div
              @click="handPhone(scope.row, scope.$index)"
              v-else-if="item.showName == '手机号码'"
              style="color: #16a589; cursor: pointer"
              >{{ scope.row[item.prop] }}</div
            >
            <div v-else>
              <el-tooltip class="item" v-if="tips=='view'&&(item.showName == '发送内容'||item.showName == '短信内容')" effect="dark" placement="top">
                <div slot="content">
                  <span v-if="scope.row[item.prop]">字数:{{scope.row[item.prop].length}}个</span>
                </div>
                <span class="span">{{scope.row[item.prop]}}</span>
              </el-tooltip>
              <span class="span" v-else>{{scope.row[item.prop]}}</span>
            </div>
            <i v-if="item.copy" style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row[item.prop],$event )"></i>
          </div>
         
         <!-- <div v-if="item.showName=='标签'">
           <el-tag v-if="scope.row[item.prop]">{{ scope.row[item.prop]}}</el-tag>
         </div>
         <span @click="handPhone(scope.row,scope.$index)" v-else-if="item.showName=='手机号码'" style="color:#16A589;cursor: pointer;">{{ scope.row[item.prop] }}</span>
         <span v-else :style="{color:item.showColorTag ? item.showColorTag.color :'' }">{{ scope.row[item.prop] }}</span>   -->
      </template>
    </el-table-column>
    <!-- 操作栏 -->
    <el-table-column label="操作" v-if="tableDataObj.tableOptions || tableDataObj.conditionOption" fixed="right" :width="tableDataObj.tableStyle.optionWidth">
      <template slot-scope="scope">
        <div v-permission class="elButton" v-if="tableDataObj.tableOptions" type="text" v-for="(item,index) in tableDataObj.tableOptions"
         :key="index"
         :style="{color:item.color}"
          @click="handelOptionButton(item.optionMethod,scope.row)"
          ><i :class="item.icon"></i>{{item.optionName}}</div>
          <!-- 特殊条件的按钮 -->
          <div v-permission class="elButton" v-if="(item.otherOptionName) || (!item.otherOptionName && item.contactData==scope.row[item.contactCondition])"  type="text" v-for="(item,index) in tableDataObj.conditionOption"
          :key="index+10">
            <span :style="{color:item.optionButtonColor}"  @click="handelOptionButton(item.optionMethod,scope.row)" v-if="item.contactData==scope.row[item.contactCondition]"><i :class="item.icon"></i>{{item.optionName}}</span>
            <span :style="{color:item.optionOtherButtonColor}" @click="handelOptionButton(item.otherOptionMethod,scope.row)" v-else-if="item.otherOptionName"><i :class="item.otherIcon"></i>{{item.otherOptionName}}</span>
          </div>
      </template>
    </el-table-column>
 </el-table>
 <slot name="pagination"></slot>
  </div>  
</template>
<script>
import clip from '../page/utils/clipboard'
export default {  
    name:'tableTemp',
    props:{
      tableDataObj:Object,
      tips:String
    //  tableData:Array,//表格数据
    //  tableLabel:Array,//表头
    //  tableStyle:Object,//表格样式
    //  tableOptions:Array,//表格操作栏
    //  tableLabelExpand:Array//折叠列
    },
    created(){
      console.log(this.tips,'tips');
    },
    methods:{ 
      handleCopy(name,event){
        clip(name, event)
      },
        handelOptionButton(methods,row){
          this.$emit("handelOptionButton",{"methods":methods,"row":row});
        },
        handelColumnBtn(){
          this.$emit('handelColumnBtn')
        },
        //将选中的行发送到父组件
        handleSelectionChange(val) {
            const selectionArr = [];
            val.forEach(function (el) {
                selectionArr.push(el);
            });
            this.$emit('handelSelection', selectionArr);
        },
        ChannelRef(val) {
            this.$emit('save',val)
        },
        handPhone(val,index){
          this.$emit('handPhone', val,index);
        }
         
    }

}
</script>
<style>
body .el-table th.gutter{
    display: table-cell!important;
}
.el-table .el-table__header tr ,.el-table .el-table__header tr th,.el-table__fixed-right-patch{
  background-color: #f5f5f5;
}
.elButton i {
  margin-right: 3px
}
.tableTem .el-form-item {
  margin-right: 0;
  margin-bottom: 0 !important;
}
.tableTem .el-table__expanded-cell {
  padding: 6px 18px !important;
}
.tableTem .el-form-item {
  width:100%;
}
.tableTem .el-form-item__content {
  max-width: calc(100% - 100px);
}
.tableTem .demo-table-expand label {
  color: #99a9bf;
}
.tableTem .el-form-item--small .el-form-item__content,.tableTem  .el-form-item--small .el-form-item__label {
  line-height: 24px;
}
.comTableTag {
    display: inline-block;
    height: 30px !important;
    line-height: 27px !important;
    margin: 3px;
    padding: 3px;
    color: #fff !important;
    padding: 0 10px !important;
}
.red {
  color:red;
}
.tableTem .el-form-item.expand2Line {
  max-width:33.33% !important;
}
.tableTem .expand2Line .el-form-item__content {
  max-width:calc(100% - 130px) !important;
  color:#b3b0b0 !important;
}
.elButton {
  cursor: pointer;
  color: #16A589;
  display: inline-block;
  padding: 0px 8px;
}
.span{
  white-space: pre-wrap;
}
.item-prop{
  display: flex;
}
</style>
<style>
.el-loading-spinner i {
    margin-top: -10px;
    margin-bottom: 10px;
    font-size: 30px;
}

</style>




