<!-- 柱状图 -->
<!-- 
abscissa 横坐标数据
ordinate 纵坐标数据
color 主题颜色
title 标题
-->
<template>
    <div :id="id" :style="style"></div>
</template>
<script>
let echarts = require('echarts')
export default {
    name:'BarGraph',
    props:{
        bargraphobj:{
            type: Object
        },
        id:{
            type: String
        },
        width: {
            type: String,
            default: "100%"
        },
        height: {
            type: String,
            default: "400px"
        }
    },
    computed: {
        style() {
            return {
                height: this.height,
                width: this.width
            };
        }
    },
    methods: {
        handleSetEcharts: function(){
            let myChart = echarts.init(document.getElementById(this.id));
            var option = {
                color:this.bargraphobj.color ,
                grid:this.bargraphobj.grid,
                tooltip: {},
                xAxis: {
                    data: this.bargraphobj.abscissa1,
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB',//左边线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#666',//坐标值得具体的颜色
    
                        }
                    }
                },
                yAxis: {
                    data: this.bargraphobj.abscissa2,
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB',//左边线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#666',//坐标值得具体的颜色
    
                        }
                    }
                },
                series: [{
                    name: this.bargraphobj.title,
                    type: 'bar',
                    barWidth : this.bargraphobj.barWidth,//柱图宽度
                    label: {
                        normal: {
                            show: true,
                            position: this.bargraphobj.position,
                            fontWeight: 'bold'
                        }
                    },
                    data: this.bargraphobj.ordinate
                }]
            };

            myChart.setOption(option); 
            window.addEventListener('resize',function(){
                myChart.resize();
            },{ passive: true })
        }
    },
    watch:{
        bargraphobj:{
            handler(newValue, oldValue) {
                this.handleSetEcharts()　
    　　　　 },
    　　　　 deep: true
        }
    },
    mounted() {
        this.handleSetEcharts()
    }
}
</script>
<style scoped>
 /* .statistical-chart{
       height:380px;
   } */
</style>
