<template>
  <div>
    <el-table
      :data="phoneList"
      class="Login-c-p-getPhone"
      border
      style="width: 100%"
    >
      <el-table-column
        align="center"
        prop="consumerName"
        label="编号"
        width="120"
      >
      </el-table-column>
      <el-table-column prop="mobile" align="center" label="手机号">
      </el-table-column>
      <el-table-column align="center" width="80" label="选择">
        <template slot-scope="scope">
          <el-radio
            v-if="!flag"
            @change.native="getCurrentRow(scope.$index)"
            :label="scope.$index"
            v-model="radio"
            class="textRadio"
            >&nbsp;</el-radio
          >
          <el-radio
            v-else
            :disabled="true"
            @change.native="getCurrentRow(scope.$index)"
            :label="scope.$index"
            v-model="radio"
            class="textRadio"
            >&nbsp;</el-radio
          >
        </template>
      </el-table-column>
    </el-table>
    <el-form
      :model="setphoneFrom.ruleForm1"
      :rules="setphoneFrom.rules1"
      ref="ruleForm1"
      class="demo-ruleForm"
      label-width="120px"
    >
      <el-form-item label="手机验证码" prop="verCode" style="margin: 40px auto">
        <el-input
          v-model="setphoneFrom.ruleForm1.verCode"
          style="display: inline-block; width: 180px"
        ></el-input>
        <el-button
          type="primary"
          plain
          style="width: 124px; padding: 9px 0px"
          @click="CountdownCode"
          v-if="nmb == 120"
          >获取验证码</el-button
        >
        <el-button
          type="primary"
          plain
          style="width: 124px; padding: 9px 0px"
          disabled
          v-else
          >重新获取({{ nmb }})</el-button
        >
      </el-form-item>
      <el-form-item style="">
        <el-button @click="handleClose()" style="width: 100px; padding: 9px 0"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="submitForm('ruleForm1')"
          style="width: 100px; padding: 9px 0"
          >确定</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: ["specificTime", "formData1", "productType", "isDownload","phoneList"],
  data() {
    var code = (rule, value, callback) => {
      if (!this.phoneData) {
        return callback(new Error("请选中手机号"));
      } else if (value == "") {
        return callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    return {
      flag: false,
      radio: "",
      phoneData: "",
      nmb: 120,
      timer: null,
      setphoneFrom: {
        ruleForm1: {
          verCode: "",
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
        },
      },
    };
  },
  methods: {
    // getLoginPhone() {
    //     this.$api.post(
    //         this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
    //         {},
    //         (res) => {
    //           if (res.code == 200) {
    //             this.phoneList = res.data.data;
    //           }
    //         }
    //       );
    // //   this.$nextTick(function () {
    // //     this.$on("getgetLoginPhone", function () {
          
    // //     });
    // //   });
    // },
    getCurrentRow(val) {
      // console.log(val,'ll');
      // this.count = val
      // if(this.count == val){
      //     this.flag = true
      // }else{
      //     this.flag = false
      // }
      this.phoneData = this.phoneList[val].mobile; //赋值手机号
    },
    // 获取验证码倒计时
    CountdownCode() {
      if (this.phoneData) {
        --this.nmb;
        this.timer = setInterval((res) => {
          --this.nmb;
          if (this.nmb < 1) {
            this.nmb = 120;
            this.flag = false;
            clearInterval(this.timer);
          } else {
            this.flag = true;
          }
        }, 1000);
        this.$api.get(
          this.API.cpus +
            "code/sendVerificationCode?phone=" +
            this.phoneData +
            "&flag=4",
          {},
          (res) => {
            if (res.code == 200) {
              this.flag = true;
              this.$message({
                type: "success",
                duration: "2000",
                message: "验证码已发送至手机!",
              });
            } else {
              this.flag = true;
              this.$message({
                type: "warning",
                message: "验证码未失效，需失效后重新获取!",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "请先选中手机号码",
          type: "warning",
        });
      }
    },
    handleClose() {
      this.$parent.handleClose();
    },
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let aa = Object.assign({}, this.formData1);
          aa.isDownload = this.isDownload;
          aa.productType = this.productType;
          aa.smsCode = this.setphoneFrom.ruleForm1.verCode
          if(aa.productType == 7 || aa.productType == 9 ){
            if (this.specificTime === "") {
                //选择时间范围
                aa.flag = "5";
            } else {
                //选择其他
                aa.flag = this.specificTime;
            }
          }
          
          this.$api.post(this.API.cpus + "statistics/export", aa, (res) => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                duration: "2000",
                message: "已加入到文件下载中心!",
              });
              this.$parent.handleClose();
            } else {
              this.$message({
                type: "error",
                duration: "2000",
                message: res.msg,
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.demo-ruleForm {
  width: 500px;
  margin: 0 auto;
}
</style>