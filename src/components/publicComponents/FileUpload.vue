<template>
    <div>
        <el-upload class="upload-demo" :action="action" :headers='header' :multiple="multiple" :limit="limit"
            :fileStyle="fileStyle" :show-file-list="showfileList" :on-success="handleSuccess" :on-remove="handleRemove"
            :before-upload="beforeAvatarUpload" :on-progress='onProgress' :list-type='listType'
            :on-exceed="handleExceed" :on-preview="handlePictureCardPreview" :file-list="fileList">
            <div class="fileuploadss">
                <el-button size="small" type="primary" plain><i class="el-icon-upload"> {{ test }}</i></el-button>
                <div slot="tip" class="el-upload__tip file-tips">{{ tip }}</div>
            </div>
        </el-upload>
        <el-dialog title="在线预览" :visible.sync="dialogVisible" width="700px">
            <el-image style="width: 650px;height: 550px;" :src="dialogImageUrl" :preview-src-list="[dialogImageUrl]">
            </el-image>
            <!-- <img width="100%" :src="dialogImageUrl" alt=""> -->
        </el-dialog>
    </div>

</template>
<script>
export default {
    name: 'FileUpload',
    props: {
        limit: Number, //最大允许上传个数
        multiple: Boolean, //是否支持多选
        fileStyle: Object, //文件格式
        showfileList: Boolean, //文件列表
        tip: String,
        action: String, //地址
        del: Boolean,//是否删除
        listType: {
            default: 'text',
            type: String
        },
        button: String,
        fileListS: String,
    },
    data() {
        return {
            fileList: [],
            header: {},
            endingName: '',
            fileName: [],
            dialogImageUrl: "",
            dialogVisible: false,
            test: "文件上传"
        };
    },
    methods: {
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
        //移除
        handleRemove(file, fileList) {
            this.$emit('fileup', file, fileList);
            this.fileList = fileList;
        },
        //上传成功
        handleSuccess(res, file, fileList) {
            this.$emit('fileupres', res, fileList);
            this.fileList = fileList;
        },
        onProgress(event, file, fileList) {
            this.$emit('onProgress', event, file, fileList);
            this.fileList = fileList;
        },
        //限制用户上传文件格式和大小
        beforeAvatarUpload(file) {
            let endingCode = file.name;//结尾字符
            this.endingName = endingCode.slice(endingCode.lastIndexOf('.') + 1, endingCode.length);
            let isStyle = false; //文件格式
            const isSize = file.size / 1024 / 1024 < this.fileStyle.size; //文件大小
            console.log(isSize)
            for (let i = 0; i < this.fileStyle.style.length; i++) {
                if (this.endingName === this.fileStyle.style[i]) {
                    isStyle = true;
                    break;
                }
            }
            //不能重复上传文件
            let fileArr = this.fileList;
            let fileNames = [];
            if (fileArr.length > 0) {
                for (let k = 0; k < fileArr.length; k++) {
                    fileNames.push(fileArr[k].name)
                }
            }
            if (fileNames.indexOf(endingCode) !== -1) {
                this.$message.error('不能重复上传文件');
                return false;
            } else if (!isStyle) { //文件格式判断
                this.$message.error(this.tip);
                return false;
            } else {
                //文件大小判断
                if (!isSize) {
                    this.$message.error('上传文件大小不能超过' + this.fileStyle.size);
                    return false;
                }
            }
        },
        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择${this.limit}个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },
    },
    mounted() {
        this.header = { Authorization: "Bearer" + this.$common.getCookie('ZTGlS_TOKEN') };
    },
    watch: {
        del(val) {
            if (val == false) {
                this.fileList = [];
            }
        },
        fileListS: {
            handler(val) {
                if (val) {
                    let img = val.split(",").filter(item => item.trim() !== '');
                    console.log(img, 'img');
                    this.fileList = img.map((item) => {
                        return {
                            name: item,
                            url: this.API.imgU + item,
                        };
                    });
                } else {
                    this.fileList = []
                }
            },
            deep: true,
            immediate: true // 立即执行
        },
        dialogVisible(val){
            if(!val){
                this.dialogImageUrl = ''
            }
        }
    }
}
</script>
<style>
.fileuploadss {
    /* margin-bottom:10px; */
    text-align: left;
}

.file-tips {
    margin-top: 0;
}
</style>
