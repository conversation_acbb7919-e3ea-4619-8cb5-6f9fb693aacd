<template>
    <div>
        <el-dialog title="操作验证 " :visible.sync="dialogVisible" width="550px" :before-close="handleClose">
            <el-form ref="smsform" label-width="120px" :model="formInline">
                <div class="Login-c-p-getPhone">
                    <p>您今日查看明码号码次数已达上限，为保护客户信息安全，</p>
                    <p>验证码将会发送至管理员手机号：{{ loginInfo.mobile }}，请注意查收！</p>
                </div>
                <el-form-item label="手机验证码" prop="verifyCode"
                    :rules="filter_rules({ required: true, type: 'code', message: '验证码必填！' })">
                    <el-input v-model="formInline.verifyCode" style="width:250px;"></el-input>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" @click.prevent="send"
                        v-if="nmb == 120">获取验证码</el-button>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{ nmb
                    }})</el-button>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
                <el-button type="primary" @click="submitForm('smsform')">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import bus from '../common/bus';
export default {
    name: 'ResetNumber',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        infoData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            dialogVisible: false,
            nmb: 120,
            formInline: {
                verifyCode: ""
            },
            loginInfo: {
                isAdmin: null,
                consumerName: "",
                mobile: "",
                remark: "",
                id: ""
            },
        }
    },
    created() {
        // console.log(this.visible,'visible');
        this.dialogVisible = this.visible;
        this.loginInfo = this.infoData;
        // this.getLoginInfo();
    },
    methods: {
        handleClose() {
            this.dialogVisible = false;
            bus.$emit('closeVideo', this.dialogVisible);
        },
        // getLoginInfo() {
        //     this.$api.get(
        //         this.API.cpus + "userLoginAdmin/loginPhoneInfo",
        //         {},
        //         (res) => {
        //             if (res.code == 200) {
        //                 this.loginInfo = res.data;
        //             }
        //         }
        //     );
        // },
        send() {
            this.$api.get(this.API.cpus + "userLoginAdmin/sendVerificationCode?flag=1&phoneId=" + this.loginInfo.id, {}, res => {
                if (res.code == 200) {
                    this.$message({
                        type: 'success',
                        duration: '2000',
                        message: '验证码已发送至手机!'
                    });
                    --this.nmb;
                    this.timer = setInterval(res => {
                        --this.nmb;
                        if (this.nmb < 1) {
                            this.nmb = 120;
                            clearInterval(this.timer);
                        };
                    }, 1000);
                } else {
                    this.$message({
                        type: 'warning',
                        message: res.msg
                    });
                }

            })
        },
        submitForm(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$api.post(
                        this.API.cpus + "userLoginAdmin/phoneDecryptUnfreeze",
                        {
                            flag:"1",
                            // phoneId: this.loginInfo.id,
                            verifyCode: this.formInline.verifyCode
                        },
                        (res) => {
                            if (res.code == 200) {
                                this.$message({
                                    type: 'success',
                                    message: '解密次数已重置成功！'
                                });
                                this.handleClose();
                            }else{
                                this.$message({
                                    type: 'error',
                                    message: res.msg
                                });
                            }
                        }
                    );
                } else {
                    console.log('error submit!!');
                    return false;
                }
            })
        },
    },
    watch: {
        // visible(val) {
        //     console.log(val,'val');

        //     this.dialogVisible = val
        // },
        dialogVisible(val) {
            if (!val) {
                this.nmb = 120;
                this.$refs['smsform'].resetFields();
                this.formInline.verifyCode = "";
                clearInterval(this.timer);
            }
        }
    }
}
</script>

<style lang="less" scoped>
.Login-c-p-getPhone {
    margin-top: 20px;
    color: #909399;
    font-size: 14px;
    margin-left: 16px;
    margin-bottom: 10px;
}
</style>