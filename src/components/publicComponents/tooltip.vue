<template>
    <div>
        <el-tooltip :placement="placement" :effect="effect" :disabled="isShowTooltip">
            <template slot="content">
                <div style="width: auto;max-width: 400px;">
                    {{`${content}`}}
                </div>
                
            </template>
            <div ref="parent" @mouseover="onMouseOver()" :class="className">
                <span  ref="child">{{`${content}`}}</span>
            </div>
        </el-tooltip>
    </div>
</template>

<script>
export default {
    props: {
        placement: {
            type: String,
            default: 'top'
        },
        //主题
        effect: {
            type: String,
            default: 'dark'
        },
        // 内容
        content: {
            type: String,
            default: ''
        },
        // 类名
        className:{
            type:String,
            default:""
        },
        // 字数
        count:{
            type:String,
            default:""
        },
        // 类型
        typeLable:{
            type:String,
            default:""
        },
        widthpx:{
            type:String,
            default:''
        }

    },
    data() {
        return {
            // parent: "",
            // child: "",
            isShowTooltip: false,
        }
    },
    methods: {
        onMouseOver() {
            console.log(this.content,'content')
            const parentWidth = this.$refs.parent.offsetWidth // 获取元素父级可视宽度
            const contentWidth = this.$refs.child.offsetWidth // 获取元素可视宽度
            this.isShowTooltip = contentWidth <= parentWidth
            // if(this.className == 'multiline-text'){
            //     this.isShowTooltip = false
            // }else{
                
            //     console.log(this.isShowTooltip,'this.isShowTooltip ')
            // }
            
            // console.log(parentWidth,'parentWidth')
            // console.log(contentWidth,'contentWidth')
            // console.log(this.isShowTooltip,'this.isShowTooltip')
        }
    },
    mounted() {

    },

}
</script>

<style scoped lang="less">
.spanColor{
    width: 400px;
}
.url {
  color: #409eff;
  text-decoration: underline;
}
// .tempContent {
//   .prompt {
//     color: #F56C6C;
//     display: inline-block;
//     font-weight: 800;
//     /* background: rgba(0, 0, 0, .5); */
//     /* text-shadow: 3px 3px 5px #FF0000; */
//   }

//   .prompt_error {
//     color: #E6A23C;
//     display: inline-block;
//     text-decoration: underline;
//     font-weight: 800;
//     /* background: rgba(0, 0, 0, .5); */
//     /* text-shadow: 3px 3px 5px #FF0000; */
//   }
// }
</style>