 <!--
  1. type 日期类型  year/month/date/dates/ week/datetime 例：2018-11-04 08:34:12/datetimerange  例：2018-11-04 08:34:12 - 2018-11-04 08:34:12 /daterange  例：2018-11-04 - 2018-11-04 08:34:12
  2. defaultTime	选中日期后的默认具体时刻
 3.  datePluginValue  选择器打开时默认显示的时间
  -->
<template>
    <el-date-picker
      v-model="datePluginValueList.datePluginValue"
      :type="datePluginValueList.type"
      :range-separator="datePluginValueList.range"
      :start-placeholder="datePluginValueList.start"
      :end-placeholder="datePluginValueList.end"
      :default-time="datePluginValueList.defaultTime" 
      :placeholder="datePluginValueList.placeholder"
      :picker-options="datePluginValueList.pickerOptions"
      :clearable="datePluginValueList.clearable"
      :value-format="datePluginValueList.value"
      @change="hande"
      @blur="getdateval"
      >
    </el-date-picker>
</template>
<script>
  export default {
    name:'DatePlugin',
    props:["datePluginValueList"],
    data () {
        return{
            value:''
        }
    },
    methods: {
        hande: function (val) {
            if(val){
                if(val.length == 2){
                    let startTime = this.moment(val[0]).format("YYYY-MM-DD");
                    let endTime = this.moment(val[1]).format("YYYY-MM-DD");
                    this.$emit('handledatepluginVal',startTime,endTime)
                }else{
                    let staTime = this.moment(val).format("YYYY-MM-DD HH:mm:ss");
                    this.$emit('handledatepluginVal',staTime)
                }
            }
            this.value=val;
        },
        getdateval: function (){//日期input框是否有值
            this.$emit('IsThereValue', this.value);
        }
    }
  }

</script>