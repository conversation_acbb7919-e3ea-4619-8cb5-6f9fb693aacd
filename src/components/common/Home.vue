<template>
    <div class="wrapper">
        <v-head></v-head>
        <v-sidebar></v-sidebar>
        <div class="content-box" :class="{ 'content-collapse': collapse }">
            <v-tags></v-tags>
            <div class="content">
                <transition name="move" mode="out-in">
                    <!-- <keep-alive :include="tagsList">
                        <router-view></router-view>
                    </keep-alive> -->
                    <router-view></router-view>
                </transition>
            </div>
        </div>
        <!-- <div v-if="isShowBar!=0" class="footer">
            <span v-if="hostname != 'partner.zthysms.com'" style="color:#aaa">Copyright ©2011-2020 融合云通信 <a href='http://www.miibeian.gov.cn' target="_Blank" style="color: #aaa;">沪ICP备09065403号-3</a> <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011202008204" target="_Blank" style="color: #aaa;">沪公网安备 31011202008204号</a><img style="position: relative;top: 3px;left: 3px;width: 16px;" src="../../assets/images/record_icon.png" alt=""></span>
        </div> -->
    </div>
</template>

<script>
import vHead from './Header.vue';
import vSidebar from './Sidebar.vue';
import vTags from './Tags.vue';
import bus from './bus';
export default {
    data() {
        return {
            hostname: window.location.hostname,
            tagsList: [],
            collapse: false,
            isShowBar: 0,
        }
    },
    components: {
        vHead, vSidebar, vTags
    },
    created() {
        bus.$on('collapse', msg => {
            this.collapse = msg;
        })

        // 只有在标签页列表里的页面才使用keep-alive，即关闭标签之后就不保存到内存中了。
        bus.$on('tags', msg => {
            let arr = [];
            for (let i = 0, len = msg.length; i < len; i++) {
                msg[i].name && arr.push(msg[i].name);
            }
            this.tagsList = arr;
        })
        this.versions()
    },
    methods: {
        versions() {
            if ((navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i))) {
                this.isShowBar = 0
            } else {
                this.isShowBar = 1
            }
        },
       
    }
}
</script>
<style scoped>
.footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 30px;
    /* left: 13%; */
    text-align: center;
    line-height: 30px;
    background: #324157;
}

.dialog-content {
    text-align: center;
    font-size: 14px;
    background: #fff2d1;
    color: #906e12;
    padding: 10px;
    border-radius: 5px;
}
.move-enter-active,
.move-leave-active {
  transition: opacity 0.1s;
}

.move-enter,
.move-leave-to {
  opacity: 0;
}
</style>
