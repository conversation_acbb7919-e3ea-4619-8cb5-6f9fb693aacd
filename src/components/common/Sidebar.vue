<template>
  <div class="sidebar">
    <el-menu
      class="sidebar-el-menu"
      :default-active="onRoutes"
      :collapse="collapse"
      @select="handleSelect"
      background-color="#324157"
      text-color="#bfcbd9"
      active-text-color="#20a0ff"
      unique-opened
      router
    >
      <template v-for="item in getMenus">
        <template v-if="item.children.length">
          <el-submenu :index="item.component + ''" :key="item.id">
            <template slot="title">
              <i
                :class="[{ icon: true, iconfont: true }, item.icon]"
                style="margin-right: 10px"
              ></i
              ><span slot="title">{{ item.name }}</span>
            </template>
            <template v-for="subItem in item.children">
              <el-submenu
                v-if="subItem.children.length"
                :index="subItem.component + ''"
                :key="subItem.id"
              >
                <template slot="title">{{ subItem.name }}</template>
                <el-menu-item
                  v-for="(threeItem, i) in subItem.children"
                  :key="i"
                  :index="threeItem.component + ''"
                >
                  {{ threeItem.name }}
                </el-menu-item>
              </el-submenu>
              <el-menu-item
                v-else
                :index="subItem.component + ''"
                :key="subItem.id"
              >
                {{ subItem.name }}
              </el-menu-item>
            </template>
          </el-submenu>
        </template>
        <template v-else>
          <el-menu-item :index="item.component + ''" :key="item.id">
            <i
              :class="[{ icon: true, iconfont: true }, item.icon]"
              style="margin-right: 10px"
            ></i
            ><span slot="title">{{ item.name }}</span>
          </el-menu-item>
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script>
import bus from "../common/bus";
export default {
  data() {
    return {
      collapse: false,
      getMenus: [],
      onRoutes: "",
      items: [
        {
          icon: "el-icon-lx-home",
          index: "dashboard",
          title: "系统首页",
        },
        {
          icon: "el-icon-lx-cascades",
          index: "table",
          title: "基础表格",
        },
        {
          icon: "el-icon-lx-copy",
          index: "tabs",
          title: "tab选项卡",
        },
        {
          icon: "el-icon-lx-calendar",
          index: "3",
          title: "表单相关",
          children: [
            {
              index: "form",
              title: "基本表单",
            },
            {
              index: "3-2",
              title: "三级菜单",
              children: [
                {
                  index: "editor",
                  title: "富文本编辑器",
                },
                {
                  index: "markdown",
                  title: "markdown编辑器",
                },
              ],
            },
            {
              index: "upload",
              title: "文件上传",
            },
          ],
        },
        {
          icon: "el-icon-lx-emoji",
          index: "icon",
          title: "自定义图标",
        },
        {
          icon: "el-icon-lx-favor",
          index: "charts",
          title: "schart图表",
        },
        {
          icon: "el-icon-rank",
          index: "drag",
          title: "拖拽列表",
        },
        {
          icon: "el-icon-lx-warn",
          index: "6",
          title: "错误处理",
          children: [
            {
              index: "permission",
              title: "权限测试",
            },
            {
              index: "404",
              title: "404页面",
            },
          ],
        },
      ],
    };
  },
  computed: {
    // onRoutes(){
    //     return this.$route.path.replace('/','');
    // }
  },
  created() {
    // 通过 Event Bus 进行组件间通信，来折叠侧边栏

    this.$api.post(
      this.API.upms + "menu/allTree",
      {
        platform: "3",
      },
      (res) => {
        this.getMenus = res;
      }
    );
    // sessionStorage.getItem
    let path = sessionStorage.getItem("path");
    if (path) {
      this.onRoutes = path;
    }
    bus.$on("collapse", (msg) => {
      this.collapse = msg;
    });
    bus.$on("path", (msg) => {
      this.onRoutes = msg;
    });
  },
  methods: {
    handleSelect(key, keyPath) {
      // console.log(key);
      sessionStorage.setItem("path", key)
      // localStorage.setItem("path", key);
    },
  },
};
</script>

<style scoped>
.sidebar {
  display: block;
  position: absolute;
  left: 0;
  top: 70px;
  bottom: 0;
  overflow-y: scroll;
  /* background: fuchsia; */
}
.sidebar::-webkit-scrollbar {
  width: 0;
}
.sidebar-el-menu {
  /* z-index: 9999; */
}
.sidebar-el-menu:not(.el-menu--collapse) {
  width: 250px;
}
.sidebar > ul {
  height: 100%;
}
</style>
