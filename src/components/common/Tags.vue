<template>
    <div class="tags" v-if="showTags">
        <ul>
            <li class="tags-li" v-for="(item,index) in tagsList" :class="{'active': isActive(item.title)}" :key="index">
                <router-link :to="item.path" class="tags-li-title">
                    {{item.title}}
                </router-link>
                <span class="tags-li-icon" @click="closeTags(index)"><i class="el-icon-close"></i></span>
            </li>
        </ul>
        <div class="tags-close-box">
            <el-dropdown @command="handleTags">
                <el-button size="mini" type="primary">
                    标签选项<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu size="small" slot="dropdown">
                    <el-dropdown-item command="other">关闭其他</el-dropdown-item>
                    <el-dropdown-item command="all">关闭所有</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
    </div>
</template>

<script>
    import bus from './bus';
    export default {
        data() {
            return {
                tagsList: []
            }
        },
        methods: {
            isActive(path) {
                return path === this.$route.meta.title;
            },
            // 关闭单个标签
            closeTags(index) {
                const delItem = this.tagsList.splice(index, 1)[0];
                const item = this.tagsList[index] ? this.tagsList[index] : this.tagsList[index - 1];
                if (item) {
                    delItem.path === this.$route.fullPath && this.$router.push(item.path);
                }else{
                    this.$router.push('/');
                }
            },
            // 关闭全部标签
            closeAll(){
                this.tagsList = [];
                this.$router.push('/');
            },
            // 关闭其他标签
            closeOther(){
                
                const curItem = this.tagsList.filter(item => {
                    return item.path === this.$route.fullPath;
                })
                this.tagsList = curItem;
            },
            // 设置标签
            setTags(route){
                bus.$emit('path', route.fullPath);
                const isExist = this.tagsList.some(item => {
                    return item.title === route.meta.title;
                })
                
                if(!isExist){
                    if(this.tagsList.length >= 10){
                        this.tagsList.shift();
                    }
                    // console.log(this.tagsList);
                    // let flag = this.tagsList.some((item) => {
                    //     return item.title == item.title;
                    // });
                    this.tagsList.push({
                        title: route.meta.title,
                        path: route.fullPath,
                        name: route.matched[1].components.default.name
                    });
                }else{
                    bus.$emit('path', route.fullPath);
                }
                bus.$emit('tags', this.tagsList);
            },
            handleTags(command){
                command === 'other' ? this.closeOther() : this.closeAll();
            }
        },
        computed: {
            showTags() {
                return this.tagsList.length > 0;
            }
        },
        watch:{
            $route(newValue, oldValue){
                this.setTags(newValue);
            }
        },
        created(){
            this.setTags(this.$route);
            // console.log(this.$route,'llll');
        }
    }

</script>


<style>
    .tags {
        position: relative;
        height: 36px;
        overflow: hidden;
        background: #e9eaec;
        padding-right: 120px;
        box-shadow: 0 2px 10px rgba(126, 87, 194, 0.1);
        transition: all .3s ease;
    }

    .tags ul {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        padding-left: 10px;
    }

    .tags-li {
        float: left;
        margin: 3px 5px 2px 3px;
        border-radius: 15px;
        font-size: 12px;
        overflow: hidden;
        cursor: pointer;
        height: 26px;
        line-height: 26px;
        border: 1px solid #e9eaec;
        background: #fff;
        padding: 0 5px 0 12px;
        vertical-align: middle;
        color: #409EFF;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        animation: tagsFadeIn 0.4s ease;
    }

    @keyframes tagsFadeIn {
        from {
            opacity: 0;
            transform: translateY(-5px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .tags-li:not(.active):hover {
        background: #F2F6FC;
        border-color: #409EFF;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(126, 87, 194, 0.1);
    }

    .tags-li.active {
        color: #fff;
        background: linear-gradient(to right, #409EFF, #409EFF);
        border-color: #409EFF;
        box-shadow: 0 2px 8px rgba(126, 87, 194, 0.3);
    }

    .tags-li-title {
        float: left;
        max-width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-right: 5px;
        color: #409EFF;
        transition: color .3s ease;
    }

    .tags-li.active .tags-li-title {
        color: #fff;
    }

    .tags-li-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        text-align: center;
        transition: all 0.3s ease;
        transform: scale(0.9);
    }

    .tags-li-icon:hover {
        background-color: rgba(0, 0, 0, 0.1);
        transform: scale(1);
    }

    .tags-li.active .tags-li-icon:hover {
        background-color: #F2F6FC;
    }

    .tags-close-box {
        position: absolute;
        right: 0;
        top: 0;
        box-sizing: border-box;
        padding-top: 3px;
        text-align: center;
        width: 110px;
        height: 36px;
        background: #e9eaec;
        box-shadow: -3px 0 15px 3px rgba(126, 87, 194, 0.1);
        z-index: 10;
    }

    /* 覆盖el-button主题色 */
    /* .tags-close-box .el-button--primary {
        background-color: var(--primary-purple) !important;
        border-color: var(--primary-purple) !important;
    }

    .tags-close-box .el-button--primary:hover,
    .tags-close-box .el-button--primary:focus {
        background-color: var(--primary-purple-dark) !important;
        border-color: var(--primary-purple-dark) !important;
    } */

</style>
