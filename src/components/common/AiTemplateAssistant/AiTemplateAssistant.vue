<template>
  <div class="ai-template-assistant">
    <!-- AI助手触发按钮 -->
    <el-button
      type="primary"
      size="mini"
      @click="toggleAiAssistant"
      class="ai-toggle-btn"
      :class="{ 'active': visible }"
    >
      <i class="ai-icon">✨</i>
      AI模版助手
    </el-button>

    <!-- 浮动AI助手面板 -->
    <div
      v-show="visible"
      class="ai-assistant-floating-panel"
      :class="{ 'minimized': minimized }"
      :style="{
        left: panelPosition.x + 'px',
        top: panelPosition.y + 'px',
        height: currentPanelHeight + 'px'
      }"
      ref="aiAssistantPanel"
      tabindex="-1"
      @keydown="handleKeyDown"
    >
      <!-- 拖拽头部 -->
      <div
        class="ai-panel-header"
        @mousedown="startDrag"
        ref="dragHandle"
      >
        <div class="ai-assistant-title">
          <i class="ai-icon">✨</i>
          <span>AI模板助手</span>
          <div class="ai-panel-controls">
            <el-button
              type="text"
              size="mini"
              @click="minimizePanel"
              class="minimize-btn"
              :title="minimized ? '恢复' : '最小化'"
            >
              <i :class="minimized ? 'el-icon-plus' : 'el-icon-minus'"></i>
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="closePanel"
              class="close-btn"
              title="关闭"
            >
              <i class="el-icon-close"></i>
            </el-button>
          </div>
        </div>
        <div class="ai-assistant-subtitle">智能生成专业短信模板</div>
      </div>

      <!-- AI面板内容 -->
      <div class="ai-panel-content" v-show="!minimized">
        <!-- AI功能选项卡 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="ai-assistant-tabs">
          <el-tab-pane label="模板生成" name="generate">
            <div class="ai-assistant-content">
              <!-- 验证码模板生成 -->
              <div class="ai-section" v-if="templateType == 1">
                <h4 class="ai-section-title">验证码模板生成</h4>

                <div class="ai-form-item">
                  <label class="ai-label">使用场景</label>
                  <el-select
                    v-model="formData.verificationScenario"
                    placeholder="请选择验证码使用场景"
                    class="ai-select"
                    size="small"
                  >
                    <el-option
                      v-for="scenario in verificationScenarios"
                      :key="scenario.value"
                      :label="scenario.label"
                      :value="scenario.value"
                    ></el-option>
                  </el-select>
                </div>



                <div class="ai-form-item">
                  <label class="ai-label-optional">补充信息</label>
                  <el-input
                    type="textarea"
                    v-model="formData.additionalInfo"
                    placeholder="如有其他特殊要求，请在此说明（可选）"
                    :rows="2"
                    maxlength="100"
                    show-word-limit
                    class="ai-textarea"
                    size="small"
                  />
                </div>

                <div class="ai-generate-btn-section">
                  <el-button
                    v-permission
                    type="primary"
                    @click="generateTemplate"
                    :loading="generating"
                    class="ai-generate-submit-btn"
                    size="small"
                  >
                    <i class="ai-icon">🚀</i>
                    {{ generating ? '生成中...' : '生成验证码模板' }}
                  </el-button>
                  <div class="ai-generate-count">今日剩余次数：{{ remainingCount }}</div>
                </div>
              </div>

              <!-- 通知模板生成 -->
              <div class="ai-section" v-else-if="templateType == 2">
                <h4 class="ai-section-title">通知模板生成</h4>

                <div class="ai-form-item">
                  <label class="ai-label">行业类型</label>
                  <el-select
                    v-model="formData.industry"
                    placeholder="请选择您的行业"
                    class="ai-select"
                    size="small"
                    @change="handleIndustryChange"
                  >
                    <el-option
                      v-for="industry in notificationIndustries"
                      :key="industry.value"
                      :label="industry.label"
                      :value="industry.value"
                    ></el-option>
                  </el-select>
                </div>

                <div class="ai-form-item">
                  <label class="ai-label-optional">通知场景</label>
                  <el-select
                    v-model="formData.notificationScenario"
                    placeholder="请选择或输入通知场景"
                    class="ai-select"
                    size="small"
                    filterable
                    allow-create
                    default-first-option
                  >
                    <el-option
                      v-for="scenario in notificationScenarios"
                      :key="scenario.value"
                      :label="scenario.label"
                      :value="scenario.value"
                    ></el-option>
                  </el-select>
                </div>

                <div class="ai-form-item">
                  <label class="ai-label-optional">补充信息</label>
                  <el-input
                    type="textarea"
                    v-model="formData.additionalInfo"
                    placeholder="如有其他特殊要求，请在此说明（可选）"
                    :rows="2"
                    maxlength="100"
                    show-word-limit
                    class="ai-textarea"
                    size="small"
                  />
                </div>

                <div class="ai-generate-btn-section">
                  <el-button
                    v-permission
                    type="primary"
                    @click="generateTemplate"
                    :loading="generating"
                    class="ai-generate-submit-btn"
                    size="small"
                  >
                    <i class="ai-icon">🚀</i>
                    {{ generating ? '生成中...' : '生成通知模板' }}
                  </el-button>
                  <div class="ai-generate-count">今日剩余次数：{{ remainingCount }}</div>
                </div>
              </div>

              <!-- 营销模板生成 -->
              <div class="ai-section" v-else-if="templateType == 3">
                <h4 class="ai-section-title">营销模板生成</h4>

                <div class="ai-form-item">
                  <label class="ai-label">行业类型</label>
                  <el-select
                    v-model="formData.industry"
                    placeholder="请选择您的行业"
                    class="ai-select"
                    size="small"
                    @change="handleIndustryChange"
                  >
                    <el-option
                      v-for="industry in marketingIndustries"
                      :key="industry.value"
                      :label="industry.label"
                      :value="industry.value"
                    ></el-option>
                  </el-select>
                </div>



                <div class="ai-form-item">
                  <label class="ai-label">产品/服务名称</label>
                  <el-input
                    v-model="formData.productName"
                    placeholder="请输入您的产品或服务名称"
                    class="ai-input"
                    size="small"
                    maxlength="30"
                  />
                </div>

                <div class="ai-form-item">
                  <label class="ai-label-optional">卖点/优惠信息</label>
                  <el-input
                    v-model="formData.promotionInfo"
                    placeholder="如：8折优惠、买一送一等（可选）"
                    class="ai-input"
                    size="small"
                    maxlength="50"
                  />
                </div>

                <div class="ai-form-item">
                  <label class="ai-label-optional">推广链接</label>
                  <el-input
                    v-model="formData.promotionLink"
                    placeholder="如需在模板中包含链接，请填写（可选）"
                    class="ai-input"
                    size="small"
                    maxlength="100"
                  />
                </div>

                <div class="ai-form-item">
                  <label class="ai-label-optional">补充信息</label>
                  <el-input
                    type="textarea"
                    v-model="formData.additionalInfo"
                    placeholder="如有其他特殊要求，请在此说明（可选）"
                    :rows="2"
                    maxlength="100"
                    show-word-limit
                    class="ai-textarea"
                    size="small"
                  />
                </div>

                <div class="ai-generate-btn-section">
                  <el-button
                    v-permission
                    type="primary"
                    @click="generateTemplate"
                    :loading="generating"
                    class="ai-generate-submit-btn"
                    size="small"
                  >
                    <i class="ai-icon">🚀</i>
                    {{ generating ? '生成中...' : '生成营销模板' }}
                  </el-button>
                  <div class="ai-generate-count">今日剩余次数：{{ remainingCount }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="模板改写" name="rewrite">
            <div class="ai-assistant-content">
              <div class="ai-section">
                <h4 class="ai-section-title">模板改写</h4>
                <p class="ai-section-desc">输入您的原始模板内容，AI将帮您优化文案</p>

                <div class="ai-form-item">
                  <label class="ai-label">请输入您的原文</label>
                  <el-input
                    type="textarea"
                    v-model="formData.originalText"
                    placeholder="请输入您的原始模板内容，我们将基于此进行优化改写（模板字数500字以内）"
                    :rows="4"
                    maxlength="500"
                    show-word-limit
                    class="ai-textarea"
                    size="small"
                  />
                </div>

                <div class="ai-form-item">
                  <label class="ai-label-optional">更多个性化内容</label>
                  <el-input
                    type="textarea"
                    v-model="formData.description"
                    placeholder="如果您有更多修改要求，请填写在这里（100字以内）"
                    :rows="3"
                    maxlength="100"
                    show-word-limit
                    class="ai-textarea"
                    size="small"
                  />
                </div>

                <div class="ai-generate-btn-section">
                  <el-button
                    v-permission
                    type="primary"
                    @click="rewriteTemplate"
                    :loading="rewriting"
                    class="ai-generate-submit-btn"
                    size="small"
                  >
                    <i class="ai-icon">✨</i>
                    {{ rewriting ? '改写中...' : '开始改写' }}
                  </el-button>
                  <div class="ai-generate-count">今日剩余次数：{{ remainingCount }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 生成结果展示 -->
        <div v-if="generating || rewriting || results.length > 0" class="ai-results-section">
          <h4 class="ai-section-title">
            <span v-if="generating || rewriting">正在生成中...</span>
            <span v-else>生成结果</span>
          </h4>

          <!-- 加载动画 -->
          <div v-if="generating || rewriting" class="ai-loading-container">
            <div class="ai-loading-animation">
              <div class="loading-dots">
                <div class="dot dot1"></div>
                <div class="dot dot2"></div>
                <div class="dot dot3"></div>
              </div>
              <div class="loading-text">
                <p class="loading-main-text">{{ generating ? 'AI正在为您生成专业模板' : 'AI正在为您优化模板内容' }}</p>
                <p class="loading-sub-text">请稍候，这可能需要30-60秒...</p>
              </div>
            </div>
          </div>

          <!-- 结果列表 -->
          <div v-else-if="results.length > 0" class="ai-results-list">
            <div
              v-for="(result, index) in results"
              :key="index"
              class="ai-result-item"
            >
              <div class="ai-result-content">{{ result.content }}</div>
              <div class="ai-result-actions">
                <el-button
                  type="primary"
                  size="mini"
                  @click="useTemplate(result)"
                >
                  使用此模板
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 高度调整边框 -->
      <div
        class="ai-panel-resize-handle"
        @mousedown="startResize"
        :class="{ 'resizing': isResizing }"
      >
        <div class="resize-indicator">
          <i class="el-icon-more"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { AiTemplateService } from './aiTemplateService.js';
import {
  VERIFICATION_SCENARIOS,
  NOTIFICATION_INDUSTRIES,
  MARKETING_INDUSTRIES,
  getNotificationScenarios,
  DEFAULT_QUOTAS
} from './templateEnums.js';

export default {
  name: 'AiTemplateAssistant',
  props: {
    // 模板类型 1:验证码 2:通知 3:营销
    templateType: {
      type: Number,
      required: true,
      validator: value => [1, 2, 3].includes(value)
    },
    // API实例
    apiInstance: {
      type: Object,
      required: true
    },
    // 消息实例
    messageInstance: {
      type: [Object, Function],
      required: true
    },
    // API基础URL
    apiBaseUrl: {
      type: String,
      required: true
    },
    // 初始显示状态
    initialVisible: {
      type: Boolean,
      default: false
    },
    // 初始位置
    initialPosition: {
      type: Object,
      default: () => ({ x: 'auto', y: 'auto' })
    },
    // 是否启用ESC键关闭面板
    enableEscClose: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 面板状态
      visible: this.initialVisible,
      minimized: false,
      panelPosition: { x: 0, y: 0 }, // 初始值，会在mounted时重新计算
      isDragging: false,
      dragOffset: { x: 0, y: 0 },

      // 高度调整相关
      isResizing: false,
      panelHeight: this.loadCachedHeight(), // 从缓存加载高度
      originalPanelHeight: this.loadCachedHeight(), // 保存原始高度，用于最小化恢复
      minHeight: 300,   // 最小高度
      maxHeight: this.getMaxHeight(), // 最大高度为屏幕高度的80%
      headerHeight: 80, // 头部高度（用于最小化状态）
      resizeStartY: 0,  // 调整开始时的Y坐标
      resizeStartHeight: 0, // 调整开始时的高度

      // 功能状态
      activeTab: 'generate',
      generating: false,
      rewriting: false,
      remainingCount: DEFAULT_QUOTAS.AI_TEMPLATE, // 统一的剩余次数
      results: [],

      // 表单数据
      formData: {
        // 验证码模板字段
        verificationScenario: '',

        // 通知模板字段
        industry: '',
        notificationScenario: '',

        // 营销模板字段
        productName: '',
        promotionInfo: '',
        promotionLink: '',

        // 通用字段
        additionalInfo: '',
        originalText: '',
        description: ''
      },

      // 枚举数据
      verificationScenarios: VERIFICATION_SCENARIOS,
      notificationIndustries: NOTIFICATION_INDUSTRIES,
      marketingIndustries: MARKETING_INDUSTRIES,
      notificationScenarios: [],

      // AI服务实例
      aiService: null
    };
  },
  computed: {
    // 计算当前面板高度（根据最小化状态）
    currentPanelHeight() {
      return this.minimized ? this.headerHeight : this.panelHeight;
    }
  },
  created() {
    // 初始化AI服务
    this.aiService = new AiTemplateService(this.apiInstance, this.messageInstance);
    this.aiService.setBaseUrl(this.apiBaseUrl);

    // 查询剩余次数
    this.loadRemainTimes();
  },
  mounted() {
    // 初始化最大高度
    this.maxHeight = this.getMaxHeight();

    // 如果组件初始就是可见的，才设置位置
    if (this.visible) {
      this.$nextTick(() => {
        setTimeout(() => {
          this.setInitialPosition();
        }, 100);
      });
    }

    // 修复Element UI组件层级问题
    this.fixElementUIZIndex();
    // 添加事件监听
    document.addEventListener('mousemove', this.onDrag);
    document.addEventListener('mouseup', this.stopDrag);
    document.addEventListener('mousemove', this.onResize);
    document.addEventListener('mouseup', this.stopResize);
    document.addEventListener('keydown', this.handleKeyDown);
    window.addEventListener('resize', this.handleWindowResize);
  },
  beforeDestroy() {
    // 清理事件监听
    document.removeEventListener('mousemove', this.onDrag);
    document.removeEventListener('mouseup', this.stopDrag);
    document.removeEventListener('mousemove', this.onResize);
    document.removeEventListener('mouseup', this.stopResize);
    document.removeEventListener('keydown', this.handleKeyDown);
    window.removeEventListener('resize', this.handleWindowResize);
  },
  methods: {
    // ========== 面板控制方法 ==========

    // 切换AI助手显示状态
    toggleAiAssistant() {
      this.visible = !this.visible;
      if (this.visible) {
        this.minimized = false;
        this.$nextTick(() => {
          this.setInitialPosition();
          // 设置焦点以接收键盘事件
          if (this.$refs.aiAssistantPanel) {
            this.$refs.aiAssistantPanel.focus();
          }
        });
      }
      this.$emit('visibility-change', this.visible);
    },

    // 最小化面板
    minimizePanel() {
      if (!this.minimized) {
        // 最小化：保存当前高度
        this.originalPanelHeight = this.panelHeight;
        console.log('最小化面板，保存原始高度:', this.originalPanelHeight);
      } else {
        // 恢复：使用保存的高度
        this.panelHeight = this.originalPanelHeight;
        console.log('恢复面板，使用原始高度:', this.panelHeight);
      }

      this.minimized = !this.minimized;

      // 触发事件，通知父组件状态变化
      this.$emit('minimize-change', this.minimized);
    },

    // 关闭面板
    closePanel() {
      this.visible = false;
      this.$emit('visibility-change', this.visible);
    },

    // 处理键盘事件
    handleKeyDown(event) {
      // 只有在面板可见且启用ESC关闭时才处理
      if (!this.visible || !this.enableEscClose) {
        return;
      }

      // 检查是否按下ESC键
      if (event.key === 'Escape' || event.keyCode === 27) {
        // 防止事件冒泡，避免影响其他组件
        event.preventDefault();
        event.stopPropagation();

        // 关闭面板
        this.closePanel();

        console.log('ESC键关闭AI助手面板');
      }
    },

    // 设置初始位置
    setInitialPosition() {
      // 尝试根据AI助手按钮定位
      if (this.positionRelativeToButton()) {
        return;
      }

      // 如果没有找到按钮，使用默认位置
      this.setDefaultPosition();
    },

    // 根据AI助手按钮定位面板（调整为最右边显示）
    positionRelativeToButton() {
      try {
        // 优先查找短信内容区域中的AI助手按钮
        let aiButton = document.querySelector('.textarea-with-ai .ai-assistant-btn');

        if (!aiButton) {
          // 尝试查找任何AI助手按钮
          aiButton = document.querySelector('.ai-assistant-btn');
        }

        if (!aiButton) {
          // 尝试其他可能的选择器
          aiButton = document.querySelector('[class*="ai-assistant"]');
        }

        if (!aiButton) {
          // 尝试查找包含"AI大模型生成"文字的元素
          const elements = document.querySelectorAll('div, span, button');
          for (let element of elements) {
            if (element.textContent && element.textContent.includes('AI大模型生成')) {
              // 找到包含文字的元素，向上查找可点击的父元素
              let clickableParent = element;
              while (clickableParent && !clickableParent.onclick && !clickableParent.getAttribute('onclick')) {
                clickableParent = clickableParent.parentElement;
                if (clickableParent && (clickableParent.classList.contains('ai-assistant-btn') ||
                    clickableParent.style.cursor === 'pointer')) {
                  aiButton = clickableParent;
                  break;
                }
              }
              if (aiButton) break;
            }
          }
        }

        if (!aiButton) {
          console.log('未找到AI助手按钮，使用最右边定位');
          return false;
        }

        console.log('找到AI助手按钮：', aiButton);
        console.log('按钮位置信息：', aiButton.getBoundingClientRect());

        const buttonRect = aiButton.getBoundingClientRect();
        const panelWidth = 380;
        const windowWidth = window.innerWidth;

        // 调整定位策略：固定在屏幕最右边
        let targetX = windowWidth - panelWidth - 20; // 距离右边20px
        let targetY = buttonRect.top;                 // 与按钮顶部对齐

        // 确保面板不会超出屏幕顶部
        if (targetY < 20) {
          targetY = 20;
        }

        // 由于高度会随内容展开，不限制底部位置
        // 让面板自然展开，不做高度限制

        this.panelPosition = { x: targetX, y: targetY };
        console.log('AI面板定位到最右边:', {
          x: targetX,
          y: targetY,
          buttonRect: {
            left: buttonRect.left,
            top: buttonRect.top,
            right: buttonRect.right,
            bottom: buttonRect.bottom
          },
          panelSize: { width: panelWidth },
          strategy: '屏幕最右边'
        });
        return true;
      } catch (error) {
        console.error('定位AI助手按钮时出错:', error);
        return false;
      }
    },

    // 设置默认位置（当找不到按钮时使用）- 调整为最右边
    setDefaultPosition() {
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const panelWidth = 380;

      // 默认位置：屏幕最右边，垂直居中
      const defaultX = windowWidth - panelWidth - 20; // 距离右边20px
      const defaultY = Math.max(60, Math.floor(windowHeight * 0.2)); // 距离顶部20%位置

      // 检查是否设置了自定义位置
      let targetX = defaultX;
      let targetY = defaultY;

      if (this.initialPosition.x !== 'auto' && typeof this.initialPosition.x === 'number') {
        targetX = this.initialPosition.x;
      }
      if (this.initialPosition.y !== 'auto' && typeof this.initialPosition.y === 'number') {
        targetY = this.initialPosition.y;
      }

      // 确保面板不会超出屏幕边界（只限制左边和顶部）
      this.panelPosition = {
        x: Math.max(20, Math.min(targetX, windowWidth - panelWidth - 20)),
        y: Math.max(20, targetY) // 不限制底部，让面板自然展开
      };

      console.log('AI面板使用默认位置（最右边）:', this.panelPosition);
    },

    // 开始拖拽
    startDrag(event) {
      this.isDragging = true;
      const panel = this.$refs.aiAssistantPanel;
      const rect = panel.getBoundingClientRect();

      this.dragOffset = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      };

      event.preventDefault();
    },

    // 拖拽过程（支持位置拖拽和高度调整）
    onDrag(event) {
      // 处理高度调整
      if (this.isResizing) {
        const deltaY = event.clientY - this.resizeStartY;
        let newHeight = this.resizeStartHeight + deltaY;

        // 获取当前的最大高度（屏幕高度的80%）
        const currentMaxHeight = this.getMaxHeight();

        // 限制高度范围
        newHeight = Math.max(this.minHeight, Math.min(newHeight, currentMaxHeight));

        // 确保不超出屏幕底部
        const windowHeight = window.innerHeight;
        const maxAllowedHeight = windowHeight - this.panelPosition.y - 20;
        newHeight = Math.min(newHeight, maxAllowedHeight);

        this.panelHeight = newHeight;

        // 实时保存到缓存
        this.saveCachedHeight(newHeight);
        return;
      }

      // 处理位置拖拽
      if (!this.isDragging) return;

      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const panelWidth = 380;

      let newX = event.clientX - this.dragOffset.x;
      let newY = event.clientY - this.dragOffset.y;

      // 边界限制
      newX = Math.max(0, Math.min(newX, windowWidth - panelWidth));
      newY = Math.max(0, Math.min(newY, windowHeight - this.currentPanelHeight));

      this.panelPosition = { x: newX, y: newY };
    },

    // 停止拖拽
    stopDrag() {
      this.isDragging = false;
      this.isResizing = false; // 同时停止高度调整
    },

    // ========== 高度调整方法 ==========

    // 获取最大高度（屏幕高度的100%）
    getMaxHeight() {
      return Math.floor(window.innerHeight * 1);
    },

    // 获取默认高度（也是屏幕高度的80%）
    getDefaultHeight() {
      return this.getMaxHeight();
    },

    // 加载缓存的高度
    loadCachedHeight() {
      try {
        const cachedHeight = localStorage.getItem('ai-assistant-panel-height');
        if (cachedHeight) {
          const height = parseInt(cachedHeight);
          const maxHeight = this.getMaxHeight();
          // 确保高度在合理范围内
          if (height >= 300 && height <= maxHeight) {
            return height;
          }
        }
      } catch (error) {
        console.warn('加载缓存高度失败:', error);
      }
      return this.getDefaultHeight(); // 默认高度为屏幕高度的80%
    },

    // 保存高度到缓存
    saveCachedHeight(height) {
      try {
        localStorage.setItem('ai-assistant-panel-height', height.toString());
        console.log('高度已保存到缓存:', height);
      } catch (error) {
        console.warn('保存高度到缓存失败:', error);
      }
    },

    // 开始高度调整
    startResize(event) {
      this.isResizing = true;
      this.resizeStartY = event.clientY;
      this.resizeStartHeight = this.panelHeight;

      event.preventDefault();
      event.stopPropagation(); // 防止触发拖拽

      console.log('开始调整高度:', {
        startY: this.resizeStartY,
        startHeight: this.resizeStartHeight
      });
    },

    // 高度调整过程
    onResize(event) {
      if (!this.isResizing) return;

      const deltaY = event.clientY - this.resizeStartY;
      let newHeight = this.resizeStartHeight + deltaY;

      // 获取当前的最大高度（屏幕高度的80%）
      const currentMaxHeight = this.getMaxHeight();

      // 限制高度范围
      newHeight = Math.max(this.minHeight, Math.min(newHeight, currentMaxHeight));

      // 确保不超出屏幕底部
      const windowHeight = window.innerHeight;
      const maxAllowedHeight = windowHeight - this.panelPosition.y - 20;
      newHeight = Math.min(newHeight, maxAllowedHeight);

      this.panelHeight = newHeight;
    },

    // 停止高度调整
    stopResize() {
      if (this.isResizing) {
        this.isResizing = false;
        // 保存最终高度到缓存
        this.saveCachedHeight(this.panelHeight);
        console.log('高度调整完成:', this.panelHeight);
      }
    },

    // 处理窗口大小变化（调整为最右边定位，动态更新最大高度）
    handleWindowResize() {
      if (this.visible) {
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const panelWidth = 380;

        // 更新最大高度为新窗口高度的80%
        this.maxHeight = this.getMaxHeight();

        // 窗口大小变化时，重新定位到最右边
        let newX = windowWidth - panelWidth - 20; // 固定在最右边
        let newY = this.panelPosition.y; // 保持当前的Y位置

        // 确保不超出边界
        newX = Math.max(20, newX);
        newY = Math.max(20, Math.min(newY, windowHeight - this.currentPanelHeight));

        // 如果当前高度超出了新的最大高度，调整高度
        const newMaxHeight = this.getMaxHeight();
        const maxAllowedHeight = Math.min(newMaxHeight, windowHeight - newY - 20);

        if (this.panelHeight > maxAllowedHeight) {
          this.panelHeight = Math.max(this.minHeight, maxAllowedHeight);
          this.saveCachedHeight(this.panelHeight);
        }

        this.panelPosition = {
          x: newX,
          y: newY
        };

        console.log('窗口大小变化，重新定位到最右边:', {
          position: this.panelPosition,
          height: this.panelHeight,
          maxHeight: this.maxHeight,
          windowSize: { width: windowWidth, height: windowHeight }
        });
      }
    },

    // 修复Element UI组件层级问题
    fixElementUIZIndex() {
      this.$nextTick(() => {
        const style = document.createElement('style');
        style.innerHTML = `
          .el-select-dropdown { z-index: 999999 !important; }
          .el-popper { z-index: 999999 !important; }
          .el-tooltip__popper { z-index: 999999 !important; }
          .el-picker-panel { z-index: 999999 !important; }
          .el-cascader-panel { z-index: 999999 !important; }
          .el-date-picker__header { z-index: 999999 !important; }
          .el-popper[x-placement^="bottom"] { z-index: 999999 !important; }
          .el-popper[x-placement^="top"] { z-index: 999999 !important; }
          .el-popper[x-placement^="left"] { z-index: 999999 !important; }
          .el-popper[x-placement^="right"] { z-index: 999999 !important; }
        `;

        if (!document.getElementById('ai-panel-zindex-fix')) {
          style.id = 'ai-panel-zindex-fix';
          document.head.appendChild(style);
        }
      });
    },

    // ========== AI功能方法 ==========

    // 查询剩余次数
    async loadRemainTimes() {
      try {
        const result = await this.aiService.getRemainTimes();
        if (result.success) {
          this.remainingCount = result.remainingCount;
          console.log('剩余次数查询成功:', this.remainingCount);
        } else {
          console.error('剩余次数查询失败:', result.error);
          // 查询失败时使用默认值，不显示错误提示
        }
      } catch (error) {
        console.error('剩余次数查询异常:', error);
        // 查询异常时使用默认值，不显示错误提示
      }
    },

    // 处理标签页切换
    handleTabClick(tab) {
      this.activeTab = tab.name;
      // 只有在没有正在进行的操作时才清空结果
      if (!this.generating && !this.rewriting) {
        this.results = [];
      }
      this.resetFormData();
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        verificationScenario: '',
        industry: '',
        notificationScenario: '',
        productName: '',
        promotionInfo: '',
        promotionLink: '',
        additionalInfo: '',
        originalText: '',
        description: ''
      };

      this.notificationScenarios = [];
    },

    // 处理行业变化
    handleIndustryChange(industry) {
      if (this.templateType === 2) {
        // 通知模板的场景选项
        this.notificationScenarios = getNotificationScenarios(industry);
        this.formData.notificationScenario = '';
      }
      // 营销模板不再需要处理营销类型
    },

    // 生成模板
    async generateTemplate() {
      if (this.generating) {
        this.messageInstance.warning('正在生成中，请稍候...');
        return;
      }

      this.generating = true;
      this.results = [];

      try {
        const result = await this.aiService.generateTemplate(
          this.templateType,
          this.formData
        );

        if (result.success) {
          this.results = result.results;
          if (result.remainingCount !== undefined) {
            this.remainingCount = result.remainingCount;
          }

          this.messageInstance.success(
            `模板生成成功！为您生成了${this.results.length}个专业模板`
          );

          this.$emit('generate-success', {
            results: this.results,
            templateType: this.templateType,
            formData: { ...this.formData }
          });

          // 刷新剩余次数
          this.loadRemainTimes();
        } else {
          this.messageInstance.error(result.error);
          this.$emit('generate-error', result.error);
        }
      } catch (error) {
        console.error('生成模板失败:', error);
        this.messageInstance.error('生成失败，请稍后重试');
        this.$emit('generate-error', error.message);
      } finally {
        this.generating = false;
      }
    },

    // 改写模板
    async rewriteTemplate() {
      if (this.rewriting) {
        this.messageInstance.warning('正在改写中，请稍候...');
        return;
      }

      this.rewriting = true;
      this.results = [];

      try {
        console.log('开始改写模板，参数:', {
          formData: this.formData,
          templateType: this.templateType
        });

        const result = await this.aiService.rewriteTemplate(
          this.formData,
          this.templateType
        );

        console.log('改写API响应:', result);

        if (result.success) {
          console.log('改写成功，结果数量:', result.results && result.results.length);
          console.log('改写结果详情:', result.results);

          this.results = result.results || [];
          if (result.remainingCount !== undefined) {
            this.remainingCount = result.remainingCount;
          }

          // 确保结果不为空
          if (this.results.length > 0) {
            this.messageInstance.success(
              `模板改写成功！为您优化了${this.results.length}个版本`
            );

            this.$emit('rewrite-success', {
              results: this.results,
              originalText: this.formData.originalText,
              description: this.formData.description
            });

            // 刷新剩余次数
            this.loadRemainTimes();
          } else {
            console.warn('改写成功但结果为空');
            this.messageInstance.warning('改写完成，但未生成新的版本');
          }
        } else {
          console.error('改写失败:', result.error);
          this.messageInstance.error(result.error);
          this.$emit('rewrite-error', result.error);
        }
      } catch (error) {
        console.error('改写模板失败:', error);
        this.messageInstance.error('改写失败，请稍后重试');
        this.$emit('rewrite-error', error.message);
      } finally {
        this.rewriting = false;
      }
    },

    // 使用模板
    useTemplate(result) {
      this.$emit('use-template', {
        content: result.content,
        variables: result.variables || [],
        templateType: this.templateType
      });

      this.messageInstance.success('模板已应用');
    },

    // ========== 公共方法 ==========

    // 显示面板
    show() {
      this.visible = true;
      this.minimized = false;

      // 使用多重延迟确保DOM完全渲染
      this.$nextTick(() => {
        // 设置面板焦点，确保能接收键盘事件
        if (this.$refs.aiAssistantPanel) {
          this.$refs.aiAssistantPanel.focus();
        }

        // 第一次尝试
        setTimeout(() => {
          this.setInitialPosition();
        }, 50);

        // 如果第一次没有找到按钮，再次尝试
        setTimeout(() => {
          if (!this.positionRelativeToButton()) {
            console.log('第二次尝试定位AI助手按钮');
            this.setInitialPosition();
          }
        }, 200);
      });
    },

    // 隐藏面板
    hide() {
      this.visible = false;
    },

    // 设置配额（统一次数）
    setQuotas(remainingCount) {
      if (remainingCount !== undefined) {
        this.remainingCount = remainingCount;
      }
    },

    // 刷新剩余次数
    async refreshRemainTimes() {
      await this.loadRemainTimes();
    },

    // 强制重新定位面板
    forceRepositionPanel() {
      console.log('强制重新定位AI助手面板');
      this.$nextTick(() => {
        setTimeout(() => {
          this.setInitialPosition();
        }, 100);
      });
    },

    // 获取当前状态
    getState() {
      return {
        visible: this.visible,
        minimized: this.minimized,
        position: { ...this.panelPosition },
        activeTab: this.activeTab,
        formData: { ...this.formData },
        results: [...this.results],
        quotas: {
          remaining: this.remainingCount
        }
      };
    },

    // 设置状态
    setState(state) {
      if (state.visible !== undefined) {
        this.visible = state.visible;
      }
      if (state.minimized !== undefined) {
        this.minimized = state.minimized;
      }
      if (state.position) {
        this.panelPosition = { ...state.position };
      }
      if (state.activeTab) {
        this.activeTab = state.activeTab;
      }
      if (state.formData) {
        this.formData = { ...state.formData };
      }
      if (state.results) {
        this.results = [...state.results];
      }
      if (state.quotas) {
        this.setQuotas(state.quotas.remaining);
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import './ai-assistant.less';
</style>
