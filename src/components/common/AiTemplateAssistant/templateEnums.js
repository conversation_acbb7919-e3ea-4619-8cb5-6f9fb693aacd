/**
 * 模板枚举数据
 * 用于AI模板助手的各种选项配置
 */

// 验证码使用场景
export const VERIFICATION_SCENARIOS = [
  { label: '用户注册', value: '用户注册' },
  { label: '用户登录', value: '用户登录' },
  { label: '密码重置', value: '密码重置' },
  { label: '手机号验证', value: '手机号验证' },
  { label: '邮箱验证', value: '邮箱验证' },
  { label: '身份验证', value: '身份验证' },
  { label: '支付确认', value: '支付确认' },
  { label: '安全验证', value: '安全验证' },
  { label: '绑定操作', value: '绑定操作' },
  { label: '解绑操作', value: '解绑操作' }
];

// 通知模板行业分类
export const NOTIFICATION_INDUSTRIES = [
  { label: '电商零售', value: '电商零售' },
  { label: '金融服务', value: '金融服务' },
  { label: '教育培训', value: '教育培训' },
  { label: '医疗健康', value: '医疗健康' },
  { label: '旅游出行', value: '旅游出行' },
  { label: '房产服务', value: '房产服务' },
  { label: '物流快递', value: '物流快递' },
  { label: '餐饮服务', value: '餐饮服务' },
  { label: '生活服务', value: '生活服务' },
  { label: '政府机构', value: '政府机构' },
  { label: '企业服务', value: '企业服务' },
  { label: '其他行业', value: '其他行业' }
];

// 营销模板行业分类
export const MARKETING_INDUSTRIES = [
  { label: '电商零售', value: '电商零售' },
  { label: '餐饮美食', value: '餐饮美食' },
  { label: '美容美发', value: '美容美发' },
  { label: '健身运动', value: '健身运动' },
  { label: '教育培训', value: '教育培训' },
  { label: '旅游出行', value: '旅游出行' },
  { label: '房产中介', value: '房产中介' },
  { label: '汽车服务', value: '汽车服务' },
  { label: '金融理财', value: '金融理财' },
  { label: '娱乐休闲', value: '娱乐休闲' },
  { label: '母婴用品', value: '母婴用品' },
  { label: '其他行业', value: '其他行业' }
];

// 根据行业获取通知场景
export function getNotificationScenarios(industry) {
  const scenarios = {
    '电商零售': [
      { label: '订单确认', value: '订单确认' },
      { label: '发货通知', value: '发货通知' },
      { label: '到货提醒', value: '到货提醒' },
      { label: '退款通知', value: '退款通知' },
      { label: '库存提醒', value: '库存提醒' }
    ],
    '金融服务': [
      { label: '交易通知', value: '交易通知' },
      { label: '账户变动', value: '账户变动' },
      { label: '还款提醒', value: '还款提醒' },
      { label: '风险提示', value: '风险提示' },
      { label: '产品到期', value: '产品到期' }
    ],
    '教育培训': [
      { label: '课程提醒', value: '课程提醒' },
      { label: '考试通知', value: '考试通知' },
      { label: '成绩发布', value: '成绩发布' },
      { label: '缴费通知', value: '缴费通知' },
      { label: '活动通知', value: '活动通知' }
    ],
    '医疗健康': [
      { label: '预约提醒', value: '预约提醒' },
      { label: '体检通知', value: '体检通知' },
      { label: '报告提醒', value: '报告提醒' },
      { label: '用药提醒', value: '用药提醒' },
      { label: '健康提示', value: '健康提示' }
    ],
    '旅游出行': [
      { label: '行程确认', value: '行程确认' },
      { label: '航班提醒', value: '航班提醒' },
      { label: '酒店确认', value: '酒店确认' },
      { label: '天气提醒', value: '天气提醒' },
      { label: '紧急通知', value: '紧急通知' }
    ],
    '物流快递': [
      { label: '揽件通知', value: '揽件通知' },
      { label: '运输更新', value: '运输更新' },
      { label: '派送通知', value: '派送通知' },
      { label: '签收确认', value: '签收确认' },
      { label: '异常提醒', value: '异常提醒' }
    ]
  };

  return scenarios[industry] || [
    { label: '状态通知', value: '状态通知' },
    { label: '提醒通知', value: '提醒通知' },
    { label: '确认通知', value: '确认通知' },
    { label: '警告通知', value: '警告通知' }
  ];
}

// 根据行业获取营销类型
export function getMarketingTypes(industry) {
  const types = {
    '电商零售': [
      { label: '新品推广', value: '新品推广' },
      { label: '促销活动', value: '促销活动' },
      { label: '会员专享', value: '会员专享' },
      { label: '清仓特卖', value: '清仓特卖' },
      { label: '节日营销', value: '节日营销' }
    ],
    '餐饮美食': [
      { label: '新品上市', value: '新品上市' },
      { label: '优惠套餐', value: '优惠套餐' },
      { label: '会员福利', value: '会员福利' },
      { label: '节日特色', value: '节日特色' },
      { label: '外卖推广', value: '外卖推广' }
    ],
    '美容美发': [
      { label: '新项目推广', value: '新项目推广' },
      { label: '体验优惠', value: '体验优惠' },
      { label: '会员特权', value: '会员特权' },
      { label: '季节护理', value: '季节护理' },
      { label: '套餐推荐', value: '套餐推荐' }
    ],
    '教育培训': [
      { label: '课程推广', value: '课程推广' },
      { label: '免费试听', value: '免费试听' },
      { label: '优惠报名', value: '优惠报名' },
      { label: '学习资料', value: '学习资料' },
      { label: '活动邀请', value: '活动邀请' }
    ],
    '旅游出行': [
      { label: '线路推荐', value: '线路推荐' },
      { label: '特价机票', value: '特价机票' },
      { label: '酒店优惠', value: '酒店优惠' },
      { label: '旅游套餐', value: '旅游套餐' },
      { label: '目的地推广', value: '目的地推广' }
    ]
  };

  return types[industry] || [
    { label: '产品推广', value: '产品推广' },
    { label: '服务推广', value: '服务推广' },
    { label: '优惠活动', value: '优惠活动' },
    { label: '会员营销', value: '会员营销' }
  ];
}

// 变量类型选项
export const VARIABLE_TYPES = [
  { label: '验证码(4-6位数字字母)', value: 'valid_code' },
  { label: '手机号码(1-15位数字)', value: 'mobile_number' },
  { label: '其他号码(1-32位数字字母横杠)', value: 'other_number' },
  { label: '金额(数字小数点中文数字)', value: 'amount' },
  { label: '日期(时间表达式含中文)', value: 'date' },
  { label: '中文(1-32位中文括号)', value: 'chinese' },
  { label: '其他(1-35位中英文符号空格)', value: 'others' }
];

// 模板类型映射
export const TEMPLATE_TYPE_MAP = {
  1: 'verification',
  2: 'notification', 
  3: 'marketing'
};

// 模板类型名称
export const TEMPLATE_TYPE_NAMES = {
  1: '验证码模板',
  2: '通知模板',
  3: '营销模板'
};

// API配置
export const AI_API_CONFIG = {
  GENERATE_URL: 'v3/ai/template/generate',
  REWRITE_URL: 'v3/ai/template/rewrite',
  REMAIN_TIMES_URL: 'v3/ai/template/remain/times', // 查询剩余次数
  TIMEOUT: {
    GENERATE: 60000, // 60秒
    REWRITE: 45000,  // 45秒
    REMAIN_TIMES: 10000 // 10秒
  },
  MAX_RETRIES: 2,
  RETRY_DELAY_BASE: 2000 // 基础重试延迟2秒
};

// 默认配额（统一使用一个次数）
export const DEFAULT_QUOTAS = {
  AI_TEMPLATE: 5 // 每日AI模板操作次数（生成+改写）
};
