# AI模板助手使用指南

## 📦 模块结构

```
src/components/common/AiTemplateAssistant/
├── index.js                    # 模块入口文件
├── AiTemplateAssistant.vue     # 主组件
├── aiTemplateService.js        # AI服务类
├── templateEnums.js            # 枚举数据
├── ai-assistant.less           # 样式文件
├── README.md                   # 详细文档
└── USAGE.md                    # 使用指南
```

## 🚀 快速开始

### 1. 基础使用

```vue
<template>
  <div>
    <!-- 在需要的地方添加AI助手 -->
    <AiTemplateAssistant
      :template-type="templateType"
      :api-instance="$api"
      :message-instance="$message"
      :api-base-url="API.cpus"
      @use-template="handleUseTemplate"
      @generate-success="handleGenerateSuccess"
      @rewrite-success="handleRewriteSuccess"
    />
  </div>
</template>

<script>
import AiTemplateAssistant from '@/components/common/AiTemplateAssistant';

export default {
  components: {
    AiTemplateAssistant
  },
  data() {
    return {
      templateType: 1 // 1:验证码 2:通知 3:营销
    };
  },
  methods: {
    // 处理模板使用
    handleUseTemplate(data) {
      // 将生成的模板内容填入表单
      this.form.temContent = data.content;
      
      // 处理变量
      if (data.variables && data.variables.length > 0) {
        this.applyAiVariableTypes(data.variables);
      }
      
      this.$message.success('模板已应用');
    },
    
    // 处理生成成功
    handleGenerateSuccess(data) {
      console.log('AI模板生成成功:', data);
    },
    
    // 处理改写成功
    handleRewriteSuccess(data) {
      console.log('AI模板改写成功:', data);
    }
  }
};
</script>
```

### 2. 作为Vue插件使用

```javascript
// main.js
import Vue from 'vue';
import AiTemplateAssistant, { install } from '@/components/common/AiTemplateAssistant';

Vue.use(install, {
  apiInstance: Vue.prototype.$api,
  messageInstance: Vue.prototype.$message
});

// 现在可以在任何组件中使用
// <AiTemplateAssistant :template-type="1" />
```

### 3. 只使用服务类

```javascript
import { AiTemplateService } from '@/components/common/AiTemplateAssistant';

export default {
  async mounted() {
    // 创建服务实例
    const aiService = new AiTemplateService(this.$api, this.$message);
    aiService.setBaseUrl(this.API.cpus);
    
    // 生成模板
    const result = await aiService.generateTemplate(1, {
      verificationScenario: 'register',
      platformName: '某某APP'
    });
    
    if (result.success) {
      console.log('生成的模板:', result.results);
    }
  }
};
```

## 🎯 集成到现有组件

### CreateTemplate1.vue 集成示例

```vue
<template>
  <div>
    <!-- 在页面头部添加AI助手按钮 -->
    <div class="card-header-actions">
      <el-button
        type="primary"
        size="mini"
        @click="showAiAssistant"
        class="ai-toggle-btn"
      >
        <i class="ai-icon">🤖</i>
        AI助手
      </el-button>
    </div>

    <!-- 页面内容 -->
    <!-- ... 其他内容 ... -->

    <!-- AI模板助手组件 -->
    <AiTemplateAssistant
      :template-type="form.temType"
      :api-instance="$api"
      :message-instance="$message"
      :api-base-url="API.cpus"
      @use-template="handleUseTemplate"
      @generate-success="handleGenerateSuccess"
      @rewrite-success="handleRewriteSuccess"
      ref="aiAssistant"
    />
  </div>
</template>

<script>
import AiTemplateAssistant from '@/components/common/AiTemplateAssistant';

export default {
  components: {
    AiTemplateAssistant
  },
  methods: {
    // 显示AI助手
    showAiAssistant() {
      if (this.$refs.aiAssistant) {
        this.$refs.aiAssistant.show();
      }
    },

    // 处理AI模板使用
    handleUseTemplate(data) {
      // 将生成的模板内容填入表单
      this.form.temContent = data.content;

      // 处理变量
      if (data.variables && data.variables.length > 0) {
        // 先触发内容变化处理，生成变量列表
        this.handelInput(data.content);
        
        // 然后应用AI返回的变量类型映射
        this.applyAiVariableTypes(data.variables);
      } else {
        // 没有变量信息时，正常触发内容变化处理
        this.handelInput(data.content);
      }

      this.$message.success('模板已应用到内容中');
    },

    // 应用AI返回的变量类型
    applyAiVariableTypes(aiVariables) {
      if (!aiVariables || !Array.isArray(aiVariables)) return;

      // 创建AI变量映射表
      const aiVariableMap = {};
      aiVariables.forEach(aiVar => {
        if (aiVar.name && aiVar.type) {
          aiVariableMap[aiVar.name] = aiVar.type;
        }
      });

      // 应用到当前的getcode数组
      this.getcode.forEach(codeItem => {
        if (aiVariableMap[codeItem.value]) {
          codeItem.codeName = aiVariableMap[codeItem.value];
        }
      });

      // 验证码模板特殊处理
      if (this.form.temType == 1) {
        if (this.getcode.length === 1) {
          const firstVariable = this.getcode[0];
          if (aiVariableMap[firstVariable.value] === 'valid_code') {
            firstVariable.codeName = 'valid_code';
          } else {
            firstVariable.codeName = 'valid_code';
            console.warn('AI返回的变量类型不适用于验证码模板，已自动设置为验证码类型');
          }
        }
      }
    },

    // 处理生成成功
    handleGenerateSuccess(data) {
      console.log('AI模板生成成功:', data);
      this.$message.success(`模板生成成功！为您生成了${data.results.length}个专业模板`);
    },

    // 处理改写成功
    handleRewriteSuccess(data) {
      console.log('AI模板改写成功:', data);
      this.$message.success(`模板改写成功！为您优化了${data.results.length}个版本`);
    }
  }
};
</script>

<style lang="less" scoped>
.ai-toggle-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  color: white;

  .ai-icon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
}
</style>
```

## 📋 API参考

### Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| templateType | Number | 是 | - | 模板类型：1-验证码，2-通知，3-营销 |
| apiInstance | Object | 是 | - | API实例对象 |
| messageInstance | Object | 是 | - | 消息提示实例 |
| apiBaseUrl | String | 是 | - | API基础URL |
| initialVisible | Boolean | 否 | false | 初始显示状态 |
| initialPosition | Object | 否 | {x:100,y:100} | 初始位置 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| use-template | {content, variables, templateType} | 用户点击使用模板时触发 |
| generate-success | {results, templateType, formData} | 模板生成成功时触发 |
| generate-error | error | 模板生成失败时触发 |
| rewrite-success | {results, originalText, description} | 模板改写成功时触发 |
| rewrite-error | error | 模板改写失败时触发 |
| visibility-change | visible | 面板显示状态变化时触发 |

### Methods

| 方法名 | 参数 | 说明 |
|--------|------|------|
| show() | - | 显示面板 |
| hide() | - | 隐藏面板 |
| setQuotas(generate, rewrite) | generate: Number, rewrite: Number | 设置配额 |
| getState() | - | 获取当前状态 |
| setState(state) | state: Object | 设置状态 |

## 🔧 自定义配置

### 枚举数据扩展

```javascript
import { VERIFICATION_SCENARIOS } from '@/components/common/AiTemplateAssistant';

// 扩展验证码场景
const customScenarios = [
  ...VERIFICATION_SCENARIOS,
  { label: '自定义场景', value: 'custom_scenario' }
];
```

### 样式自定义

```less
// 自定义AI助手主题色
.ai-assistant-floating-panel {
  .ai-panel-header {
    background: linear-gradient(135deg, #your-color-1 0%, #your-color-2 100%);
  }
  
  .ai-generate-submit-btn {
    background: linear-gradient(135deg, #your-color-1 0%, #your-color-2 100%);
  }
}
```

## 🚨 注意事项

1. **API兼容性**：确保后端API接口符合组件期望的数据格式
2. **样式冲突**：组件使用了高z-index值，注意与其他浮动元素的层级关系
3. **性能优化**：大量使用时建议实现组件懒加载
4. **移动端适配**：在移动端使用时注意拖拽体验优化

## 🎉 完成

现在您已经成功将AI模板助手拆分为独立的可复用模块！模块具有以下特点：

- ✅ **完全独立**：可以在任何Vue项目中使用
- ✅ **高度可配置**：支持自定义配置和扩展
- ✅ **事件驱动**：完整的事件系统便于集成
- ✅ **样式隔离**：不会影响其他组件
- ✅ **类型安全**：完整的参数验证和错误处理
