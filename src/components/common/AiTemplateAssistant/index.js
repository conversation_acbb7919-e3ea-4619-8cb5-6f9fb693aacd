/**
 * AI模板助手模块入口文件
 * 提供统一的导出接口
 */

import AiTemplateAssistant from './AiTemplateAssistant.vue';
import { AiTemplateService } from './aiTemplateService.js';
import * as TemplateEnums from './templateEnums.js';

// 导出主组件
export default AiTemplateAssistant;

// 导出服务类
export { AiTemplateService };

// 导出枚举数据
export {
  VERIFICATION_SCENARIOS,
  NOTIFICATION_INDUSTRIES,
  MARKETING_INDUSTRIES,
  getNotificationScenarios,
  getMarketingTypes,
  VARIABLE_TYPES,
  TEMPLATE_TYPE_MAP,
  TEMPLATE_TYPE_NAMES,
  AI_API_CONFIG,
  DEFAULT_QUOTAS
} from './templateEnums.js';

// 导出所有枚举
export { TemplateEnums };

// Vue插件安装方法
export const install = function(Vue, options = {}) {
  // 检查Vue是否可用
  if (!Vue || typeof Vue.component !== 'function') {
    console.error('AiTemplateAssistant: Vue is not available or not properly imported');
    return;
  }

  // 避免重复安装
  if (install.installed) {
    return;
  }
  install.installed = true;

  // 注册全局组件
  Vue.component('AiTemplateAssistant', AiTemplateAssistant);

  // 注册全局属性
  if (options.apiInstance) {
    Vue.prototype.$aiTemplateService = new AiTemplateService(
      options.apiInstance,
      options.messageInstance || Vue.prototype.$message
    );
  }
};

// 标记安装状态
install.installed = false;

// 注意：不进行自动安装，避免在模块加载时出现问题
// 如需使用插件方式，请手动调用 Vue.use(install, options)

/**
 * 使用示例：
 * 
 * // 1. 作为Vue插件使用
 * import Vue from 'vue';
 * import AiTemplateAssistant, { install } from '@/components/common/AiTemplateAssistant';
 * 
 * Vue.use(install, {
 *   apiInstance: this.$api,
 *   messageInstance: this.$message
 * });
 * 
 * // 2. 作为组件直接使用
 * import AiTemplateAssistant from '@/components/common/AiTemplateAssistant';
 * 
 * export default {
 *   components: {
 *     AiTemplateAssistant
 *   },
 *   template: `
 *     <AiTemplateAssistant
 *       :template-type="1"
 *       :api-instance="$api"
 *       :message-instance="$message"
 *       :api-base-url="API.cpus"
 *       @use-template="handleUseTemplate"
 *       @generate-success="handleGenerateSuccess"
 *       @rewrite-success="handleRewriteSuccess"
 *     />
 *   `
 * }
 * 
 * // 3. 只使用服务类
 * import { AiTemplateService } from '@/components/common/AiTemplateAssistant';
 * 
 * const aiService = new AiTemplateService(this.$api, this.$message);
 * aiService.setBaseUrl(this.API.cpus);
 * 
 * const result = await aiService.generateTemplate({}, 1, formData);
 */
