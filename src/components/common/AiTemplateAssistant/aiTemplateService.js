/**
 * AI模板服务模块
 * 处理AI模板生成和改写的业务逻辑
 */

import { AI_API_CONFIG, TEMPLATE_TYPE_MAP } from './templateEnums.js';

export class AiTemplateService {
  constructor(apiInstance, messageInstance) {
    this.api = apiInstance;
    this.message = messageInstance;
    this.baseUrl = '';

    // 确保message实例有正确的方法
    this.ensureMessageMethods();
  }

  /**
   * 确保message实例有正确的方法
   */
  ensureMessageMethods() {
    if (typeof this.message === 'function') {
      // 如果message是函数，为其添加方法
      if (!this.message.success) {
        this.message.success = (msg) => this.message({ type: 'success', message: msg });
      }
      if (!this.message.warning) {
        this.message.warning = (msg) => this.message({ type: 'warning', message: msg });
      }
      if (!this.message.error) {
        this.message.error = (msg) => this.message({ type: 'error', message: msg });
      }
    }
  }

  /**
   * 设置API基础URL
   */
  setBaseUrl(baseUrl) {
    this.baseUrl = baseUrl;
  }

  /**
   * 生成模板
   * @param {number} templateType - 模板类型 1:验证码 2:通知 3:营销
   * @param {Object} formData - 表单数据
   * @returns {Promise} 生成结果
   */
  async generateTemplate(templateType, formData) {
    // 验证必填字段
    const validation = this.validateGenerateParams(templateType, formData);
    if (!validation.valid) {
      this.message.warning(validation.message);
      return { success: false, error: validation.message };
    }

    // 构建请求参数
    const requestParams = this.buildGenerateParams(templateType, formData);
    
    try {
      // 调用API
      const response = await this.makeApiRequest(
        this.baseUrl + AI_API_CONFIG.GENERATE_URL,
        requestParams,
        AI_API_CONFIG.TIMEOUT.GENERATE
      );

      // 处理响应
      return this.processApiResponse(response, 'generate');
    } catch (error) {
      console.error('AI模板生成失败:', error);
      return { 
        success: false, 
        error: this.getErrorMessage(error, 'generate') 
      };
    }
  }

  /**
   * 改写模板
   * @param {Object} formData - 表单数据
   * @param {number} templateType - 模板类型
   * @returns {Promise} 改写结果
   */
  async rewriteTemplate(formData, templateType) {
    console.log('AiTemplateService.rewriteTemplate called with:', { formData, templateType });

    // 验证必填字段
    const validation = this.validateRewriteParams(formData);
    if (!validation.valid) {
      console.log('改写参数验证失败:', validation.message);
      this.message.warning(validation.message);
      return { success: false, error: validation.message };
    }

    // 构建请求参数
    const requestParams = {
      template_type: TEMPLATE_TYPE_MAP[templateType],
      text: formData.originalText.trim(),
      description: formData.description || ''
    };

    console.log('改写请求参数:', requestParams);

    try {
      // 调用API
      const response = await this.makeApiRequest(
        this.baseUrl + AI_API_CONFIG.REWRITE_URL,
        requestParams,
        AI_API_CONFIG.TIMEOUT.REWRITE
      );

      console.log('改写API原始响应:', response);

      // 处理响应
      const result = this.processApiResponse(response, 'rewrite');
      console.log('改写处理后结果:', result);

      return result;
    } catch (error) {
      console.error('AI模板改写失败:', error);
      return {
        success: false,
        error: this.getErrorMessage(error, 'rewrite')
      };
    }
  }

  /**
   * 查询剩余次数
   * @returns {Promise} 查询结果
   */
  async getRemainTimes() {
    console.log('查询AI模板剩余次数');

    try {
      // 调用GET API
      const response = await this.makeGetRequest(
        this.baseUrl + AI_API_CONFIG.REMAIN_TIMES_URL,
        AI_API_CONFIG.TIMEOUT.REMAIN_TIMES
      );

      console.log('剩余次数API响应:', response);

      // 检查响应状态
      if (!response || response.code !== 200) {
        console.log('查询剩余次数失败:', { code: response && response.code, msg: response && response.msg });
        return {
          success: false,
          error: (response && response.msg) || '查询剩余次数失败'
        };
      }

      // 解析数据
      const data = response.data;
      console.log('剩余次数数据:', data);

      // 处理不同的数据格式
      let remainingCount = 0;
      if (typeof data === 'number') {
        // 直接返回数字的情况：{"code":200,"msg":"OK","data":9}
        remainingCount = data;
      } else if (data && typeof data.remaining_count === 'number') {
        // 返回对象的情况：{"code":200,"msg":"OK","data":{"remaining_count":9}}
        remainingCount = data.remaining_count;
      } else if (data && typeof data.count === 'number') {
        // 其他可能的字段名
        remainingCount = data.count;
      }

      console.log('解析后的剩余次数:', remainingCount);

      return {
        success: true,
        remainingCount: remainingCount
      };

    } catch (error) {
      console.error('查询剩余次数失败:', error);
      return {
        success: false,
        error: '查询剩余次数失败，请稍后重试'
      };
    }
  }

  /**
   * 验证生成参数
   */
  validateGenerateParams(templateType, formData) {
    if (templateType === 1) {
      // 验证码模板
      if (!formData.verificationScenario) {
        return { valid: false, message: '请选择验证码使用场景' };
      }
    } else if (templateType === 2) {
      // 通知模板
      if (!formData.industry) {
        return { valid: false, message: '请选择行业' };
      }
      if (!formData.notificationScenario) {
        return { valid: false, message: '请选择通知场景' };
      }
    } else if (templateType === 3) {
      // 营销模板
      if (!formData.industry) {
        return { valid: false, message: '请选择行业' };
      }
      if (!formData.productName) {
        return { valid: false, message: '请输入产品/服务名称' };
      }
    }
    
    return { valid: true };
  }

  /**
   * 验证改写参数
   */
  validateRewriteParams(formData) {
    if (!formData.originalText) {
      return { valid: false, message: '请输入原文' };
    }
    if (formData.originalText.trim().length < 10) {
      return { valid: false, message: '原文内容过短，请输入至少10个字符' };
    }
    if (formData.originalText.trim().length > 500) {
      return { valid: false, message: '原文内容过长，请控制在500字符以内' };
    }
    
    return { valid: true };
  }

  /**
   * 构建生成请求参数
   */
  buildGenerateParams(templateType, formData) {
    // 添加调试日志
    console.log('buildGenerateParams called with:', { templateType, formData });

    if (templateType === 1) {
      // 验证码模板（已移除平台名称字段）
      return {
        template_type: "verification",
        usage_scenario: formData.verificationScenario || '',
        additional_info: formData.additionalInfo || ''
      };
    } else if (templateType === 2) {
      // 通知模板（已移除机构名称字段）
      return {
        template_type: "notification",
        industry: formData.industry || '',
        usage_scenario: formData.notificationScenario || '',
        additional_info: formData.additionalInfo || ''
      };
    } else if (templateType === 3) {
      // 营销模板（已移除营销类型字段）
      return {
        template_type: "marketing",
        industry: formData.industry || '',
        product_name: formData.productName || '',
        selling_point: formData.promotionInfo || '',
        promotion_link: formData.promotionLink || '',
        additional_info: formData.additionalInfo || ''
      };
    }

    throw new Error(`不支持的模板类型: ${templateType}`);
  }

  /**
   * 发起API请求 (POST)
   */
  async makeApiRequest(url, params, timeout) {
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        reject(new Error('请求超时'));
      }, timeout);

      // 发起POST请求
      this.api.post(
        url,
        params,
        (response) => {
          clearTimeout(timeoutId);
          resolve(response);
        },
        (error) => {
          clearTimeout(timeoutId);
          reject(error);
        }
      );
    });
  }

  /**
   * 发起GET请求
   */
  async makeGetRequest(url, timeout) {
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        reject(new Error('请求超时'));
      }, timeout);

      // 发起GET请求 - 修复参数传递方式
      this.api.get(
        url,
        {},  // GET请求的参数对象（空对象）
        (response) => {
          clearTimeout(timeoutId);
          resolve(response);
        },
        (error) => {
          clearTimeout(timeoutId);
          reject(error);
        }
      );
    });
  }

  /**
   * 处理API响应
   */
  processApiResponse(response, requestType) {
    try {
      console.log(`处理${requestType}API响应:`, response);

      // 检查响应状态
      if (!response || response.code !== 200) {
        console.log('API响应状态异常:', { code: response && response.code, msg: response && response.msg });
        return {
          success: false,
          error: (response && response.msg) || '请求失败',
          code: response && response.code
        };
      }

      // 解析数据
      const data = response.data;
      console.log('API响应数据:', data);

      if (!data) {
        console.log('API返回数据为空');
        return {
          success: false,
          error: '返回数据为空'
        };
      }

      // 处理模板数据
      let results = [];
      if (data.templates && Array.isArray(data.templates)) {
        console.log('处理多个模板结果:', data.templates);
        results = data.templates.map(template => ({
          content: template.content || '',
          variables: template.variables || []
        }));
      } else if (data.template && data.template.content) {
        console.log('处理改写接口单个模板结果:', data.template);
        // 改写接口返回的单个模板结果
        results = [{
          content: data.template.content,
          variables: data.template.variables || []
        }];
      } else if (data.content) {
        console.log('处理生成接口单个模板结果:', data.content);
        // 生成接口返回的单个模板结果
        results = [{
          content: data.content,
          variables: data.variables || []
        }];
      } else {
        console.log('未找到模板内容，数据结构:', Object.keys(data));
      }

      console.log('最终处理结果:', results);

      return {
        success: true,
        results: results,
        remainingCount: data.remaining_count,
        requestType: requestType
      };

    } catch (error) {
      console.error('处理API响应失败:', error);
      return {
        success: false,
        error: '数据处理失败'
      };
    }
  }

  /**
   * 获取错误信息
   */
  getErrorMessage(error, requestType) {
    if (error.message === '请求超时') {
      return requestType === 'generate' 
        ? 'AI生成超时，请检查网络连接后重试'
        : 'AI改写超时，请检查网络连接后重试';
    }
    
    if (error.code === 'NETWORK_ERROR') {
      return '网络连接异常，请检查网络后重试';
    }
    
    return requestType === 'generate' 
      ? '生成失败，请稍后重试'
      : '改写失败，请稍后重试';
  }

  /**
   * 重试请求
   */
  async retryRequest(requestFn, maxRetries = AI_API_CONFIG.MAX_RETRIES) {
    let retryCount = 0;
    
    while (retryCount <= maxRetries) {
      try {
        return await requestFn();
      } catch (error) {
        retryCount++;
        
        if (retryCount > maxRetries) {
          throw error;
        }
        
        // 指数退避延迟
        const delay = Math.pow(2, retryCount) * AI_API_CONFIG.RETRY_DELAY_BASE;
        console.log(`请求失败，${delay/1000}秒后进行第${retryCount}次重试...`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * 获取进度提示文本
   */
  getProgressText(requestType, elapsed) {
    const messages = {
      generate: [
        'AI正在分析您的需求...',
        'AI正在生成专业模板...',
        'AI正在优化模板内容...',
        '即将完成，请稍候...'
      ],
      rewrite: [
        'AI正在分析原文内容...',
        'AI正在优化文案表达...',
        'AI正在调整语言风格...',
        '即将完成，请稍候...'
      ]
    };

    const messageList = messages[requestType] || messages.generate;
    const index = Math.min(Math.floor(elapsed / 10000), messageList.length - 1);
    return messageList[index];
  }
}
