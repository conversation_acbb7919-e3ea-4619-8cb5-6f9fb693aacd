# AI模板助手组件

一个功能完整的AI模板生成和改写组件，支持验证码、通知、营销三种类型的短信模板智能生成。

## 特性

- 🤖 **智能生成**：基于AI的专业模板生成
- ✨ **内容改写**：优化现有模板文案
- 🎯 **多种类型**：支持验证码、通知、营销模板
- 🎨 **浮动面板**：可拖拽、可最小化的浮动界面
- 📱 **响应式设计**：适配不同屏幕尺寸
- 🔧 **高度可配置**：支持自定义配置和扩展

## 安装使用

### 1. 基础使用

```vue
<template>
  <div>
    <!-- 在需要的地方添加AI助手 -->
    <AiTemplateAssistant
      :template-type="templateType"
      :api-instance="$api"
      :message-instance="$message"
      :api-base-url="API.cpus"
      @use-template="handleUseTemplate"
      @generate-success="handleGenerateSuccess"
      @rewrite-success="handleRewriteSuccess"
    />
  </div>
</template>

<script>
import AiTemplateAssistant from '@/components/common/AiTemplateAssistant';

export default {
  components: {
    AiTemplateAssistant
  },
  data() {
    return {
      templateType: 1 // 1:验证码 2:通知 3:营销
    };
  },
  methods: {
    // 处理模板使用
    handleUseTemplate(data) {
      console.log('使用模板:', data);
      // data.content - 模板内容
      // data.variables - 变量信息
      // data.templateType - 模板类型
    },
    
    // 处理生成成功
    handleGenerateSuccess(data) {
      console.log('生成成功:', data);
    },
    
    // 处理改写成功
    handleRewriteSuccess(data) {
      console.log('改写成功:', data);
    }
  }
};
</script>
```

### 2. 作为Vue插件使用

```javascript
// main.js
import Vue from 'vue';
import AiTemplateAssistant, { install } from '@/components/common/AiTemplateAssistant';

Vue.use(install, {
  apiInstance: Vue.prototype.$api,
  messageInstance: Vue.prototype.$message
});

// 现在可以在任何组件中使用
// <AiTemplateAssistant :template-type="1" />
```

### 3. 只使用服务类

```javascript
import { AiTemplateService } from '@/components/common/AiTemplateAssistant';

export default {
  async mounted() {
    // 创建服务实例
    const aiService = new AiTemplateService(this.$api, this.$message);
    aiService.setBaseUrl(this.API.cpus);
    
    // 生成模板
    const result = await aiService.generateTemplate({}, 1, {
      verificationScenario: 'register',
      platformName: '某某APP'
    });
    
    if (result.success) {
      console.log('生成的模板:', result.results);
    }
  }
};
```

## API文档

### Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| templateType | Number | 是 | - | 模板类型：1-验证码，2-通知，3-营销 |
| apiInstance | Object | 是 | - | API实例对象 |
| messageInstance | Object | 是 | - | 消息提示实例 |
| apiBaseUrl | String | 是 | - | API基础URL |
| initialVisible | Boolean | 否 | false | 初始显示状态 |
| initialPosition | Object | 否 | {x:100,y:100} | 初始位置 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| use-template | {content, variables, templateType} | 用户点击使用模板时触发 |
| generate-success | {results, templateType, formData} | 模板生成成功时触发 |
| generate-error | error | 模板生成失败时触发 |
| rewrite-success | {results, originalText, description} | 模板改写成功时触发 |
| rewrite-error | error | 模板改写失败时触发 |
| visibility-change | visible | 面板显示状态变化时触发 |

### Methods

| 方法名 | 参数 | 说明 |
|--------|------|------|
| show() | - | 显示面板 |
| hide() | - | 隐藏面板 |
| setQuotas(generate, rewrite) | generate: Number, rewrite: Number | 设置配额 |
| getState() | - | 获取当前状态 |
| setState(state) | state: Object | 设置状态 |

## 配置选项

### 模板类型

```javascript
// 从枚举中导入
import { TEMPLATE_TYPE_MAP, TEMPLATE_TYPE_NAMES } from '@/components/common/AiTemplateAssistant';

console.log(TEMPLATE_TYPE_MAP); // {1: 'verification', 2: 'notification', 3: 'marketing'}
console.log(TEMPLATE_TYPE_NAMES); // {1: '验证码模板', 2: '通知模板', 3: '营销模板'}
```

### 场景选项

```javascript
import { 
  VERIFICATION_SCENARIOS,
  NOTIFICATION_INDUSTRIES,
  MARKETING_INDUSTRIES,
  getNotificationScenarios,
  getMarketingTypes
} from '@/components/common/AiTemplateAssistant';

// 验证码场景
console.log(VERIFICATION_SCENARIOS);

// 通知行业
console.log(NOTIFICATION_INDUSTRIES);

// 根据行业获取通知场景
const scenarios = getNotificationScenarios('ecommerce');
console.log(scenarios);
```

## 样式自定义

组件使用Less编写样式，支持自定义主题：

```less
// 自定义AI助手主题色
.ai-assistant-floating-panel {
  .ai-panel-header {
    background: linear-gradient(135deg, #your-color-1 0%, #your-color-2 100%);
  }
  
  .ai-generate-submit-btn {
    background: linear-gradient(135deg, #your-color-1 0%, #your-color-2 100%);
  }
}
```

## 高级用法

### 1. 状态持久化

```javascript
export default {
  data() {
    return {
      aiAssistantState: null
    };
  },
  methods: {
    // 保存状态
    saveAiState() {
      const state = this.$refs.aiAssistant.getState();
      localStorage.setItem('aiAssistantState', JSON.stringify(state));
    },
    
    // 恢复状态
    restoreAiState() {
      const savedState = localStorage.getItem('aiAssistantState');
      if (savedState) {
        this.$refs.aiAssistant.setState(JSON.parse(savedState));
      }
    }
  }
};
```

### 2. 自定义API处理

```javascript
import { AiTemplateService } from '@/components/common/AiTemplateAssistant';

class CustomAiService extends AiTemplateService {
  // 重写API请求方法
  async makeApiRequest(url, params, timeout) {
    // 自定义请求逻辑
    return super.makeApiRequest(url, params, timeout);
  }
  
  // 重写响应处理
  processApiResponse(response, requestType) {
    // 自定义响应处理
    return super.processApiResponse(response, requestType);
  }
}
```

### 3. 扩展枚举数据

```javascript
import { VERIFICATION_SCENARIOS } from '@/components/common/AiTemplateAssistant';

// 扩展验证码场景
const customScenarios = [
  ...VERIFICATION_SCENARIOS,
  { label: '自定义场景', value: 'custom_scenario' }
];
```

## 注意事项

1. **API兼容性**：确保后端API接口符合组件期望的数据格式
2. **样式冲突**：组件使用了高z-index值，注意与其他浮动元素的层级关系
3. **性能优化**：大量使用时建议实现组件懒加载
4. **移动端适配**：在移动端使用时注意拖拽体验优化

## 更新日志

### v1.0.0
- 初始版本发布
- 支持三种模板类型的AI生成
- 支持模板改写功能
- 浮动面板界面
- 完整的事件系统
