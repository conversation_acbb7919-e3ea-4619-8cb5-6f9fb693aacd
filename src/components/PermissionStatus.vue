<template>
  <div class="permission-status">
    <el-tag 
      :type="tagType" 
      size="small"
      :class="{'permission-loading': !isPermissionsLoaded}"
    >
      <span class="permission-indicator" :class="indicatorClass"></span>
      {{ permissionStatusText }}
    </el-tag>
  </div>
</template>

<script>
import { permissionMixin } from '@/mixins/permissionMixin'

export default {
  name: 'PermissionStatus',
  mixins: [permissionMixin],
  
  computed: {
    tagType() {
      if (!this.isPermissionsLoaded) return 'info'
      return this.hasWritePermission ? 'success' : 'warning'
    },
    
    indicatorClass() {
      if (!this.isPermissionsLoaded) return 'loading'
      return this.hasWritePermission ? 'read-write' : 'read-only'
    }
  }
}
</script>

<style scoped>
.permission-status {
  display: inline-block;
}

.permission-status .el-tag {
  font-size: 12px;
  user-select: none;
}
</style> 