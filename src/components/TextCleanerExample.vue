<template>
  <div class="text-cleaner-example">
    <h2>文本清理功能示例</h2>
    
    <!-- 方式1: 使用指令（推荐） -->
    <div class="example-section">
      <h3>方式1: 使用 v-text-cleaner 指令（推荐）</h3>
      <div class="input-group">
        <label>自动清理粘贴内容的输入框：</label>
        <input 
          v-text-cleaner 
          v-model="inputValue1" 
          placeholder="粘贴包含隐藏字符的文本试试"
          class="example-input"
        />
        <p class="value-display">当前值: {{ inputValue1 }}</p>
      </div>
      
      <div class="input-group">
        <label>同时清理粘贴和输入的文本框：</label>
        <input 
          v-text-cleaner="{ onInput: true, onPaste: true }" 
          v-model="inputValue2" 
          placeholder="输入或粘贴文本都会自动清理"
          class="example-input"
        />
        <p class="value-display">当前值: {{ inputValue2 }}</p>
      </div>
      
      <div class="input-group">
        <label>文本域示例：</label>
        <textarea 
          v-text-cleaner 
          v-model="textareaValue" 
          placeholder="粘贴多行文本进行测试"
          class="example-textarea"
          rows="4"
        ></textarea>
        <p class="value-display">当前值: {{ textareaValue }}</p>
      </div>
    </div>
    
    <!-- 方式2: 手动调用方法 -->
    <div class="example-section">
      <h3>方式2: 手动调用清理方法</h3>
      <div class="input-group">
        <label>原始文本：</label>
        <textarea 
          v-model="originalText" 
          placeholder="在这里输入或粘贴包含隐藏字符的文本"
          class="example-textarea"
          rows="3"
        ></textarea>
      </div>
      
      <button @click="cleanText" class="clean-button">清理文本</button>
      
      <div class="input-group">
        <label>清理后的文本：</label>
        <textarea 
          v-model="cleanedText" 
          readonly 
          class="example-textarea cleaned-text"
          rows="3"
        ></textarea>
      </div>
      
      <div class="info-section">
        <p><strong>原始长度:</strong> {{ originalText.length }}</p>
        <p><strong>清理后长度:</strong> {{ cleanedText.length }}</p>
        <p><strong>是否有隐藏字符:</strong> {{ originalText.length !== cleanedText.length ? '是' : '否' }}</p>
      </div>
    </div>
    
    <!-- 测试数据 -->
    <div class="example-section">
      <h3>测试数据</h3>
      <p>你可以复制下面包含隐藏字符的文本进行测试：</p>
      <div class="test-data">
        <code>{{ testDataWithHiddenChars }}</code>
      </div>
      <button @click="copyTestData" class="copy-button">复制测试数据</button>
    </div>
  </div>
</template>

<script>
import { removeHiddenCharacters } from '@/utils/textCleaner'

export default {
  name: 'TextCleanerExample',
  data() {
    return {
      inputValue1: '',
      inputValue2: '',
      textareaValue: '',
      originalText: '',
      cleanedText: '',
      // 包含各种隐藏字符的测试数据
      testDataWithHiddenChars: 'Hello​World‌Test‍​‌‍'
    }
  },
  methods: {
    cleanText() {
      this.cleanedText = removeHiddenCharacters(this.originalText)
    },
    
    copyTestData() {
      // 创建包含隐藏字符的测试文本
      const testText = 'Hello\u200BWorld\u200CTest\u200D\uFEFF\u202A测试文本\u202C'
      navigator.clipboard.writeText(testText).then(() => {
        this.$message.success('测试数据已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    }
  }
}
</script>

<style scoped>
.text-cleaner-example {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.input-group {
  margin-bottom: 15px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.example-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.example-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
}

.cleaned-text {
  background-color: #f5f5f5;
}

.value-display {
  margin-top: 5px;
  padding: 5px;
  background-color: #f9f9f9;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.clean-button, .copy-button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.clean-button:hover, .copy-button:hover {
  background-color: #66b1ff;
}

.info-section {
  background-color: #f0f9ff;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.test-data {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  margin: 10px 0;
}

h2 {
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

h3 {
  color: #666;
  margin-top: 0;
}
</style> 