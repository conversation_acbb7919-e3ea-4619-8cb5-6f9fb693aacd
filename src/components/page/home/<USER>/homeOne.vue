<template>
  <div class="consolo">
    <div v-if="Announcementlist.length > 0 && showflag">
      <div class="announcement">
        <i class="iconfont icon-gonggao"></i>
        <div class="announcement-con">
          <div id="text" class="announcement-content">
            {{ Announcementlist[0].messageContent }}
          </div>
        </div>
        <div class="announcement-close">
          <i
            class="el-icon-error"
            style="font-size: 14px; cursor: pointer"
            @click="closeAnnouncement"
          ></i>
        </div>
      </div>
    </div>
    <div
      v-if="certificate !== null && certificate != 2"
      class="attestation_con"
    >
      <div class="t_tips">
        <div class="tips_txt">
          根据《中华人民共和国网络安全法》相关规定，您需要在平台完成实名认证后，才可正常使用助通信息相关服务，请根据协议要求，上传所需认证资料及信息
        </div>
        <el-button
          v-if="certificate == 0"
          class="rz_btn"
          type="warning"
          @click="goAttestation"
          >去认证</el-button
        >
        <el-button
          v-if="certificate == 1"
          class="rz_btn"
          type="warning"
          @click="goAttestation"
          >查看进度</el-button
        >
      </div>
    </div>
    <div class="header_h">
      你好，{{ userName }}!
      <span style="margin-left: 10px">{{ compName }}</span>
    </div>
    <div class="consolo_content">
      <div class="consolo_left">
        <div class="product_group">
          <div class="set_tit">产品余额</div>
          <div class="sms-container-items">
            <div
              class="sms-item"
              v-for="(item, index) in tableData"
              :key="index"
            >
              <div class="sms-wnk" @click="goProduct(item.productId)">
                <img
                  class="sms-img"
                  v-if="item.productId == 1"
                  :src="icon4"
                  alt=""
                />
                <!-- <img class="sms-img" v-if="item.productId == 2" :src="icon5" alt="" @click="$router.push('/quickActions/multimediaMessage')"> -->
                <img
                  class="sms-img"
                  v-if="item.productId == 3"
                  :src="icon6"
                  alt=""
                />
                <!-- @click="$router.push('/quickActions/videoMessage')" -->
                <img
                  class="sms-img"
                  v-if="item.productId == 4"
                  :src="icon7"
                  alt=""
                />
                <!--@click="$router.push('/quickActions/internationalMessage')" -->
                <img
                  class="sms-img"
                  v-if="item.productId == 5"
                  :src="icon8"
                  alt=""
                />
                <img
                  class="sms-img"
                  v-if="item.productId == 6"
                  :src="icon9"
                  alt=""
                />
                <img
                  class="sms-img"
                  v-if="item.productId == 7"
                  :src="icon10"
                  alt=""
                />
                <img
                  class="sms-img"
                  v-if="item.productId == 8"
                  :src="icon11"
                  alt=""
                />
                <img
                  class="sms-img"
                  v-if="item.productId == 9"
                  :src="icon12"
                  alt=""
                />
                <img
                  class="sms-img"
                  v-if="item.productId == 10"
                  :src="icon13"
                  alt=""
                />
                <div v-if="item.productId != 2" class="sms-item-text">
                  <div class="sms-item-text-title ellipsis">
                    {{ item.productName }}
                  </div>
                  <div class="sms-item-text-value"   :class="{ 'ellipsis': !item.isOverflow }">
                    <span class="balance_val"  @click="toggleOverflow(item)">{{ item.balance }}</span>
                    <span v-if="item.productId == 4 || item.productId == 9"
                      >元</span
                    >
                    <span v-else>条</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="statistics">
          <div class="set_tit">
            <span>今日发送量</span>
            <i class="el-icon-refresh active" @click="getNum"></i>
          </div>
          <div class="pruduct_num" v-loading="loding">
            <div class="send_num">
              <img class="img_num" :src="icon4" alt="" />
              <div class="tit_num">
                <div>短信</div>
                <div class="count_num">
                  <span class="count_num_1">{{ smsData.sendNum }}</span
                  >条
                </div>
              </div>
            </div>
            <div class="send_num">
              <img class="img_num" :src="icon6" alt="" />
              <div class="tit_num">
                <div>视频短信</div>
                <div class="count_num">
                  <span class="count_num_1">{{ rmsData.sendNum }}</span
                  >条
                </div>
              </div>
            </div>
            <div class="send_num">
              <img class="img_num" :src="icon7" alt="" />
              <div class="tit_num">
                <div>国际短信</div>
                <div class="count_num">
                  <span class="count_num_1">{{ imsData.sendNum }}</span
                  >条
                </div>
              </div>
            </div>
            <div class="send_num">
              <img class="img_num" :src="icon8" alt="" />
              <div class="tit_num">
                <div>闪验</div>
                <div class="count_num">
                  <span class="count_num_1">{{ flashData.sendAmounts }}</span
                  >条
                </div>
              </div>
            </div>
            <div class="send_num">
              <img class="img_num" :src="icon9" alt="" />
              <div class="tit_num">
                <div>语音验证码</div>
                <div class="count_num">
                  <span class="count_num_1">{{ voiceData.sendyzmNum }}</span
                  >条
                </div>
              </div>
            </div>
            <div class="send_num">
              <img class="img_num" :src="icon10" alt="" />
              <div class="tit_num">
                <div>语音通知</div>
                <div class="count_num">
                  <span class="count_num_1">{{ voiceData.sendtzNum }}</span
                  >条
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <!-- <div v-if="certificate == 0" class="attestation">
            <div class="logo_att">
              <img
                style="margin-left: 30px"
                src="../../../../assets/images/861.png"
                alt=""
              />
              <div>企业资质未认证</div>
            </div>
            <div class="logo_t">
              <i style="color: orange" class="el-icon-warning"></i>
              <span>快去实名认证吧，开启您的真正使用之旅</span>
            </div>
            <div class="logo_b">
              <el-button @click="goAttestation">去认证</el-button>
            </div>
            <div class="btn_b">
              <div class="img_l">
                <router-link to="/guide">
                  <img
                    src="../../../../assets/images/yd.png"
                    width="180px"
                    alt=""
                  />
                </router-link>
              </div>
              <div v-if="hostflag" class="img_r">
                <a
                  href="https://doc.zthysms.com"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img
                    src="../../../../assets/images/jk.png"
                    width="180px"
                    alt=""
                  />
                </a>
              </div>
            </div>
          </div>
          <div v-if="certificate == 1" class="attestation">
            <div class="logo_att">
              <img
                style="margin-left: 30px"
                src="../../../../assets/images/861.png"
                alt=""
              />
              <div>企业资质认证中</div>
            </div>
            <div class="logo_b1">
              <el-button @click="goAttestation">查看进度</el-button>
            </div>
            <div class="btn_b">
              <div class="img_l">
                <router-link to="/guide">
                  <img
                    src="../../../../assets/images/yd.png"
                    width="180px"
                    alt=""
                  />
                </router-link>
              </div>
              <div v-if="hostflag" class="img_r">
                <a
                  href="https://doc.zthysms.com"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img
                    src="../../../../assets/images/jk.png"
                    width="180px"
                    alt=""
                  />
                </a>
              </div>
            </div>
          </div>
          <div v-if="certificate == 2" class="setting">
            <div class="set_tit">设置提醒</div>
            <div class="set_mian">
              <div class="set_t">
                <router-link to="/PersonalInformation">
                  <el-card shadow="hover"> 设置余额提醒 </el-card>
                </router-link>
              </div>
              <div class="set_t">
                <router-link to="/LoginCellPhone">
                  <el-card shadow="hover"> 设置登录手机号 </el-card>
                </router-link>
              </div>
              <div class="set_t">
                <router-link to="/NotificationAlert">
                  <el-card shadow="hover"> 设置通知预警 </el-card>
                </router-link>
              </div>
              <div class="btn_1">
                <router-link to="/guide">
                  <img
                    src="../../../../assets/images/yd.png"
                    width="180px"
                    alt=""
                  />
                </router-link>

                <a
                  class="img_r"
                  v-if="hostflag"
                  href="https://doc.zthysms.com"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img
                    src="../../../../assets/images/jk.png"
                    width="180px"
                    alt=""
                  />
                </a>
              </div>
            </div>
          </div> -->
        </div>
        <div v-if="roleId != 13 && hostflag" class="product_shop">
          <div
            style="
              height: 36px;
              border-bottom: 2px solid #e4e7ed;
              line-height: 36px;
              font-weight: bold;
              display: flex;
              justify-content: space-between;
            "
          >
            <span style="margin-left: 10px">热销套餐</span>
            <a
            v-if="shoppingCapability === true"
              style="margin-right: 10px; color: #aaa"
              :href="API.herf"
              target="_blank"
              rel="noopener noreferrer"
              >前往查看更多&nbsp;>
            </a>
          </div>
          <div v-if="shoppingCapability === true" class="hotshop">
            <div
              class="item"
              v-for="(item, index) in product"
              :key="index"
              @click="button(item)"
            >
              <div class="HOT-H">
                <div class="hot-h">
                  <img
                    v-if="item.bestSell == 1"
                    src="../../../../assets/images/hot.png"
                    alt=""
                  />
                </div>
                <div class="img">
                  <img
                    v-if="item.simage"
                    :src="API.imgU + item.simage"
                    alt=""
                    style="width: 80px; height: 80px"
                  />

                  <img v-else src="../../../../assets/images/11.png" alt="" />
                </div>
                <div class="title" style="font-weight: 800">
                  {{ item.name }}
                </div>
                <div
                  class="text"
                  style="
                    margin-top: 10px;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    overflow: hidden;
                  "
                >
                  {{ item.shortDesc }}
                </div>
                <div class="count">
                  价格：<span>￥{{ item.realPrice }}</span>
                </div>
              </div>
              <div class="button">
                <button @click="button(item)">查看详情</button>
              </div>
            </div>
          </div>
          <div v-else>
            <el-empty description="暂无商品，如需购买请联系商务！"></el-empty>
          </div>
        </div>
        <div class="send_msg">
          <div class="set_tit">发送短信</div>
          <div class="msg_mian">
            <div class="template_msg">
              <div>
                <span class="span_number">1</span>
                <span class="span_QM">创建签名</span>
              </div>
              <div>
                <span
                  >应运营商要求，每条短信必须附加短信签名，否则无法正常发送，可在此处申请属于您自己的短信签名</span
                >
              </div>
              <div>
                <router-link to="/CreateSign">
                  <el-button
                    class="btn_2"
                    type="primary"
                    @click="logUrl('SignatureManagement')"
                    plain
                    >创建签名</el-button
                  >
                </router-link>
              </div>
            </div>
            <div class="template_msg">
              <div>
                <span class="span_number">2</span
                ><span class="span_QM">创建模板</span>
              </div>
              <div class="div_content">
                <span
                  >模板创建并审核通过后,发送短信时可免人工审核，在此可以快捷创建模板</span
                >
              </div>
              <div>
                <router-link to="/CreateTemplate?i=1">
                  <el-button
                    class="btn_2"
                    type="primary"
                    @click="logUrl('TemplateManagement')"
                    plain
                    >创建模板</el-button
                  >
                </router-link>
              </div>
            </div>
            <div class="template_msg">
              <div>
                <span class="span_number">3</span
                ><span class="span_QM">短信发送</span>
              </div>
              <div class="div_content">
                <span>签名与模板均审核通过后，即可进行短信发送</span>
              </div>
              <div>
                <router-link to="/sendDetails">
                  <el-button
                    class="btn_2"
                    type="primary"
                    @click="logUrl('sendDetails')"
                    plain
                    >短信发送</el-button
                  >
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="consolo_right">
        <div class="product_msg">
          <div class="yuexin">
            <div
              style="
                height: 36px;
                border-bottom: 2px solid #e4e7ed;
                line-height: 36px;
                font-weight: bold;
                display: flex;
                justify-content: space-between;
              "
            >
              <span style="margin-left: 10px">阅信平台</span>
              <span style="margin-right: 10px">
                <a
                  :href="API.yuexinHerf"
                  target="_blank"
                  rel="noopener noreferrer"
                  >隆重上线，点击进入&nbsp;></a
                >
              </span>
            </div>
            <div class="yuexin_mian">
              <a
                :href="API.yuexinHerf"
                target="_blank"
                rel="noopener noreferrer"
                ><img
                  style="width: 100%; height: 100%"
                  src="../../../../assets/images/yuexin.jpg"
                  alt=""
                />
              </a>
            </div>
          </div>
          <div class="communication">
            <div
              style="
                height: 36px;
                border-bottom: 2px solid #e4e7ed;
                line-height: 36px;
                font-weight: bold;
                display: flex;
                justify-content: space-between;
              "
            >
              <span style="margin-left: 10px">智能通讯录</span>
              <span style="margin-right: 10px">
                <a
                  :href="API.contactHerf"
                  target="_blank"
                  rel="noopener noreferrer"
                  >点击进入&nbsp;></a
                >
              </span>
            </div>
            <div class="yuexin_mian">
              <a
                :href="API.contactHerf"
                target="_blank"
                rel="noopener noreferrer"
                ><img
                  style="width: 100%; height: 100%"
                  src="../../../../assets/images/userVip.png"
                  alt=""
                />
                <!-- <h3 class="small">{{ item + "会员管理" }}</h3> -->
              </a>
            </div>
          </div>
          <div class="operation">
            <div class="set_tit">快捷操作</div>
            <div class="operation_mian">
              <div class="main_top_banner" @click="$router.push('/showMsg')">
                <div class="main_top_text">
                  <p class="title-client">最新公告</p>
                  <p>平台公告信息等</p>
                </div>
                <img src="../../../../assets/images/main_bg2.png" />
              </div>
              <div class="main_top_banner" @click="$router.push('/guide')">
                <div class="main_top_text">
                  <p class="title-client">操作指南</p>
                  <p>平台操作演示</p>
                </div>
                <img src="../../../../assets/images/main_bg1.png" />
              </div>
              <div
                class="main_top_banner"
                style="padding-bottom: 10px"
                @click="$router.push('/helpCenterHome')"
              >
                <div class="main_top_text">
                  <p class="title-client">常见问题</p>
                  <p>常见问题汇总，助您快速解决问题熟悉平台规则</p>
                </div>
                <img src="../../../../assets/images/main_bg3.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="contact" v-if="roleId != 13 && hostflag">
      <img
        class="contact-img"
        src="../../../../assets/images/kf43.png"
        alt=""
      />
      <div class="card-client">
        <div class="user-picture">
          <img src="../../../../assets/images/kf43.png" alt="" />
        </div>
        <p class="name-client">
          客服经理
          <span style="font-size: 14px">李薇 </span>
          <span style="font-size: 14px">直线联系电话（同微信)</span>
          <span style="font-size: 14px">15601838771</span>
        </p>
        <div class="social-media"></div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import { mapState, mapMutations, mapActions } from "vuex";
import {
  icon4,
  icon5,
  icon6,
  icon7,
  icon8,
  icon9,
  icon10,
  icon11,
  icon12,
  icon13,
} from "@/components/page/h5/imgs.js";
export default {
  data() {
    return {
      product: [],
      showflag: false,
      shoppingCapability:null,
      hostname: window.location.hostname,
      nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
      hostflag: true,
      token: "",
      certificate: null,
      forGG: "",
      url: "",
      tableData: [],
      isShowBar: 0,
      smsData: {
        sendNum: 0,
      },
      rmsData: {
        sendNum: 0,
      },
      imsData: {
        sendNum: 0,
      },
      voiceData: {
        sendyzmNum: 0,
        sendtzNum: 0,
      },
      flashData: {
        sendAmounts: 0,
      },
      Announcementlist: [],
      loding: false,
      icon4,
      icon5,
      icon6,
      icon7,
      icon8,
      icon9,
      icon10,
      icon11,
      icon12,
      icon13,
    };
  },
  methods: {
    //   热销商品
    bestSell() {
      this.$api.get(this.API.shops + "shopgoods/bestSell", {}, (res) => {
        // console.log(res.data);
        this.product = res.data;
      });
    },
    goProduct(productId) {
      if (productId == 10) {
        var tempwindow = window.open("_blank");
        tempwindow.location = this.API.yuexinHerf
      }else if (productId == 9) {
        this.$router.push({ path: "/ChatBotManage" });
      }else if (productId == 8) {
        var tempwindow = window.open("_blank");
        tempwindow.location = this.API.contactHerf
      }
    },
    versions() {
      //     var u = navigator.userAgent, app = navigator.appVersion;
      //     console.log(u,'u');
      //    var mobile = !!u.match(/AppleWebKit.*Mobile.*/)||!!u.match(/AppleWebKit/)
      //    console.log(mobile,'mobile');
      if (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i)) {
        // localStorage.setItem('h5Lg',"0");
        this.isShowBar = 0;
        // window.location.href = "mobile.html";
      } else {
        this.isShowBar = 1;
        //    localStorage.setItem('h5Lg',"1");
        // window.location.href = "pc.html";
      }
    },
    getNum() {
      this.loding = true; // 设置加载状态为 true
      Promise.all([
        this.getSmsNum(),
        this.getRmsNum(),
        this.getImsNum(),
        this.getFlashNum(),
        this.getVoiceyzmNum(),
        this.getVoicetzNum(), // 添加这一行来调用获取语音通知数量的方法
      ]).then(() => {
        this.loding = false; // 设置加载状态为 false
      });
    },
    getSmsNum() {
      this.$api.get(
        this.API.cpus + "consumerdataoverviewday/businessOverview",
        {},
        (res) => {
          this.smsData.sendNum = res.data.billingNumber;
          // this.restNumSum = res.data.restNumSum;
          // this.sendAmounts = res.data.sendAmount;
          // this.successNum = res.data.successNum;
          // this.successRates = res.data.successRate;
          // this.updateTime = res.data.updateTime;
        }
      );
    },
    getRmsNum() {
      this.$api.get(
        this.API.cpus + "consumerdataoverviewday/businessOverview?productId=3",
        {},
        (res) => {
          this.voiceData.sendNum = res.data.billingNumber;
        }
      );
    },
    getImsNum() {
      this.$api.get(
        this.API.cpus + "consumerdataoverviewday/businessOverview?productId=4",
        {},
        (res) => {
          this.imsData.sendNum = res.data.billingNumber;
        }
      );
    },
    getFlashNum() {
      this.$api.get(
        this.API.cpus + "consumerdataoverviewday/businessOverview?productId=5",
        {},
        (res) => {
          this.flashData.sendAmounts = res.data.sendAmount;
        }
      );
    },
    getVoiceyzmNum() {
      this.$api.get(
        this.API.cpus + "consumerdataoverviewday/businessOverview?productId=6",
        {},
        (res) => {
          this.imsData.sendyzmNum = res.data.billingNumber;
        }
      );
    },
    getVoicetzNum() {
      this.$api.get(
        this.API.cpus + "consumerdataoverviewday/businessOverview?productId=7",
        {},
        (res) => {
          this.imsData.sendtzNum = res.data.billingNumber;
        }
      );
    },

    guide() {
      this.$router.push({ path: "/guide" });
    },
    button(item) {
      console.log(item, "item");
      var tempwindow = window.open("_blank");
      tempwindow.location =
        this.API.herf + "#/productDetail?goodsid=" + item.goodsNo;
      // this.url =
    },
    goAttestation() {
      this.$router.push({
        path: "/authentication",
      });
    },
    // 获取项目绝对路径
    ...mapActions([
      //比如'movies/getHotMovies
      "saveUrl",
    ]),
    logUrl(val) {
      let logUrl = {
        logUrl: val,
      };
      this.saveUrl(logUrl);
      window.sessionStorage.setItem("logUrl", val);
    },
    isTextOverflowing(div) {
      const style = window.getComputedStyle(div);
      const divWidth = parseInt(style.width);
      const textWidth = this.getTextWidth(div.innerText, style.font);

      return textWidth > divWidth;
    },

    getTextWidth(text, font) {
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      context.font = font;
      return context.measureText(text).width;
    },
    //公告信息
    getTableData() {
      this.$api.get(
        this.API.cpus + "consumerclientmessage/selectTopFive",
        {},
        (res) => {
          if (res.code == 200) {
            this.Announcementlist = res.data.filter((item) => {
              return item.messageType == 5;
            });
            if (this.Announcementlist.length > 0) {
              const textElement = document.getElementById("text");
              const isOverflowing = this.isTextOverflowing(textElement);
              if (isOverflowing) {
                const textWidth = this.getTextWidth(
                  textElement.innerText,
                  window.getComputedStyle(textElement).font
                );
                const duration = textWidth / 100 + "s";
                textElement.style.animationDuration = duration;
                textElement.classList.add("scrolling");
              } else {
                console.log("文本未超出 div 的宽度");
              }
            }
          }
          // // this.Announcementlist =
          // console.log( this.Announcementlist);
        }
      );
    },
    closeAnnouncement() {
      this.showflag = false;
      const expirationTime = new Date().getTime() + 24 * 60 * 60 * 1000; // 当前时间加上两小时
      localStorage.setItem(
        "announcementCloseTime",
        JSON.stringify({ value: "true", expiration: expirationTime })
      );
    },
    toggleOverflow(item){
      item.isOverflow = !item.isOverflow;
    },
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      userName: (state) => state.userName,
      compName: (state) => state.compName,
      roleId: (state) => state.roleId,
    }),
  },
  created() {
    this.nameTile.forEach((item) => {
      // console.log(item);
      if (this.hostname == item) {
        // console.log(1);
        this.hostflag = false;
      }
    });
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data);
        this.certificate = res.data.certificate;
        this.shoppingCapability = res.data.shoppingCapability;
        // this.userBalance=res.data.balanceList[0].num
        // this.userMMSbalance=res.data.balanceList[1].num
        // this.userRMSbalance=res.data.balanceList[2].num
      }
    );
    // console.log(this.$route.params.token,'token');
    this.$api.get(this.API.cpus + "home/balance", {}, (res) => {
      this.tableData = res.data.filter((item) => {
        return item.productId != 2;
      });
      this.tableData.forEach((item) => {
        this.$set(item, 'isOverflow', false);
      })
    });
    this.token = this.$route.params.token;
    this.bestSell();
    this.versions();
    this.getNum(); // 调用获取数量的方法
    // this.getSmsNum();
    // this.getRmsNum();
    // this.getImsNum();
    // this.getFlashNum();
    // this.getVoiceyzmNum();
    // this.getVoicetzNum();
    // if(window.location.hostname ==''){

    // }
    // console.log(window.location.hostname);
  },
  mounted() {
    // 公告
    let flag = localStorage.getItem("announcementCloseTime");
    if (flag) {
      const flagObj = JSON.parse(flag);
      const currentTime = new Date().getTime();
      if (currentTime < flagObj.expiration) {
        this.showflag = false;
      } else {
        localStorage.removeItem("announcementCloseTime");
        this.showflag = true;
      }
    } else {
      this.showflag = true;
    }
    this.getTableData();
  },
};
</script>
  
  
  <style lang="less" scoped>
.consolo {
  width: 100%;
}
.header_h {
  // width: 100%;
  height: 50px;
  background: #fff;
  line-height: 50px;
  padding-left: 10px;
  border-radius: 8px;
}
.consolo_content {
  display: flex;
  /* padding: 0 20px; */
  margin-top: 10px;
  padding-bottom: 20px;
}
.consolo_left {
  display: flex;
  /* -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column; */
  flex-direction: column;
  width: 65%;
  /* background: #fff; */
}
.consolo_right {
  flex: 1;
  margin-left: 20px;

  /* padding: 10px; */
}
.attestation {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background: #fff;
  margin-bottom: 14px;
  border-radius: 8px;
}
.attestation_con {
  background: #fff;
  padding: 14px 18px;
  position: relative;
  border-radius: 8px;
  display: flex;
  z-index: 1;
  margin-bottom: 12px;
  margin-top: 8px;
}
.t_tips {
  padding: 0 70px 0 0;
  width: 0;
  flex: 1;
  position: relative;
  line-height: 16px;
}
.tips_txt {
  overflow: hidden;
  color: #ec6e06;
}
.rz_btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.setting {
  background: #fff;
}
.logo_att {
  margin-top: 35px;
}
.set_tit {
  height: 40px;
  border-bottom: 1px solid #ccc;
  line-height: 40px;
  padding: 0 10px;
  font-weight: 800;
}
.set_mian {
  /* display: flex; */
  margin: 20px 0;
  flex-shrink: 0;
  /* flex-wrap: wrap; */
}
.set_t {
  width: calc(100% / 2);
  height: 65px;
  text-align: center;
  /* background: #ebf7f5; */
  margin: 0 auto;
  /* padding: 10px 20px; */
}
.logo_t {
  margin-top: 35px;
}
.logo_b {
  margin-top: 35px;
}
.logo_b1 {
  margin-top: 35px;
}
.btn_b {
  display: flex;
  justify-content: space-around;
  margin-top: 35px;
  margin-bottom: 10px;
}
.img_r {
  margin-left: 20px;
}
.btn_1 {
  /* display: flex;
    align-items: center;
    justify-items: center; */
  margin-bottom: 10px;
  text-align: center;
}
.send_msg {
  background: #fff;
  border-radius: 8px;
  margin-top: 10px;
}
.msg_mian {
  display: flex;
  padding: 8px;
}
.template_msg {
  width: calc(100% / 3);
  /* height: 120px; */
  /* border: 1px solid #dcdfe6; */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  line-height: 24px;
  padding: 10px;
  margin: 0 8px;
}
.span_number {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid;
  text-align: center;
  border-radius: 20px;
  font-weight: 600;
  font-family: monospace;
  line-height: 15px;
  color: #00a489;
}
.span_QM {
  font-family: monospace;
  font-size: 13px;
  font-weight: 600;
  color: #00a489;
  margin-left: 5px;
}
.btn_2 {
  margin-top: 14px;
}
.sms-container-items {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  /* justify-content: space-between; */
  align-items: center;
  /* margin-top: 20px;
  padding: 10px; */
}
.sms-item {
  width: calc(100% / 3.3);
  padding: 8px 8px;
  // -webkit-transition: all 0.3s;

  cursor: pointer;
}
.sms-item:hover {
  .sms-img {
    transform: scale(1.2);
  }
}
.sms-img {
  width: 54px;
  height: 54px;
  transition: all 0.3s;
}
.sms-wnk {
  border-radius: 8px;
  // background: #f6f7fa;
  // border: 1px solid #bfbfbf;
  box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.2);
  display: flex;
  padding: 16px 18px;
}
.sms-item-text {
  padding-left: 16px;
  -webkit-box-flex: 1;
  flex: 1;
  width: 0;
}
.sms-item-text-title {
  margin-top: 2px;
  font-size: 14px;
  color: #333;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.sms-item-text-value {
  margin-top: 4px;
}
.balance_val {
  color: #000;
  font-weight: bold;
  font-size: 17px;
}
.product_msg {
  // margin-top: 10px;

  // display: flex;
}
.yuexin {
  // width: 50%;
  background: #fff;
  border-radius: 8px;
}
.yuexin_mian {
  width: 100%;
  height: 286px;
}
.communication {
  flex: 1;
  background: #fff;
  margin-top: 10px;
  border-radius: 8px;
}
.product_group {
  background: #fff;
  border-radius: 8px;
}
.product_shop {
  background: #fff;
  // margin-top: 10px;
  border-radius: 8px;
}
.operation {
  background: #fff;
  margin-top: 10px;
  border-radius: 8px;
  padding: 0 8px;
}
.operation_mian {
}
.main_top_banner {
  border-radius: 6px;
  color: #fff;
  cursor: pointer;
  position: relative;
  margin: 10px 0;
}
.main_top_text {
  position: absolute;
  line-height: 32px;
  z-index: 2;
  top: 0;
  margin-top: 20px;
  left: 0;
  margin-left: 20px;
}
.title-client {
  font-size: 18px;
}
.hotshop {
  width: 100%;
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  flex-shrink: 0;
  overflow: auto;
}
.item {
  width: calc(100% / 6.2);
  // height: 300px;
  margin: 15px 8px;
  border: 1px solid #bfbfbf;
  box-shadow: 0px 0px 5px 1px #aaa;
  border-radius: 5px;
  padding: 10px 6px;
  cursor: pointer;
}
.img {
  width: 100%;
  height: 100px;
  line-height: 100px;
  text-align: center;
  margin-top: 25px;
}
.title {
  width: 100%;
  text-align: center;
}
.text {
  /* margin-top: 10px; */
  padding: 0 10px;
  font-size: 14px;
  color: rgb(126, 126, 126);
}
.count {
  font-size: 14px;
  padding: 0 10px;
  margin: 15px 0;
  font-weight: 800;
}
.count span {
  color: red;
}
.price {
  font-weight: 800;
  font-size: 14px;
}
.price span {
  color: red;
}
b {
  color: rgb(25, 0, 255);
}
.button {
  width: 100%;
  margin-top: 15px;
}
.button button {
  width: 100%;
  height: 30px;
  border: none;
  outline: none;
  border-radius: 5px;
  cursor: pointer;
}
.span_GG {
  display: inline-block;
  white-space: nowrap;
  margin-left: 15px;
  overflow: hidden;
  width: 60%;
  text-overflow: ellipsis;
}
.HOT-H {
  position: relative;
}
.hot-h {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: -10px;
  margin-right: -15px;
}
.contact {
  position: fixed;
  bottom: 5%;
  margin-bottom: -28px;
  right: 5%;
  margin-right: -48px;
  cursor: pointer;
  z-index: 9;
}
.contact-img {
  animation: contact 1.5s infinite alternate;
}
.contact:hover {
  .contact-img {
    display: none;
  }
  .card-client {
    display: block;
  }
}

@keyframes contact {
  0% {
    transform: rotate(0deg) scale(1);
  }
  00% {
    transform: rotate(0deg) scale(1.1);
  }
  100% {
    transform: rotate(0deg) scale(1);
  }
}

.card-client {
  background: #2cb5a0;
  width: 13rem;
  padding-top: 25px;
  padding-bottom: 25px;
  padding-left: 20px;
  padding-right: 20px;
  border: 4px solid #7cdacc;
  box-shadow: 0 6px 10px rgba(207, 212, 222, 1);
  border-radius: 10px;
  text-align: center;
  color: #fff;
  font-family: "Poppins", sans-serif;
  transition: all 0.3s ease;
  display: none;
}

.card-client:hover {
  transform: translateY(-10px);
}

.user-picture {
  overflow: hidden;
  object-fit: cover;
  width: 5rem;
  height: 5rem;
  border: 4px solid #7cdacc;
  border-radius: 999px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
}

.user-picture svg {
  width: 2.5rem;
  fill: currentColor;
}

.name-client {
  margin: 0;
  margin-top: 20px;
  font-weight: 600;
  font-size: 18px;
  line-height: 1.5;
}

.name-client span {
  display: block;
  font-weight: 200;
  font-size: 16px;
}

.social-media:before {
  content: " ";
  display: block;
  width: 100%;
  height: 2px;
  margin: 20px 0;
  background: #7cdacc;
}
.statistics {
  background: #fff;
  margin: 10px 0;
  border-radius: 8px;
}
.pruduct_num {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.send_num {
  width: calc(100% / 3.3);
  // min-width:240px;
  height: 100px;
  // border: 1px solid #bfbfbf;
  box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  position: relative;
  margin: 8px;
}
.img_num {
  position: absolute;
  right: 0;
  width: 100px;
  height: 100%;
  bottom: 0;
  opacity: 0.5;
}
.tit_num {
  margin: 18px;
  color: #899caf;
}
.count_num {
  margin-top: 18px;
}
.count_num_1 {
  font-size: 26px;
  font-weight: bolder;
  color: #000;
}
.announcement {
  /* display: inline-block;  */
  display: flex;
  align-items: center;

  color: #ed6a0c; /* 字体颜色 */
  /* line-height: 35px; */
  border-radius: 8px;
  padding: 5px 4px;
  margin: 8px 0;
  font-size: 12px;
  background: #fffbe8;
}
.announcement-con {
  width: 100%;
  overflow: hidden;
  cursor: pointer;
}
.announcement-close {
  margin: 0 5px;
}
.announcement-content {
  white-space: nowrap;
}
.scrolling {
  animation: moveText linear infinite;
}
@keyframes moveText {
  0% {
    transform: translateX(100%); /* 从右侧外部开始 */
  }
  100% {
    transform: translateX(-100%);
  }
}
.active {
  margin-left: 8px;
  font-size: 16px;
  font-weight: 800;
  cursor: pointer;
}
.active:hover {
  color: #409eff;
}
</style>