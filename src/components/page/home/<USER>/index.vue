<template>
    <div id="indexOne" style="widht:100%;">
        <div class="homewe">
            <div class="home-bottom-purpley">
                <div class=" home-bottom-purple home-bottom-purple2">
                    <div class="floatDiv" v-for="(item,index) in rest" :key=index>
                        <span>{{item.name}}  剩余</span><span>{{item.num}}</span><span v-if="item.name=='国际短信'||item.name=='5G消息'">元</span><span v-else>条</span>
                    </div>
                </div>
                <div class=" home-bottom-purple home-bottom-purple3" style="">
                    <div style="width:40%; border-right:1px solid #eee;padding-right:12px;"  class="home-user-info-item">
                        <p class="icon iconfont icon-yonghu" style="color:#16a589;font-size:30px;font-weight:bold;padding-bottom:42px;"></p>
                        <router-link to="/account"><span class="phe" @click="logUrl('account')">登录手机管理</span></router-link>
                    </div>
                    <div style="width:60%;padding-left:12px;"  class="home-user-info-item"><p class="icon iconfont icon-zhengpinbaozhangduigou" style="color:#16a589;font-size:30px;margin-bottom:12px;"></p><p class="sTime">上次登录时间:</p><p class="sTime">{{lastTime}}</p></div>
                </div>
            </div>
        </div>
        <div style="background: #fff;padding: 20px;margin-top: 20px;heith:100%">
            <div class="home-title" style="margin-bottom: 10px;">产品展示</div>
            <div class="home-product-box home-product-box1">
                <div class="home-product-img home-product-img1"><img src="../../../../assets/images/6.png" alt=""></div>
                <div class="home-product-text">
                    <div class="home-product-title">短信</div>
                    <div  class="home-product-aic">三网合一，秒级触达，极速验证，安全稳定，全网覆盖，支持免审发送。</div>
                </div>
            </div>
            <div class="home-product-box home-product-box6" >
                <div class="home-product-img home-product-img5"><img src="../../../../assets/images/1.png" alt=""></div>
                <div class="home-product-text">
                    <div class="home-product-title">短链转换</div>
                    <div  class="home-product-aic">为您提供稳定的短网址转换及统计服务，帮您了解用户，为推广营销提供大数据支持。</div>
                </div>
            </div>
            <div class="home-product-box home-product-box5">
                <div class="home-product-img home-product-img2"><img src="../../../../assets/images/3.png" alt=""></div>
                <div class="home-product-text">
                    <div class="home-product-title">国际短信</div>
                    <div  class="home-product-aic">支持200多个国家的短信下发，实现产品全球化服务。</div>
                </div>
            </div>
            <div class="home-product-box home-product-box2" >
                <div class="home-product-img home-product-img3"><img src="../../../../assets/images/4.png" alt=""></div>
                <div class="home-product-text">
                    <div class="home-product-title">语音短信</div>
                    <div  class="home-product-aic">通过API的形式，将语音短信准确的下发给用户手机。</div>
                </div>
            </div>
            <div class="home-product-box home-product-box3" >
                <div class="home-product-img home-product-img4"><img src="../../../../assets/images/5.png" alt=""></div>
                <div class="home-product-text">
                    <div class="home-product-title">彩信</div>
                    <div  class="home-product-aic">一条彩信可容纳数万字和数张图片，传达给客户最生动的讯息。</div>
                </div>
            </div>
            <div class="home-product-box home-product-box4" >
                <div class="home-product-img home-product-img4"><img src="../../../../assets/images/322335.png" alt=""></div>
                <div class="home-product-text">
                    <div class="home-product-title">视频短信</div>
                    <div  class="home-product-aic">一条视频短信可容纳数万字和数张图片，传达给客户最生动的讯息。</div>
                </div>
            </div>
        </div>
        <div style="display:flex" v-if="hostflag">
            <div class="shopG">
            <div>
                <div
                  style="
                    height: 36px;
                    border-bottom: 2px solid #e4e7ed;
                    line-height: 36px;
                    font-weight: bold;
                    display: flex;
                    justify-content: space-between;
                  "
                >
                  <span style="margin-left: 10px">热销套餐</span>
                  <!-- <span style="margin-right: 10px" @click="tostname">
                   查看更多>
                  </span> -->
                  <a
                    style="margin-right: 10px;color:#555"
                    :href="API.herf"
                    target="_blank"
                    rel="noopener noreferrer"
                    >前往查看更多&nbsp;> </a
                  >
                </div>
            </div>
             <div class="hotshop">
                  <div
                    class="item"
                    v-for="(item, index) in product"
                    :key="index"
                    @click="button(item)"
                  >
                    <div class="HOT-H">
                      <div class="hot-h">
                        <img
                          v-if="item.bestSell == 1"
                          src="../../../../assets/images/hot.png"
                          alt=""
                        />
                      </div>
                      <div class="img">
                        <!-- <img :src="item.img" alt="" /> -->
                        <img v-if="item.simage" :src="API.imgU+item.simage" alt="" style="width:80px;height:80px">
                        <!-- <img v-else sr../c="../../assets/icon/11.png" alt="" > -->
                        <img v-else src="../../../../assets/images/11.png" alt="" />
                      </div>
                      <div class="title" style="font-weight: 800">
                        {{ item.name }}
                      </div>
                      <div class="text" style="margin-top: 10px;display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;">
                        {{ item.shortDesc }}
                      </div>
                      <div class="count">
                        价格：<span>￥{{ item.realPrice }}</span>
                      </div>
                    </div>
                    <div class="button">
                      <button @click="button(item)">查看详情</button>
                    </div>
                    <!-- <div>
                        <span style="font-size:12px;color:red;font-weight: 800;">注：时间截止到{{item.endTime}}</span>
                    </div> -->
                  </div>
             </div>
        </div>
        <!-- <div class="right-b">
              <div class="certificate-title"><span style="padding-left: 10px;">我的专属商务</span></div>
              <div class="kf">
                <div style="margin-left:200px; margin-top:10px">
                  <img src="../../../../../../assets/images/kf43.png" alt="">
                </div>
                <div style="line-height: 20px;">
                  <div style="text-align: center;color:#b0b0b0;font-weight: 800;">商务经理</div>
                  <div style="text-align: center;color:#000;font-weight: 800;font-size:16px">李薇</div>
                </div>
                
                <div style="line-height: 20px;margin-top:10px;">
                  <div style="text-align: center;color:#b0b0b0;font-weight: 800;">直线联系电话（同微信）</div>
                  <div style="text-align: center;color:#000;font-weight: 800;font-size:16px">13248296554</div>
                </div>
              </div>
              <div style="line-height: 20px;margin-top:10px;">
                  <div style="text-align: center;color:#b0b0b0;font-weight: 800;">QQ</div>
                  <div style="text-align: center;color:#000;font-weight: 800;font-size:16px">2880703378</div>
                </div>
        </div> -->
        </div>
        
    </div>
</template>
<script>
import { mapState, mapMutations,mapActions } from "vuex";
export default {
    name: "indexOne",
        data () {
        return {
            rest:[],
            lastTime:'',
            product:[],
            hostname: window.location.hostname,
            nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
            hostflag: true,
        }
    },
    methods: {
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveUrl',
        ]),
         //热销商品
    bestSell() {
      this.$api.get(this.API.shops + "shopgoods/bestSell", {}, (res) => {
        // console.log(res.data);
        this.product = res.data;
      });
    },
    button(item) {
    //   console.log(item, "item");
      var tempwindow = window.open("_blank");
      tempwindow.location =
        this.API.herf+"#/productDetail?goodsid=" + item.goodsNo;
      // this.url =
    },
        logUrl(val){
            let logUrl={
                logUrl:val
            } 
            this.saveUrl(logUrl);
            window.sessionStorage.setItem('logUrl',val)            
        }
    },
    mounted(){
        this.$api.get(this.API.cpus+'consumerclientinfo/getClientInfo',null,res=>{
          if(res.data.balanceList){
            this.rest=res.data.balanceList
          }
            
        })
        this.$api.post(this.API.cpus + 'consumerclientinfo/homePage',{},res=>{
            this.lastTime=res.data.lastTime
        })
    },
    created(){
      this.nameTile.forEach((item) => {
      // console.log(item);
      if (this.hostname == item) {
        // console.log(1);
        this.hostflag = false;
      }
    });
        this.bestSell()
    }
}
</script>

<style scoped>
@media screen and (min-width: 1200px){
    .home-box{
    margin-bottom: 10px;
    padding:20px;
}
.home-user-info{
    margin-bottom: 10px;
}
.home-bottom-purple{
    padding:10px;
    background: #fff;
    box-sizing: border-box;
}
.home-user-info-item{
    padding:10px 0;
    text-align: center;
}
.shopG{
    width: 1000px;
    background: #fff;
    margin: 20px 0;
}
.hotshop {
  width: 100%;
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
}
.item {
  width: 200px;
  height: 300px;
  margin: 15px 15px;
  border: 1px solid #bfbfbf;
  box-shadow: 0px 0px 5px 1px #aaa;
  border-radius: 5px;
  padding: 10px 6px;
  cursor: pointer;
}
.img {
  width: 100%;
  height: 100px;
  line-height: 100px;
  text-align: center;
  margin-top: 25px;
}
.title {
  width: 100%;
  text-align: center;
}
.text {
  /* margin-top: 10px; */
  padding: 0 10px;
  font-size: 14px;
  color: rgb(126, 126, 126);
  
}
.count {
  font-size: 14px;
  padding: 0 10px;
  margin: 15px 0;
  font-weight: 800;
}
.count span {
  color: red;
}
.price {
  font-weight: 800;
  font-size: 14px;
}
.price span {
  color: red;
}
b {
  color: rgb(25, 0, 255);
}
.button {
  width: 100%;
  margin-top: 15px;
}
.button button {
  width: 100%;
  height: 30px;
  border: none;
  outline: none;
  border-radius: 5px;
  cursor: pointer;
}
.span_GG {
  display: inline-block;
  white-space: nowrap;
  margin-left: 15px;
  overflow: hidden;
  width: 60%;
  text-overflow: ellipsis;
}
.HOT-H {
  position: relative;
}
.hot-h {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: -10px;
  margin-right: -15px;
}
.right-b{
  width: 500px;
  height: 320px;
  background: #fff;
  margin-top: 20px;
  margin-left: 60px;
}
.certificate-title{
  height: 36px;
  line-height: 36px;
  font-weight: 800;
  border-bottom: 2px solid #e4e7ed;
}
.home-title{
    font-weight: bold;
    padding-bottom: 16px;
}
.home-product-box{
    width:calc(33% - 12px);
    padding:24px 20px;
    margin-right:12px;
    display: inline-block;
    border:1px solid #eee;
    box-sizing: border-box;
    margin-bottom: 12px;
}
.home-product-box:hover{
    border:1px solid #16a589;
}
.home-product-img{
    display: inline-block;
    width:88px;
}
.home-product-img img{
    width:88px;
    position: relative;
    top:8px;
}
.home-product-img6 img{
    top:1px;
}
.home-product-text{
    width:calc(100% - 93px);
    display: inline-block;
    padding-left: 12px;
    box-sizing: border-box;
}
.home-product-title{
    font-weight: bold;
    padding-bottom: 8px;
}

.dataScreening-chart-box{
    margin:10px 0;
    padding:20px 20px 20px 20px;
}
.dataScreening-chart-title{
    display: flex;
}
.dataScreening-chart{
    height:360px;
}
.dataScreening-title{
    padding-top:40px;
    font-weight: bold;
}
.look-at-more{
    color:#16a589;
}
.dataScreening-select{
    position: relative;
    margin-top:10px;
}
.floatDiv{
    width: 50%;
    float: left;
}   
.home-user-xd .el-col{
    padding:20px 4%;
} 
.home-user-title{
    height:40px;
    line-height: 40px;
    border:1px solid #fff;
    border-radius:20px;
    text-align: center;
    color:#fff;
    background: -webkit-linear-gradient(top,rgba(22,165,137, .1), rgba(22,165,137,1)); 
    background: -o-linear-gradient(top,rgba(22,165,137, .1), rgba(22,165,137,1));  
    background: -moz-linear-gradient(top,rgba(22,165,137, .1), rgba(22,165,137,1));  
    background: linear-gradient(to top,rgba(22,165,137, .1), rgba(22,165,137,1));  
 }
 .notice-table{
     margin-top:16px;
 }
.homewe{
    display:flex;
    height:150px;
}
.home-bottom-purple1{
    display:flex;
    width:56%;
}
.home-bottom-purple2{
    width:calc(100% - 360px);
    margin-left:12px;
    text-align: center;line-height: 28px;font-size: 18px;
}
.home-bottom-purple3{
    display:flex;
    width:80%;
    margin-left:12px;
}
.home-bottom-purpley{
    display:flex;
    width:100%;
}
.home-user-info-item1{
    width:calc(50% - 70px); border-right:1px solid #eee;
}
.home-user-info-item2{
    width:140px; border-right:1px solid #eee;
}
.home-user-info-item3{
    width:calc(50% - 70px);
}
.homewe{
    display:flex;
    height:150px;
}
.sTime{
   margin-bottom:12px;
}
.phe{
    font-size: 14px;
}
}

@media screen and (max-width: 1200px){
        .home-box{
    margin-bottom: 10px;
    padding:20px;
}
.home-user-info{
    margin-bottom: 10px;
}
    .homewe{
        display:block;
    }
    .home-user-info-item{
    padding:10px 0;
    text-align: center;
}
.shopG{
    width: 1000px;
    background: #fff;
    margin: 20px 0;
}
.hotshop {
  width: 100%;
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
}
.item {
  width: 200px;
  height: 300px;
  margin: 15px 15px;
  border: 1px solid #bfbfbf;
  box-shadow: 0px 0px 5px 1px #aaa;
  border-radius: 5px;
  padding: 10px 6px;
  cursor: pointer;
}
.img {
  width: 100%;
  height: 100px;
  line-height: 100px;
  text-align: center;
  margin-top: 25px;
}
.title {
  width: 100%;
  text-align: center;
}
.text {
  /* margin-top: 10px; */
  padding: 0 10px;
  font-size: 14px;
  color: rgb(126, 126, 126);
  
}
.count {
  font-size: 14px;
  padding: 0 10px;
  margin: 15px 0;
  font-weight: 800;
}
.count span {
  color: red;
}
.price {
  font-weight: 800;
  font-size: 14px;
}
.price span {
  color: red;
}
b {
  color: rgb(25, 0, 255);
}
.button {
  width: 100%;
  margin-top: 15px;
}
.button button {
  width: 100%;
  height: 30px;
  border: none;
  outline: none;
  border-radius: 5px;
  cursor: pointer;
}
.span_GG {
  display: inline-block;
  white-space: nowrap;
  margin-left: 15px;
  overflow: hidden;
  width: 60%;
  text-overflow: ellipsis;
}
.HOT-H {
  position: relative;
}
.hot-h {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: -10px;
  margin-right: -15px;
}
.right-b{
  width: 500px;
  height: 320px;
  background: #fff;
  margin-top: 20px;
  margin-left: 60px;
}
.certificate-title{
  height: 36px;
  line-height: 36px;
  font-weight: 800;
  border-bottom: 2px solid #e4e7ed;
}
.home-title{
    font-weight: bold;
    padding-bottom: 16px;
}
.home-product-box{
    width:calc(33% - 12px);
    padding:24px 20px;
    margin-right:12px;
    display: inline-block;
    border:1px solid #eee;
    box-sizing: border-box;
    margin-bottom: 12px;
}
.home-product-box:hover{
    border:1px solid #16a589;
}
.home-product-img{
    display: inline-block;
    width:88px;
}
.home-product-img img{
    width:88px;
    position: relative;
    top:8px;
}
.home-product-img6 img{
    top:1px;
}
.home-product-text{
    width:calc(100% - 93px);
    display: inline-block;
    padding-left: 12px;
    box-sizing: border-box;
}
.home-product-title{
    font-weight: bold;
    padding-bottom: 8px;
}

.dataScreening-chart-box{
    margin:10px 0;
    padding:20px 20px 20px 20px;
}
.dataScreening-chart-title{
    display: flex;
}
.dataScreening-chart{
    height:360px;
}
.dataScreening-title{
    padding-top:40px;
    font-weight: bold;
}
.look-at-more{
    color:#16a589;
}
.dataScreening-select{
    position: relative;
    margin-top:10px;
}
.floatDiv{
    width: 50%;
    float: left;
}   
.home-user-xd .el-col{
    padding:20px 4%;
} 
.home-user-title{
    height:40px;
    line-height: 40px;
    border:1px solid #fff;
    border-radius:20px;
    text-align: center;
    color:#fff;
    
    background: -webkit-linear-gradient(to top,rgba(22,165,137, .1), rgba(22,165,137,1)); 
    
    background: -o-linear-gradient(to top,rgba(22,165,137, .1), rgba(22,165,137,1));  
    background: -moz-linear-gradient(to top,rgba(22,165,137, .1), rgba(22,165,137,1));  
    background: linear-gradient(to top,rgba(22,165,137, .1), rgba(22,165,137,1));  
 }
 .notice-table{
     margin-top:16px;
 }
    .home-bottom-purple1{
        width:100%;
    }
    .home-bottom-purpley{
        /* display:flex; */
        margin-top:12px;
        width:100%;
        /* background: #fff; */
    }
    .home-bottom-purple2{
        width:calc(100%);
        margin-left:0px;
        height: 100px;
        background: #fff;
        text-align: center;line-height: 25px;font-size: 14px;
    }
    .home-bottom-purple3{
        display:flex;
        width:100%;
        margin-top: 10px;
        background: #fff;
    }
   .home-user-info-item1{
        width:calc(50% - 140px); border-right:1px solid #eee;
    }
    .home-user-info-item2{
        width:280px; border-right:1px solid #eee;
    }
    .home-user-info-item3{
        width:calc(50% - 140px);
    }
    .sTime{
    margin-bottom:12px;
    font-size: 14px;
}

}
@media screen and (max-width: 1200px){
    .home-product-box{
        width:calc(100% - 12px);
        padding:24px 20px;
        display: inline-block;
        border:1px solid #eee;
        box-sizing: border-box;
        margin: 5px;
    }
}
</style>
<style>
#indexOne.threeDay .el-radio-button__inner{
    border-right: 0 ;
}
.dataScreening-chart-box .el-radio-button:last-child .el-radio-button__inner{
    border-radius: 0;
}
.dataScreening-chart-box .el-range-editor.el-input__inner{
    border-radius: 0px 4px 4px 0;
}
</style>