<template>
    <div style="width:100%; padding:10px;" class="bag">
        <div class="Top_title" style="margin:10px 0px;padding-left:4px;">短信操作快速上手</div>
        <div>
            <div class="title">1.报备签名</div>
            <div>签名使用公司简称、活动名称或者产品名称。确认之后点击提交审核。客服审核通过后可以使用。 “保存但不提交”是针对使用签名不确定的情况后期可以在签名管理里进行修改再提交<router-link to="/CreateSign" >
                  《立即报备》
                </router-link></div>
            <div><img src="../../../assets/images/guide1.png" alt="" width="75%" ></div>
        </div>
        <div>
            <div class="title">报备模板</div>
            <div>客户报备需要发送的短信模板，审核通过之后可以直接通过模板进行发送。<router-link :to="{path:'TemplateManagement'}" >《立即报备》</router-link></div>
            <div><img src="../../../assets/images/guide2.png" alt="" width="75%" ></div>
            
        </div>
        <div>
            <div class="title">2.自定义发送</div>
            <div>自定义短信内容发送，内容须自带签名，支持定时发送。<router-link to="/sendDetails" >《立即发送》</router-link></div>
            <div><img src="../../../assets/images/guide3.png" alt="" width="75%" ></div>
        </div>
        <div>
            <div class="title">模板发送</div>
            <div>通过已报备的短信模板进行发送，一次报备，永久发送，到达及时。<router-link to="/sendDetails" >《立即发送》</router-link></div>
            <div><img src="../../../assets/images/guide4.png" alt="" width="75%" ></div>
        </div>
         <div>
            <div class="title">个性化发送</div>
            <div>可以单次请求对多个号码进行发送内容。<router-link to="/PersonalizedDelivery" >《立即发送》</router-link></div>
            <div><img src="../../../assets/images/guide6.png" alt="" width="75%" ></div>
        </div>
        <div>
            <div class="title">3.统计中心</div>
            <div >可查看30天的下发明细以及数据导出。<router-link to="/ShortMessageRecording2" >《立即查看》</router-link></div>
            <div><img src="../../../assets/images/guide5.png" alt="" width="75%" ></div>
        </div>
    </div>
</template>

<script>
export default {
name:"guide",
data(){
    return{
    }
},
}
</script>

<style scoped>
    .title{
        font-size: 18px;
        font-weight: 800;
        color: #000;
        margin: 20px 0;
    }
    .tit-t{
         font-size: 14px;
        color: #000;
        font-weight: 800;
        margin: 10px 0;
    }
</style>