<template>
    <div style="widht:100%;">
        <!-- 终端 -->
       <!-- <index v-if="roleId =='13'||roleId == '14'"></index>  -->
       <Index  v-if=" roleId =='12' "></Index>
       <IndexOne v-if=" roleId =='22'|| roleId =='13'||roleId == '14' "></IndexOne>
       <!-- <IndexOne></IndexOne> -->
    </div>    
</template>

<script>
import Index from './components/index'
// import IndexOne from './components/indexOne'
import IndexOne from './components/homeOne'
// import IndexOline from './components/indexOline'
// import IndexShop from './components/indexShop'
import { mapState, mapMutations,mapActions } from "vuex";
export default {
    name: "home",
    components: {Index,IndexOne},
    computed:{
        ...mapState({  //比如'movies/hotMovies
            roleId:state=>state.roleId,
         })
    },
    created(){
        console.log();
    }
}
</script>