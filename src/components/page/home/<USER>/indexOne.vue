<template>
  <div class="shop">
    <div class="headers">
      你好，{{ userName }}!
      <span style="margin-left: 10px">{{ compName }}</span>
      <!-- <a href="">11111</a> -->
    </div>
    <div class="main">
      <div class="main-left">
        <div class="top_t">
          <div v-if="isShowBar != 0" class="left-top">
            <div
              v-if="certificate == 0"
              class="attestation"
              style="position: relative"
            >
              <div style="margin: 50px 0 0 200px">
                <img
                  style="margin-left: 30px"
                  src="../../../../assets/images/861.png"
                  alt=""
                />
                <div>企业资质未认证</div>
              </div>
              <div
                style="margin-left: 130px; font-size: 14px; margin-top: 10px"
              >
                <i style="color: orange" class="el-icon-warning"></i>
                
              </div>
              <div
                style="margin-left: 130px; font-size: 14px; margin-top: 10px"
              >
                <el-button
                  @click="goAttestation"
                  style="margin-left: 80px; font-size: 14px; margin-top: 10px"
                  >去认证</el-button
                >

                <router-link
                  to="/guide"
                  style="position: absolute; bottom: 10px; left: 10px"
                >
                  <img src="../../../../assets/images/yd.png" alt="" />
                </router-link>
                <a
                  style="position: absolute; bottom: 10px; right: 10px"
                  href="https://doc.zthysms.com"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img src="../../../../assets/images/jk.png" alt="" />
                </a>
                <!-- <a style="position: absolute;bottom:10px;left:10px" href="/guide">短信接口指南</a> -->
                <!-- <a style="position: absolute;bottom:10px;left:10px" href="/guide" rel="noopener noreferrer">短信操作快速上手</a> -->
                <!-- <a  style="position: absolute;bottom:10px;right:10px" href="https://doc.zthysms.com" target="_blank" rel="noopener noreferrer">短信接口指南</a> -->
              </div>
            </div>
            <div
              v-if="certificate == 1"
              class="attestation"
              style="position: relative"
            >
              <div style="margin: 50px 0 0 200px">
                <img
                  style="margin-left: 30px"
                  src="../../../../assets/images/861.png"
                  alt=""
                />
                <div>企业资质认证中</div>
              </div>
              <div
                style="margin-left: 130px; font-size: 14px; margin-top: 10px"
              >
                <el-button
                  @click="goAttestation"
                  style="margin-left: 80px; font-size: 14px; margin-top: 10px"
                  >查看进度</el-button
                >
                <router-link
                  to="/guide"
                  style="position: absolute; bottom: 10px; left: 10px"
                >
                  <img src="../../../../assets/images/yd.png" alt="" />
                </router-link>
                <a
                  style="position: absolute; bottom: 10px; right: 10px"
                  href="https://doc.zthysms.com"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img src="../../../../assets/images/jk.png" alt="" />
                </a>
              </div>
            </div>
            <div
              v-if="certificate == 2"
              class="attestation"
              style="position: relative"
            >
              <div class="certificate-title">
                <span style="padding-left: 10px">设置提醒</span>
              </div>
              <div style="display: flex">
                <div class="certificate">
                  <div style="text-align: center; margin-top: 5px">
                    设置余额提醒
                  </div>
                  <router-link to="/PersonalInformation">
                    <el-button style="margin: 10px 40px" type="primary" plain
                      >设置</el-button
                    >
                  </router-link>
                </div>
                <div class="certificate">
                  <div style="text-align: center; margin-top: 5px">
                    设置登录手机号
                  </div>
                  <router-link to="/LoginCellPhone">
                    <el-button style="margin: 10px 40px" type="primary" plain
                      >设置</el-button
                    >
                  </router-link>
                </div>
                <div class="certificate">
                  <div style="text-align: center; margin-top: 5px">
                    设置通知预警
                  </div>
                  <router-link to="/NotificationAlert">
                    <el-button style="margin: 10px 40px" type="primary" plain
                      >设置</el-button
                    >
                  </router-link>
                </div>
                <router-link
                  to="/guide"
                  style="position: absolute; bottom: 10px; left: 10px"
                >
                  <img src="../../../../assets/images/yd.png" alt="" />
                </router-link>
                <a
                  style="position: absolute; bottom: 10px; right: 10px"
                  href="https://doc.zthysms.com"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img src="../../../../assets/images/jk.png" alt="" />
                </a>
              </div>
            </div>
          </div>
          <div v-else style="background: #fff; padding: 10px 0">
            <div v-if="certificate == 0">
              <div style="margin-left: 110px">
                <img
                  style="margin-left: 30px"
                  src="../../../../assets/images/861.png"
                  alt=""
                />
                <div>企业资质未认证</div>
              </div>
              <div style="margin-left: 30px; font-size: 14px; margin-top: 10px">
                <i style="color: orange" class="el-icon-warning"></i>
                快去实名认证吧，开启您的真正使用之旅
              </div>
              <div
                style="margin-left: 100px; font-size: 14px; margin-top: 10px"
              >
                <el-button
                  @click="goAttestation"
                  style="margin-left: 30px; font-size: 14px; margin-top: 10px"
                  >去认证</el-button
                >
              </div>
            </div>
            <div v-if="certificate == 1">
              <div style="margin-left: 120px">
                <img
                  style="margin-left: 30px"
                  src="../../../../assets/images/861.png"
                  alt=""
                />
                <div>企业资质认证中</div>
              </div>
              <div
                style="margin-left: 100px; font-size: 14px; margin-top: 10px"
              >
                <el-button
                  @click="goAttestation"
                  style="margin-left: 30px; font-size: 14px; margin-top: 10px"
                  >查看进度</el-button
                >
              </div>
            </div>
            <div v-if="certificate == 2">
              <div class="certificate-title">
                <span style="padding-left: 10px">设置提醒</span>
              </div>
              <div>
                <div class="certificate">
                  <div style="text-align: center; margin-top: 5px">
                    设置余额提醒
                  </div>
                  <router-link to="/PersonalInformation">
                    <el-button style="margin: 10px 40px" type="primary" plain
                      >设置</el-button
                    >
                  </router-link>
                </div>
                <div class="certificate">
                  <div style="text-align: center; margin-top: 5px">
                    设置登录手机号
                  </div>
                  <router-link to="/LoginCellPhone">
                    <el-button style="margin: 10px 40px" type="primary" plain
                      >设置</el-button
                    >
                  </router-link>
                </div>
                <div class="certificate">
                  <div style="text-align: center; margin-top: 5px">
                    设置通知预警
                  </div>
                  <router-link to="/NotificationAlert">
                    <el-button style="margin: 10px 40px" type="primary" plain
                      >设置</el-button
                    >
                  </router-link>
                </div>
              </div>
            </div>
          </div>
          <div class="top_l" style="margin-top: 10px">
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#e7f6f3' }"
              border
              style="width: 100%"
            >
              <el-table-column prop="productName" label="产品名称">
              </el-table-column>
              <el-table-column label="当前剩余数量" width="">
                <template slot-scope="scope">
                  <div style="color: #f96702">
                    {{ scope.row.balance }}
                    <span v-if="scope.row.productName == '国际短信'">元</span>
                    <span v-else>条</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="divmCLass">
          <div class="home-title">发送短信</div>
          <div class="smsDply">
            <div class="home-item">
              <div class="div_border">
                <div>
                  <span class="span_number">1</span
                  ><span class="span_QM">创建签名</span>
                </div>
                <div class="div_content">
                  <span
                    >应运营商要求，每条短信必须附加短信签名，否则无法正常发送，可在此处申请属于您自己的短信签名</span
                  >
                </div>
                <div>
                  <router-link to="/CreateSign">
                    <el-button
                      style="position: absolute; bottom: 20px"
                      type="primary"
                      @click="logUrl('SignatureManagement')"
                      plain
                      >创建签名</el-button
                    >
                  </router-link>
                </div>
              </div>
            </div>
            <div class="home-item">
              <div class="div_border">
                <div>
                  <span class="span_number">2</span
                  ><span class="span_QM">创建模板</span>
                </div>
                <div class="div_content">
                  <span
                    >模板创建并审核通过后,发送短信时可免人工审核，在此可以快捷创建模板</span
                  >
                </div>
                <div>
                  <router-link to="/CreateTemplate?i=1">
                    <el-button
                      style="position: absolute; bottom: 20px"
                      type="primary"
                      @click="logUrl('TemplateManagement')"
                      plain
                      >创建模板</el-button
                    >
                  </router-link>
                </div>
              </div>
            </div>
            <div class="home-item">
              <div class="div_border">
                <div>
                  <span class="span_number">3</span
                  ><span class="span_QM">短信发送</span>
                </div>
                <div class="div_content">
                  <span>签名与模板均审核通过后，即可进行短信发送</span>
                </div>
                <div>
                  <router-link to="/sendDetails">
                    <el-button
                      style="position: absolute; bottom: 20px"
                      type="primary"
                      @click="logUrl('sendDetails')"
                      plain
                      >短信发送</el-button
                    >
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="left-bottom" v-if="roleId != 13">
          <div class="hot-shop">
            <div class="home-bottom-purple1-div">
              <div class="home-bottom-purple home-bottom-purple1">
                <div
                  style="
                    height: 36px;
                    border-bottom: 2px solid #e4e7ed;
                    line-height: 36px;
                    font-weight: bold;
                    display: flex;
                    justify-content: space-between;
                  "
                >
                  <span style="margin-left: 10px">热销套餐</span>
                  <a
                    style="margin-right: 10px; color: #aaa"
                    :href="API.herf"
                    target="_blank"
                    rel="noopener noreferrer"
                    >前往查看更多&nbsp;>
                  </a>
                </div>
                <div class="hotshop">
                  <div
                    class="item"
                    v-for="(item, index) in product"
                    :key="index"
                    @click="button(item)"
                  >
                    <div class="HOT-H">
                      <div class="hot-h">
                        <img
                          v-if="item.bestSell == 1"
                          src="../../../../assets/images/hot.png"
                          alt=""
                        />
                      </div>
                      <div class="img">
                        <img
                          v-if="item.simage"
                          :src="API.imgU + item.simage"
                          alt=""
                          style="width: 80px; height: 80px"
                        />

                        <img
                          v-else
                          src="../../../../assets/images/11.png"
                          alt=""
                        />
                      </div>
                      <div class="title" style="font-weight: 800">
                        {{ item.name }}
                      </div>
                      <div
                        class="text"
                        style="
                          margin-top: 10px;
                          display: -webkit-box;
                          -webkit-box-orient: vertical;
                          -webkit-line-clamp: 3;
                          overflow: hidden;
                        "
                      >
                        {{ item.shortDesc }}
                      </div>
                      <div class="count">
                        价格：<span>￥{{ item.realPrice }}</span>
                      </div>
                    </div>
                    <div class="button">
                      <button @click="button(item)">查看详情</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="main-right">
        <div class="table-h">
          <div>
            <div class="notice">
              <div
                style="
                  height: 36px;
                  border-bottom: 2px solid #e4e7ed;
                  line-height: 36px;
                  font-weight: bold;
                  display: flex;
                  justify-content: space-between;
                "
              >
                <span style="margin-left: 10px">会员管理</span>
                <span style="margin-right: 10px">
                  <a href="https://contact.zthysms.com" target="_blank" rel="noopener noreferrer">会员系统隆重上线，点击进入&nbsp;></a>
                  <!-- <router-link style="color: #aaa" to="/showMsg"
                    >会员系统隆重上线，点击进入&nbsp;></router-link
                  > -->
                </span>
              </div>
              <div style="width:100%">
                <!-- <el-carousel height="240px" style="padding: 10px">
                  <el-carousel-item v-for="item in 4" :key="item">
                    <a
                      href="https://contact.zthysms.com"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <h3 class="small">{{ item + "会员管理" }}</h3>
                    </a>
                  </el-carousel-item>
                </el-carousel> -->
                <a
                      href="https://contact.zthysms.com"
                      target="_blank"
                      rel="noopener noreferrer"
                    ><img style="width:340px;margin-left:60px"  src="../../../../assets/images/userVip.png" alt="">
                      <!-- <h3 class="small">{{ item + "会员管理" }}</h3> -->
                    </a>
                <!-- <a
                  href="http://contacs.zthysms.com:9527/#/Management/userList"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <img
                    src="../../../../assets/images/vip.jpg"
                    alt=""
                    width="100%"
                    height="200px"
                  />
                </a> -->
              </div>
            </div>
          </div>
        </div>
        <div class="table-h">
          <!-- <div class="certificate-title">
            <span style="padding-left: 10px">账户余额</span>
          </div> -->

          <div>
            <div class="notice">
              <div class="divCLass">
                <div class="home-bottom-purple1-div">
                  <div class="home-bottom-purple home-bottom-purple1">
                    <div
                      style="
                        height: 36px;
                        border-bottom: 2px solid #e4e7ed;
                        line-height: 36px;
                        font-weight: bold;
                        display: flex;
                        justify-content: space-between;
                      "
                    >
                      <span style="margin-left: 10px">消息</span>
                      <span style="margin-right: 10px">
                        <router-link style="color: #aaa" to="/showMsg"
                          >查看更多&nbsp;></router-link
                        >
                      </span>
                    </div>
                    <div
                      class="div_GG"
                      v-for="(item, index) in forGG"
                      :key="index"
                    >
                      <span class="span-radius"></span
                      ><router-link style="color: #aaa" to="/showMsg"
                        ><span class="span_GG" style="margin-right: 20px">{{
                          item.messageContent
                        }}</span></router-link
                      ><span
                        style="position: absolute; right: 10px; color: #aaa"
                        >{{ item.createTime }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <el-table
              :data="tableData"
              :header-cell-style="{ background: '#e7f6f3' }"
              border
              style="width: 100%"
            >
              <el-table-column prop="productName" label="产品名称">
              </el-table-column>
              <el-table-column label="当前剩余数量" width="">
                <template slot-scope="scope">
                  <div style="color: #f96702">
                    {{ scope.row.balance }}
                    <span v-if="scope.row.productName == '国际短信'">元</span>
                    <span v-else>条</span>
                  </div>
                </template>
              </el-table-column>
            </el-table> -->
          </div>
        </div>
        <div v-if="roleId != 13" class="right-b">
          <div class="certificate-title">
            <span style="padding-left: 10px">我的专属商务</span>
          </div>
          <div class="kf">
            <div class="simg">
              <img src="../../../../assets/images/kf43.png" alt="" />
            </div>
            <div style="line-height: 20px" class="sw">
              <div style="text-align: center; color: #b0b0b0; font-weight: 800">
                客服经理
              </div>
              <div
                style="
                  text-align: center;
                  color: #000;
                  font-weight: 800;
                  font-size: 16px;
                "
              >
                李薇
              </div>
            </div>

            <div style="line-height: 20px; margin-top: 10px">
              <div style="text-align: center; color: #b0b0b0; font-weight: 800">
                直线联系电话（同微信）
              </div>
              <div
                style="
                  text-align: center;
                  color: #000;
                  font-weight: 800;
                  font-size: 16px;
                "
              >
                15601838771
              </div>
            </div>
          </div>
          <div style="line-height: 20px; margin-top: 10px">
            <div style="text-align: center; color: #b0b0b0; font-weight: 800">
              QQ
            </div>
            <div
              style="
                text-align: center;
                color: #000;
                font-weight: 800;
                font-size: 16px;
              "
            >
              2880703368
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  data() {
    return {
      product: [],
      hostname: window.location.hostname,
      token: "",
      certificate: null,
      forGG: "",
      url: "",
      tableData: [],
      isShowBar: 0,
    };
  },
  methods: {
    //热销商品
    bestSell() {
      this.$api.get(this.API.shops + "shopgoods/bestSell", {}, (res) => {
        // console.log(res.data);
        this.product = res.data;
      });
    },
    versions() {
      //     var u = navigator.userAgent, app = navigator.appVersion;
      //     console.log(u,'u');
      //    var mobile = !!u.match(/AppleWebKit.*Mobile.*/)||!!u.match(/AppleWebKit/)
      //    console.log(mobile,'mobile');
      if (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i)) {
        // localStorage.setItem('h5Lg',"0");
        this.isShowBar = 0;
        // window.location.href = "mobile.html";
      } else {
        this.isShowBar = 1;
        //    localStorage.setItem('h5Lg',"1");
        // window.location.href = "pc.html";
      }
    },
    guide() {
      this.$router.push({ path: "/guide" });
    },
    button(item) {
      console.log(item, "item");
      var tempwindow = window.open("_blank");
      tempwindow.location =
        this.API.herf + "#/productDetail?goodsid=" + item.goodsNo;
      // this.url =
    },
    goAttestation() {
      this.$router.push({
        path: "/authentication",
      });
    },
    // 获取项目绝对路径
    ...mapActions([
      //比如'movies/getHotMovies
      "saveUrl",
    ]),
    logUrl(val) {
      let logUrl = {
        logUrl: val,
      };
      this.saveUrl(logUrl);
      window.sessionStorage.setItem("logUrl", val);
    },
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      userName: (state) => state.userName,
      compName: (state) => state.compName,
      roleId: (state) => state.roleId,
    }),
  },
  created() {
    // console.log(this.$route.params.token,'token');
    this.$api.get(this.API.cpus + "home/balance", {}, (res) => {
      console.log(res.data);
      this.tableData = res.data;
    });
    this.token = this.$route.params.token;
    this.bestSell();
    this.versions();
  },
  mounted() {
    // 公告
    this.$api.get(
      this.API.cpus + "consumerclientmessage/selectTopFive",
      {},
      (res) => {
        this.forGG = res.data;
      }
    );
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data);
        this.certificate = res.data.certificate;
        // this.userBalance=res.data.balanceList[0].num
        // this.userMMSbalance=res.data.balanceList[1].num
        // this.userRMSbalance=res.data.balanceList[2].num
      }
    );
  },
};
</script>

<style scoped>
@media screen and (min-width: 1200px) {
  .shop {
    width: 100%;
  }
  .headers {
    width: 100%;
    /* height: 50px; */
    background: #fff;
    line-height: 50px;
    margin-top: 10px;
    padding-left: 15px;
  }
  .main {
    width: 100%;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
  }
  .main-left {
    width: 50%;
  }
  .top_t {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
  }
  .left-top {
    /* width: 990px; */
    display: flex;
    margin-top: 10px;
    background: #fff;
  }
  .top_l {
    flex: 1;
    width: 300px;
    margin-top: 10px;
  }
  .left-bottom {
    width: 100%;
    background: #fff;
  }
  .attestation {
    width: 490px;
    height: 320px;
    background: #fff;
  }
  .notice {
    width: 490px;
    height: 300px;
    background: #fff;
    margin: 10px;
  }
  .nav {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ccc;
    font-weight: 800;
  }
  .divCLass {
    height: 253px;
    overflow: hidden;
    margin-bottom: 10px;
  }
  .smsDply {
    display: flex;
  }
  .div_GG {
    height: 35px;
    line-height: 35px;
    position: sticky;
  }
  .span-radius {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #16a589;
    display: inline-block;
    margin-right: 5px;
    position: absolute;
    top: 16px;
    margin-left: 5px;
  }
  .hotshop {
    width: 100%;
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
  }
  .item {
    width: 200px;
    height: 300px;
    margin: 15px 15px;
    border: 1px solid #bfbfbf;
    box-shadow: 0px 0px 5px 1px #aaa;
    border-radius: 5px;
    padding: 10px 6px;
    cursor: pointer;
  }
  .img {
    width: 100%;
    height: 100px;
    line-height: 100px;
    text-align: center;
    margin-top: 25px;
  }
  .title {
    width: 100%;
    text-align: center;
  }
  .text {
    /* margin-top: 10px; */
    padding: 0 10px;
    font-size: 14px;
    color: rgb(126, 126, 126);
  }
  .count {
    font-size: 14px;
    padding: 0 10px;
    margin: 15px 0;
    font-weight: 800;
  }
  .count span {
    color: red;
  }
  .price {
    font-weight: 800;
    font-size: 14px;
  }
  .price span {
    color: red;
  }
  b {
    color: rgb(25, 0, 255);
  }
  .button {
    width: 100%;
    margin-top: 15px;
  }
  .button button {
    width: 100%;
    height: 30px;
    border: none;
    outline: none;
    border-radius: 5px;
    cursor: pointer;
  }
  .span_GG {
    display: inline-block;
    white-space: nowrap;
    margin-left: 15px;
    overflow: hidden;
    width: 60%;
    text-overflow: ellipsis;
  }
  .HOT-H {
    position: relative;
  }
  .hot-h {
    position: absolute;
    top: 0;
    right: 0;
    margin-top: -10px;
    margin-right: -15px;
  }
  .main-right {
    flex: 1;
    margin-left: 30px;
    /* position: fixed;
  right: 150px; */
  }
  .divmCLass {
    height: 220px;
    overflow: hidden;
    background: #fff;
    margin: 10px 0;
  }
  .home-item {
    width: 310px;
    margin-left: 10px;
    position: relative;
  }
  .div_border {
    border: 1px solid rgb(238, 238, 238);
    height: 140px;
    padding: 15px;
    position: relative;
  }
  .span_number {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid;
    text-align: center;
    border-radius: 20px;
    font-weight: 600;
    font-family: monospace;
    line-height: 15px;
    color: #00a489;
  }
  .div_content {
    margin-top: 5px;
    font-size: 13px;
    line-height: 25px;
  }
  .span_QM {
    font-family: monospace;
    font-size: 13px;
    font-weight: 600;
    color: #00a489;
    margin-left: 5px;
  }
  .home-title {
    font-weight: bold;
    height: 30px;
    line-height: 30px;
    margin-left: 10px;
  }
  .certificate {
    width: 140px;
    height: 70px;
    background: #ebf7f5;
    margin-top: 50px;
    margin-left: 20px;
  }
  .certificate-title {
    height: 36px;
    line-height: 36px;
    font-weight: 800;
    border-bottom: 2px solid #e4e7ed;
  }
  .table-h {
    width: 500px;
    background: #fff;
    margin-top: 10px;
  }
  .right-b {
    width: 500px;
    height: 320px;
    background: #fff;
    margin-top: 20px;
  }
  .simg {
    margin-left: 200px;
    margin-top: 10px
      "
}
}
@media screen and (max-width: 1200px){
   .shop {
  width: 100%;
}
.headers {
  width: 100%;
  /* height: 50px; */
  font-size:12px ;
  background: #fff;
  /* line-height: 50px; */
  margin-top: 10px;
  padding-left: 5px;
  padding: 10px;
  /* display: none; */
}
.main {
  width: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.main-left {
  width: 100%;
}
.left-top {
  width: 100%;
  /* display: flex; */
  margin-top: 10px;
}
.left-bottom {
  width: 100%;
  background: #fff;
}
.attestation {
  width: 490px;
  height: 320px;
  background: #fff;
}
.notice {
  width: 100%;
  /* height: 300px; */
  background: #fff;
  margin-top:20px ;
  font-size: 12px;
}
.nav {
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ccc;
  font-weight: 800;
}
.divCLass {
  /* height: 253px; */
  overflow: hidden;
  margin-bottom: 10px;
  /* color: #aaa; */
}

.div_GG {
  height: 35px;
  line-height: 35px;
  position: sticky;
}
.span-radius {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #16a589;
  display: inline-block;
  margin-right: 5px;
  position: absolute;
  top: 16px;
  margin-left: 5px;
}
.hotshop {
  width: 100%;
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  font-size: 12px;
}
.item {
  width: 200px;
  height: 300px;
  margin: 15px 15px;
  border: 1px solid #bfbfbf;
  box-shadow: 0px 0px 5px 1px #aaa;
  border-radius: 5px;
  padding: 10px 6px;
  cursor: pointer;
}
.img {
  width: 100%;
  height: 100px;
  line-height: 100px;
  text-align: center;
  margin-top: 25px;
}
.title {
  width: 100%;
  text-align: center;
}
.text {
  /* margin-top: 10px; */
  padding: 0 10px;
  font-size: 14px;
  color: rgb(126, 126, 126);
}
.count {
  font-size: 14px;
  padding: 0 10px;
  margin: 15px 0;
  font-weight: 800;
}
.count span {
  color: red;
}
.price {
  font-weight: 800;
  font-size: 14px;
}
.price span {
  color: red;
}
b {
  color: rgb(25, 0, 255);
}
.button {
  width: 100%;
  margin-top: 15px;
}
.button button {
  width: 100%;
  height: 30px;
  border: none;
  outline: none;
  border-radius: 5px;
  cursor: pointer;
}
.span_GG {
  display: inline-block;
  white-space: nowrap;
  margin-left: 15px;
  overflow: hidden;
  width: 50%;
  text-overflow: ellipsis;
}
.HOT-H {
  position: relative;
}
.hot-h {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: -10px;
  margin-right: -15px;
}
.main-right {
  flex: 1;
  /* margin-left: 30px; */
  /* position: fixed;
  right: 150px; */
}
.divmCLass {
  /* height: 220px; */
  overflow: hidden;
  background: #fff;
  margin: 10px 0;
}
.home-item {
  width: 100%;
  margin-left: 10px;
  position: relative;
}
.div_border {
  border: 1px solid rgb(238, 238, 238);
  height: 140px;
  padding: 15px;
  position: relative;
}
.span_number {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid;
  text-align: center;
  border-radius: 20px;
  font-weight: 600;
  font-family: monospace;
  line-height: 15px;
  color: #00a489;
}
.div_content {
  margin-top: 5px;
  font-size: 13px;
  line-height: 25px;
}
.span_QM {
  font-family: monospace;
  font-size: 13px;
  font-weight: 600;
  color: #00a489;
  margin-left: 5px;
}
.home-title {
  font-weight: bold;
  height: 30px;
  line-height: 30px;
  margin-left: 10px;
}
.certificate {
  width: 140px;
  height: 70px;
  background: #ebf7f5;
  margin-top: 10px;
  margin-left: 100px;
  padding: 10px 0;
}
.certificate-title {
  height: 36px;
  line-height: 36px;
  font-weight: 800;
  border-bottom: 2px solid #e4e7ed;
}
.table-h {
  width: 100%;
  background: #fff;
  margin-top: 10px;
}
.right-b {
  width: 100%;
  height: 320px;
  background: #fff;
  margin-top: 20px;
}
.simg{
  margin-left: 110px; margin-top: 10px";
    /* position: absolute;
  left: 50%;
  margin-left: -50px;
  margin-bottom: 50px;" */
  }

  /* .kf{
  position: relative;
} */
}
</style>
<style>
.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}
</style>