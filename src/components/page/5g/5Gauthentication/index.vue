<template>
  <div class="smrz">
    <div class="smrz-h">
      <div style="font-size: 20px; font-weight: 800; color: #000">
        5G消息注册
      </div>
      <!-- <div class="smrz-x">
        根据《中华人民共和国网络安全法》及相关法律的规定，网络运营者为用户办理网络接入、域名注册服务，办理固定电话、移动电话等入网手续，或者为用户提供信息发布、即时通讯等服务，在与用户签订协议或者确认提供服务时，应当要求用户提供真实身份信息。用户不提供真实身份信息的，网络运营者不得为其提供相关服务。
        <p>在您使用助通服务前，需要进行实名认证。</p>
        <p>
          认证时间：周一至周六9:00--22:00（晚22点后提交会在次日9点后审核认证），认证时长20分钟内；周日提交认证则会延期至周一工作日。
        </p>
        <p>紧急联系电话：李薇13248296554。</p>
      </div> -->
    </div>
    <div class="smrz-mian">
      <div class="smrz-m-h"></div>
      <div class="smrz-mean">
        <div class="mean-h">
          <!-- <span>认证信息</span> -->
          <el-button
            v-if="
              registerFrom.customerStatus == 0 ||
              registerFrom.customerStatus == 30
            "
            type="text"
            style="margin-right: 100px; font-size: 14px; color: #16a589"
            @click="handleEdit('registerFrom')"
            >修改</el-button
          >
        </div>
        <div v-if="regFlag">
          <div>
            <!-- <div class="mean-item">
              <span>状态</span>
              <span style="color: red">未认证</span>
            </div> -->
            <el-form
              :model="registerFrom"
              label-width="100px"
              :rules="rules"
              ref="registerFrom"
              class="demo-ruleForm"
            >
            <div v-if="registerFrom.customerStatus">
              <div class="mean-item">
                
                  <span>状态</span>
                  <span v-if="registerFrom.customerStatus == 0">待审核</span>
                  <span
                    v-if="registerFrom.customerStatus == 5"
                    style="color: #f56c6c"
                    >初审中</span
                  >
                  <span
                    v-if="registerFrom.customerStatus == 10"
                    style="color: #f56c6c"
                    >运营商审核</span
                  >
                  <span
                    v-if="registerFrom.customerStatus == 20"
                    style="color: #f56c6c"
                    >审核通过</span
                  >
                  <span
                    v-if="registerFrom.customerStatus == 30"
                    style="color: #16a589"
                    >审核失败</span
                  >
                </div>
                
              </div>
              <div v-else>
                   <div class="mean-item">
                  <span>状态</span>
                  <span>待审核</span>
                  </div>
                </div>
              <div class="mean-item">
                <el-form-item label="客户名称" prop="customerName">
                  <el-input
                    v-model="registerFrom.customerName"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="mean-item">
                <el-form-item label="客户联系人" prop="customerContactPerson">
                  <el-input
                    v-model="registerFrom.customerContactPerson"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="mean-item">
                <el-form-item label="联系人电话" prop="contactPersonPhone">
                  <el-input
                    v-model="registerFrom.contactPersonPhone"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="mean-item">
                <el-form-item label="联系人邮箱" prop="contactPersonEmail">
                  <el-input
                    v-model="registerFrom.contactPersonEmail"
                  ></el-input>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div v-else>
          <div>
            <el-form
              :model="registerFrom"
              label-width="100px"
              :rules="rules"
              ref="registerFrom"
              class="demo-ruleForm"
            >
              <div class="mean-item">
                <span>状态</span>
                <span v-if="registerFrom.customerStatus == 0">待审核</span>
                <span
                  v-if="registerFrom.customerStatus == 5"
                  style="color: #f56c6c"
                  >初审中</span
                >
                <span
                  v-if="registerFrom.customerStatus == 10"
                  style="color: #f56c6c"
                  >运营商审核</span
                >
                <span
                  v-if="registerFrom.customerStatus == 20"
                  style="color: #f56c6c"
                  >审核通过</span
                >
                <span
                  v-if="registerFrom.customerStatus == 30"
                  style="color: #16a589"
                  >审核失败</span
                >
              </div>
              <div class="mean-item">
                <el-form-item label="客户名称" prop="customerName">
                  <span v-if="editFlag">{{ registerFrom.customerName }}</span>
                  <el-input
                    v-if="!editFlag"
                    v-model="registerFrom.customerName"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="mean-item">
                <el-form-item label="客户联系人" prop="customerContactPerson">
                  <span v-if="editFlag">{{
                    registerFrom.customerContactPerson
                  }}</span>
                  <el-input
                    v-if="!editFlag"
                    v-model="registerFrom.customerContactPerson"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="mean-item">
                <el-form-item label="联系人电话" prop="contactPersonPhone">
                  <span v-if="editFlag">{{
                    registerFrom.contactPersonPhone
                  }}</span>
                  <el-input
                    v-if="!editFlag"
                    v-model="registerFrom.contactPersonPhone"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="mean-item">
                <el-form-item label="联系人邮箱" prop="contactPersonEmail">
                  <span v-if="editFlag">{{
                    registerFrom.contactPersonEmail
                  }}</span>
                  <el-input
                    v-if="!editFlag"
                    v-model="registerFrom.contactPersonEmail"
                  ></el-input>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div style="padding: 20px">
          <!-- <el-button
            v-if="
              regFlag ||
              registerFrom.customerStatus == 0 ||
              registerFrom.customerStatus == 30
            "
            @click="register5g('registerFrom')"
            type="primary"
            >保存</el-button
          > -->

          <el-button
            v-if="
              regFlag ||
              registerFrom.customerStatus == 0 ||
              registerFrom.customerStatus == 30
            "
            @click="audit"
            type="primary"
            >提交审核</el-button
          >
        </div>
      </div>
    </div>
    <!-- <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="handleClose"
    >
      <el-form
        :model="registerFrom"
        :inline="true"
        label-width="150px"
        :rules="rules"
        ref="registerFrom"
        class="demo-ruleForm"
      >
        <el-form-item label="客户名称" prop="customerName">
          <el-input readonly  v-model="registerFrom.customerName"></el-input>
        </el-form-item>
        <el-form-item label="客户联系人" prop="customerContactPerson">
          <el-input readonly v-model="registerFrom.customerContactPerson"></el-input>
        </el-form-item>
        <el-form-item label="联系人电话" prop="contactPersonPhone">
          <el-input readonly v-model="registerFrom.contactPersonPhone"></el-input>
        </el-form-item>
        <el-form-item label="联系人邮箱" prop="contactPersonEmail">
          <el-input readonly v-model="registerFrom.contactPersonEmail"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="register5g('registerFrom')"
          >确 定</el-button
        >
      </span>
    </el-dialog> -->
  </div>
</template>
<script>
export default {
  name: "5gIndex",
  //   components:{
  //     smrz
  //   },
  data() {
    return {
      title: "",
      regFlag: false,
      dialogVisible: false,
      editFlag: true,
      registerFrom: {
        customerName: "", //客户名称
        customerContactPerson: "", //客户联系人
        contactPersonPhone: "", //联系人电话
        contactPersonEmail: "", //联系人邮箱
      },
      rules: {
        customerName: [
          { required: true, message: "请输入客户名称", trigger: "change" },
        ],
        customerContactPerson: [
          { required: true, message: "请输入客户联系人", trigger: "change" },
        ],
        contactPersonPhone: [
          { required: true, message: "请输入联系人电话", trigger: "change" },
        ],
        contactPersonEmail: [
          { required: true, message: "请输入联系人邮箱", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.init();
  },

  methods: {
    handSmrz() {
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    register5g(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.editFlag) {
            this.$api.post(
              this.API.fiveWeb + "/customer",
              this.registerFrom,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.init()
                  this.editFlag = true
                //   this.getchatList();
                //   this.dialogVisible = false;
                }
              }
            );
          } else {
            this.$api.put(
              this.API.fiveWeb + "/customer",
              this.registerFrom,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.init()
                  this.editFlag = true
                //   this.getchatList();
                //   this.dialogVisible = false;
                }
              }
            );
          }
        }
      });
    },
    audit(){
        this.$confirms.confirmation(
                "post",
                "正在提交审核中",
                this.API.fiveWeb + "/customer/toPreAudit",
                this.registerFrom,
                (res) => {
                    this.init()
                //     this.$message({
                //     message: ress.msg,
                //     type: "success",
                //   });
                // this.getChallDate();
                }
            );
    },
    handleEdit(formop) {
      this.editFlag = false;
      //   this.$refs[formop].validate((valid) => {
      //     if (valid) {
      //       this.$api.put(
      //         this.API.fiveWeb + "/customer",
      //         this.registerFrom,
      //         (res) => {
      //           if (res.code == 200) {
      //             this.$message({
      //               message: res.msg,
      //               type: "success",
      //             });
      //             this.init();
      //           }
      //         }
      //       );
      //     }
      //   });
    },
    init() {
      this.$api.get(this.API.fiveWeb + "/customer", {}, (res) => {
        if (res.code == 400) {
          this.regFlag = true;
        } else {
          this.regFlag = false;
          if (res.data) {
            this.registerFrom = res.data;
          }
        }
        // console.log(res);
      });
    },
  },

  mounted() {},
  computed: {},

  watch: {
    smrzObj(val, newval) {
      // console.log(val,'val');
      // console.log(newval,'new');
      // this.smrzObj = val
    },
  },
};
</script>
<style scoped>
@media screen and (min-width: 1200px) {
  .lega_authentication {
    display: flex;
    justify-content: space-around;
  }
  .lega {
    width: 300px;
    height: 180px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 5px 1px #eee;
    padding: 20px;
    position: relative;
  }
  .lega_title {
    font-weight: 800;
    font-size: 18px;
    color: #000;
  }
  .lega_content {
    /* height: 100px; */
    margin-top: 15px;
    line-height: 23px;
  }
  .lega_btn {
    position: absolute;
    bottom: 20px;
    left: 50%;
    margin-left: -60px;
  }
  .smrz {
    width: 100%;
    background: #fff;
  }
  .smrz-h {
    width: 100%;
    padding: 20px;
    background: #fff2d1;
  }
  .smrz-mian {
    width: 100%;
    background: #fff2d1;
  }
  .smrn-h-t {
    margin-top: 10px;
    width: 970px;
    height: 30px;
    border-bottom: 1px solid #ccc;
  }
  .smrz-m-h {
    width: 400px;
    margin-left: 20px;
  }
  .smrz-p {
    margin-left: 15px;
    font-weight: 800;
  }
  .smrz-x {
    background: #fff2d1;
    color: #906e12;
    border-radius: 10px;
    padding: 15px;
    line-height: 25px;
    width: 600px;
    margin-top: 15px;
  }
  .smrz-mean {
    width: 500px;
    margin-left: 15px;
  }
  .mean-h {
    width: 100%;
    height: 50px;
    line-height: 50px;
    display: flex;
    justify-content: space-between;
  }
  .mean-h span {
    margin-left: 10px;
    color: #1e7cfc;
  }
  .mean-item {
    width: 80%;
    height: 30px;
    padding: 10px 0;
    /* margin-left: 30px; */
    border-bottom: 1px solid #ccc;
    line-height: 33px;
    display: flex;
    justify-content: space-between;
    /* margin-top: 5px; */
  }
  .btn {
    margin: 20px;
  }
}
@media screen and (max-width: 1200px) {
  /* .lega_authentication{
  display: flex;
  justify-content: space-around;
} */
  .lega {
    width: 100%;
    height: 170px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 5px 1px #eee;
    padding: 5px;
    margin: 10px 0;
    position: relative;
  }
  .lega_title {
    font-weight: 800;
    font-size: 14px;
    color: #000;
  }
  .lega_content {
    /* height: 100px; */
    margin-top: 15px;
    line-height: 23px;
  }
  .lega_btn {
    position: absolute;
    bottom: 10px;
    left: 50%;
    margin-left: -60px;
    /* margin-top: 10px; */
    /* margin-left: 50%; */
  }
  .smrz {
    width: 100%;
    background: #fff;
  }
  .smrz-h {
    width: 90%;
    padding: 0 10px;
  }
  .smrz-mian {
    width: 100%;
  }
  .smrz-m-h {
    width: 100%;
    margin-left: 10px;
  }
  .smrz-p {
    margin-left: 15px;
    font-weight: 800;
  }
  .smrz-x {
    background: #fff2d1;
    color: #906e12;
    border-radius: 10px;
    padding: 10px;
    line-height: 25px;
    width: 100%;
    margin-top: 15px;
    font-size: 12px;
  }
  .smrz-mean {
    width: 100%;
    margin-left: 10px;
  }
  .mean-h {
    width: 100%;
    height: 50px;
    line-height: 50px;
  }
  .mean-h span {
    margin-left: 20px;
    color: #1e7cfc;
  }
  .mean-item {
    width: 80%;
    height: 30px;
    margin-left: 30px;
    border-bottom: 1px solid #ccc;
    line-height: 33px;
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
  }
  .btn {
    margin: 20px;
  }
}
/* .btn button {
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 5px;
  color: #1e7cfc;
  cursor: pointer;
  outline: none;
} */
</style>