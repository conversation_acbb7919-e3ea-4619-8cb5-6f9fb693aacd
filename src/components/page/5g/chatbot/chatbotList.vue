<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 统计信息区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <div class="page-info">
                  <!-- <i class="el-icon-lx-emoji"></i> -->
                  <span class="page-title">ChatBot管理</span>
                </div>
                <el-button
                  icon="el-icon-plus"
                  class="action-btn primary"
                  @click="chatbotAdd('ruleForm')"
                  v-permission
                >
                  新增
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">第 {{ tabelAlllist.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form 
                ref="ruleForm"
                :model="ruleForm"
                class="advanced-search-form" 
                inline 
                size="small">
                <div class="search-row">
                  <el-form-item label="ChatBot名称" prop="chatbotName" class="search-item">
                    <el-input
                      v-model="ruleForm.chatbotName"
                      placeholder="请输入ChatBot名称"
                      class="search-input"
                      clearable>
                    </el-input>
                  </el-form-item>
                  
                  <el-form-item label="企业名称" prop="customerIdArr" class="search-item">
                    <el-select
                      v-model="ruleForm.customerIdArr"
                      placeholder="请选择企业"
                      class="search-select"
                      multiple
                      style="width: 300px;"
                      clearable>
                      <el-option 
                        v-for="item in customerList" 
                        :key="item.customerId" 
                        :label="item.customerName" 
                        :value="item.customerId">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item class="search-buttons">
                    <el-button 
                      @click="submitForm('ruleForm')" 
                      class="search-btn primary" 
                      icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button 
                      @click="resetForm('ruleForm')" 
                      class="search-btn">
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- ChatBot列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">ChatBot列表</h3>
            <span class="table-count">共 {{ tableDataObj.total }} 条记录</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)"
              ref="multipleTable"
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无ChatBot数据">
              
              <el-table-column label="Chatbot头像" width="120" align="center">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span
                      style="cursor: pointer"
                      v-if="scope.row.availableChannelCount > 0"
                      @click="sendMessage(scope.row)"
                    >
                      <img :src="scope.row.logoUrl" alt="" width="40px" style="border-radius: 4px;" />
                    </span>
                    <span v-else>
                      <img :src="scope.row.logoUrl" alt="" width="40px" style="border-radius: 4px;" />
                    </span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="Chatbot名称" min-width="180">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span
                      v-if="scope.row.availableChannelCount > 0"
                      class="send_msg"
                      @click="sendMessage(scope.row)"
                      style="color: #409eff; cursor: pointer; font-weight: 500;"
                      >{{ scope.row.chatbotName }}</span
                    >
                    <span v-else style="font-weight: 500;">{{ scope.row.chatbotName }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="ChatbotId" min-width="180">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span style="font-family: monospace; color: #666;">{{ scope.row.chatbotId }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="签名" min-width="150">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span>{{ scope.row.autoGraph || '-' }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="描述信息" min-width="300">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <Tooltip v-if="scope.row.description" :content="scope.row.description" className="wrapper-text" effect="light">
                    </Tooltip>
                    <span v-else class="no-data">-</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="状态" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag 
                    :type="scope.row.status == 30 ? 'success' : (scope.row.status == 20 ? 'danger' : (scope.row.status == 5 ? 'warning' : 'info'))" 
                    size="small">
                    <span v-if="scope.row.status == 0">待提交</span>
                    <span v-if="scope.row.status == 5">审核中</span>
                    <span v-if="scope.row.status == 20">审核失败</span>
                    <span v-if="scope.row.status == 30">审核通过</span>
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column label="原因" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <Tooltip v-if="scope.row.auditReason" :content="scope.row.auditReason" className="wrapper-text" effect="light">
                    </Tooltip>
                    <span v-else class="no-data">-</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="创建时间" min-width="160">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <i class="el-icon-time" style="margin-right: 4px; color: #999;"></i>
                    <span>{{ moment(scope.row.createdTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="180" fixed="right">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-button
                      v-if="
                        scope.row.status == 0 ||
                        scope.row.status == 20 ||
                        scope.row.status == 30
                      "
                      v-permission
                      type="text"
                      class="action-btn-small edit"
                      @click="edit(scope.row)"
                      ><i class="el-icon-edit"></i> 编辑</el-button
                    >
                    <el-button
                      v-if="scope.row.availableChannelCount > 0"
                      type="text"
                      class="action-btn-small info"
                      @click="sendMessage(scope.row)"
                      ><i class="el-icon-position"></i> 消息发送</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div class="pagination-section">
            <el-pagination
              class="simple-pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框保持不变 -->
    <el-dialog
      title="实名认证"
      :visible.sync="dialogSmrzFlag"
      width="30%"
      center
    >
      <span
        >尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
      </span>
    </el-dialog>
    
    <el-dialog title="5G消息" :visible.sync="messageFlag" width="30%" center>
      <p style="line-height: 30px">
        &nbsp;&nbsp;&nbsp;&nbsp;5G消息云平台是基于5G消息为基础构建的开放生态平台，在兼顾传统短信服务能力的同时，围绕5G消息丰富的展现形式，动态的场景设定，以及多元的智慧能力融合，形成了丰富的5G消息一站式解决方案，可帮助短信服务商快速转型5G消息服务商;5G消息云平台可以为合作伙伴提供强大的5G消息的内容管理及运营能力，通过平台提供的素材管理、模板管理、场景管理等等服务，让客户仅需配置就能够立即使用5G消息;客户还可基于5G消息的开发者平台，使用助通提供的更多的SaaS增值服务，帮助客户构建一站式业务体验。
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="message5g">同意</el-button>
        <el-button type="danger" @click="messageFlag = false">不同意</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Tooltip from "@/components/publicComponents/tooltip";
import { mapState } from "vuex";
export default {
  name: "ChatBotManage",
  components: {
    Tooltip
  },
  data() {
    return {
      dialogSmrzFlag: false,
      messageFlag: false,
      gFlag: false,
      flag: false,
      name: "",
      ruleForm: {
        ltStatus: "",
        ydStatus: "",
        dxStatus: "",
        userId: "",
        customerIdArr:[],
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        ltStatus: "",
        ydStatus: "",
        dxStatus: "",
        userId: "",
        customerIdArr:[],
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      customerList: [],
    };
  },
  created() {
    this.ruleForm.userId = this.userId;
    this.tabelAlllist.userId = this.userId;
    this.getFindCustomerByUser();
    this.getchatList();
    this.getDomain();
    this.getClientInfo();
  },
  computed: {
    ...mapState({
      userId: (state) => state.userId,
    }),
  },
  methods: {
    goSmrz() {
      this.dialogSmrzFlag = false;
      this.$router.push("/authentication");
    },
    //判断是否认证以及同意开启5G
    getClientInfo() {
      this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
        (res) => {
          // console.log(res.data);
          if (res.data.certificate == 0) {
            this.flag = false;
            this.dialogSmrzFlag = true;
          } else {
            this.flag = true;
            this.register();
          }
        }
      );
    },
    //开启5G
    register() {
      this.$api.post(this.API.fiveWeb + "/customer/isRegister", {}, (res) => {
        if (res.code == 200 && res.data) {
          this.messageFlag = false;
          this.gFlag = true;
        } else {
          this.messageFlag = true;
          this.gFlag = false;
        }
      });
    },
    message5g() {
      this.$api.post(this.API.fiveWeb + "/customer/register", {}, (res) => {
        if (res.code == 200) {
          this.register();
        } else {
          this.$message.error(res.msg);
          this.messageFlag = false;
        }
        // this.messageFlag = false;
      });
    },
    getchatList() {
      this.tableDataObj.loading2 = true;
      // this.tabelAlllist.customerIdArr[0] = this.customerList[0].customerId
      this.$api.post(
        this.API.fiveWeb + "/chatbot/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.loading2 = false;
          // console.log(res, "res");
        }
      );
    },
    //根据用户查找对应的5G企业
    getFindCustomerByUser() {
      this.$api.post(
        this.API.fiveWeb + "/customer/findCustomerByUser",
        {},
        (res) => {
          if (res.code == 200) {
            this.customerList = res.data;
            this.ruleForm.customerIdArr[0] = this.customerList[0].customerId
            Object.assign(this.tabelAlllist, this.ruleForm);
            this.getchatList()
          } else {
            this.$message({
              type: "error",
              message: res.msg
            });
          }
        }
      );
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size;
      this.getchatList();
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage;
      this.getchatList();
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm);
      this.getchatList();
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
      this.ruleForm.customerIdArr[0] = this.customerList[0].customerId
      Object.assign(this.tabelAlllist, this.ruleForm);
      this.getchatList();
    },
    chatbotAdd() {
      if (this.flag) {
        if (this.gFlag) {
          // console.log(this.gFlag);
          this.$router.push({
            path: "/chatBot",
          });
        } else {
          this.messageFlag = true;
        }
      } else {
        this.dialogSmrzFlag = true;
      }
    },
    //删除
    delChat(row) {
      this.$confirms.confirmation(
        "delete",
        "确认删除该Chatbot？",
        this.API.fiveWeb + "/chatbot/" + row.chatbotId,
        {},
        (res) => {
          this.getchatList();
        }
      );
    },
    submitAudit(row) {
      this.$confirms.confirmation(
        "post",
        "提交审核！",
        this.API.fiveWeb + "/chatbot/toAudit",
        {
          chatbotId: row.chatbotId,
        },
        (res) => {
          this.getchatList();
        }
      );
    },
    //注销
    logout(row) {
      this.$confirms.confirmation(
        "get",
        "确认注销该Chatbot？注销后，数据将被全部清除",
        this.API.fiveWeb + "/chatbot/cancel" + row.chatbotId,
        {},
        (res) => {
          this.getchatList();
        }
      );
    },
    //跳转5G平台
    sendMessage(row) {
      let d = new Date();
      d.setTime(d.getTime() + 1000 * 60 * 240);
      let expires = "expires=" + d.toUTCString();
      document.cookie =
        "charbotId=" +
        row.chatbotId +
        ";path=/;domain=." +
        this.name +
        ";expires=" +
        expires;+"secure";

      var tempwindow = window.open("_blank");
      tempwindow.location = this.API.fiveGHerf;
    },
    //获取域名
    getDomain() {
      var hostname = window.location.hostname;
      var name = hostname.split(".");
      this.name = name[name.length - 2] + "." + name[name.length - 1];
    },
    edit(row) {
      this.$router.push({
        path: "/chatBot",
        query: {
          chatbotId: row.chatbotId,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/template-common.less';

/* 组件特有样式 */
.no-data {
  color: #999;
}

.send_msg {
  cursor: pointer;
  color: #409eff;
  font-weight: 500;
  transition: color 0.2s ease;
}

.send_msg:hover {
  color: #66b1ff;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
}

.page-info i {
  font-size: 18px;
  color: #409eff;
}

.page-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}
</style>