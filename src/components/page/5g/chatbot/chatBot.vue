<template>
  <div class="modern-signature-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <!-- <i class="el-icon-lx-emoji"></i> -->
            {{ statusOf }}
          </h1>
        </div>
        <div class="header-right">
          <el-tag :type="id ? 'warning' : 'success'" size="medium">
            {{ id ? '编辑模式' : '新建ChatBot' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <el-row class="chatbot-layout" :gutter="20">
          <el-col :span="15" class="form-section">
            <!-- ChatBot配置表单卡片 -->
            <el-card shadow="hover" class="form-card chatbot-config-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-setting"></i>
                  ChatBot配置
                </span>
              </div>

              <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="130px" class="modern-form">
                <!-- 基础信息分组 -->
                <div class="form-group">
                  <h3 class="group-title">
                    <i class="el-icon-info"></i>
                    基础信息
                  </h3>

                  <el-form-item label="企业名称" prop="customerId" class="form-item-modern">
                    <el-select v-model="ruleForm.customerId" placeholder="请选择企业" size="large" class="modern-select"
                      clearable style="width: 100%;">
                      <el-option v-for="item in customerList" :key="item.customerId" :label="item.customerName"
                        :value="item.customerId">
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="ChatBot名称" prop="chatbotName" class="form-item-modern">
                    <el-input v-model="ruleForm.chatbotName" :maxlength="20" show-word-limit placeholder="请输入ChatBot名称"
                      size="large" class="modern-input">
                      <i slot="prefix" class="el-icon-edit-outline"></i>
                    </el-input>
                    <div class="field-tip">
                      <i class="el-icon-info"></i>
                      针对具备5G消息能力的终端，在终端的消息列表及聊天页面顶端展示
                    </div>
                  </el-form-item>

                  <el-form-item label="ChatBot头像" prop="logoUrl" class="form-item-modern">
                    <div class="avatar-upload-section">
                      <div v-if="!logoFlag" class="avatar-preview">
                        <img :src="ruleForm.logoUrl" alt="ChatBot头像" class="avatar-image" />
                        <el-button v-permission icon="el-icon-plus" type="primary" size="small" class="change-avatar-btn"
                          @click="Remove">
                          更换头像
                        </el-button>
                      </div>
                      <div v-else class="avatar-upload">
                        <el-upload v-permission :action="action" :headers="token" list-type="picture-card" :limit="1"
                          :file-list="fileList" :on-preview="handlePictureCardPreview"
                          :before-upload="beforeAvatarUpload" :on-remove="handleRemove" :on-success="handleSuccess"
                          class="modern-upload">
                          <i class="el-icon-plus"></i>
                        </el-upload>
                        <div class="upload-tips">
                          <el-alert title="图片要求" type="info" :closable="false" show-icon>
                            <template slot="default">
                              <div class="tip-item">图片尺寸：400*400</div>
                              <div class="tip-item">图片大小：不能超过50k</div>
                            </template>
                          </el-alert>
                        </div>
                      </div>
                    </div>
                  </el-form-item>

                  <el-form-item label="签名" prop="autoGraph" class="form-item-modern">
                    <el-input v-model="ruleForm.autoGraph" :maxlength="20" show-word-limit placeholder="不能超过20个字符"
                      size="large" class="modern-input">
                      <i slot="prefix" class="el-icon-edit"></i>
                    </el-input>
                    <div class="field-tip">
                      <i class="el-icon-info"></i>
                      针对回落5G行业消息（标准版）的终端，在每条消息内容前携带此内容
                    </div>
                  </el-form-item>
                </div>

                <!-- 联系信息分组 -->
                <div class="form-group">
                  <h3 class="group-title">
                    <i class="el-icon-phone"></i>
                    联系信息
                  </h3>

                  <el-form-item label="邮箱" prop="email" class="form-item-modern">
                    <el-input v-model="ruleForm.email" :maxlength="50" show-word-limit placeholder="不能超过50个字符"
                      size="large" class="modern-input">
                      <i slot="prefix" class="el-icon-message"></i>
                    </el-input>
                  </el-form-item>

                  <el-form-item label="服务电话" prop="callbackPhone" class="form-item-modern">
                    <el-input v-model="ruleForm.callbackPhone" :maxlength="21" show-word-limit placeholder="请输入服务电话"
                      size="large" class="modern-input">
                      <i slot="prefix" class="el-icon-phone"></i>
                    </el-input>
                  </el-form-item>
                </div>

                <!-- 地址信息分组 -->
                <div class="form-group">
                  <h3 class="group-title">
                    <i class="el-icon-location"></i>
                    地址信息
                  </h3>

                  <el-form-item label="主页地址" prop="website" class="form-item-modern">
                    <el-input v-model="ruleForm.website" placeholder="请输入chatbot主页地址" size="large" class="modern-input">
                      <i slot="prefix" class="el-icon-link"></i>
                    </el-input>
                  </el-form-item>

                  <el-form-item label="办公地址" prop="address" class="form-item-modern">
                    <el-input v-model="ruleForm.address" placeholder="请输入chatbot办公地址" size="large" class="modern-input">
                      <i slot="prefix" class="el-icon-location-outline"></i>
                    </el-input>
                  </el-form-item>

                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="经度" prop="longitude" class="form-item-modern">
                        <el-input v-model="ruleForm.longitude" placeholder="请输入经度" size="large" class="modern-input">
                          <i slot="prefix" class="el-icon-coordinate"></i>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="纬度" prop="latitude" class="form-item-modern">
                        <el-input v-model="ruleForm.latitude" placeholder="请输入纬度" size="large" class="modern-input">
                          <i slot="prefix" class="el-icon-coordinate"></i>
                        </el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="field-tip">
                    <i class="el-icon-info"></i>
                    请到相关网站查询您公司所在经纬度，便于支持类似"附近"功能
                  </div>
                </div>

                <!-- 详细信息分组 -->
                <div class="form-group">
                  <h3 class="group-title">
                    <i class="el-icon-document"></i>
                    详细信息
                  </h3>

                  <el-form-item label="服务条款" prop="terms" class="form-item-modern">
                    <el-input v-model="ruleForm.terms" placeholder="请输入服务条款" size="large" class="modern-input">
                      <i slot="prefix" class="el-icon-document-copy"></i>
                    </el-input>
                  </el-form-item>

                  <el-form-item label="服务描述" prop="description" class="form-item-modern">
                    <el-input v-model="ruleForm.description" :maxlength="166" show-word-limit type="textarea"
                      placeholder="请输入服务描述" :rows="4" class="modern-textarea">
                    </el-input>
                  </el-form-item>

                  <el-form-item label="附件上传" prop="attachment" class="form-item-modern">
                    <el-upload v-permission :action="action" :headers="token" :limit="1" :file-list="attachmentList"
                      :before-upload="beforeAvatarUploadAtt" :on-remove="handleRemoveAtt" :on-success="handleSuccessAtt"
                      class="modern-file-upload" multiple>
                      <el-button size="large" type="primary" icon="el-icon-upload">
                        点击上传
                      </el-button>
                    </el-upload>
                    <div class="upload-tips">
                      <el-alert title="文件要求" type="info" :closable="false" show-icon>
                        <template slot="default">
                          支持pdf、doc、jpg、jpeg、gif、docx、rar、zip格式，且不超过5MB
                        </template>
                      </el-alert>
                    </div>
                  </el-form-item>
                </div>

                <!-- 操作按钮 -->
                <el-form-item class="form-item-modern action-buttons">
                  <div class="button-group">
                    <el-button v-permission v-if="!id" icon="el-icon-check" type="primary" size="large"
                      @click="submitForm('ruleForm')" class="action-btn primary">
                      保存
                    </el-button>
                    <el-button v-permission v-else icon="el-icon-check" type="primary" size="large" @click="submitForm('ruleForm')"
                      class="action-btn primary">
                      更新
                    </el-button>
                    <el-button v-permission v-if="!id" type="success" size="large" icon="el-icon-check"
                      @click="submitAudit('ruleForm')" class="action-btn success">
                      提交审核
                    </el-button>
                    <el-button icon="el-icon-caret-left" type="info" size="large" @click="resetForm('ruleForm')"
                      class="action-btn">
                      返回
                    </el-button>
                  </div>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>

          <!-- 预览区域 -->
          <el-col :span="9" class="preview-section">
            <el-card shadow="hover" class="preview-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-view"></i>
                  预览效果
                </span>
              </div>

              <div class="preview-content">
                <div class="mobile-phone">
                  <!-- 手机外框 -->
                  <div class="phone-frame">
                    <!-- 手机顶部 -->
                    <div class="phone-top">
                      <div class="camera"></div>
                      <div class="speaker"></div>
                    </div>

                    <!-- 手机屏幕 -->
                    <div class="phone-screen">
                      <!-- 状态栏 -->
                      <div class="status-bar">
                        <div class="status-left">
                          <span class="time">9:41</span>
                        </div>
                        <div class="status-right">
                          <div class="signal-icon">📶</div>
                          <div class="wifi-icon">📶</div>
                          <div class="battery-icon">🔋</div>
                        </div>
                      </div>

                      <!-- 应用头部 -->
                      <div class="app-header">
                        <div class="app-nav">
                          <i class="el-icon-arrow-left nav-back"></i>
                          <span class="app-title">ChatBot详情</span>
                          <i class="el-icon-more nav-more"></i>
                        </div>
                      </div>

                      <!-- 聊天机器人内容 -->
                      <div class="chatbot-content">
                        <!-- 头像区域 -->
                        <div class="avatar-area">
                          <div class="avatar-container">
                            <img v-if="ruleForm.logoUrl" :src="ruleForm.logoUrl" alt="ChatBot头像" class="main-avatar" />
                            <div v-else class="main-avatar-placeholder">
                              <i class="el-icon-picture"></i>
                            </div>
                          </div>
                        </div>

                        <!-- 标题区域 -->
                        <div class="title-area">
                          <h2 class="main-title">{{ ruleForm.chatbotName || 'xxx' }}</h2>
                        </div>

                        <!-- 信息列表区域 -->
                        <div class="info-list">
                          <div class="info-section">
                            <div class="section-title">服务描述</div>
                            <div class="section-content">
                              {{ ruleForm.description || '' }}
                            </div>
                          </div>

                          <div class="info-section">
                            <div class="section-title">短信客服</div>
                            <div class="section-content">
                              {{ ruleForm.callbackPhone || '' }}
                            </div>
                          </div>

                          <div class="info-section">
                            <div class="section-title">邮箱</div>
                            <div class="section-content">
                              {{ ruleForm.email || '' }}
                            </div>
                          </div>

                          <div class="info-section">
                            <div class="section-title">提供商</div>
                            <div class="section-content">
                              {{ ruleForm.chatbotName || 'xxx' }}
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 底部操作栏 -->
                      <div class="bottom-actions">
                        <div class="action-button primary">
                          <i class="el-icon-chat-line-round"></i>
                          <span>开始聊天</span>
                        </div>
                        <div class="action-button secondary">
                          <i class="el-icon-star-off"></i>
                          <span>关注</span>
                        </div>
                      </div>
                    </div>

                    <!-- 手机底部 -->
                    <div class="phone-bottom">
                      <div class="home-indicator"></div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="dialogVisible" width="60%" center>
      <img width="100%" :src="ruleForm.logoUrl" alt="头像预览" style="border-radius: 8px;" />
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "chatBot",
  data() {
    var coderules = (rule, value, callback) => {
      if (value) {
        let reg = /^\d+$/;
        if (!reg.test(value)) {
          callback(new Error("扩展号只允许输入数字"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入扩展码"));
      }
    };
    var longitude = (rule, value, callback) => {
      if (value) {
        let reg = /^[+-]?\d+(?:\.\d{1,7})?$/;
        if (!reg.test(value)) {
          callback(new Error("小数点后最多七位，请重新输入"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入经度"));
      }
    };
    var latitude = (rule, value, callback) => {
      if (value) {
        let reg = /^[+-]?\d+(?:\.\d{1,7})?$/;
        if (!reg.test(value)) {
          callback(new Error("小数点后最多七位，请重新输入"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入纬度"));
      }
    };

    return {
      dialogVisible: false,
      // office: [],
      id: "",
      // city: [],
      // province: [],
      attachmentList: [],
      fileList: [],
      disabled: false,
      flag: false,
      logoFlag: true,
      action: this.API.cpus + "v3/file/uploadFile",
      token: {},
      categoryData: [],
      ruleForm: {
        customerId: "",
        chatbotName: "", //chatbot名称
        autoGraph: "", //签名
        bubbleColor: "#FFFFFF", //气泡颜色
        // ext: "", //扩展号
        // industry: "", //实际下发行业
        // category: "", //行业类型
        callbackPhone: "", //服务电话
        // cityCode: "", //地市编码
        // officeCode: "", //区编码
        // provinceCode: "", //省份编码
        // ext: "", //扩展号
        logoUrl: "", //头像
        email: "", //邮箱
        description: "", //描述信息
        terms: "", //服务条款
        website: "", //chatbot主页地址
        longitude: "", //经度
        latitude: "", //纬度
        address: "", //chatbot办公地址
        // debugWhiteAddress: "", //调试白名单
        attachment: "",//附件地址
        statusOf: "新增chatbot信息",
      },
      rules: {
        chatbotName: [
          { required: true, message: "请输入chatbot名称", trigger: "blur" },
        ],
        customerId: [
          { required: true, message: "请选择企业名称", trigger: "blur" },
        ],
        logoUrl: [
          { required: true, message: "请上传chatbot头像", trigger: "blur" },
        ],
        // attachment: [
        //   { required: true, message: "请上传附件", trigger: "blur" },
        // ],
        // category: [
        //   { required: true, message: "请选择行业类型", trigger: "blur" },
        // ],
        // industry: [
        //   { required: true, message: "请选择实际下发行业", trigger: "blur" },
        // ],
        bubbleColor: [
          { required: true, message: "请选择气泡颜色", trigger: "blur" },
        ],
        autoGraph: [{ required: true, message: "请输入签名", trigger: "blur" }],
        callbackPhone: [
          { required: true, message: "请输入服务电话", trigger: "blur" },
        ],
        // cityCode: [
        //   { required: true, message: "请选择地市编码", trigger: "blur" },
        // ],
        // officeCode: [
        //   { required: true, message: "请选择区编码", trigger: "blur" },
        // ],
        terms: [{ required: true, message: "请输入服务条款", trigger: "blur" }],
        website: [
          { required: true, message: "请输入chatbot主页地址", trigger: "blur" },
        ],
        longitude: [{ required: true, validator: longitude, trigger: "blur" }],
        latitude: [{ required: true, validator: latitude, trigger: "blur" }],
        address: [
          { required: true, message: "chatbot办公地址", trigger: "blur" },
        ],
        // provinceCode: [
        //   { required: true, message: "请选择省份编码", trigger: "blur" },
        // ],
        // ext: [{ required: true, validator: coderules, trigger: "blur" }],
        email: [{ required: true, message: "请输入邮箱", trigger: "blur" }],
        description: [
          { required: true, message: "描述信息不能为空", trigger: "blur" },
        ],
        debugWhiteAddress: [
          { required: true, message: "调试白名单不能为空", trigger: "blur" },
        ],
      },
      addUserDialog_status: 1, //用户信息是否是新增---1为新增，0为编辑
      customerList: []
    };
  },
  created() {
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.getFindCustomerByUser()
    if (!this.$route.query.chatbotId) {
      this.statusOf = "新增chatbot信息";
      this.addUserDialog_status = 1;
      // window.location.reload()
    } else {
      this.id = this.$route.query.chatbotId;
      this.statusOf = "编辑chatbot信息";
      this.addUserDialog_status = 0;

      this.handleEdit();
    }

    // this.region();
    // this.getcategory();
  },
  methods: {
    // citys(code) {
    //   this.$api.get(
    //     this.API.fiveWeb + "/city/list/" + code,
    //     {
    //       //   provinceCode:this.ruleForm.provinceCode
    //     },
    //     (res) => {
    //       this.city = res.data;
    //     }
    //   );
    // },
    // provinces(code) {
    //   this.$api.get(
    //     this.API.fiveWeb + "/province/list/" + code,
    //     {
    //       //   regionCode:this.ruleForm.officeCode
    //     },
    //     (res) => {
    //       this.province = res.data;
    //     }
    //   );
    // },
    //根据用户查找对应的5G企业
    getFindCustomerByUser() {
      this.$api.post(
        this.API.fiveWeb + "/customer/findCustomerByUser",
        {},
        (res) => {
          if (res.code == 200) {
            this.customerList = res.data;
            this.ruleForm.customerId = res.data[0].customerId
          } else {
            this.$message({
              type: "error",
              message: res.msg
            });
          }
        }
      );
    },
    getcategory() {
      this.$api.get(this.API.fiveWeb + "/industry/firstIndustry", {}, (res) => {
        this.categoryData = res.data;
      });
    },
    //根据用户查找对应的5G企业
    getFindCustomerByUser() {
      this.$api.post(
        this.API.fiveWeb + "/customer/findCustomerByUser",
        {},
        (res) => {
          if (res.code == 200) {
            this.customerList = res.data;
            this.ruleForm.customerId = res.data[0].customerId
          } else {
            this.$message({
              type: "error",
              message: res.msg
            });
          }
        }
      );
    },
    // handelchatName(val) {
    //   this.ruleForm.autoGraph = val;
    // },
    // region() {
    //   this.$api.post(
    //     this.API.fiveWeb + "/region/page",
    //     {
    //       currentPage: 1,
    //       pageSize: 10,
    //     },
    //     (res) => {
    //       this.office = res.data.records;
    //     }
    //   );
    // },

    handleRemove(file, fileList) {
      this.ruleForm.logoUrl = "";
    },
    handleRemoveAtt() {
      this.ruleForm.attachment = "";
    },
    Remove() {
      this.ruleForm.logoUrl = "";
      this.logoFlag = true;
    },
    handlePictureCardPreview(file) {
      this.dialogVisible = true;
    },
    beforeAvatarUpload(file) {
      // console.log(file,'file');
      const siJPGGIF = file.name.split(".")[1];
      const is4kb = file.size;
      // const isLt5M = Math.round(file.size/1024*100)/100 < 4 //单位为KB;
      function objectSize(size) {
        var divisor = 1;
        var unit = "bytes";
        if (size >= 1024 * 1024) {
          divisor = 1024 * 1024;
          unit = "MB";
        } else if (size >= 1024) {
          divisor = 1024;
          unit = "KB";
        }
        if (divisor == 1) return size + " " + unit;

        return (size / divisor).toFixed(2) < 50;
      }
      let flagSize = objectSize(is4kb);
      if (siJPGGIF != "jpg" && siJPGGIF != "jpeg" && siJPGGIF != "png") {
        this.$message.warning("上传头像图片只能是 jpeg 、png格式!");
        return false;
      }
      if (!flagSize) {
        this.$message.warning("上传缩略图大小不能超过50kb！");
        return false;
      }

      // return isJPG && isLt2M;
    },
    beforeAvatarUploadAtt(file) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const isLt5M = file.size / 1024 / 1024 < 5;
      const fileType = ["jpg", "jpeg", "gif", "pdf", "doc", "docx", "rar", "zip"];
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning("上传文件只能是 pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!");
        return false;
      }
      if (!isLt5M) {
        this.$message.warning("上传文件大小不能超过 5MB!");
        return false;
      }
    },
    handleSuccess(res, fileList) {
      if (res.code == 200) {
        this.ruleForm.logoUrl = res.data;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleSuccessAtt(res, fileList) {
      if (res.code == 200) {
        this.ruleForm.attachment = res.data;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    resetForm() {
      this.$router.push({
        path: "/ChatBotManage",
      });
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.addUserDialog_status == 1) {
            this.$api.post(
              this.API.fiveWeb + "/chatbot",
              this.ruleForm,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.$router.push({
                    path: "/ChatBotManage",
                  });
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              }
            );
          } else {
            this.ruleForm.chatbotId = this.$route.query.chatbotId;
            this.$api.put(
              this.API.fiveWeb + "/chatbot",
              this.ruleForm,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.$router.push({
                    path: "/ChatBotManage",
                  });
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              }
            );
          }
        }
      });
    },
    submitAudit(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            "提交审核！",
            this.API.fiveWeb + "/chatbot/toAudit",
            this.ruleForm,
            (res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.msg,
                  type: "success",
                });
                // this.getchatList();
                this.$router.push({
                  path: "/ChatBotManage",
                });
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            }
          );
        }
      });
      // this.$confirms.confirmation(
      //   "post",
      //   "提交审核！",
      //   this.API.fiveWeb + "/chatbot/toAudit",
      //   {
      //     chatbotId: row.chatbotId,
      //   },
      //   (res) => {
      //     this.getchatList();
      //   }
      // );
    },
    //编辑赋值
    handleEdit() {
      this.$api.get(
        this.API.fiveWeb + "/chatbot/" + this.$route.query.chatbotId,
        {
          //   provinceCode:this.ruleForm.provinceCode
        },
        (res) => {
          // console.log(res.data);
          Object.assign(this.ruleForm, res.data);
          if (res.data.customerId) {
            this.ruleForm.customerId = res.data.customerId
          }
          if (this.ruleForm.attachment) {
            let a = this.ruleForm.attachment.split('/')
            this.attachmentList[0] = {
              name: a[a.length - 1],
              url: this.ruleForm.attachment
            }
          }
          if (this.ruleForm.logoUrl) {
            this.logoFlag = false;
          } else {
            this.logoFlag = true;
          }
          if (!res.data.bubbleColor) {
            this.ruleForm.bubbleColor = "#FFFFFF";
          }
          // this.fileList[0] = this.API.img+res.data.logoUrl
          // this.provinces(this.ruleForm.officeCode);
          // this.citys(this.ruleForm.provinceCode);
          // this.ruleForm.provinceCode = this.ruleForm.provinceCode+""
          // this.ruleForm.cityCode = this.ruleForm.cityCode+""
          //   this.city = res.data;
        }
      );
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/template-common.less';

/* ChatBot 特有样式 */
.chatbot-layout {
  min-height: calc(100vh - 200px);
}

.form-section {
  .chatbot-config-card {
    .card-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 2px solid #e8e8e8;
      padding: 20px;

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #409eff;
          font-size: 20px;
        }
      }
    }
  }
}

.modern-form {
  padding: 20px;

  .form-group {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .group-title {
      margin: 0 0 24px 0;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;
      padding-bottom: 12px;
      border-bottom: 2px solid #409eff;

      i {
        color: #409eff;
        font-size: 18px;
      }
    }
  }

  .form-item-modern {
    margin-bottom: 24px;

    /deep/ .el-form-item__label {
      color: #333;
      font-weight: 500;
      font-size: 14px;
      line-height: 1.6;
    }

    .modern-input,
    .modern-select,
    .modern-textarea {
      width: 100%;

      /deep/ .el-input__inner,
      /deep/ .el-textarea__inner {
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        transition: all 0.3s ease;

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }

      /deep/ .el-input__prefix {
        left: 12px;
        color: #999;
      }

      /deep/ .el-input__inner {
        padding-left: 40px;
      }
    }

    .field-tip {
      margin-top: 8px;
      color: #666;
      font-size: 13px;
      display: flex;
      align-items: flex-start;
      gap: 6px;
      line-height: 1.5;

      i {
        color: #409eff;
        margin-top: 2px;
        flex-shrink: 0;
      }
    }
  }

  .avatar-upload-section {
    .avatar-preview {
      display: flex;
      align-items: center;
      gap: 20px;

      .avatar-image {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        object-fit: cover;
        border: 2px solid #e8e8e8;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
      }

      .change-avatar-btn {
        border-radius: 6px;
        font-weight: 500;
      }
    }

    .avatar-upload {
      display: flex;
      gap: 20px;
      align-items: flex-start;

      .modern-upload {
        /deep/ .el-upload {
          border: 2px dashed #dcdfe6;
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #409eff;
          }
        }

        /deep/ .el-upload-dragger {
          border: none;
          border-radius: 12px;
        }
      }

      .upload-tips {
        max-width: 300px;

        /deep/ .el-alert {
          border-radius: 6px;
        }

        .tip-item {
          margin: 4px 0;
          font-size: 13px;
        }
      }
    }
  }

  .modern-file-upload {
    /deep/ .el-upload {
      .el-button {
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  .upload-tips {
    margin-top: 12px;

    /deep/ .el-alert {
      border-radius: 6px;
    }
  }

  .action-buttons {
    padding-top: 20px;
    margin-top: 20px;
    border-top: 2px solid #f0f0f0;

    .button-group {
      display: flex;
      justify-content: center;
      gap: 16px;
      flex-wrap: wrap;

      .action-btn {
        min-width: 120px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.primary {
          background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
          border-color: #409eff;

          &:hover {
            background: linear-gradient(135deg, #66b1ff 0%, #85c1ff 100%);
          }
        }

        &.success {
          background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
          border-color: #67c23a;

          &:hover {
            background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
          }
        }
      }
    }
  }
}

/* 预览区域样式 */
.preview-section {
  .preview-card {
    height: fit-content;
    position: sticky;
    top: 20px;

    .card-header {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border-bottom: 2px solid #e8e8e8;
      padding: 20px;

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #409eff;
          font-size: 20px;
        }
      }
    }
  }

  .preview-content {
    padding: 20px;
    display: flex;
    justify-content: center;
  }

  /* 手机外观 */
  .mobile-phone {
    .phone-frame {
      width: 280px;
      height: 580px;
      background: linear-gradient(145deg, #2c3e50, #34495e);
      border-radius: 30px;
      padding: 8px;
      box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 6px 20px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        right: 2px;
        bottom: 2px;
        border-radius: 28px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        pointer-events: none;
      }
    }

    .phone-top {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8px;
      height: 20px;
      margin-bottom: 4px;

      .camera {
        width: 8px;
        height: 8px;
        background: #1a1a1a;
        border-radius: 50%;
        border: 1px solid #333;
      }

      .speaker {
        width: 40px;
        height: 4px;
        background: #1a1a1a;
        border-radius: 2px;
        border: 1px solid #333;
      }
    }

    .phone-screen {
      width: 100%;
      height: 540px;
      background: #ffffff;
      border-radius: 22px;
      overflow: hidden;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .phone-bottom {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 16px;

      .home-indicator {
        width: 120px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
      }
    }
  }

  /* 手机界面内容 */
  .status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: #ffffff;
    font-size: 12px;
    font-weight: 600;
    color: #1a1a1a;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    .status-left {
      .time {
        letter-spacing: 0.5px;
      }
    }

    .status-right {
      display: flex;
      align-items: center;
      gap: 4px;

      .signal-icon,
      .wifi-icon,
      .battery-icon {
        font-size: 10px;
        opacity: 0.8;
      }
    }
  }

  .app-header {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    color: white;
    padding: 12px 16px;

    .app-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .nav-back,
      .nav-more {
        font-size: 18px;
        cursor: pointer;
        opacity: 0.9;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 1;
        }
      }

      .app-title {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  .chatbot-content {
    flex: 1;
    background: white;
    overflow-y: auto;
    padding: 20px 16px;
  }

  .avatar-area {
    text-align: center;
    margin-bottom: 16px;

    .avatar-container {
      display: inline-block;

      .main-avatar {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        object-fit: cover;
        border: 1px solid #e9ecef;
        background: #f8f9fa;
      }

      .main-avatar-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 32px;
      }
    }
  }

  .title-area {
    text-align: center;
    margin-bottom: 32px;

    .main-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      line-height: 1.2;
    }
  }

  .info-list {
    .info-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 15px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .section-content {
        font-size: 14px;
        color: #6c757d;
        line-height: 1.5;
        min-height: 20px;

        &:empty::before {
          content: '';
          display: block;
          height: 20px;
        }
      }
    }
  }

  .bottom-actions {
    background: white;
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;

    .action-button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      padding: 12px 16px;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &.primary {
        background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
        }
      }

      &.secondary {
        background: #f8f9fa;
        color: #6c757d;
        border: 1px solid #e9ecef;

        &:hover {
          background: #e9ecef;
          color: #495057;
        }
      }

      i {
        font-size: 16px;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .chatbot-layout {

    .form-section,
    .preview-section {
      width: 100% !important;
    }

    .form-section {
      margin-bottom: 20px;
    }
  }
}

@media (max-width: 768px) {
  .modern-form {
    .form-item-modern {
      .field-tip {
        font-size: 12px;
      }
    }

    .action-buttons {
      .button-group {
        flex-direction: column;

        .action-btn {
          width: 100%;
          min-width: auto;
        }
      }
    }
  }

  .preview-section {
    .phone-container {
      padding: 12px;
    }

    .phone-body {
      padding: 16px;
    }
  }
}
</style>