<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 标题信息区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <div class="page-info">
                  <i class="el-icon-download"></i>
                  <span class="page-title">文件下载中心</span>
                </div>
              </div>
            </div>

            <!-- 提示信息区域 -->
            <div class="tips-section">
              <div class="tips-container">
                <div class="tip-header">
                  <i class="el-icon-info"></i>
                  <span class="tip-title">下载须知</span>
                </div>
                <div class="tip-content">
                  <div class="tip-item">
                    <i class="el-icon-time"></i>
                    <span>文件有效期3天，超过3天将不能下载，请重新进行导出</span>
                  </div>
                  <div class="tip-item">
                    <i class="el-icon-refresh"></i>
                    <span>请刷新查看最新状态</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="table-container">
          <el-table 
            v-loading="tableDataObj.loading2" 
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading" 
            element-loading-background="rgba(0, 0, 0, 0.6)" 
            ref="multipleTable"
            border 
            :data="tableDataObj.tableData" 
            class="enhanced-table"
            @selection-change="handelSelection">
            
            <el-table-column prop="name" label="文件名称" min-width="180">
              <template slot-scope="scope">
                <div class="file-name">
                  <i class="el-icon-document"></i>
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="createTime" label="创建时间" width="180" align="center">
              <template slot-scope="scope">
                <div class="create-time">
                  <i class="el-icon-time"></i>
                  <span>{{ formatTime(scope.row.createTime) }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="type" label="文件类型" width="180" align="center">
              <template slot-scope="scope">
                <el-tag :type="getFileTypeTag(scope.row.type)" size="small">
                  {{ getFileTypeName(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- <el-table-column label="状态" width="120" align="center">
              <template slot-scope="scope">
                <el-tag 
                  :type="getStatusType(scope.row.status)" 
                  size="small"
                  :effect="scope.row.status == 2 ? 'dark' : 'plain'">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column> -->
            
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.decode === true">
                  <el-tag v-if="scope.row.status == 2" type="success" size="small">已发送</el-tag>
                  <el-tag v-else-if="scope.row.status == -1" type="danger" size="small">文件异常</el-tag>
                  <el-tag v-else-if="scope.row.status == 4" type="info" size="small">文件已过期</el-tag>
                  <el-tag v-else type="warning" size="small">
                    <i class="el-icon-loading"></i>
                    未生成
                  </el-tag>
                </div>
                <div v-else>
                  <el-button 
                    v-if="scope.row.status == 2" 
                    type="primary" 
                    size="mini"
                    @click="download(scope.row, scope.$index)"
                    class="download-btn">
                    <i class="el-icon-download"></i>
                    下载
                  </el-button>
                  <el-tag v-else-if="scope.row.status == -1" type="danger" size="small">文件异常</el-tag>
                  <el-tag v-else-if="scope.row.status == 4" type="info" size="small">文件已过期</el-tag>
                  <el-tag v-else type="warning" size="small">
                    <i class="el-icon-loading"></i>
                    未生成
                  </el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination 
              @size-change="handleSizeChange" 
              @current-change="handleCurrentChange"
              :current-page="sensitiveConditions.currentPage" 
              :page-size="sensitiveConditions.pageSize"
              :page-sizes="[20, 50, 100, 200]" 
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.totalRow"
              class="enhanced-pagination">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 手机验证对话框 -->
    <el-dialog 
      title="手机验证" 
      :visible.sync="exportFlag" 
      width="600px" 
      top="20vh"
      class="enhanced-dialog">
      <div class="verification-dialog">
        <div class="phone-selection">
          <div class="section-title">
            <i class="el-icon-mobile-phone"></i>
            <span>选择验证手机号</span>
          </div>
          <el-table 
            :data="phoneList" 
            border 
            class="phone-table">
            <el-table-column align="center" prop="consumerName" label="编号" width="120">
            </el-table-column>
            <el-table-column prop="mobile" align="center" label="手机号">
            </el-table-column>
            <el-table-column align="center" width="80" label="选择">
              <template slot-scope="scope">
                <el-radio 
                  v-if="!flag" 
                  @change.native="getCurrentRow(scope.$index)" 
                  :label="scope.$index" 
                  v-model="radio"
                  class="textRadio">
                  &nbsp;
                </el-radio>
                <el-radio 
                  v-else 
                  :disabled="true" 
                  @change.native="getCurrentRow(scope.$index)" 
                  :label="scope.$index"
                  v-model="radio" 
                  class="textRadio">
                  &nbsp;
                </el-radio>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="verification-form">
          <el-form 
            :model="setphoneFrom.ruleForm1" 
            :rules="setphoneFrom.rules1" 
            ref="ruleForm1" 
            class="enhanced-form"
            label-width="120px">
            <el-form-item label="手机验证码" prop="verCode">
              <div class="verification-input">
                <el-input 
                  v-model="setphoneFrom.ruleForm1.verCode" 
                  placeholder="请输入6位验证码"
                  class="code-input">
                </el-input>
                <el-button 
                  type="primary" 
                  @click="CountdownCode"
                  :disabled="flag || nmb < 120"
                  class="code-btn">
                  {{ nmb == 120 ? '获取验证码' : `重新获取(${nmb})` }}
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <div class="dialog-actions">
          <el-button @click="handleClose()" class="action-btn cancel">
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="submitForm('ruleForm1')"
            class="action-btn primary">
            确定下载
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
var axios = require("axios");
import getNoce from "../../../../plugins/getNoce";
export default {
  name: "FileExport",
  data() {
    var code = (rule, value, callback) => {
      if (!this.phoneData) {
        return callback(new Error("请选中手机号"));
      } else if (value == "") {
        return callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    return {
      name: "FileExport",
      exportFlag: false,
      tableDataObj: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
        tableData: [],
      },
      sensitiveConditions: {
        //赋值查询条件的值
        currentPage: 1,
        pageSize: 20,
      },
      selectId: "",
      phoneList: [],
      validateObj: {},
      flag: false,
      radio: "",
      phoneData: "",
      nmb: 120,
      fileIndex: null,
      timer: null,
      setphoneFrom: {
        ruleForm1: {
          verCode: "",
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
        },
      },
    };
  },
  created() {
    this.getLoginPhone();
  },
  methods: {
    GettableDtate() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      this.$api.get(
        this.API.cpus + "v3/export/page",
        this.sensitiveConditions,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tablecurrent.totalRow = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 获取文件类型名称
    getFileTypeName(type) {
      const typeMap = {
        '1': '短信明细',
        '2': '黑名单',
        '3': '彩信明细',
        '4': '视频明细',
        '5': '国际明细',
        '6': '语音明细',
        '7': '短信回复',
        '9': '视频回复',
        '11': '短链查看明细',
        '12': '模板管理',
        '13': '视频短信web发送任务',
        '14': '客户签名实名信息'
      };
      return typeMap[type] || '未知类型';
    },
    // 获取文件类型标签颜色
    getFileTypeTag(type) {
      const tagMap = {
        '1': 'primary',
        '2': 'danger',
        '3': 'success',
        '4': 'warning',
        '5': 'info',
        '6': 'primary',
        '7': 'success',
        '9': 'warning',
        '11': 'info',
        '12': 'primary',
        '13': 'warning',
        '14': 'success'
      };
      return tagMap[type] || '';
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        2: 'success',
        '-1': 'danger',
        4: 'info'
      };
      return statusMap[status] || 'warning';
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        2: '可下载',
        '-1': '异常',
        4: '已过期'
      };
      return statusMap[status] || '生成中';
    },
    getCurrentRow(val) {
      // console.log(val,'ll');
      // this.count = val
      // if(this.count == val){
      //     this.flag = true
      // }else{
      //     this.flag = false
      // }
      this.phoneData = this.phoneList[val].mobile; //赋值手机号
    },
    CountdownCode() {
      if (this.phoneData) {
        --this.nmb;
        this.timer = setInterval((res) => {
          --this.nmb;
          if (this.nmb < 1) {
            this.nmb = 120;
            this.flag = false;
            clearInterval(this.timer);
          } else {
            this.flag = true;
          }
        }, 1000);
        this.$api.get(
          this.API.cpus +
          "code/sendVerificationCode?phone=" +
          this.phoneData +
          "&flag=4",
          {},
          (res) => {
            if (res.code == 200) {
              this.flag = true;
              this.$message({
                type: "success",
                duration: "2000",
                message: "验证码已发送至手机!",
              });
            } else {
              this.flag = true;
              this.$message({
                type: "warning",
                message: "验证码未失效，需失效后重新获取!",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "请先选中手机号码",
          type: "warning",
        });
      }
    },
    getLoginPhone() {
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data;
          }
        }
      );
    },
    handleClose() {
      this.exportFlag = false;
    },
    async download(val, index) {
      const nonce = await getNoce.useNonce();
      //   this.exportFlag = true;
      //   this.validateObj = val;
      //   this.fileIndex = index
      Date.prototype.format = function (format) {
        var args = {
          "M+": this.getMonth() + 1,
          "d+": this.getDate(),
          "h+": this.getHours(),
          "m+": this.getMinutes(),
          "s+": this.getSeconds(),
          "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        };
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var i in args) {
          var n = args[i];
          if (new RegExp("(" + i + ")").test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
            );
        }
        return format;
      };
      var that = this;
      filedownload();
      function filedownload() {
        axios({
          method: "get",
          url:
            that.API.cpus +
            "v3/file/downloadFile?fileName=" +
            val.name +
            "&group=" +
            val.group +
            "&path=" +
            val.path,
          data: {},
          headers: {
            "Content-Type": "application/json",
            Authorization:
              "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
            'Once': nonce,
          },
          responseType: 'blob',
        })
          .then(function (response) {
            let blobUrl = window.URL.createObjectURL(response.data);
            download(blobUrl);
            that.exportFlag = false;
          })
        //    +
        //     "&phone=" + that.phoneData
        //   "&smsCode=" +
        //     that.setphoneFrom.ruleForm1.verCode +
        // fetch(
        //   that.API.cpus +
        //     "v3/file/download?fileName=" +
        //     val.name +
        //     "&group=" +
        //     val.group +
        //     "&path=" +
        //     val.path +
        //     "&smsCode=" +
        //     that.setphoneFrom.ruleForm1.verCode,
        //   {
        //     method: "get",
        //     headers: {
        //       "Content-Type": "application/json",
        //       Authorization:
        //         "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
        //     },
        //     // body: JSON.stringify({
        //     //     batchNo: val.row.batchNo,
        //     // })
        //   }
        // )
        //   .then((res) => {
        //     console.log(res,'res');
        //     res.blob()
        //   })
        //   .then((data) => {
        //     console.log(data,'data');
        //     let blobUrl = window.URL.createObjectURL(data);
        //     download(blobUrl);
        //     that.exportFlag = true;
        //   })
      }
      function download(blobUrl) {
        var a = document.createElement("a");
        a.style.display = "none";
        a.download =
          "(" +
          new Date().format("yyyy-MM-dd hh:mm:ss") +
          ") " +
          val.name +
          ".zip";
        a.href = blobUrl;
        a.click();
      }
    },
    async submitForm(form) {
      const nonce = await getNoce.useNonce();
      this.$refs[form].validate((valid) => {
        if (valid) {
          // 时间过滤
          Date.prototype.format = function (format) {
            var args = {
              "M+": this.getMonth() + 1,
              "d+": this.getDate(),
              "h+": this.getHours(),
              "m+": this.getMinutes(),
              "s+": this.getSeconds(),
              "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
              S: this.getMilliseconds(),
            };
            if (/(y+)/.test(format))
              format = format.replace(
                RegExp.$1,
                (this.getFullYear() + "").substr(4 - RegExp.$1.length)
              );
            for (var i in args) {
              var n = args[i];
              if (new RegExp("(" + i + ")").test(format))
                format = format.replace(
                  RegExp.$1,
                  RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
                );
            }
            return format;
          };
          var that = this;
          filedownload();
          function filedownload() {
            axios({
              method: "get",
              url:
                that.API.cpus +
                "v3/file/downloadFile?fileName=" +
                that.validateObj.name +
                "&group=" +
                that.validateObj.group +
                "&path=" +
                that.validateObj.path +
                "&smsCode=" +
                that.setphoneFrom.ruleForm1.verCode +
                "&phone=" + that.phoneData,
              data: {},
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
              responseType: 'blob',
            })
              .then(function (response) {
                let blobUrl = window.URL.createObjectURL(response.data);
                download(blobUrl);
                that.exportFlag = false;
              })
            // fetch(
            //   that.API.cpus +
            //     "v3/file/download?fileName=" +
            //     that.validateObj.name +
            //     "&group=" +
            //     that.validateObj.group +
            //     "&path=" +
            //     that.validateObj.path +
            //     "&smsCode=" +
            //     that.setphoneFrom.ruleForm1.verCode,
            //   {
            //     method: "get",
            //     headers: {
            //       "Content-Type": "application/json",
            //       Authorization:
            //         "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
            //     },
            //     // body: JSON.stringify({
            //     //     batchNo: that.validateObj.row.batchNo,
            //     // })
            //   }
            // )
            //   .then((res) => {
            //     console.log(res,'res');
            //     res.blob()
            //   })
            //   .then((data) => {
            //     console.log(data,'data');
            //     let blobUrl = window.URL.createObjectURL(data);
            //     download(blobUrl);
            //     that.exportFlag = true;
            //   })
          }
          function download(blobUrl) {
            var a = document.createElement("a");
            a.style.display = "none";
            a.download =
              "(" +
              new Date().format("yyyy-MM-dd hh:mm:ss") +
              ") " +
              that.validateObj.name +
              ".zip";
            a.href = blobUrl;
            a.click();
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      let timingStatus = [];
      let sendTimess = [];
      for (let i = 0; i < val.length; i++) {
        // selectId.push(val[i].timingSmsId);
        selectId.push(val[i].id);
      }
      this.selectId = selectId.join(","); //批量操作选中消息id
    },
    handleSizeChange(size) {
      this.sensitiveConditions.pageSize = size;
    },
    handleCurrentChange: function (currentPage) {
      console.log(currentPage);
      this.sensitiveConditions.currentPage = currentPage;
    },
  },
  // mounted(){
  //     this.GettableDtate()
  // },
  // activated(){
  //     this.GettableDtate()
  // },
  watch: {
    sensitiveConditions: {
      handler(val) {
        this.GettableDtate();
      },
      deep: true,
      immediate: true,
    },
    exportFlag(val) {
      if (!val) {
        this.$refs.ruleForm1.resetFields();
      }
    },
    fileIndex(val) {
      if (val) {
        this.nmb = 120;
        this.flag = false;
        clearInterval(this.timer);
      }
    }
  },
};
</script>

<style lang="less" scoped>
@import "~@/styles/template-common.less";

.table-container {
    margin-top: 20px;

    .enhanced-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .file-name {
            display: flex;
            align-items: center;
            gap: 8px;

            i {
                color: #409eff;
                font-size: 16px;
            }

            span {
                font-weight: 500;
            }
        }

        .create-time {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;

            i {
                color: #666;
            }
        }

        .download-btn {
            background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
            border: none;
            border-radius: 4px;
            font-size: 12px;
            padding: 4px 12px;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        background: #ffffff;
        padding: 16px 0;

        .enhanced-pagination {
            ::v-deep .el-pagination__total {
                color: #409eff;
                font-weight: 500;
            }

            ::v-deep .el-pager li.active {
                background: #409eff;
                color: #ffffff;
            }

            ::v-deep .el-pagination__jump {
                color: #666;
            }
        }
    }
}

// 验证对话框样式
.enhanced-dialog {
    ::v-deep .el-dialog__header {
        background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
        padding: 16px 20px;

        .el-dialog__title {
            color: #ffffff;
            font-weight: 600;
        }

        .el-dialog__headerbtn {
            .el-dialog__close {
                color: #ffffff;
                
                &:hover {
                    color: #ecf5ff;
                }
            }
        }
    }

    ::v-deep .el-dialog__body {
        padding: 24px;
    }

    .verification-dialog {
        .phone-selection {
            margin-bottom: 24px;

            .section-title {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 16px;
                color: #333;

                i {
                    color: #409eff;
                    font-size: 18px;
                }
            }

            .phone-table {
                border-radius: 8px;
                overflow: hidden;

                ::v-deep .el-table__header {
                    background: #f8f9fa;
                }
            }
        }

        .verification-form {
            padding: 20px;
            background: #fafbfc;
            border-radius: 8px;
            margin-bottom: 24px;

            .enhanced-form {
                .verification-input {
                    display: flex;
                    gap: 12px;

                    .code-input {
                        flex: 1;
                    }

                    .code-btn {
                        min-width: 120px;
                        background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
                        border: none;

                        &:hover:not(:disabled) {
                            transform: translateY(-1px);
                            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
                        }

                        &:disabled {
                            background: #f5f7fa;
                            color: #c0c4cc;
                        }
                    }
                }
            }
        }

        .dialog-actions {
            display: flex;
            justify-content: center;
            gap: 16px;

            .action-btn {
                min-width: 100px;
                height: 36px;
                border-radius: 6px;
                font-weight: 500;

                &.cancel {
                    background: #ffffff;
                    border: 1px solid #d9d9d9;
                    color: #333;

                    &:hover {
                        border-color: #409eff;
                        color: #409eff;
                    }
                }

                &.primary {
                    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
                    border: none;
                    color: #ffffff;

                    &:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .table-container {
        .enhanced-table {
            font-size: 12px;

            .file-name {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .create-time {
                flex-direction: column;
                gap: 2px;
            }
        }

        .pagination-container {
            padding: 12px 0;

            .enhanced-pagination {
                ::v-deep .el-pagination {
                    text-align: center;
                }
            }
        }
    }

    .enhanced-dialog {
        ::v-deep .el-dialog {
            width: 90% !important;
            margin: 0 auto;
        }

        .verification-dialog {
            .verification-form {
                .verification-input {
                    flex-direction: column;

                    .code-btn {
                        min-width: auto;
                    }
                }
            }

            .dialog-actions {
                flex-direction: column;

                .action-btn {
                    width: 100%;
                }
            }
        }
    }
}
</style>
