<template>
    <div class="docs-container docsBlacklistManage111">
        <h1 class="main-title">短信服务问题</h1>
        
        <!-- 接口问题 -->
        <div class="docsBlacklistManage111_111 docsBox" index="111">
            <h2 class="section-title">接口问题</h2>
            <div class="qa-container">
                <div class="question-item">
                    <div class="question-title">用接口怎么发送短信？</div>
                    <div class="answer-content">
                        我们有详细的<span class="highlight">接口使用文档</span>帮助您对接，我们的技术人员也会协助您完成。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">平台API账号是否需要认证？</div>
                    <div class="answer-content">
                        没有进行认证的账号是无效的，是<span class="highlight">无法发送短信</span>的。
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 模板问题 -->
        <div class="docsBlacklistManage111_222 docsBox" index="222">
            <h2 class="section-title">模板问题</h2>
            <div class="qa-container">
                <div class="question-item">
                    <div class="question-title">模板为什么需要审核？</div>
                    <div class="answer-content">
                        当您提交了模板内容后，会有客服进行审核，若包含<span class="highlight">敏感词</span>或存在违法违规行为会被驳回，
                        审核通过的模板在下次发送短信时可直接使用。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">什么是短信签名？</div>
                    <div class="answer-content">
                        短信签名是加在短信的开头，以<span class="highlight">【】</span>为标记，在【】里加入您的公司名称或店铺名称等，
                        如 <span v-if="flag">【助通科技】</span><span v-else>【xxxx】</span>。
                        根据运营商的规定，每条短信前必须附加短信签名，否则将无法正常发送。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">模板发送和自定义内容发送有什么区别？</div>
                    <div class="answer-content">
                        当您使用审核通过的模板进行短信发送时，会<span class="highlight">跳过人工审核流程</span>，直接发送出去；
                        而使用自定义发送短信时，系统会对短信内容进行校验，若含有敏感词，短信将被拦截，进入人工审核阶段。
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 发送问题 -->
        <div class="docsBlacklistManage111_333 docsBox" index="333">
            <h2 class="section-title">发送问题</h2>
            <div class="qa-container">
                <div class="question-item">
                    <div class="question-title">可以同时发多个手机吗？</div>
                    <div class="answer-content">
                        可以，相同内容使用Web一次最多可提交 <span class="highlight">100</span> 万个号码，
                        使用API接口提交一次最多是 <span class="highlight">100</span> 万个号码。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">三网（移动、联通、电信）都可以发吗？</div>
                    <div class="answer-content">
                        是的。<span class="highlight">三网都支持</span>，全国号码都可以发。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">发送后为什么没有收到短信？</div>
                    <div class="answer-content">
                        首先确认一下，短信发送是否返回成功。如果返回失败，请按具体返回的失败代码和出错提示排查； 
                        如果返回成功，请检查：
                        <ol style="margin-top: 8px; padding-left: 20px;">
                            <li>手机是否处于关机或欠费停机状态，可以拨打手机号码确认</li>
                            <li>请检查手机信号是否正常，必要时重启一下手机</li>
                            <li>是否被短信屏蔽软件拦截，检查是否在屏蔽的短信列表中</li>
                            <li>通过接口查询短信接受状态和错误代码</li>
                        </ol>
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">短信发送字数有限制吗？</div>
                    <div class="answer-content">
                        一条短信最多包含 <span class="highlight">1000</span> 个字，短信字数≤70个字，按照70个字一条短信计算；
                        短信字数>70个字，即为长短信，按照67个字记为一条短信计算
                        <span class="highlight">（注：短信签名是包含算在计费字数里）</span>。
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 使用问题 -->
        <div class="docsBlacklistManage111_444 docsBox" index="444">
            <h2 class="section-title">使用问题</h2>
            <div class="qa-container">
                <div class="question-item">
                    <div class="question-title">短信能测试吗？</div>
                    <div class="answer-content">
                        可以，可以先跟您的<span class="highlight">专属销售</span>联系，给您开通账号赠送短信条数进行测试。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">手机号进黑名单了怎么办？</div>
                    <div class="answer-content">
                        可联系<span class="highlight">客服</span>解除黑名单限制。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">哪些情况手机号会进入黑名单？</div>
                    <div class="answer-content">
                        <ul style="padding-left: 20px;">
                            <li>之前投诉过运营商，如打过10086、10010或10000投诉的，可能会被运营商加入黑名单</li>
                            <li>有过退订历史，如回复过含有T、TD、退订或取消等代表拒绝接收短信的指令</li>
                        </ul>
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">提交短信的频率有限制吗？</div>
                    <div class="answer-content">
                        没有限制。如想更快的提交短信，可以使用<span class="highlight">多线程提交</span>，
                        但相同手机号为防止骚扰，会限制频率和日上限，如有需要可以与客服联系取消限制。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">怎么预防短信轰炸？</div>
                    <div class="answer-content">
                        我们有完善的验证码防轰炸机制：对同一手机号用户可收到的信息数量进行限制：
                        <div class="tip-box warning" style="margin-top: 12px;">
                            ①一分钟内不可超过1条<br>
                            ②一小时内不可超过5条<br>
                            ③一天内不超过20条
                        </div>
                        同时，用户可自己在平台上进行发送频率的设置，设置单日提交次数和同一号码单日提交次数。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">提交时间、发送时间、回执时间有什么区别？</div>
                    <div class="answer-content">
                        <ul style="padding-left: 20px;">
                            <li>提交时间是客户短信提交至我们平台的时间</li>
                            <li>发送时间是<span v-if="flag">助通平台</span><span v-else>平台</span>向通道提交的时间</li>
                            <li>状态回执时间是通道向我们返回状态时，记录的当前服务器时间</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>    
</template>

<script>
export default {
    props: {
        hostflag: {
            type: Boolean,
            default: true
        },
        searchKeyword: {
            type: String,
            default: ''
        }
    },
    name: "docsBlacklistManage",
    data() {
        return {
            flag: true
        }
    },
    watch: {
        hostflag: {
            handler(val) {
                this.flag = val
            },
            deep: true,
            immediate: true
        },
        searchKeyword(newVal) {
            if (newVal) {
                this.highlightSearchKeyword();
            }
        }
    },
    methods: {
        highlightSearchKeyword() {
            // 实现搜索关键词高亮
            this.$nextTick(() => {
                const keyword = this.searchKeyword;
                if (!keyword) return;
                
                const content = this.$el;
                const walker = document.createTreeWalker(
                    content,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                const textNodes = [];
                while (walker.nextNode()) {
                    textNodes.push(walker.currentNode);
                }
                
                textNodes.forEach(node => {
                    const text = node.textContent;
                    if (text.includes(keyword)) {
                        const span = document.createElement('span');
                        span.innerHTML = text.replace(
                            new RegExp(keyword, 'gi'),
                            `<span class="search-highlight">${keyword}</span>`
                        );
                        node.parentNode.replaceChild(span, node);
                    }
                });
            });
        }
    }
}
</script>

<style lang="less" scoped>
@import '~@/styles/help-center-content.less';

// 列表样式优化
ul, ol {
    li {
        margin-bottom: 8px;
        line-height: 1.8;
        color: #606266;
    }
}
</style>