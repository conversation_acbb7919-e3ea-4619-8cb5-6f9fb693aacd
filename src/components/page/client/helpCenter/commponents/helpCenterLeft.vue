<template>
    <div class="help-menu-container">
        <el-menu 
            class="help-menu"
            @open="handleOpen"
            @select="handleSelect"
            :unique-opened="true"
            :default-active="defaultActive"
            :default-openeds="defaultOpeneds"
            active-text-color="#667eea"
            text-color="#606266"
        >
            <!-- 个人账户 -->
            <el-submenu index="docsBasicConfiguration1">
                <template slot="title">
                    <i class="el-icon-user"></i>
                    <span>个人账户</span>
                </template>               
                <el-menu-item index="1">
                    <span slot="title">账号问题</span>
                </el-menu-item>
                <el-menu-item index="2">
                    <span slot="title">基础配置问题</span>
                </el-menu-item>
            </el-submenu>
            
            <!-- 支付问题 -->
            <el-submenu index="docsNotificationAlarm11">
                <template slot="title">
                    <i class="el-icon-wallet"></i>
                    <span>支付问题</span>
                </template>               
                <el-menu-item index="11">
                    <span slot="title">账号充值</span>
                </el-menu-item>
                <el-menu-item index="22">
                    <span slot="title">开票问题</span>
                </el-menu-item>
            </el-submenu>
            
            <!-- 短信服务问题 -->
            <el-submenu index="docsBlacklistManage111">
                <template slot="title">
                    <i class="el-icon-message"></i>
                    <span>短信服务问题</span>
                </template>               
                <el-menu-item index="111">
                    <span slot="title">接口问题</span>
                </el-menu-item>
                <el-menu-item index="222">
                    <span slot="title">模板问题</span>
                </el-menu-item>
                <el-menu-item index="333">
                    <span slot="title">发送问题</span>
                </el-menu-item>
                <el-menu-item index="444">
                    <span slot="title">使用问题</span>
                </el-menu-item>
            </el-submenu>
            
            <!-- 审核常见问题 -->
            <el-submenu index="docsContact1111">
                <template slot="title">
                    <i class="el-icon-document-checked"></i>
                    <span>审核常见问题</span>
                </template>               
                <el-menu-item index="1111">
                    <span slot="title">规范和要求</span>
                </el-menu-item>
                <el-menu-item index="2222">
                    <span slot="title">审核问题</span>
                </el-menu-item>
            </el-submenu>
            
            <!-- 动态与公告 -->
            <el-submenu index="docsAnnouncement11111">
                <template slot="title">
                    <i class="el-icon-bell"></i>
                    <span>动态与公告</span>
                </template>               
                <el-menu-item index="11111">
                    <span slot="title">系统公告</span>
                </el-menu-item>
                <el-menu-item index="22222">
                    <span slot="title">模板报备提示</span>
                </el-menu-item>
                <el-menu-item index="33333">
                    <span slot="title">电信业务调整</span>
                </el-menu-item>
                <el-menu-item index="44444">
                    <span slot="title">联通模板公告</span>
                </el-menu-item>
                <el-menu-item index="55555">
                    <span slot="title">引流号码和链接报备指引</span>
                </el-menu-item>
                <el-menu-item index="66666">
                    <span slot="title">短信签名报备规则</span>
                </el-menu-item>
            </el-submenu>
        </el-menu>
    </div>    
</template>

<script>
export default {
    name: "helpCenterLeft",
    props: {
        defaultActive: {
            type: String,
            default: '1'
        }
    },
    data() {
        return {
            // 默认展开的菜单
            defaultOpeneds: []
        }
    },
    computed: {
        // 获取当前激活菜单所属的父菜单
        activeParentMenu() {
            const activeIndex = this.defaultActive;
            const menuMap = {
                '1': 'docsBasicConfiguration1',
                '2': 'docsBasicConfiguration1',
                '11': 'docsNotificationAlarm11',
                '22': 'docsNotificationAlarm11',
                '111': 'docsBlacklistManage111',
                '222': 'docsBlacklistManage111',
                '333': 'docsBlacklistManage111',
                '444': 'docsBlacklistManage111',
                '1111': 'docsContact1111',
                '2222': 'docsContact1111',
                '11111': 'docsAnnouncement11111',
                '22222': 'docsAnnouncement11111',
                '33333': 'docsAnnouncement11111',
                '44444': 'docsAnnouncement11111',
                '55555': 'docsAnnouncement11111',
                '66666': 'docsAnnouncement11111'
            };
            return menuMap[activeIndex] || '';
        }
    },
    watch: {
        // 监听激活项变化，自动展开对应的父菜单
        defaultActive: {
            handler(newVal) {
                if (this.activeParentMenu) {
                    this.defaultOpeneds = [this.activeParentMenu];
                }
            },
            immediate: true
        }
    },
    methods: {
        // 处理菜单展开
        handleOpen(index, indexPath) {
            const num = this.extractNumber(index);
            const url = this.extractUrl(index);
            
            this.$emit('clickFirstIndex', {
                url: url,
                num: num
            });
        },
        
        // 处理菜单选择
        handleSelect(index, indexPath) {
            this.$emit('clickWhere', indexPath);
        },
        
        // 提取数字部分
        extractNumber(str) {
            const matches = str.match(/\d+/g);
            return matches ? matches[0] : '';
        },
        
        // 提取URL部分
        extractUrl(str) {
            return str.replace(/\d+/g, '');
        }
    }
}
</script>

<style lang="less" scoped>
.help-menu-container {
    height: 100%;
    
    .help-menu {
        border: none;
        background: transparent;
        
        /deep/ .el-submenu__title {
            padding-left: 20px !important;
            font-weight: 500;
            
            i {
                margin-right: 10px;
                font-size: 18px;
                vertical-align: middle;
            }
            
            &:hover {
                background: rgba(102, 126, 234, 0.05);
                
                i {
                    color: #667eea;
                }
            }
        }
        
        /deep/ .el-submenu.is-active > .el-submenu__title {
            color: #667eea;
            
            i {
                color: #667eea;
            }
        }
        
        /deep/ .el-menu-item {
            padding-left: 50px !important;
            position: relative;
            
            &::before {
                content: '';
                position: absolute;
                left: 35px;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 4px;
                background: #d0d0d0;
                border-radius: 50%;
                transition: all 0.3s;
            }
            
            &:hover {
                background: rgba(102, 126, 234, 0.05);
                
                &::before {
                    background: #667eea;
                    width: 6px;
                    height: 6px;
                }
            }
            
            &.is-active {
                background: rgba(102, 126, 234, 0.1);
                color: #667eea;
                font-weight: 500;
                
                &::before {
                    background: #667eea;
                    width: 6px;
                    height: 6px;
                }
                
                &::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    width: 3px;
                    background: #667eea;
                }
            }
        }
        
        // 分隔线样式
        /deep/ .el-submenu:not(:last-child) {
            position: relative;
            
            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 20px;
                right: 20px;
                height: 1px;
                background: #e8e8e8;
            }
        }
    }
}

// 动画效果
.el-menu-item,
.el-submenu__title {
    transition: all 0.3s ease;
}

// 图标颜色
.el-icon-user { color: #409eff; }
.el-icon-wallet { color: #67c23a; }
.el-icon-message { color: #e6a23c; }
.el-icon-document-checked { color: #f56c6c; }
.el-icon-bell { color: #909399; }
</style>
