<template>
    <div class="docs-container docsAnnouncement11111">
        <h1 class="main-title">动态与公告</h1>
        
        <!-- 系统公告 -->
        <div class="docsAnnouncement11111_11111 docsBox" index="11111">
            <h2 class="section-title">
                <i class="el-icon-bell"></i>
                系统公告
            </h2>
            <div class="announcement-container">
                <div v-if="contentLoading[0]" class="loading-wrapper">
                    <i class="el-icon-loading"></i>
                    <span>正在加载公告内容...</span>
                </div>
                <div v-else-if="htmlContent1" class="announcement-content">
                    <div v-html="htmlContent1" class="word-content"></div>
                </div>
                <div v-else class="empty-content">
                    <i class="el-icon-document-delete"></i>
                    <span>暂无公告内容</span>
                </div>
            </div>
        </div>
        
        <!-- 模板报备提示 -->
        <div class="docsAnnouncement11111_22222 docsBox" index="22222">
            <h2 class="section-title">
                <i class="el-icon-document"></i>
                模板报备提示
            </h2>
            <div class="announcement-container">
                <div v-if="contentLoading[1]" class="loading-wrapper">
                    <i class="el-icon-loading"></i>
                    <span>正在加载内容...</span>
                </div>
                <div v-else-if="htmlContent2" class="announcement-content">
                    <div v-html="htmlContent2" class="word-content"></div>
                </div>
                <div v-else class="empty-content">
                    <i class="el-icon-document-delete"></i>
                    <span>暂无内容</span>
                </div>
            </div>
        </div>
        
        <!-- 电信业务调整 -->
        <div class="docsAnnouncement11111_33333 docsBox" index="33333">
            <h2 class="section-title">
                <i class="el-icon-warning-outline"></i>
                电信业务调整
            </h2>
            <div class="announcement-container">
                <div v-if="contentLoading[2]" class="loading-wrapper">
                    <i class="el-icon-loading"></i>
                    <span>正在加载内容...</span>
                </div>
                <div v-else-if="htmlContent3" class="announcement-content">
                    <div v-html="htmlContent3" class="word-content"></div>
                </div>
                <div v-else class="empty-content">
                    <i class="el-icon-document-delete"></i>
                    <span>暂无内容</span>
                </div>
            </div>
        </div>
        
        <!-- 联通模板公告 -->
        <div class="docsAnnouncement11111_44444 docsBox" index="44444">
            <h2 class="section-title">
                <i class="el-icon-tickets"></i>
                联通模板公告
            </h2>
            <div class="announcement-container">
                <div v-if="contentLoading[3]" class="loading-wrapper">
                    <i class="el-icon-loading"></i>
                    <span>正在加载内容...</span>
                </div>
                <div v-else-if="htmlContent4" class="announcement-content">
                    <div v-html="htmlContent4" class="word-content"></div>
                </div>
                <div v-else class="empty-content">
                    <i class="el-icon-document-delete"></i>
                    <span>暂无内容</span>
                </div>
            </div>
        </div>
        
        <!-- 引流号码和链接报备指引 -->
        <div class="docsAnnouncement11111_55555 docsBox" index="55555">
            <h2 class="section-title">
                <i class="el-icon-guide"></i>
                引流号码和链接报备指引
            </h2>
            <div class="announcement-container">
                <div v-if="contentLoading[4]" class="loading-wrapper">
                    <i class="el-icon-loading"></i>
                    <span>正在加载内容...</span>
                </div>
                <div v-else-if="htmlContent5" class="announcement-content">
                    <div v-html="htmlContent5" class="word-content"></div>
                </div>
                <div v-else class="empty-content">
                    <i class="el-icon-document-delete"></i>
                    <span>暂无内容</span>
                </div>
            </div>
        </div>
        
        <!-- 短信签名报备规则 -->
        <div class="docsAnnouncement11111_66666 docsBox" index="66666">
            <h2 class="section-title">
                <i class="el-icon-edit-outline"></i>
                短信签名报备规则
            </h2>
            <div class="announcement-container">
                <div v-if="contentLoading[5]" class="loading-wrapper">
                    <i class="el-icon-loading"></i>
                    <span>正在加载内容...</span>
                </div>
                <div v-else-if="htmlContent6" class="announcement-content">
                    <div v-html="htmlContent6" class="word-content"></div>
                </div>
                <div v-else class="empty-content">
                    <i class="el-icon-document-delete"></i>
                    <span>暂无内容</span>
                </div>
            </div>
        </div>
        
        <!-- 图片预览对话框 -->
        <el-dialog 
            :visible.sync="imagePreviewVisible" 
            width="95%" 
            class="image-preview-dialog"
            :close-on-click-modal="true"
            :show-close="false"
            :modal-append-to-body="false"
            append-to-body
            custom-class="image-dialog">
            <div class="image-preview-container">
                <!-- 顶部工具栏 -->
                <div class="preview-toolbar">
                    <div class="toolbar-left">
                        <div class="preview-title">
                            <i class="el-icon-picture-outline"></i>
                            图片预览
                        </div>
                        <div class="preview-info" v-if="previewImages.length > 1">
                            {{ currentImageIndex + 1 }} / {{ previewImages.length }}
                        </div>
                    </div>
                    
                    <div class="toolbar-right">
                        <!-- 缩放控制 -->
                        <div class="zoom-controls">
                            <el-button 
                                type="text" 
                                icon="el-icon-zoom-out"
                                @click="zoomOut"
                                :disabled="zoomLevel <= 0.5"
                                class="zoom-btn">
                            </el-button>
                            <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
                            <el-button 
                                type="text" 
                                icon="el-icon-zoom-in"
                                @click="zoomIn"
                                :disabled="zoomLevel >= 3"
                                class="zoom-btn">
                            </el-button>
                            <el-button 
                                type="text" 
                                icon="el-icon-refresh-left"
                                @click="resetZoom"
                                class="zoom-btn">
                            </el-button>
                        </div>
                        
                        <!-- 下载按钮 -->
                        <el-button 
                            type="text" 
                            icon="el-icon-download"
                            @click="downloadImage"
                            class="action-btn">
                        </el-button>
                        
                        <!-- 关闭按钮 -->
                        <el-button 
                            type="text" 
                            icon="el-icon-close"
                            @click="imagePreviewVisible = false"
                            class="close-btn">
                        </el-button>
                    </div>
                </div>
                
                <!-- 图片显示区域 -->
                <div class="preview-content" @wheel="handleWheel" @click="closeOnContentClick">
                    <div class="preview-image-wrapper" :class="{ 'zoomed': zoomLevel !== 1 }">
                        <div 
                            class="image-container"
                            :style="imageContainerStyle"
                            @mousedown="startDrag"
                            @mousemove="drag"
                            @mouseup="endDrag"
                            @mouseleave="endDrag">
                            <img 
                                :src="currentPreviewImage" 
                                :alt="'预览图片 ' + (currentImageIndex + 1)"
                                class="preview-image"
                                :style="imageStyle"
                                @load="onImageLoad"
                                @error="onImageError"
                                @dragstart.prevent
                            />
                        </div>
                        
                        <!-- 加载状态 -->
                        <div v-if="imageLoading" class="image-loading">
                            <div class="loading-spinner">
                                <i class="el-icon-loading"></i>
                            </div>
                            <span>图片加载中...</span>
                        </div>
                        
                        <!-- 错误状态 -->
                        <div v-if="imageError" class="image-error">
                            <i class="el-icon-warning-outline"></i>
                            <span>图片加载失败</span>
                            <el-button size="mini" @click="retryLoad">重试</el-button>
                        </div>
                    </div>
                    
                    <!-- 导航按钮 -->
                    <div v-if="previewImages.length > 1" class="preview-navigation">
                        <el-button 
                            type="primary" 
                            icon="el-icon-arrow-left"
                            :disabled="currentImageIndex === 0"
                            @click="previousImage"
                            circle
                            class="nav-btn prev-btn"
                            size="large">
                        </el-button>
                        <el-button 
                            type="primary" 
                            icon="el-icon-arrow-right"
                            :disabled="currentImageIndex === previewImages.length - 1"
                            @click="nextImage"
                            circle
                            class="nav-btn next-btn"
                            size="large">
                        </el-button>
                    </div>
                </div>
                
                <!-- 底部缩略图导航 -->
                <div v-if="previewImages.length > 1" class="preview-thumbnails">
                    <div class="thumbnails-wrapper">
                        <div 
                            v-for="(img, index) in previewImages" 
                            :key="index"
                            class="thumbnail-item"
                            :class="{ active: index === currentImageIndex }"
                            @click="selectImage(index)">
                            <img :src="img.src" :alt="'缩略图 ' + (index + 1)" />
                            <div class="thumbnail-overlay">
                                <span>{{ index + 1 }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>    
</template>

<script>
import mammoth from 'mammoth';

export default {
    name: "docsAnnouncement",
    props: {
        searchKeyword: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            htmlContent1: "", // 系统公告
            htmlContent2: "", // 模板报备提示
            htmlContent3: "", // 电信业务调整
            htmlContent4: "", // 联通模板公告
            htmlContent5: "", // 引流号码和链接报备指引
            htmlContent6: "", // 短信签名报备规则
            contentLoading: [false, false, false, false, false, false], // 每个内容的加载状态
            
            // 图片预览相关
            imagePreviewVisible: false,
            previewImages: [],
            currentImageIndex: 0,
            imageLoading: false,
            imageError: false,
            
            // 缩放和拖拽相关
            zoomLevel: 1,
            isDragging: false,
            dragStartX: 0,
            dragStartY: 0,
            translateX: 0,
            translateY: 0,
            maxZoom: 3,
            minZoom: 0.5
        }
    },
    computed: {
        currentPreviewImage() {
            return this.previewImages[this.currentImageIndex] && this.previewImages[this.currentImageIndex].src || '';
        },
        
        imageStyle() {
            return {
                transform: `scale(${this.zoomLevel}) translate(${this.translateX}px, ${this.translateY}px)`,
                transition: this.isDragging ? 'none' : 'transform 0.3s ease',
                cursor: this.zoomLevel > 1 ? 'move' : 'default'
            };
        },
        
        imageContainerStyle() {
            return {
                overflow: this.zoomLevel > 1 ? 'hidden' : 'visible'
            };
        }
    },
    async mounted() {
        await this.loadAllDocuments();
        this.initImagePreview();
    },
    watch: {
        searchKeyword(newVal) {
            if (newVal) {
                this.highlightSearchKeyword();
            }
        },
        
        // 监听内容变化，重新初始化图片预览
        htmlContent1() {
            this.$nextTick(() => this.initImagePreview());
        },
        htmlContent2() {
            this.$nextTick(() => this.initImagePreview());
        },
        htmlContent3() {
            this.$nextTick(() => this.initImagePreview());
        },
        htmlContent4() {
            this.$nextTick(() => this.initImagePreview());
        },
        htmlContent5() {
            this.$nextTick(() => this.initImagePreview());
        },
        htmlContent6() {
            this.$nextTick(() => this.initImagePreview());
        }
    },
    methods: {
        async loadAllDocuments() {
            try {
                const options = {
                    styleMap: [
                        "p[style-name='Heading 1'] => h1:fresh",
                        "p[style-name='Heading 2'] => h2:fresh",
                        "p[style-name='Heading 3'] => h3:fresh",
                        "p[style-name='Normal'] => p:fresh",
                        "p[style-name='Title'] => p.doc-title:fresh",
                        "p[style-name='Subtitle'] => p.subtitle:fresh",
                        "r[style-name='Strong'] => strong",
                        "r[style-name='Emphasis'] => em",
                        "table => table.doc-table",
                        "tr => tr",
                        "td => td"
                    ],
                    preserveStyles: true,
                    includeDefaultStyleMap: true,
                    ignoreEmptyParagraphs: false
                };

                // 并行加载所有文档
                const loadPromises = [
                    this.loadDocument('/announcement.docx', options, 0),
                    this.loadDocument('/关于短信模板报备更新的温馨提示.docx', options, 1),
                    this.loadDocument('/【电信业务调整通知】.docx', options, 2),
                    this.loadDocument('/联通模板公告.docx', options, 3),
                    this.loadDocument('/助通-引流号码和链接报备指引客户版.docx', options, 4),
                    this.loadDocument('/短信签名报备规则.docx', options, 5)
                ];
                
                await Promise.all(loadPromises);
                
            } catch (error) {
                console.error('加载文档失败:', error);
                this.$message.error('部分文档加载失败');
            }
        },

        async loadDocument(filePath, options, index) {
            // 设置加载状态
            this.$set(this.contentLoading, index, true);
            
            try {
                const response = await fetch(filePath);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const arrayBuffer = await response.arrayBuffer();
                const result = await mammoth.convertToHtml({ 
                    arrayBuffer: arrayBuffer,
                    styleMap: options.styleMap,
                    includeDefaultStyleMap: options.includeDefaultStyleMap,
                    ignoreEmptyParagraphs: options.ignoreEmptyParagraphs
                });

                // 处理HTML内容
                let processedHtml = this.processHtml(result.value, index);
                
                // 根据索引设置对应的内容
                switch(index) {
                    case 0:
                        this.htmlContent1 = processedHtml;
                        break;
                    case 1:
                        this.htmlContent2 = processedHtml;
                        break;
                    case 2:
                        this.htmlContent3 = processedHtml;
                        break;
                    case 3:
                        this.htmlContent4 = processedHtml;
                        break;
                    case 4:
                        this.htmlContent5 = processedHtml;
                        break;
                    case 5:
                        this.htmlContent6 = processedHtml;
                        break;
                }
                
            } catch (error) {
                console.error(`加载文档 ${filePath} 失败:`, error);
                // 设置错误提示
                const errorMsg = `<div class="error-message">
                    <i class="el-icon-warning"></i>
                    <span>文档加载失败，请稍后重试</span>
                </div>`;
                switch(index) {
                    case 0:
                        this.htmlContent1 = errorMsg;
                        break;
                    case 1:
                        this.htmlContent2 = errorMsg;
                        break;
                    case 2:
                        this.htmlContent3 = errorMsg;
                        break;
                    case 3:
                        this.htmlContent4 = errorMsg;
                        break;
                    case 4:
                        this.htmlContent5 = errorMsg;
                        break;
                    case 5:
                        this.htmlContent6 = errorMsg;
                        break;
                }
            } finally {
                // 取消加载状态
                this.$set(this.contentLoading, index, false);
            }
        },
        
        processHtml(html, index) {
            // 处理标题
            const titlePatterns = [
                { pattern: /<p>(.*?公告.*?)<\/p>/gi, replacement: '<p class="doc-title">$1</p>' },
                { pattern: /<p>(.*?关于.*?)<\/p>/gi, replacement: '<p class="doc-title">$1</p>' },
                { pattern: /<p>(.*?通知.*?)<\/p>/gi, replacement: '<p class="doc-title">$1</p>' },
                { pattern: /<p>(.*?提示.*?)<\/p>/gi, replacement: '<p class="doc-title">$1</p>' }
            ];
            
            let processedHtml = html;
            titlePatterns.forEach(({ pattern, replacement }) => {
                processedHtml = processedHtml.replace(pattern, replacement);
            });
            
            // 添加时间戳
            const now = new Date().toLocaleString('zh-CN');
            processedHtml = `
                <div class="doc-header">
                    <span class="update-time">更新时间：${now}</span>
                </div>
                ${processedHtml}
            `;
            
            return processedHtml;
        },
        
        highlightSearchKeyword() {
            // 实现搜索关键词高亮
            this.$nextTick(() => {
                const keyword = this.searchKeyword;
                if (!keyword) return;
                
                const contents = this.$el.querySelectorAll('.word-content');
                contents.forEach(content => {
                    const walker = document.createTreeWalker(
                        content,
                        NodeFilter.SHOW_TEXT,
                        null,
                        false
                    );
                    
                    const textNodes = [];
                    while (walker.nextNode()) {
                        textNodes.push(walker.currentNode);
                    }
                    
                    textNodes.forEach(node => {
                        const text = node.textContent;
                        if (text.includes(keyword)) {
                            const span = document.createElement('span');
                            span.innerHTML = text.replace(
                                new RegExp(keyword, 'gi'),
                                `<span class="search-highlight">${keyword}</span>`
                            );
                            node.parentNode.replaceChild(span, node);
                        }
                    });
                });
            });
        },
        
        // 初始化图片预览功能
        initImagePreview() {
            this.$nextTick(() => {
                // 为所有图片添加点击事件
                const images = this.$el.querySelectorAll('.word-content img');
                images.forEach((img, index) => {
                    img.addEventListener('click', () => {
                        this.openImagePreview(img, index);
                    });
                    
                    // 添加图片加载完成事件，重新绑定点击事件
                    img.addEventListener('load', () => {
                        img.style.cursor = 'pointer';
                    });
                });
            });
        },
        
        // 打开图片预览
        openImagePreview(clickedImg, clickedIndex) {
            // 收集所有图片
            const allImages = this.$el.querySelectorAll('.word-content img');
            this.previewImages = Array.from(allImages).map((img, index) => ({
                src: img.src,
                alt: img.alt || `图片 ${index + 1}`,
                index: index
            }));
            
            // 重置状态
            this.resetImageState();
            
            // 设置当前图片索引
            this.currentImageIndex = clickedIndex;
            this.imageLoading = true;
            this.imageError = false;
            this.imagePreviewVisible = true;
        },
        
        // 上一张图片
        previousImage() {
            if (this.currentImageIndex > 0) {
                this.resetImageState();
                this.currentImageIndex--;
                this.imageLoading = true;
                this.imageError = false;
            }
        },
        
        // 下一张图片
        nextImage() {
            if (this.currentImageIndex < this.previewImages.length - 1) {
                this.resetImageState();
                this.currentImageIndex++;
                this.imageLoading = true;
                this.imageError = false;
            }
        },
        
        // 选择特定图片
        selectImage(index) {
            if (index !== this.currentImageIndex) {
                this.resetImageState();
                this.currentImageIndex = index;
                this.imageLoading = true;
                this.imageError = false;
            }
        },
        
        // 图片加载完成
        onImageLoad() {
            this.imageLoading = false;
            this.imageError = false;
        },
        
        // 图片加载失败
        onImageError() {
            this.imageLoading = false;
            this.imageError = true;
        },
        
        // 重试加载图片
        retryLoad() {
            this.imageLoading = true;
            this.imageError = false;
            // 强制重新加载图片
            const img = new Image();
            img.onload = () => {
                this.imageLoading = false;
                this.imageError = false;
            };
            img.onerror = () => {
                this.imageLoading = false;
                this.imageError = true;
                this.$message.error('图片重新加载失败');
            };
            img.src = this.currentPreviewImage + '?t=' + Date.now();
        },
        
        // 重置图片状态（缩放、位移等）
        resetImageState() {
            this.zoomLevel = 1;
            this.translateX = 0;
            this.translateY = 0;
            this.isDragging = false;
        },
        
        // 缩放功能
        zoomIn() {
            if (this.zoomLevel < this.maxZoom) {
                this.zoomLevel = Math.min(this.zoomLevel + 0.25, this.maxZoom);
            }
        },
        
        zoomOut() {
            if (this.zoomLevel > this.minZoom) {
                this.zoomLevel = Math.max(this.zoomLevel - 0.25, this.minZoom);
                // 缩小时调整位移，避免图片跑出视野
                this.adjustTranslateOnZoom();
            }
        },
        
        resetZoom() {
            this.zoomLevel = 1;
            this.translateX = 0;
            this.translateY = 0;
        },
        
        // 滚轮缩放
        handleWheel(event) {
            event.preventDefault();
            const delta = event.deltaY;
            if (delta < 0) {
                this.zoomIn();
            } else {
                this.zoomOut();
            }
        },
        
        // 拖拽功能
        startDrag(event) {
            if (this.zoomLevel > 1) {
                this.isDragging = true;
                this.dragStartX = event.clientX - this.translateX;
                this.dragStartY = event.clientY - this.translateY;
                event.preventDefault();
            }
        },
        
        drag(event) {
            if (this.isDragging && this.zoomLevel > 1) {
                this.translateX = event.clientX - this.dragStartX;
                this.translateY = event.clientY - this.dragStartY;
                event.preventDefault();
            }
        },
        
        endDrag() {
            this.isDragging = false;
        },
        
        // 缩放时调整位移
        adjustTranslateOnZoom() {
            const maxTranslate = (this.zoomLevel - 1) * 200; // 根据缩放级别限制位移
            this.translateX = Math.max(-maxTranslate, Math.min(maxTranslate, this.translateX));
            this.translateY = Math.max(-maxTranslate, Math.min(maxTranslate, this.translateY));
        },
        
        // 下载图片
        downloadImage() {
            const currentImage = this.previewImages[this.currentImageIndex];
            if (currentImage) {
                const link = document.createElement('a');
                link.href = currentImage.src;
                link.download = `image_${this.currentImageIndex + 1}.jpg`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                this.$message.success('图片下载中...');
            }
        },
        
        // 点击背景关闭
        closeOnContentClick(event) {
            if (event.target.classList.contains('preview-content')) {
                this.imagePreviewVisible = false;
            }
        },
        
        // 键盘导航
        handleKeydown(event) {
            if (!this.imagePreviewVisible) return;
            
            switch(event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    this.previousImage();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    this.nextImage();
                    break;
                case 'Escape':
                    event.preventDefault();
                    this.imagePreviewVisible = false;
                    break;
                case '+':
                case '=':
                    event.preventDefault();
                    this.zoomIn();
                    break;
                case '-':
                    event.preventDefault();
                    this.zoomOut();
                    break;
                case '0':
                    event.preventDefault();
                    this.resetZoom();
                    break;
                case 's':
                case 'S':
                    if (event.ctrlKey || event.metaKey) {
                        event.preventDefault();
                        this.downloadImage();
                    }
                    break;
            }
        }
    },
    
    created() {
        // 添加键盘事件监听
        document.addEventListener('keydown', this.handleKeydown);
    },
    
    beforeDestroy() {
        // 移除键盘事件监听
        document.removeEventListener('keydown', this.handleKeydown);
    }
}
</script>

<style lang="less" scoped>
@import '~@/styles/help-center-content.less';

// 公告容器样式
.announcement-container {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 24px;
    min-height: 200px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    }
}

// 加载状态
.loading-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #909399;
    
    i {
        font-size: 32px;
        margin-bottom: 12px;
        color: #667eea;
    }
    
    span {
        font-size: 14px;
    }
}

// 空内容状态
.empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #c0c4cc;
    
    i {
        font-size: 48px;
        margin-bottom: 12px;
    }
    
    span {
        font-size: 14px;
    }
}

// 错误提示
.error-message {
    text-align: center;
    padding: 40px;
    color: #f56c6c;
    
    i {
        font-size: 32px;
        display: block;
        margin-bottom: 12px;
    }
}

// 节标题样式
.section-title {
    i {
        margin-right: 8px;
        vertical-align: middle;
    }
}

// 图片预览对话框样式
.image-preview-dialog {
    /deep/ .el-dialog {
        background: rgba(0, 0, 0, 0.95);
        border-radius: 12px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
        
        .el-dialog__header {
            padding: 0;
            border: none;
        }
        
        .el-dialog__body {
            padding: 0;
            height: calc(100vh - 120px);
            max-height: 900px;
        }
        
        .el-dialog__close {
            display: none;
        }
    }
}

.image-preview-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    // 顶部工具栏
    .preview-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        background: rgba(0, 0, 0, 0.8);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 20px;
            
            .preview-title {
                display: flex;
                align-items: center;
                color: #fff;
                font-size: 16px;
                font-weight: 500;
                
                i {
                    margin-right: 8px;
                    font-size: 18px;
                    color: #667eea;
                }
            }
            
            .preview-info {
                color: #ccc;
                font-size: 14px;
                background: rgba(255, 255, 255, 0.1);
                padding: 4px 12px;
                border-radius: 12px;
            }
        }
        
        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .zoom-controls {
                display: flex;
                align-items: center;
                gap: 8px;
                background: rgba(255, 255, 255, 0.1);
                padding: 4px 8px;
                border-radius: 20px;
                
                .zoom-btn {
                    color: #fff;
                    padding: 4px;
                    
                    &:hover {
                        color: #667eea;
                        background: rgba(102, 126, 234, 0.2);
                    }
                    
                    &:disabled {
                        color: rgba(255, 255, 255, 0.3);
                    }
                }
                
                .zoom-level {
                    color: #fff;
                    font-size: 12px;
                    min-width: 40px;
                    text-align: center;
                    font-weight: 500;
                }
            }
            
            .action-btn, .close-btn {
                color: #fff;
                padding: 8px;
                border-radius: 6px;
                transition: all 0.3s ease;
                
                &:hover {
                    background: rgba(255, 255, 255, 0.1);
                    color: #667eea;
                }
            }
            
            .close-btn {
                &:hover {
                    background: rgba(239, 68, 68, 0.2);
                    color: #ef4444;
                }
            }
        }
    }
    
    // 图片展示区域
    .preview-content {
        flex: 1;
        position: relative;
        overflow: hidden;
        background: radial-gradient(circle at center, rgba(255, 255, 255, 0.05) 0%, rgba(0, 0, 0, 0.3) 100%);
        
        .preview-image-wrapper {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            
            &.zoomed {
                .image-container {
                    cursor: grab;
                    
                    &:active {
                        cursor: grabbing;
                    }
                }
            }
            
            .image-container {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                
                .preview-image {
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                    border-radius: 8px;
                    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
                    user-select: none;
                    pointer-events: none;
                }
            }
            
            // 加载状态
            .image-loading {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #fff;
                text-align: center;
                
                .loading-spinner {
                    margin-bottom: 16px;
                    
                    i {
                        font-size: 36px;
                        color: #667eea;
                        animation: spin 1s linear infinite;
                    }
                }
                
                span {
                    font-size: 14px;
                    color: #ccc;
                }
            }
            
            // 错误状态
            .image-error {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #fff;
                text-align: center;
                
                i {
                    font-size: 48px;
                    color: #f56c6c;
                    margin-bottom: 16px;
                    display: block;
                }
                
                span {
                    font-size: 14px;
                    color: #ccc;
                    margin-bottom: 16px;
                    display: block;
                }
                
                .el-button {
                    background: rgba(102, 126, 234, 0.2);
                    border-color: #667eea;
                    color: #667eea;
                    
                    &:hover {
                        background: #667eea;
                        color: #fff;
                    }
                }
            }
        }
        
        // 导航按钮
        .preview-navigation {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            transform: translateY(-50%);
            display: flex;
            justify-content: space-between;
            pointer-events: none;
            z-index: 10;
            
            .nav-btn {
                pointer-events: auto;
                width: 56px;
                height: 56px;
                background: rgba(0, 0, 0, 0.6);
                border: 2px solid rgba(255, 255, 255, 0.2);
                color: #fff;
                margin: 0 24px;
                backdrop-filter: blur(10px);
                transition: all 0.3s ease;
                
                &:hover {
                    background: rgba(102, 126, 234, 0.8);
                    border-color: #667eea;
                    transform: scale(1.05);
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
                }
                
                &:disabled {
                    background: rgba(255, 255, 255, 0.1);
                    color: rgba(255, 255, 255, 0.3);
                    border-color: rgba(255, 255, 255, 0.1);
                    transform: none;
                    box-shadow: none;
                }
                
                i {
                    font-size: 20px;
                }
            }
        }
    }
    
    // 底部缩略图
    .preview-thumbnails {
        background: rgba(0, 0, 0, 0.8);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding: 16px 24px;
        backdrop-filter: blur(10px);
        
        .thumbnails-wrapper {
            display: flex;
            justify-content: center;
            gap: 12px;
            max-width: 100%;
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(102, 126, 234, 0.5) transparent;
            
            &::-webkit-scrollbar {
                height: 6px;
            }
            
            &::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }
            
            &::-webkit-scrollbar-thumb {
                background: rgba(102, 126, 234, 0.5);
                border-radius: 3px;
                
                &:hover {
                    background: rgba(102, 126, 234, 0.7);
                }
            }
            
            .thumbnail-item {
                position: relative;
                flex-shrink: 0;
                width: 64px;
                height: 64px;
                border-radius: 8px;
                overflow: hidden;
                cursor: pointer;
                border: 2px solid transparent;
                transition: all 0.3s ease;
                
                &:hover {
                    border-color: rgba(102, 126, 234, 0.6);
                    transform: scale(1.05);
                }
                
                &.active {
                    border-color: #667eea;
                    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
                    transform: scale(1.05);
                }
                
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                
                .thumbnail-overlay {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    background: rgba(0, 0, 0, 0.7);
                    color: #fff;
                    font-size: 10px;
                    padding: 2px 6px;
                    border-radius: 6px 0 6px 0;
                    font-weight: 500;
                }
            }
        }
    }
}

// 动画定义
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
    .image-preview-dialog {
        /deep/ .el-dialog {
            width: 98% !important;
            margin: 10px !important;
            
            .el-dialog__body {
                height: calc(100vh - 80px);
            }
        }
    }
    
    .image-preview-container {
        .preview-toolbar {
            padding: 12px 16px;
            
            .toolbar-left {
                gap: 12px;
                
                .preview-title {
                    font-size: 14px;
                    
                    i {
                        font-size: 16px;
                    }
                }
                
                .preview-info {
                    font-size: 12px;
                    padding: 2px 8px;
                }
            }
            
            .toolbar-right {
                gap: 8px;
                
                .zoom-controls {
                    padding: 2px 6px;
                    
                    .zoom-level {
                        font-size: 11px;
                        min-width: 35px;
                    }
                }
            }
        }
        
        .preview-content {
            .preview-navigation {
                .nav-btn {
                    width: 48px;
                    height: 48px;
                    margin: 0 16px;
                    
                    i {
                        font-size: 18px;
                    }
                }
            }
        }
        
        .preview-thumbnails {
            padding: 12px 16px;
            
            .thumbnails-wrapper {
                gap: 8px;
                
                .thumbnail-item {
                    width: 56px;
                    height: 56px;
                    
                    .thumbnail-overlay {
                        font-size: 9px;
                        padding: 1px 4px;
                    }
                }
            }
        }
    }
}
</style>

<style>
/* Word文档内容样式 */
.announcement-content {
    .word-content {
        font-size: 15px;
        line-height: 1.8;
        color: #606266;
        
        .doc-header {
            text-align: right;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #ebeef5;
            
            .update-time {
                font-size: 12px;
                color: #909399;
            }
        }
        
        p {
            margin: 12px 0;
        }
        
        .doc-title {
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin: 24px 0;
            padding: 12px 0;
            border-bottom: 2px solid #667eea;
            display: inline-block;
            width: 100%;
        }
        
        /* 表格样式 */
        .doc-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            
            td, th {
                border: 1px solid #e4e7ed;
                padding: 12px;
                text-align: left;
            }
            
            th {
                background: #f5f7fa;
                font-weight: 600;
                color: #303133;
            }
            
            tr:hover {
                background: #f5f7fa;
            }
        }
        
        /* 图片样式优化 */
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        img:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            transform: scale(1.02);
        }
        
        /* 针对特别大的图片进行额外限制 */
        img[width], img[style*="width"] {
            max-width: 800px !important;
            width: auto !important;
        }
        
        /* 针对表格中的图片 */
        .doc-table img {
            max-width: 200px;
            margin: 8px auto;
        }
        
        /* 图片容器样式 */
        p img {
            margin: 20px auto;
            display: block;
        }
        
        /* 响应式图片处理 */
        @media (max-width: 768px) {
            img {
                max-width: 100%;
                margin: 15px auto;
            }
            
            img[width], img[style*="width"] {
                max-width: 100% !important;
            }
        }
        
        /* 超大图片额外限制 */
        img[width*="1000"], img[width*="1200"], img[width*="1500"] {
            max-width: 600px !important;
        }
        
        /* 图片加载失败处理 */
        img::before {
            content: "图片加载中...";
            display: block;
            text-align: center;
            color: #999;
            font-size: 14px;
            padding: 20px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        
        /* 列表样式 */
        ul {
            list-style-type: disc;
            padding-left: 30px;
            margin: 12px 0;
            
            li {
                margin: 6px 0;
            }
        }
        
        ol {
            list-style-type: decimal;
            padding-left: 30px;
            margin: 12px 0;
            
            li {
                margin: 6px 0;
            }
        }
        
        /* 强调文本 */
        strong {
            font-weight: 600;
            color: #303133;
        }
        
        em {
            font-style: italic;
            color: #667eea;
        }
        
        /* 签名和日期样式 */
        p[style*="text-align: right"] {
            text-align: right;
            margin-top: 30px;
            color: #606266;
            font-style: italic;
        }
    }
}
</style> 