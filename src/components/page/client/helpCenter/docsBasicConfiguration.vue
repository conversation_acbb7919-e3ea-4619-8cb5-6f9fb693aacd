<template>
    <div class="docs-container docsBasicConfiguration1">
        <h1 class="main-title">个人账户</h1>
        
        <!-- 账户问题 -->
        <div class="docsBasicConfiguration1_1 docsBox" index="1">
            <h2 class="section-title">账户问题</h2>
            <div class="qa-container">
                <div class="question-item">
                    <div class="question-title">忘记登录密码了怎么办？哪里可以修改密码？</div>
                    <div class="answer-content">
                        若忘记登录密码，可以先使用<span class="highlight">手机号验证码登录</span>方式。
                        用户可以使用原密码验证来修改登录密码。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">在哪里修改基本资料？可以修改头像吗？</div>
                    <div class="answer-content">
                        用户不可以修改基本的基本信息，若需修改可以找客服人员修改您的基本信息；
                        用户可以在<span class="highlight">个人信息设置</span>中修改您的专属头像。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">平台是否可以设置IP？</div>
                    <div class="answer-content">
                        平台上可以设置<span class="highlight">IP绑定</span>，如果设置了IP绑定，
                        客户端和接口提交将都会受限制，只能使用绑定的IP登录或者接口提交信息才可以，
                        其他IP都无法提交信息。
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 基础配置问题 -->
        <div class="docsBasicConfiguration1_2 docsBox" index="2">
            <h2 class="section-title">基础配置问题</h2>
            <div class="qa-container">
                <div class="question-item">
                    <div class="question-title">签名和模板审核是否可以通知？</div>
                    <div class="answer-content">
                        可以，在<span class="highlight">通知告警人</span>中设置后，
                        当审核有进展后会以邮件或短信的形式通知；
                        若没有设置告警联系人，也可以在<span class="highlight">消息管理审批提醒</span>中查看。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">设置通知告警联系人有什么作用？</div>
                    <div class="answer-content">
                        设置告警联系人，当人工审核有消息时会通过<span class="highlight">短信或邮件</span>进行提醒。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">是否可以修改接口信息？</div>
                    <div class="answer-content">
                        您的接口信息将保存在基础配置页面，您可在基础配置中修改您的<span class="highlight">接口密码</span>。
                        <div class="tip-box warning" style="margin-top: 12px;">
                            请妥善保管您的密码，不要泄露，避免造成不必要的损失。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>    
</template>

<script>
export default {
    name: "docsBasicConfiguration",
    props: {
        searchKeyword: {
            type: String,
            default: ''
        }
    },
    watch: {
        searchKeyword(newVal) {
            if (newVal) {
                this.highlightSearchKeyword();
            }
        }
    },
    methods: {
        highlightSearchKeyword() {
            // 实现搜索关键词高亮
            this.$nextTick(() => {
                const keyword = this.searchKeyword;
                if (!keyword) return;
                
                const content = this.$el;
                const walker = document.createTreeWalker(
                    content,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                const textNodes = [];
                while (walker.nextNode()) {
                    textNodes.push(walker.currentNode);
                }
                
                textNodes.forEach(node => {
                    const text = node.textContent;
                    if (text.includes(keyword)) {
                        const span = document.createElement('span');
                        span.innerHTML = text.replace(
                            new RegExp(keyword, 'gi'),
                            `<span class="search-highlight">${keyword}</span>`
                        );
                        node.parentNode.replaceChild(span, node);
                    }
                });
            });
        }
    }
}
</script>

<style lang="less" scoped>
@import '~@/styles/help-center-content.less';
</style>
