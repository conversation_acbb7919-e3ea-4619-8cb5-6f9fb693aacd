<template>
    <div class="docs-container docsContact1111">
        <h1 class="main-title">审核常见问题</h1>
        
        <!-- 规范和要求 -->
        <div class="docsContact1111_1111 docsBox" index="1111">
            <h2 class="section-title">规范和要求</h2>
            <div class="qa-container">
                <div class="question-item">
                    <div class="question-title">短信签名和模板要求规范？</div>
                    <div class="answer-content">
                        针对短信签名与模板内容请按<span class="highlight">签名规则</span>和<span class="highlight">模板规则</span>提交，
                        审核通过后方可使用。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">短信签名要求是什么？</div>
                    <div class="answer-content">
                        签名字数要求为不超过<span class="highlight">20个字</span>，可包含中文数字和字母，不含违法违规字样。
                        提交签名时需提供对应的资料，方便进行报备。
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 审核问题 -->
        <div class="docsContact1111_2222 docsBox" index="2222">
            <h2 class="section-title">审核问题</h2>
            <div class="qa-container">
                <div class="question-item">
                    <div class="question-title">签名审核大概需要多久？</div>
                    <div class="answer-content">
                        签名审核将在<span class="highlight">1个工作日</span>内完成，审核通过即可使用，
                        如您急用可联系客服，我们将为您加速审核。
                        <div class="tip-box" style="margin-top: 12px;">
                            客服工作时间：8:00-22:00（全年无休）
                        </div>
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">短信模板审核大概需要多久？</div>
                    <div class="answer-content">
                        模板审核将在<span class="highlight">1个工作日</span>内完成，审核通过即可使用，
                        如您急用可联系客服，我们将为您加速审核。
                        <div class="tip-box" style="margin-top: 12px;">
                            客服工作时间：8:00-22:00（全年无休）
                        </div>
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">为什么会出现批量发送短信被驳回？</div>
                    <div class="answer-content">
                        如出现批量短信发送驳回的情况，可能为<span class="highlight">"敏感词拦截"</span>进入人工审核，
                        请与客服联系进行处理。
                    </div>
                </div>
                
                <div class="question-item">
                    <div class="question-title">无法通过审核的原因是什么？</div>
                    <div class="answer-content">
                        融合云通信平台有完善的文字过滤系统及人工审核流程，
                        如果您的内容含有<span class="highlight">敏感词</span>或者内容违规违法，
                        将无法通过审核，请及时修改短信内容。
                    </div>
                </div>
            </div>
        </div>
    </div>    
</template>

<script>
export default {
    name: "docsContact",
    props: {
        searchKeyword: {
            type: String,
            default: ''
        }
    },
    watch: {
        searchKeyword(newVal) {
            if (newVal) {
                this.highlightSearchKeyword();
            }
        }
    },
    methods: {
        highlightSearchKeyword() {
            // 实现搜索关键词高亮
            this.$nextTick(() => {
                const keyword = this.searchKeyword;
                if (!keyword) return;
                
                const content = this.$el;
                const walker = document.createTreeWalker(
                    content,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );
                
                const textNodes = [];
                while (walker.nextNode()) {
                    textNodes.push(walker.currentNode);
                }
                
                textNodes.forEach(node => {
                    const text = node.textContent;
                    if (text.includes(keyword)) {
                        const span = document.createElement('span');
                        span.innerHTML = text.replace(
                            new RegExp(keyword, 'gi'),
                            `<span class="search-highlight">${keyword}</span>`
                        );
                        node.parentNode.replaceChild(span, node);
                    }
                });
            });
        }
    }
}
</script>

<style lang="less" scoped>
@import '~@/styles/help-center-content.less';
</style>