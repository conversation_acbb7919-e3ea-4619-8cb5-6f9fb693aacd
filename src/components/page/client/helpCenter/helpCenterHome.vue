<template>
    <div class="help-center-container">
        <!-- 顶部搜索栏 -->
        <div class="help-center-header">
            <div class="header-content">
                <h1 class="header-title">
                    <i class="el-icon-question"></i>
                    帮助中心
                </h1>
                <!-- <div class="header-search">
                    <el-input
                        v-model="searchKeyword"
                        placeholder="搜索你想了解的问题..."
                        prefix-icon="el-icon-search"
                        clearable
                        @keyup.enter.native="handleSearch"
                        class="search-input"
                    >
                        <el-button 
                            slot="append" 
                            icon="el-icon-search"
                            @click="handleSearch"
                        >
                            搜索
                        </el-button>
                    </el-input>
                </div> -->
            </div>
        </div>

        <!-- 主体内容区 -->
        <div class="help-center-body">
            <el-container class="main-container">
                <!-- 左侧导航 -->
                <el-aside width="280px" class="help-sidebar">
                    <div class="sidebar-content">
                        <div class="nav-header">
                            <i class="el-icon-notebook-2"></i>
                            <span>帮助分类</span>
                        </div>
                        <help-center-left  
                            @clickWhere='handleNavClick' 
                            @clickFirstIndex='handleCategoryChange' 
                            :defaultActive='activeIndex'
                            class="nav-menu"
                        ></help-center-left>
                    </div>
                </el-aside>

                <!-- 右侧内容区 -->
                <el-main class="help-content" id="docsBody">
                    <!-- 面包屑导航 -->
                    <div class="breadcrumb-wrapper">
                        <el-breadcrumb separator-class="el-icon-arrow-right">
                            <el-breadcrumb-item :to="{ path: '/home' }">
                                <i class="el-icon-house"></i> 首页
                            </el-breadcrumb-item>
                            <el-breadcrumb-item>帮助中心</el-breadcrumb-item>
                            <el-breadcrumb-item>{{ currentCategoryName }}</el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>

                    <!-- 内容加载动画 -->
                    <transition name="content-fade" mode="out-in">
                        <div v-if="contentLoading" class="loading-container">
                            <i class="el-icon-loading"></i>
                            <span>内容加载中...</span>
                        </div>
                        <div v-else class="content-wrapper" :key="currentTabComponent">
                            <component 
                                v-bind:is="currentTabComponent" 
                                :hostflag="hostflag"
                                :searchKeyword="searchKeyword"
                            ></component>
                        </div>
                    </transition>

                    <!-- 返回顶部按钮 -->
                    <transition name="fade">
                        <div 
                            class="back-to-top" 
                            @click="backToTop" 
                            v-show="showBackTop"
                            :title="'返回顶部'"
                        >
                            <i class="el-icon-arrow-up"></i>
                        </div>  
                    </transition>

                    <!-- 快速反馈 -->
                    <!-- <div class="quick-feedback">
                        <el-tooltip content="意见反馈" placement="left">
                            <div class="feedback-btn" @click="showFeedback">
                                <i class="el-icon-edit"></i>
                            </div>
                        </el-tooltip>
                    </div> -->
                </el-main>
            </el-container>
        </div>

        <!-- 反馈对话框 -->
        <el-dialog 
            title="意见反馈" 
            :visible.sync="feedbackVisible"
            width="500px"
            :close-on-click-modal="false"
        >
            <el-form :model="feedbackForm" :rules="feedbackRules" ref="feedbackForm">
                <el-form-item label="反馈类型" prop="type">
                    <el-select v-model="feedbackForm.type" placeholder="请选择反馈类型">
                        <el-option label="内容错误" value="content"></el-option>
                        <el-option label="功能建议" value="feature"></el-option>
                        <el-option label="其他问题" value="other"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="反馈内容" prop="content">
                    <el-input 
                        type="textarea" 
                        v-model="feedbackForm.content"
                        :rows="4"
                        placeholder="请详细描述您的问题或建议..."
                    ></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="feedbackVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitFeedback" :loading="feedbackLoading">
                    提 交
                </el-button>
            </span>
        </el-dialog>
    </div>    
</template>

<script>
import helpCenterLeft from './commponents/helpCenterLeft'
import docsBasicConfiguration from './docsBasicConfiguration'
import docsBlacklistManage from './docsBlacklistManage'
import docsContact from './docsContact'
import docsNotificationAlarm from './docsNotificationAlarm'
import docsAnnouncement from './docsAnnouncement'

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

export default {
    name: "helpCenterHome",
    components:{
        helpCenterLeft,
        docsBasicConfiguration,
        docsBlacklistManage,
        docsContact,
        docsNotificationAlarm,
        docsAnnouncement
    },
    data(){
        return {
            // 组件状态
            currentTabComponent: 'docsBasicConfiguration',
            activeIndex: '1',
            showBackTop: false,
            contentLoading: false,
            
            // 搜索相关
            searchKeyword: '',
            searchResults: [],
            
            // 反馈相关
            feedbackVisible: false,
            feedbackLoading: false,
            feedbackForm: {
                type: '',
                content: ''
            },
            feedbackRules: {
                type: [
                    { required: true, message: '请选择反馈类型', trigger: 'change' }
                ],
                content: [
                    { required: true, message: '请输入反馈内容', trigger: 'blur' },
                    { min: 10, message: '反馈内容至少10个字符', trigger: 'blur' }
                ]
            },
            
            // 分类映射
            categoryMap: {
                'docsBasicConfiguration': '个人账户',
                'docsNotificationAlarm': '支付问题',
                'docsBlacklistManage': '短信服务问题',
                'docsContact': '审核常见问题',
                'docsAnnouncement': '动态与公告'
            },
            
            // 滚动相关
            scrollHandler: null,
            
            // 站点相关
            hostname: window.location.hostname,
            nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
            hostflag: true
        }
    },
    
    computed: {
        currentCategoryName() {
            return this.categoryMap[this.currentTabComponent] || '帮助文档';
        }
    },
    
    created() {
        // 初始化站点标识
        this.initHostFlag();
        
        // 创建防抖的滚动处理函数
        this.scrollHandler = throttle(this.handleScroll.bind(this), 100);
    },
    
    mounted() {
        // 监听滚动事件
        const scrollContainer = document.getElementById('docsBody');
        if (scrollContainer) {
            scrollContainer.addEventListener('scroll', this.scrollHandler, { passive: true });
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize, { passive: true });
        
        // 初始化高度
        this.handleResize();
        
        // 检查路由参数
        this.checkRouteParams();
    },
    
    beforeDestroy() {
        // 清理事件监听
        const scrollContainer = document.getElementById('docsBody');
        if (scrollContainer) {
            scrollContainer.removeEventListener('scroll', this.scrollHandler);
        }
        window.removeEventListener('resize', this.handleResize);
    },
    
    methods: {
        // 初始化站点标识
        initHostFlag() {
            this.hostflag = !this.nameTile.includes(this.hostname);
        },
        
        // 检查路由参数
        checkRouteParams() {
            const { category, index } = this.$route.query;
            if (category && this.categoryMap[category]) {
                this.currentTabComponent = category;
                this.activeIndex = index || '1';
            }
        },
        
        // 处理分类切换
        handleCategoryChange(val) {
            this.contentLoading = true;
            this.activeIndex = val.num;
            
            // 模拟加载效果
            setTimeout(() => {
                this.currentTabComponent = val.url;
                this.contentLoading = false;
                
                // 更新URL参数
                this.$router.push({
                    query: {
                        category: val.url,
                        index: val.num
                    }
                });
            }, 300);
        },
        
        // 处理导航点击（平滑滚动）
        handleNavClick(val) {
            this.$nextTick(() => {
                const targetClass = `${val[0]}_${val[1]}`;
                const targetElement = document.getElementsByClassName(targetClass)[0];
                
                if (targetElement) {
                    const container = document.getElementById('docsBody');
                    const targetPosition = targetElement.offsetTop - 20;
                    
                    this.smoothScroll(container, targetPosition, 500);
                }
            });
        },
        
        // 平滑滚动实现
        smoothScroll(element, target, duration) {
            const start = element.scrollTop;
            const distance = target - start;
            const startTime = performance.now();
            
            function animation(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 使用缓动函数
                const easeProgress = 1 - Math.pow(1 - progress, 3);
                element.scrollTop = start + distance * easeProgress;
                
                if (progress < 1) {
                    requestAnimationFrame(animation);
                }
            }
            
            requestAnimationFrame(animation);
        },
        
        // 处理滚动事件
        handleScroll() {
            const scrollContainer = document.getElementById('docsBody');
            if (!scrollContainer) return;
            
            const scrollTop = scrollContainer.scrollTop;
            
            // 显示/隐藏返回顶部按钮
            this.showBackTop = scrollTop > 300;
            
            // 更新当前激活的菜单项
            this.updateActiveMenuItem(scrollTop);
        },
        
        // 更新激活的菜单项
        updateActiveMenuItem(scrollTop) {
            const docsBoxes = document.getElementsByClassName('docsBox');
            let activeIndex = this.activeIndex;
            
            for (let i = 0; i < docsBoxes.length; i++) {
                const box = docsBoxes[i];
                const offsetTop = box.offsetTop;
                const height = box.offsetHeight;
                
                if (scrollTop >= offsetTop - 100 && scrollTop < offsetTop + height - 100) {
                    activeIndex = box.getAttribute('index');
                    break;
                }
            }
            
            if (activeIndex !== this.activeIndex) {
                this.activeIndex = activeIndex;
            }
        },
        
        // 返回顶部
        backToTop() {
            const scrollContainer = document.getElementById('docsBody');
            if (scrollContainer) {
                this.smoothScroll(scrollContainer, 0, 300);
            }
        },
        
        // 处理搜索
        handleSearch() {
            if (!this.searchKeyword.trim()) {
                this.$message.warning('请输入搜索关键词');
                return;
            }
            
            // TODO: 实现搜索功能
            this.$message.info('搜索功能开发中...');
        },
        
        // 显示反馈对话框
        showFeedback() {
            this.feedbackForm = {
                type: '',
                content: ''
            };
            this.feedbackVisible = true;
        },
        
        // 提交反馈
        submitFeedback() {
            this.$refs.feedbackForm.validate((valid) => {
                if (valid) {
                    this.feedbackLoading = true;
                    
                    // TODO: 调用反馈接口
                    setTimeout(() => {
                        this.feedbackLoading = false;
                        this.feedbackVisible = false;
                        this.$message.success('感谢您的反馈！');
                    }, 1000);
                }
            });
        },
        
        // 处理窗口大小变化
        handleResize() {
            // 动态调整内容区高度
            const mainElement = document.querySelector('.el-main');
            if (mainElement) {
                const windowHeight = window.innerHeight;
                const headerHeight = 120;
                const contentHeight = windowHeight - headerHeight;
                mainElement.style.minHeight = `${contentHeight}px`;
            }
        }
    }
}
</script>

<style scoped lang="less">
.help-center-container {
    min-height: 100vh;
    background: #f5f7fa;
}

// 顶部搜索栏
.help-center-header {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    color: #fff;
    padding: 40px 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    
    .header-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        text-align: center;
    }
    
    .header-title {
        font-size: 32px;
        margin-bottom: 20px;
        font-weight: 500;
        
        i {
            margin-right: 10px;
        }
    }
    
    .header-search {
        max-width: 600px;
        margin: 0 auto;
        
        /deep/ .el-input__inner {
            height: 50px;
            font-size: 16px;
            border-radius: 25px;
            padding-left: 25px;
        }
        
        /deep/ .el-input-group__append {
            background: transparent;
            border: none;
            
            .el-button {
                background: #fff;
                color: #667eea;
                border-radius: 0 25px 25px 0;
                height: 50px;
                padding: 0 30px;
                font-size: 16px;
                transition: all 0.3s;
                
                &:hover {
                    background: #f0f2f5;
                }
            }
        }
    }
}

// 主体内容区
.help-center-body {
    padding: 30px 0;
    
    .main-container {
        max-width: 1200px;
        margin: 0 auto;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
}

// 左侧导航
.help-sidebar {
    background: #fafafa;
    border-right: 1px solid #e8e8e8;
    height: calc(100vh - 200px);
    overflow-y: auto;
    
    &::-webkit-scrollbar {
        width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 3px;
    }
    
    .sidebar-content {
        padding: 20px 0;
    }
    
    .nav-header {
        padding: 0 20px 15px;
        font-size: 18px;
        font-weight: 500;
        color: #333;
        border-bottom: 1px solid #e8e8e8;
        margin-bottom: 15px;
        
        i {
            margin-right: 8px;
            color: #667eea;
        }
    }
}

// 右侧内容区
.help-content {
    padding: 0;
    height: calc(100vh - 200px);
    overflow-y: auto;
    position: relative;
    
    &::-webkit-scrollbar {
        width: 8px;
    }
    
    &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 4px;
    }
}

// 面包屑导航
.breadcrumb-wrapper {
    padding: 20px 30px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    
    /deep/ .el-breadcrumb {
        font-size: 14px;
    }
}

// 内容包装器
.content-wrapper {
    padding: 30px;
    min-height: 500px;
}

// 加载动画
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #999;
    
    i {
        font-size: 32px;
        margin-bottom: 10px;
    }
}

// 返回顶部按钮
.back-to-top {
    position: fixed;
    bottom: 80px;
    right: 40px;
    width: 48px;
    height: 48px;
    background: #667eea;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    transition: all 0.3s;
    z-index: 999;
    
    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
    }
    
    i {
        font-size: 20px;
    }
}

// 快速反馈按钮
.quick-feedback {
    position: fixed;
    bottom: 140px;
    right: 40px;
    z-index: 999;
    
    .feedback-btn {
        width: 48px;
        height: 48px;
        background: #67c23a;
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
        transition: all 0.3s;
        
        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 20px rgba(103, 194, 58, 0.5);
        }
        
        i {
            font-size: 20px;
        }
    }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
}

.content-fade-enter-active, .content-fade-leave-active {
    transition: opacity 0.3s, transform 0.3s;
}

.content-fade-enter {
    opacity: 0;
    transform: translateY(10px);
}

.content-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

// 响应式设计
@media (max-width: 768px) {
    .help-center-header {
        padding: 20px 0;
        
        .header-title {
            font-size: 24px;
        }
    }
    
    .help-sidebar {
        width: 200px !important;
    }
    
    .content-wrapper {
        padding: 20px;
    }
    
    .back-to-top,
    .quick-feedback .feedback-btn {
        width: 40px;
        height: 40px;
        
        i {
            font-size: 16px;
        }
    }
}
</style>

<style>
/* 全局样式调整 */
.docssing .el-submenu__title {
    height: 48px !important;
    line-height: 48px !important;
    font-size: 15px;
    transition: all 0.3s;
}

.docssing .el-menu-item {
    height: 42px !important;
    line-height: 42px !important;
    font-size: 14px;
    transition: all 0.3s;
}

.docssing .el-menu-item:hover {
    background: #f0f2f5 !important;
    padding-left: 30px !important;
}

.docssing .el-menu-item.is-active {
    background: #ecf5ff !important;
    border-right: 3px solid #667eea;
    font-weight: 500;
}
</style>

