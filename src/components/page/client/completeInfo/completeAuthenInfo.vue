<template>
    <div class="simple-signature-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 标题信息区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <div class="page-info">
                                    <i class="el-icon-document-add"></i>
                                    <span class="page-title">资料完善</span>
                                </div>
                            </div>

                            <!-- 返回按钮 -->
                            <div class="action-buttons">
                                <el-button 
                                    @click="$router.push('authentication')" 
                                    class="action-btn" 
                                    icon="el-icon-back">
                                    返回认证
                                </el-button>
                            </div>
                        </div>

                        <!-- 提示信息区域 -->
                        <div class="tips-section">
                            <div class="tips-container">
                                <div class="tip-header">
                                    <i class="el-icon-info"></i>
                                    <span class="tip-title">上传要求</span>
                                </div>
                                <div class="tip-content">
                                    <div class="tip-item">
                                        <i class="el-icon-check"></i>
                                        <span>证件照应清晰可见容易识别，且边框完整</span>
                                    </div>
                                    <div class="tip-item">
                                        <i class="el-icon-check"></i>
                                        <span>必须真实拍摄，不能使用复印件</span>
                                    </div>
                                    <div class="tip-item">
                                        <i class="el-icon-check"></i>
                                        <span>大小不超过5M，仅支持.jpg格式</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 表单内容 -->
                <el-form 
                    :rules="formRule" 
                    ref="formRefs" 
                    :model="formInline"
                    class="enhanced-form">

                    <!-- 营业执照部分 -->
                    <div class="form-section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="el-icon-office-building"></i>
                                <span>营业执照</span>
                            </div>
                        </div>

                        <div class="section-content">
                            <div class="upload-form-grid">
                                <!-- 上传区域 -->
                                <div class="upload-area">
                                    <div class="upload-group">
                                        <el-form-item label="营业执照" prop="businessLicense">
                                            <div class="enhanced-upload">
                                                <div @click.capture="fileType = 'fd_L'">
                                                    <el-upload 
                                                        :action="action" 
                                                        :headers="token" 
                                                        :limit="1" 
                                                        list-type="picture-card"
                                                        :before-upload="beforeAvatarUpload" 
                                                        :on-preview="handlePictureCardPreview"
                                                        :on-remove="handleRemove" 
                                                        :on-success="handlewqsSuccess" 
                                                        :file-list="PhotoLege">
                                                        <i class="el-icon-plus"></i>
                                                        <div class="upload-hint">点击上传</div>
                                                    </el-upload>
                                                </div>
                                            </div>
                                        </el-form-item>

                                        <div class="example-section">
                                            <div class="example-title">示例图片</div>
                                            <el-image 
                                                class="example-image"
                                                src="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358290932.jpeg"
                                                :preview-src-list="['https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358290932.jpeg']">
                                            </el-image>
                                        </div>
                                    </div>
                                </div>

                                <!-- 信息区域 -->
                                <div class="info-area" v-loading="loading3">
                                    <div class="info-card">
                                        <div class="card-title">企业信息</div>
                                        <div class="form-fields">
                                            <el-form-item label="企业名称" prop="compName">
                                                <el-input 
                                                    v-model="formInline.compName" 
                                                    placeholder="请输入企业名称"
                                                    class="enhanced-input">
                                                </el-input>
                                            </el-form-item>

                                            <el-form-item label="企业资质编号" prop="number">
                                                <el-input 
                                                    v-model="formInline.number" 
                                                    placeholder="请输入企业资质编号"
                                                    class="enhanced-input">
                                                </el-input>
                                            </el-form-item>

                                            <el-form-item label="企业法人姓名" prop="corporateName">
                                                <el-input 
                                                    v-model="formInline.corporateName" 
                                                    placeholder="请输入企业法人姓名"
                                                    class="enhanced-input">
                                                </el-input>
                                            </el-form-item>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 身份证部分 -->
                    <div class="form-section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="el-icon-user"></i>
                                <span>身份证件</span>
                            </div>
                        </div>

                        <div class="section-content">
                            <div class="upload-form-grid">
                                <!-- 上传区域 -->
                                <div class="upload-area">
                                    <div class="upload-group">
                                        <el-form-item label="身份证正面" prop="idCardFront">
                                            <div class="enhanced-upload">
                                                <div @click.capture="fileType = 'fd_0'">
                                                    <el-upload 
                                                        :action="action" 
                                                        :headers="token" 
                                                        :limit="1" 
                                                        list-type="picture-card"
                                                        :before-upload="beforeAvatarUpload" 
                                                        :on-preview="handlePictureCardPreview"
                                                        :on-remove="handleRemove" 
                                                        :on-success="handlewqsSuccess" 
                                                        :file-list="PhotoFront">
                                                        <i class="el-icon-plus"></i>
                                                        <div class="upload-hint">点击上传</div>
                                                    </el-upload>
                                                </div>
                                            </div>
                                        </el-form-item>

                                        <el-form-item label="身份证反面" prop="idCardBack">
                                            <div class="enhanced-upload">
                                                <div @click.capture="fileType = 'fd_1'">
                                                    <el-upload 
                                                        :action="action" 
                                                        :headers="token" 
                                                        :limit="1" 
                                                        list-type="picture-card"
                                                        :before-upload="beforeAvatarUpload" 
                                                        :on-preview="handlePictureCardPreview"
                                                        :on-remove="handleRemove" 
                                                        :on-success="handlewqsSuccess" 
                                                        :file-list="PhotoReverse">
                                                        <i class="el-icon-plus"></i>
                                                        <div class="upload-hint">点击上传</div>
                                                    </el-upload>
                                                </div>
                                            </div>
                                        </el-form-item>

                                        <div class="example-section">
                                            <div class="example-title">示例图片</div>
                                            <el-image 
                                                class="example-image"
                                                src="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358330557.jpeg"
                                                :preview-src-list="['https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358330557.jpeg']">
                                            </el-image>
                                        </div>
                                    </div>
                                </div>

                                <!-- 信息区域 -->
                                <div class="info-area" v-loading="loading2">
                                    <div class="info-card">
                                        <div class="card-title">个人信息</div>
                                        <div class="form-fields">
                                            <el-form-item label="姓名" prop="name">
                                                <el-input 
                                                    v-model="formInline.name" 
                                                    placeholder="请输入姓名"
                                                    class="enhanced-input">
                                                </el-input>
                                            </el-form-item>

                                            <el-form-item label="身份证号码" prop="idCard">
                                                <el-input 
                                                    v-model="formInline.idCard" 
                                                    placeholder="请输入身份证号码"
                                                    class="enhanced-input">
                                                </el-input>
                                            </el-form-item>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 办公&授权部分 -->
                    <div class="form-section">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="el-icon-files"></i>
                                <span>办公&授权</span>
                            </div>
                        </div>

                        <div class="section-content">
                            <div class="office-upload-grid">
                                <div class="upload-item">
                                    <el-form-item label="办公照片" prop="officePhoto">
                                        <div class="enhanced-upload">
                                            <div @click.capture="fileType = 'fd_R'">
                                                <el-upload 
                                                    :action="action" 
                                                    :headers="token" 
                                                    :limit="1" 
                                                    list-type="picture-card"
                                                    :before-upload="beforeAvatarUpload" 
                                                    :on-preview="handlePictureCardPreview"
                                                    :on-remove="handleRemove" 
                                                    :on-success="handlewqsSuccess" 
                                                    :file-list="officeFile">
                                                    <i class="el-icon-plus"></i>
                                                    <div class="upload-hint">点击上传</div>
                                                </el-upload>
                                            </div>
                                        </div>
                                    </el-form-item>

                                    <div class="example-section">
                                        <div class="example-title">办公照片示例</div>
                                        <el-image 
                                            class="example-image"
                                            src="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721369256360.png"
                                            :preview-src-list="['https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721369256360.png']">
                                        </el-image>
                                    </div>
                                </div>

                                <div class="upload-item">
                                    <el-form-item label="授权书" prop="authFile">
                                        <div class="enhanced-upload">
                                            <div @click.capture="fileType = 'fd_S'">
                                                <el-upload 
                                                    :action="action" 
                                                    :headers="token" 
                                                    :limit="1" 
                                                    list-type="picture-card"
                                                    :before-upload="beforeAvatarUpload" 
                                                    :on-preview="handlePictureCardPreview"
                                                    :on-remove="handleRemove" 
                                                    :on-success="handlewqsSuccess" 
                                                    :file-list="authFront">
                                                    <i class="el-icon-plus"></i>
                                                    <div class="upload-hint">点击上传</div>
                                                    <div class="el-upload__tip">
                                                        <a class="template-link"
                                                            href="https://doc.zthysms.com/Public/Uploads/2021-03-24/605ad686a9473.docx"
                                                            download="template.docx" 
                                                            rel="noopener noreferrer" 
                                                            target="_blank">
                                                            <i class="el-icon-download"></i>
                                                            模板下载
                                                        </a>
                                                    </div>
                                                </el-upload>
                                            </div>
                                        </div>
                                    </el-form-item>

                                    <div class="example-section">
                                        <div class="example-title">授权书示例</div>
                                        <el-image 
                                            class="example-image"
                                            src="https://doc.zthysms.com/server/../Public/Uploads/2021-03-24/605af850c8491.png"
                                            :preview-src-list="['https://doc.zthysms.com/server/../Public/Uploads/2021-03-24/605af850c8491.png']">
                                        </el-image>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="submit-section">
                        <div class="submit-buttons">
                            <el-button 
                                @click="$router.push('authentication')" 
                                class="submit-btn cancel">
                                返回
                            </el-button>
                            <el-button 
                                v-if="submitLoading" 
                                class="submit-btn primary" 
                                :loading="true">
                                提交中
                            </el-button>
                            <el-button 
                                v-else 
                                class="submit-btn primary" 
                                @click="submit('formRefs')">
                                提交审核
                            </el-button>
                        </div>
                    </div>
                </el-form>
            </div>
        </div>

        <!-- 图片预览对话框 -->
        <el-dialog 
            :visible.sync="dialogVisible"
            class="enhanced-dialog image-preview">
            <img width="100%" :src="dialogImageUrl" alt="预览图片" />
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios';
export default {
    name:"completeAuthenInfo",
    data() {
        return {
            dialogImageUrl: "",
            src: "https://doc.zthysms.com/Public/Uploads/2021-04-25/6085176a6c0ff.pdf",
            action: this.API.cpus + "v3/file/upload",
            fileType: "",
            PhotoFront: [],
            PhotoReverse: [],
            PhotoLege: [],
            officeFile: [],
            authFront: [],
            loading3: false,
            loading2: false,
            dialogVisible: false,
            submitLoading: false,
            formInline: {
                businessLicense: "", //营业执照
                officePhoto: "", //办公照片
                idCardBack: "", //身份证反面照
                idCardFront: "", //身份证正面照
                authFile: "", //授权书
                id: "",
                compName: "",//企业名称
                number: "",//企业资质编号
                corporateName: "",//企业法人姓名
                name: "",//身份证姓名
                idCard: ""//身份证号码
            },
            formRule: {
                businessLicense: [
                    {
                        required: true,
                        message: "请上传营业执照",
                        trigger: "change",
                    },
                ],
                idCardBack: [
                    {
                        required: true,
                        message: "请上传身份证反面",
                        trigger: "change",
                    },
                ],
                idCardFront: [
                    {
                        required: true,
                        message: "请上传身份证正面",
                        trigger: "change",
                    },
                ],
                officePhoto: [
                    {
                        required: true,
                        message: "请上传办公照片",
                        trigger: "blur",
                    },
                ],
                compName:[
                    {
                        required: true,
                        message: "请输入企业名称",
                        trigger: "blur",
                    },
                ],

                number:[
                    {
                        required: true,
                        message: "请输入企业资质编号",
                        trigger: "blur",
                    },
                ],
                corporateName:[
                    {
                        required: true,
                        message: "请输入企业法人姓名",
                        trigger: "blur",
                    },
                ],
                name: [
                    {
                        required: true,
                        message: "请输入身份证姓名",
                        trigger: "blur",
                    },
                ],
                idCard: [
                    {
                        required: true,
                        message: "请输入身份证号码",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        getnfo() {
            this.$api.get(
                this.API.cpus + "consumerclientinfo/info",
                {},
                (res) => {
                    if (res.code == 200) {
                        if (res.data) {
                            this.formInline.idCardFront = res.data.idCardFront;
                            this.formInline.idCardBack = res.data.idCardBack;
                            this.formInline.businessLicense = res.data.businessLicense;
                            this.formInline.officePhoto = res.data.officePhoto;
                            this.formInline.authFile = res.data.authFile;
                            this.formInline.compName = res.data.compName;
                            this.formInline.number = res.data.number;
                            this.formInline.corporateName = res.data.corporateName;
                            this.formInline.name = res.data.name;
                            this.formInline.idCard = res.data.idCard;
                            this.formInline.id = res.data.id;
                            if (res.data.idCardFront) {
                                this.PhotoFront = [
                                    {
                                        name: "身份证正面",
                                        url: this.API.imgU + res.data.idCardFront,
                                    },
                                ];
                            }
                            if (res.data.idCardBack) {
                                this.PhotoReverse = [
                                    {
                                        name: "身份证反面",
                                        url: this.API.imgU + res.data.idCardBack,
                                    },
                                ];
                            }
                            if (res.data.businessLicense) {
                                this.PhotoLege = [
                                    {
                                        name: "营业执照",
                                        url: this.API.imgU + res.data.businessLicense,
                                    },
                                ];
                            }
                            if (res.data.officePhoto) {
                                this.officeFile = [
                                    {
                                        name: "办公照片",
                                        url: this.API.imgU + res.data.officePhoto,
                                    },
                                ];
                            }
                            if (res.data.authFile) {
                                this.authFront = [
                                    {
                                        name: "授权书",
                                        url: this.API.imgU + res.data.authFile, //res.data.authFile
                                    },
                                ];
                            }
                        }
                    }
                })
        },

        // async downloadTemplate() {
        //     const imageUrl = 'https://doc.zthysms.com/server/../Public/Uploads/2021-03-24/605af850c8491.png';
        //     try {
        //         const response = await axios.get(imageUrl, { responseType: 'blob' });
        //         const url = window.URL.createObjectURL(response.data);
        //         const link = document.createElement('a');
        //         link.href = url;
        //         link.download = 'template.png';
        //         document.body.appendChild(link);
        //         link.click();
        //         document.body.removeChild(link);
        //         window.URL.revokeObjectURL(url);
        //     } catch (error) {
        //         console.error('下载失败:', error);
        //     }
        // },
        submit() {
            this.$refs["formRefs"].validate((valid, value) => {
                if (valid) {
                    this.submitLoading = true;
                    let data = {
                        businessLicense: this.formInline.businessLicense,
                        idCardBack: this.formInline.idCardBack,
                        idCardFront: this.formInline.idCardFront,
                        officePhoto: this.formInline.officePhoto,
                        authFile: this.formInline.authFile,
                        compName: this.formInline.compName,
                        number: this.formInline.number,
                        corporateName: this.formInline.corporateName,
                        name: this.formInline.name,
                        idCard: this.formInline.idCard,
                        // id: "",
                    };
                    if (this.formInline.id) {
                        data.id = this.formInline.id;
                    }
                    this.$api.post(
                        this.API.cpus + "consumerclientinfo/setting",
                        data,
                        (res) => {
                            if (res.code == 200) {
                                this.$message({
                                    type: "success",
                                    duration: "2000",
                                    message: "提交成功!",
                                });
                                this.submitLoading = false;
                                this.$router.push({ path: "/authentication" });
                            } else {
                                this.$message({
                                    type: "error",
                                    duration: "2000",
                                    message: res.msg,
                                });
                                this.submitLoading = false;
                            }
                        }
                    );
                }else{
                    this.$message({
                        type: "error",
                        message: "请检查信息是否有遗漏！"
                    });
                }
            });
        },
        beforeAvatarUpload(file) {
            const siJPGGIF = file.name.split(".")[1];
            const isLt5M = file.size / 1024 / 1024 < 5; //单位MB
            if (siJPGGIF !== "jpg") {
                this.$message.error("上传图片只能是 jpg格式!");
                return false;
            }
            if (!isLt5M) {
                this.$message.error("上传图片大小不能超过 5MB!");
                return false;
            }
        },
        handlewqsSuccess(res, file, fileList) {
            let _this = this;
            if (res.code == 200) {
                if (_this.fileType == "fd_0") {
                    this.loading2 = true;
                    this.formInline.idCardFront = res.data.fullpath;
                    this.$api.get(this.API.cpus + 'consumerCertificate/idCardInfo', { filePath: res.data.fullpath }, ress => {
                        if (ress.code == 200) {
                            this.formInline.name = ress.data.personName
                            this.formInline.idCard = ress.data.personIdCard
                            this.loading2 = false
                        } else {
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
                            this.loading2 = false
                        }

                    })
                } else if (_this.fileType == "fd_1") {
                    this.formInline.idCardBack = res.data.fullpath;
                } else if (_this.fileType == "fd_L") {
                    this.loading3 = true
                    this.formInline.businessLicense = res.data.fullpath;
                    this.$api.get(this.API.cpus + 'consumerCertificate/entLicenseInfo', { filePath: res.data.fullpath }, ress => {
                        if (ress.code == 200) {
                            this.formInline.corporateName = ress.data.corporateName
                            this.formInline.compName = ress.data.entName
                            this.formInline.number = ress.data.entQualificationNum
                            // this.formInline.qualificationType = ress.data.entQualificationType+''
                            this.loading3 = false
                        } else {
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
                            this.loading3 = false
                        }
                    })
                } else if (_this.fileType == "fd_R") {
                    this.formInline.officePhoto = res.data.fullpath;
                } else if (_this.fileType == "fd_S") {
                    this.formInline.authFile = res.data.fullpath;
                }
            } else {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: res.msg,
                });
                this.loading2 = false;
            }
        },
        handleRemove(file, fileList) {
            let _this = this;
            if (_this.fileType == "fd_0") {
                _this.PhotoFront = [];
                this.formInline.idCardFront = "";
                this.loading2 = false;
            } else if (_this.fileType == "fd_1") {
                _this.PhotoReverse = [];
                this.formInline.idCardBack = "";
            } else if (_this.fileType == "fd_L") {
                console.log(_this.fileType, "ll");
                _this.PhotoLege = [];
                this.formInline.businessLicense = "";
                this.loading2 = false;
            } else if (_this.fileType == "fd_R") {
                _this.officeFile = [];
                this.formInline.officePhoto = "";
            } else if (_this.fileType == "fd_S") {
                _this.authFront = [];
                this.formInline.authFile = "";
            }
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
    },
    created() {
        // let data = JSON.parse(localStorage.getItem("userInfo"));
        // this.roleId = data.roleId;
        // this.formInline.type = this.$route.query.type;
        // if (this.$route.query.status == "1") {
        //     this.active = 3;
        // }
        this.token = {
            Authorization: "Bearer " + this.$common.getCookie("ZTGlS_TOKEN"),
        };
        this.getnfo();
    },
};
</script>

<style lang="less" scoped>
@import "~@/styles/template-common.less";

.enhanced-form {
    .form-section {
        margin-bottom: 32px;
        background: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .section-header {
            background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
            padding: 16px 20px;
            margin-bottom: 0;

            .section-title {
                display: flex;
                align-items: center;
                color: #ffffff;
                font-size: 16px;
                font-weight: 600;

                i {
                    margin-right: 12px;
                    font-size: 18px;
                }
            }
        }

        .section-content {
            padding: 24px;

            .upload-form-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 32px;
                align-items: flex-start;

                .upload-area {
                    .upload-group {
                        display: flex;
                        flex-direction: column;
                        gap: 20px;

                        .enhanced-upload {
                            border: 2px dashed #e1e5f0;
                            border-radius: 8px;
                            padding: 24px;
                            background: #fafbfe;
                            transition: all 0.3s ease;

                            &:hover {
                                border-color: #409eff;
                                background: #ecf5ff;
                            }

                            .upload-hint {
                                margin-top: 8px;
                                color: #666;
                                font-size: 12px;
                                text-align: center;
                            }
                        }

                        .example-section {
                            .example-title {
                                font-size: 14px;
                                font-weight: 500;
                                color: #333;
                                margin-bottom: 12px;
                                text-align: center;
                            }

                            .example-image {
                                width: 160px;
                                height: 160px;
                                border-radius: 8px;
                                border: 1px solid #e1e5f0;
                                display: block;
                                margin: 0 auto;
                            }
                        }
                    }
                }

                .info-area {
                    .info-card {
                        background: #fafbfe;
                        border: 1px solid #e1e5f0;
                        border-radius: 8px;
                        padding: 20px;

                        .card-title {
                            font-size: 16px;
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 20px;
                            padding-bottom: 10px;
                            border-bottom: 1px solid #e1e5f0;
                        }

                        .form-fields {
                            .el-form-item {
                                margin-bottom: 20px;

                                &:last-child {
                                    margin-bottom: 0;
                                }
                            }
                        }
                    }
                }
            }

            .office-upload-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 32px;

                .upload-item {
                    display: flex;
                    flex-direction: column;
                    gap: 20px;

                    .enhanced-upload {
                        border: 2px dashed #e1e5f0;
                        border-radius: 8px;
                        padding: 24px;
                        background: #fafbfe;
                        transition: all 0.3s ease;

                        &:hover {
                            border-color: #409eff;
                            background: #ecf5ff;
                        }

                        .upload-hint {
                            margin-top: 8px;
                            color: #666;
                            font-size: 12px;
                            text-align: center;
                        }

                        .template-link {
                            display: inline-flex;
                            align-items: center;
                            color: #409eff;
                            text-decoration: none;
                            font-size: 12px;
                            margin-top: 8px;

                            &:hover {
                                color: #337ecc;
                                text-decoration: underline;
                            }

                            i {
                                margin-right: 4px;
                            }
                        }
                    }

                    .example-section {
                        .example-title {
                            font-size: 14px;
                            font-weight: 500;
                            color: #333;
                            margin-bottom: 12px;
                            text-align: center;
                        }

                        .example-image {
                            width: 160px;
                            height: 160px;
                            border-radius: 8px;
                            border: 1px solid #e1e5f0;
                            display: block;
                            margin: 0 auto;
                        }
                    }
                }
            }
        }
    }

    .submit-section {
        text-align: center;
        padding: 32px 0;
        border-top: 1px solid #e1e5f0;
        margin-top: 32px;

        .submit-buttons {
            display: inline-flex;
            gap: 16px;
            align-items: center;

            .submit-btn {
                min-width: 120px;
                height: 40px;
                border-radius: 6px;
                font-weight: 500;
                font-size: 14px;
                transition: all 0.3s ease;

                &.cancel {
                    background: #ffffff;
                    border: 1px solid #d9d9d9;
                    color: #333;

                    &:hover {
                        border-color: #409eff;
                        color: #409eff;
                    }
                }

                &.primary {
                    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
                    border: none;
                    color: #ffffff;

                    &:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .enhanced-form {
        .form-section {
            .section-content {
                .upload-form-grid,
                .office-upload-grid {
                    grid-template-columns: 1fr;
                    gap: 20px;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .enhanced-form {
        .form-section {
            .section-content {
                padding: 16px;

                .upload-area {
                    .upload-group {
                        .example-section {
                            .example-image {
                                width: 120px;
                                height: 120px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>