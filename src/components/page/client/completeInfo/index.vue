<template>
    <div class="completeInfo">
        <div class="completeInfo-title">
            <h2>实名信息完善</h2>
            <div class="completeInfo-desc">
                <p>为了更好的为您提供服务，请您提供以下信息，以便我们更好的为您服务。</p>
                <p>（注：请您务必提供真实有效的信息，以便我们更好的为您服务）</p>
            </div>
            <div class="completeInfo-mean">
                <div class="mean-h">
                    <span>完善信息</span>
                </div>
                <div>
                    <div class="mean-item">
                        <span>状态</span>
                        <span v-if="showflag === true" style="color: #F56C6C;">未完善</span>
                        <span v-else-if="showflag === false" style="color: #67C23A;">已完善</span>
                    </div>
                    <!-- <div class="mean-item">
                        <span>状态</span>
                        <span>已完善</span>
                    </div>
                    <div class="mean-item">
                        <span>状态</span>
                        <span>已完善</span>
                    </div>
                    <div class="mean-item">
                        <span>状态</span>
                        <span>已完善</span>
                    </div> -->
                </div>
                <div v-if="showflag" style="margin: 15px;">
                    <el-button type="primary" @click="$router.push('completeAuthenInfo')">去完善</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
//   import smrz from './smrz'
import axios from 'axios'
export default {
    name: "completeInfo",
    components: {
        //   smrz
    },
    data() {
        return {
            hostname: window.location.hostname,
            nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
            hostflag: true,
            showflag: false,
        };
    },
    created() {
        //   this.token = {
        //     Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        //   };
        this.nameTile.forEach((item) => {
            if (this.hostname == item) {
                this.hostflag = false;
            }
        });
        this.init()
    },
    methods: {
        init() {
            this.$api.get(
                this.API.cpus + "consumerclientinfo/checkRealStatus",    // 接口地址
                {},
                (res) => {
                    if(res.code === 200){
                        this.showflag = res.data
                    }
                })
        }
    },

    mounted() {
    },
    computed: {
    },

    watch: {
    },
};
</script>
<style scoped>
.completeInfo {
    background: #fff;
    padding: 20px;
}

.completeInfo-desc {
    background: #fff2d1;
    color: #906e12;
    border-radius: 10px;
    padding: 15px;
    line-height: 25px;
    width: 600px;
    margin-top: 15px;

}

.completeInfo-mean {
    width: 500px;
    margin-left: 15px;
}

.mean-h {
    width: 100%;
    height: 50px;
    line-height: 50px;
}

.mean-h span {
    margin-left: 20px;
    color: #1e7cfc;
}

.mean-item {
    width: 80%;
    height: 30px;
    margin-left: 30px;
    border-bottom: 1px solid #ccc;
    line-height: 33px;
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
}
</style>