<template>
    <div class="simple-sendtask-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <el-button
                                    @click="exportNums1"
                                    class="action-btn primary"
                                    icon="el-icon-download"
                                >
                                    导出数据
                                </el-button>
                                <el-button
                                    @click="sendReport"
                                    class="action-btn"
                                    icon="el-icon-refresh"
                                >
                                    刷新列表
                                </el-button>
                            </div>

                            <!-- 统计信息 -->
                            <div class="stats-info">
                                <span class="stats-text">共 {{ tableDataObj.totalRow }} 条记录</span>
                                <el-divider direction="vertical"></el-divider>
                                <span class="stats-text">当前第 {{ formData.currentPage }} 页</span>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <el-form :model="form" :inline="true" ref="SHformList" class="advanced-search-form">
                                <div class="search-row">
                                    <el-form-item label="手机号码" prop="mobile" class="search-item">
                                        <el-input
                                            v-model="form.mobile"
                                            placeholder="请输入手机号"
                                            class="search-input"
                                            clearable
                                        />
                                    </el-form-item>

                                    <el-form-item label="国家" prop="area" class="search-item">
                                        <el-input
                                            v-model="form.area"
                                            placeholder="请输入国家"
                                            class="search-input"
                                            clearable
                                        />
                                    </el-form-item>

                                    <el-form-item label="发送状态" prop="smsStatus" v-if="this.$store.state.isDateState == 1" class="search-item">
                                        <el-select v-model="form.smsStatus" placeholder="请选择发送状态" class="search-select" clearable>
                                            <el-option label="全部" value=""></el-option>
                                            <el-option label="成功" value="1"></el-option>
                                            <el-option label="失败" value="2"></el-option>
                                            <el-option label="待返回" value="3"></el-option>
                                        </el-select>
                                    </el-form-item>

                                    <el-form-item label="短信内容" prop="content" class="search-item">
                                        <el-input
                                            v-model="form.content"
                                            placeholder="请输入短信内容"
                                            class="search-input"
                                            clearable
                                        />
                                    </el-form-item>

                                    <el-form-item label="发送时间" prop="time1" class="search-item date-item">
                                        <el-date-picker
                                            v-model="form.time1"
                                            value-format="yyyy-MM-dd"
                                            type="daterange"
                                            range-separator="-"
                                            :picker-options="pickerOptions"
                                            :clearable="false"
                                            @change="getTimeOperating"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            class="search-date"
                                        />
                                    </el-form-item>

                                    <el-form-item class="search-buttons">
                                        <el-button
                                            type="primary"
                                            @click="querySending"
                                            class="search-btn primary"
                                            icon="el-icon-search"
                                        >
                                            查询
                                        </el-button>
                                        <el-button
                                            @click="resetSending('SHformList')"
                                            class="search-btn"
                                            icon="el-icon-refresh"
                                        >
                                            重置
                                        </el-button>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>

                <!-- 发送明细列表 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3 class="table-title">发送明细列表</h3>
                        <span class="table-subtitle">查看国际短信发送详情和状态信息</span>
                    </div>

                    <div class="table-container">
                        <el-table
                            v-loading="tableDataObj.loading2"
                            element-loading-text="正在加载发送明细..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(255, 255, 255, 0.9)"
                            ref="multipleTable"
                            border
                            :data="tableDataObj.tableData"
                            class="enhanced-table"
                            stripe
                            :header-cell-style="{ background: '#fafafa', color: '#333' }"
                            :row-style="{ height: '60px' }"
                            empty-text="暂无发送明细数据"
                        >
                            <!-- 手机号码 -->
                            <el-table-column label="手机号码" width="140" align="center">
                                <template slot-scope="scope">
                                    <div class="phone-cell">
                                        <el-button
                                            type="text"
                                            @click="tableContent(scope.row, scope.$index)"
                                            class="phone-btn"
                                        >
                                            {{ scope.row.mobile }}
                                        </el-button>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 短信内容 -->
                            <el-table-column label="短信内容" min-width="350">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <Tooltip v-if="scope.row.content" :content="scope.row.content" className="wrapper-text" effect="light"/>
                                        <span v-else>-</span>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 计费条数 -->
                            <el-table-column prop="chargeNum" label="计费条数" width="100" align="center">
                                <template slot-scope="scope">
                                    <span class="charge-num">{{ scope.row.chargeNum }}</span>
                                </template>
                            </el-table-column>

                            <!-- 国家 -->
                            <el-table-column prop="area" label="国家" width="100" align="center">
                                <template slot-scope="scope">
                                    <el-tag type="info" size="small">{{ scope.row.area || '-' }}</el-tag>
                                </template>
                            </el-table-column>

                            <!-- 消息ID -->
                            <el-table-column label="消息ID" width="220" align="center">
                                <template slot-scope="scope">
                                    <div class="message-id-cell">
                                        <span class="message-id">{{ scope.row.msgid }}</span>
                                        <el-tooltip content="复制消息ID" placement="top">
                                            <i 
                                                class="el-icon-document-copy copy-icon" 
                                                @click="handleCopy(scope.row.msgid, $event)"
                                            />
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 发送时间 -->
                            <el-table-column label="发送时间" width="180" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.sendTime }}
                                </template>
                            </el-table-column>

                            <!-- 状态上报时间 -->
                            <el-table-column label="状态上报时间" width="180" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.reportTime || '-' }}
                                </template>
                            </el-table-column>

                            <!-- 发送状态 -->
                            <el-table-column label="发送状态" width="100" align="center" v-if="this.$store.state.isDateState == 1">
                                <template slot-scope="scope">
                                    <el-tag
                                        :type="getStatusTagType(scope.row.status)"
                                        size="small"
                                    >
                                        {{ getStatusText(scope.row.status) }}
                                    </el-tag>
                                </template>
                            </el-table-column>

                            <!-- 失败代码/备注 -->
                            <el-table-column label="失败代码" width="150" v-if="this.$store.state.isDateState == 1">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <Tooltip v-if="scope.row.originalCode" :content="scope.row.originalCode" className="wrapper-text" effect="light"/>
                                        <span v-else>-</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                    <!-- 简约分页 -->
                    <div class="pagination-section">
                        <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="formData.currentPage"
                            :page-size="formData.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="tableDataObj.totalRow"
                            class="simple-pagination"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出数据弹框 -->
        <el-dialog title="导出数据" :visible.sync="exportShow" width="30%" :before-close="handleClose">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="导出类型" prop="decode">
                    <el-radio-group v-model="ruleForm.decode">
                        <el-radio label="0">掩码</el-radio>
                        <el-radio label="1">明码</el-radio>
                    </el-radio-group>
                </el-form-item>
                <div style="margin-left: 28px;color: #F56C6C;" v-if="ruleForm.decode == '1'">
                    tips: 明码文件将会发送您的{{ emailInfo.email }}邮箱，请注意查收。
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="exportShow = false">取 消</el-button>
                <el-button type="primary" @click="submitExport">{{ ruleForm.decode == '0' ? '下载' : '发送' }}</el-button>
            </span>
        </el-dialog>

        <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
    </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import DownLoadExport from "@/components/publicComponents/downLodExport"
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../common/bus";
import common from "../../../../assets/js/common";
import moment from 'moment'
import Tooltip from "@/components/publicComponents/tooltip";
import clip from '../../utils/clipboard'

export default {
    name: "ImsSendDetails",
    components: {
        TableTem,
        DatePlugin,
        DownLoadExport,
        ResetNumberVue,
        Tooltip
    },
    data() {
        return {
            name: "ImsSendDetails",
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 30 * 24 * 3600 * 1000 || time.getTime() > Date.now();
                }
            },
            rules: {},
            phoneList: [],
            exportFlag: false,
            //复选框的值
            selectId: '',
            // 单条存值
            AddBlackVal: '',
            // 备注
            AddBlackform: {
                remark: ''
            },
            AddBlacksStatus: false,
            isDateState: '',
            //发送查询的值
            form: {
                mobile: '',
                smsStatus: '',
                signature: '',
                content: '',
                temId: '',
                area: '',
                sendBeginTime: moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
                sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
                time1: [
                    moment().subtract(1, 'months').format('YYYY-MM-DD'),
                    moment(Date.now()).format('YYYY-MM-DD')
                ],
                currentPage: 1,
                pageSize: 10,
                time: '',
                isDownload: 2
            },
            //复制发送查询的值
            formData: {
                mobile: '',
                smsStatus: '',
                signature: '',
                content: '',
                temId: '',
                area: '',
                sendBeginTime: moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
                sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
                time1: [
                    moment().subtract(1, 'months').format('YYYY-MM-DD'),
                    moment(Date.now()).format('YYYY-MM-DD')
                ],
                currentPage: 1,
                pageSize: 10,
                isDownload: 2
            },
            tableDataObj: { //表格数据
                loading2: false,
                totalRow: 0,
                tableData: []
            },
            resetVideo: false,
            infoData: {},
            emailInfo: {
                email: "",
                username: ""
            },
            exportShow: false,
            ruleForm: {
                decode: "0",
            },
        }
    },
    created() {
        bus.$on("closeVideo", (msg) => {
            this.resetVideo = msg;
        });
        this.sendReport();
    },
    methods: {
        //发送请求
        sendReport() {
            let data = Object.assign({}, this.formData);
            data.flag = 5;
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumerimsmessage/messages', data, res => {
                this.tableDataObj.tableData = res.data.records;
                this.tableDataObj.totalRow = res.data.total;
                this.tableDataObj.loading2 = false;
            })
        },
        // 发送时间
        getTimeOperating(val) {
            if (val) {
                this.form.sendBeginTime = val[0] + " 00:00:00"
                this.form.sendEndTime = val[1] + " 23:59:59"
            } else {
                this.form.sendBeginTime = ''
                this.form.sendEndTime = ''
            }
        },
        //查询 （发送）
        querySending() {
            Object.assign(this.formData, this.form);
            this.formData.currentPage = 1; // 重置页码
            this.sendReport();
        },
        //重置 （发送）
        resetSending(formName) {
            this.$refs[formName].resetFields();
            this.form.sendBeginTime = moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss');
            this.form.sendEndTime = moment(Date.now()).format('YYYY-MM-DD HH:mm:ss');
            this.form.time1 = [
                moment().subtract(1, 'months').format('YYYY-MM-DD'),
                moment(Date.now()).format('YYYY-MM-DD')
            ];
            Object.assign(this.formData, this.form);
            this.formData.currentPage = 1; // 重置页码
            this.sendReport();
        },
        handleClose() {
            this.exportFlag = false;
            this.exportShow = false;
        },
        getLoginPhone() {
            this.$api.post(
                this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.phoneList = res.data.data;
                    }
                }
            );
        },
        exportFn(obj) {
            this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
                if (res.code == 200) {
                    this.exportShow = false
                    this.$message({
                        type: "success",
                        duration: "2000",
                        message: "已加入到文件下载中心!",
                    });
                    this.$router.push('/FileExport');
                } else {
                    this.$message({
                        type: "error",
                        duration: "2000",
                        message: res.msg,
                    });
                }
            });
        },
        exportNums1() {
            if (this.tableDataObj.tableData.length == 0) {
                this.$message({
                    message: "列表无数据，不可导出！",
                    type: "warning",
                });
            } else {
                this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
                    if (res.code == 200) {
                        this.emailInfo.email = res.data.email;
                        this.emailInfo.username = res.data.username;
                        if (res.data.decode) {
                            this.exportShow = true
                        } else {
                            let data = Object.assign({}, this.formData);
                            data.productType = 5
                            this.exportFn(data)
                        }
                    }
                })
            }
        },
        submitExport() {
            let data = Object.assign({}, this.formData);
            data.productType = 5
            data.decode = this.ruleForm.decode == 0 ? false : true;
            this.exportFn(data)
        },
        tableContent(row, index) {
            this.$api.post(
                this.API.upms + "/generatekey/decryptMobile",
                {
                    keyId: row.keyId,
                    smsInfoId: row.decryptMobile,
                    cipherMobile: row.cipherMobile,
                },
                (res) => {
                    if (res.code == 200) {
                        this.tableDataObj.tableData[index].mobile = res.data;
                    } else if (res.code == 4004002) {
                        common.fetchData().then((res) => {
                            if (res.code == 200) {
                                if (res.data.isAdmin == 1) {
                                    this.resetVideo = true;
                                    this.infoData = res.data;
                                } else {
                                    this.$message({
                                        message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                        type: "warning",
                                    });
                                }
                            } else {
                                this.$message({
                                    message: res.msg,
                                    type: "error",
                                });
                            }
                        })
                    } else {
                        this.$message({
                            message: res.msg,
                            type: "warning",
                        });
                    }
                }
            );
        },
        //获取分页的每页数量
        handleSizeChange(size) {
            this.formData.pageSize = size;
            this.sendReport();
        },
        //获取分页的第几页
        handleCurrentChange(currentPage) {
            this.formData.currentPage = currentPage;
            this.sendReport();
        },
        // 复制功能
        handleCopy(name, event) {
            clip(name, event)
        },
        // 获取发送状态标签类型
        getStatusTagType(status) {
            const statusMap = {
                '1': 'success',   // 成功
                '2': 'danger',    // 失败
                '3': 'warning'    // 待返回
            };
            return statusMap[status] || 'info';
        },
        // 获取发送状态文本
        getStatusText(status) {
            const statusMap = {
                '1': '成功',
                '2': '失败',
                '3': '待返回'
            };
            return statusMap[status] || '未知';
        },
    },
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* ImsSendDetails 特有样式 */

/* 消息ID单元格样式 */
.message-id-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.message-id {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #606266;
    flex: 1;
    word-break: break-all;
}

.copy-icon {
    color: #409eff;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
        color: #66b1ff;
        transform: scale(1.1);
    }
}

/* 内容单元格样式 */
.content-cell {
    word-break: break-all;
    line-height: 1.4;
}

/* 手机号码按钮样式 */
.phone-cell {
    display: flex;
    justify-content: center;
}

.phone-btn {
    color: #409eff;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
        color: #66b1ff;
        background: rgba(64, 158, 255, 0.1);
    }
}

/* 计费条数样式 */
.charge-num {
    font-weight: 600;
    color: #f56c6c;
    font-size: 14px;
}

/* 导出对话框样式 */
.demo-ruleForm {
    .el-form-item {
        margin-bottom: 20px;
    }

    .el-radio-group {
        .el-radio {
            margin-right: 24px;
            
            &:last-child {
                margin-right: 0;
            }
        }
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .message-id-cell {
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
    }

    .message-id {
        font-size: 11px;
    }

    .charge-num {
        font-size: 12px;
    }
}
</style>

<style>
@media screen and (max-width: 1200px) {
    .el-picker-panel {
        width: 370px;
        height: 350px;
        overflow: auto;
    }

    .el-date-range-picker__content {
        width: 100%;
    }

    .el-pagination {
        white-space: pre-wrap;
    }
}
</style>
