<template>
    <div class="login_cell_phone bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 国际价格表</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet Statistics-box">
            <div class="OuterFrame fillet" style="height: 100%;">
                <div class="Mail-table"  style="padding-bottom:40px;">
                    <el-table
                        element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading"
                        element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable"
                        border
                        :data="tableDataObj"
                        style="width: 100%"
                        >
                        <el-table-column label="地区" >
                            <template slot-scope="scope">{{scope.row.area1 +" ("+scope.row.type1+")"}}</template>
                        </el-table-column>
                        <el-table-column label="价格">
                            <template slot-scope="scope">{{scope.row.price1 }}</template>
                        </el-table-column>
                        <el-table-column label="地区" >
                            <template slot-scope="scope">{{ scope.row.area2 +" ("+scope.row.type2+")"}}</template>
                        </el-table-column>
                        <el-table-column label="价格">
                            <template slot-scope="scope">{{ scope.row.price2}}</template>
                        </el-table-column>
                        <el-table-column label="地区" >
                            <template slot-scope="scope">{{scope.row.area3  +" ("+scope.row.type3+")"}}</template>
                        </el-table-column>
                        <el-table-column label="价格">
                            <template slot-scope="scope">{{scope.row.price3 }}</template>
                        </el-table-column>
                        <el-table-column label="地区" >
                            <template slot-scope="scope">{{scope.row.area4  +" ("+scope.row.type4+")"}}</template>
                        </el-table-column>
                        <el-table-column label="价格">
                            <template slot-scope="scope">{{ scope.row.price4}}</template>
                        </el-table-column>
                    </el-table>
                </div>  
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name:'InternationalPrice',
    data() {
        return {
            tableDataObj:[],
        }
    },
    methods: {
        getPirce(){
            this.$api.get(this.API.recharge + 'client/gjPrice',{},res=>{
            let obj =res.data
            var newObj = [];
            var item = {};
            var j=1;
            obj.forEach(function(v, i) {
                var k='area'+j
                var q='price'+j
                var t='type'+j
                item[k]=v.area
                item[q]=v.price
                item[t]=v.type
                j++
                if ((i + 1) % 4 === 0) {
                    newObj.push(item);
                    item={}
                    j=1
                }
            })
            this.tableDataObj=newObj
        })
        }
    },
    watch:{
    },
    // activated(){
    //     this.getPirce()
    // },
    mounted(){
        this.getPirce()
    }
}
</script>
<style scoped>
.Statistics-box{
    padding:20px;
}
.addC .el-select {
    width: 100%;
}
.addC .el-cascader{
    width: 100%;
}
</style>
