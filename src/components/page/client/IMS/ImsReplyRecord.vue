<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="getdate1"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj1.totalRow }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ formData1.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="form1" :inline="true" ref="SHformList1" class="advanced-search-form">
                <div class="search-row">
                  <!-- 时间筛选 -->
                  <el-form-item label="时间筛选" class="search-item">
                    <el-radio-group 
                      v-model="specificTime" 
                      size="small" 
                      @change="handleChangeTimeOptions"
                      class="time-radio-group"
                    >
                      <el-radio-button label="1">今天</el-radio-button>
                      <el-radio-button label="2">近4天</el-radio-button>
                    </el-radio-group>
                  </el-form-item>

                  <!-- 日期选择器 -->
                  <el-form-item class="search-item date-item">
                    <date-plugin 
                      class="search-date" 
                      :datePluginValueList="datePluginValueList"  
                      @handledatepluginVal="handledatepluginVal"
                    />
                  </el-form-item>

                  <!-- 手机号 -->
                  <el-form-item label="手机号" prop="mobile" class="search-item">
                    <el-input
                      v-model="form1.mobile"
                      placeholder="请输入手机号"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button
                      type="primary"
                      @click="queryReply"
                      class="search-btn primary"
                      icon="el-icon-search"
                    >
                      查询
                    </el-button>
                    <el-button
                      @click="resetReply('SHformList1')"
                      class="search-btn"
                      icon="el-icon-refresh"
                    >
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 回复记录列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">回复记录列表</h3>
            <span class="table-subtitle">查看国际短信回复记录详情</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj1.loading2"
              element-loading-text="正在加载回复记录..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              ref="multipleTable"
              border
              :data="tableDataObj1.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无回复记录"
              @selection-change="handelSelection"
            >
              <!-- 复选框 -->
              <el-table-column
                type="selection"
                width="55"
                align="center"
              />

              <!-- 手机号 -->
              <el-table-column label="手机号" width="140" align="center">
                <template slot-scope="scope">
                  <div class="phone-cell">
                    <span class="phone-text">{{ scope.row.mobile }}</span>
                  </div>
                </template>
              </el-table-column>

              <!-- 回复内容 -->
              <el-table-column label="回复内容" min-width="400">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <Tooltip v-if="scope.row.content" :content="scope.row.content" className="wrapper-text" effect="light"/>
                    <span v-else>-</span>
                  </div>
                </template>
              </el-table-column>

              <!-- 回复时间 -->
              <el-table-column label="回复时间" width="180" align="center">
                <template slot-scope="scope">
                  <span class="time-text">{{ scope.row.createTime }}</span>
                </template>
              </el-table-column>

              <!-- 操作列 -->
              <el-table-column label="操作" width="120" align="center" v-if="false">
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.isBlacknumber === '2'"
                    type="text"
                    @click="handelAddblack(scope.row)"
                    class="action-btn-small primary"
                    icon="el-icon-circle-plus"
                  >
                    加入黑名单
                  </el-button>
                  <span v-else class="status-text">已加入</span>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination
              @size-change="handleSizeChange1"
              @current-change="handleCurrentChange1"
              :current-page="formData1.currentPage"
              :page-size="formData1.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj1.totalRow"
              class="simple-pagination"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 加入黑名单弹框 -->
    <el-dialog
      title="加入黑名单"
      :visible.sync="AddBlacks"
      width="30%"
      :before-close="handleClose"
      v-if="false"
    >
      <el-form :model="AddBlackform" ref="AddBlackformRef" label-width="80px" class="dialog-form">
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            :rows="4"
            maxlength="70"
            placeholder="请输入备注内容，不超过70个字"
            v-model="AddBlackform.remark"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="AddBlacks = false">取 消</el-button>
        <el-button type="primary" @click="batchAddblack">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import Tooltip from "@/components/publicComponents/tooltip";

export default {
  name: "ImsReplyRecord",
  components: {
    TableTem,
    DatePlugin,
    Tooltip
  },
  data() {
    return {
      name: "ImsReplyRecord",
      //复选框的值
      selectId: '',
      // 单条存值
      AddBlackVal: '',
      // 备注
      AddBlackform: {
        remark: ''
      },
      AddBlacks: false,
      AddBlacksStatus: false,

      //回复查询的值
      form1: {
        mobile: '',
        currentPage: 1,
        beginTime: '',
        endTime: '',
        pageSize: 10,
        productId: 4,
        isDownload: 2
      },
      //复制回复查询的值
      formData1: {
        mobile: '',
        currentPage: 1,
        beginTime: '',
        endTime: '',
        pageSize: 10,
        productId: 4,
        isDownload: 2
      },
      specificTime: '1', //选择那一天
      datePluginValueList: { //日期选择器
        type: "daterange",
        start: "",
        end: '',
        range: '-',
        clearable: false,
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
        },
        datePluginValue: ''
      },
      tableDataObj1: { //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
      }
    }
  },
  created() {
    // 初始化时获取数据
    this.getdate1();
  },
  methods: {
    //发送请求
    sendReport1(flag) {
      let data = Object.assign({}, this.formData1);
      data.flag = flag;
      this.tableDataObj1.loading2 = true;
      this.$api.post(this.API.cpus + 'v1/consumermms/replyPage', data, res => {
        this.tableDataObj1.tableData = res.data.records;
        this.tableDataObj1.totalRow = res.data.total;
        this.tableDataObj1.loading2 = false;
      })
    },
    //获取回复列表数据
    getdate1() {
      if (this.specificTime === '') {//选择时间范围
        this.sendReport1('5');
      } else {//选择其他
        this.sendReport1(this.specificTime);
      }
    },
    //重置 （回复）
    resetReply(formName) {
      this.$refs[formName].resetFields();
      Object.assign(this.formData1, this.form1);
      this.specificTime = '1';
      this.handleChangeTimeOptions();
      this.formData1.currentPage = 1; // 重置页码
    },
    //查询 （回复）
    queryReply() {
      Object.assign(this.formData1, this.form1);
      this.formData1.currentPage = 1; // 重置页码
      let flag = this.specificTime;
      if (flag == '') flag = '5'
      this.sendReport1(flag);
    },
    //时间范围选择器
    handledatepluginVal: function (val1, val2) {
      if (val1) {
        this.formData1.beginTime = val1;
        this.formData1.endTime = val2;
        this.form1.beginTime = val1;
        this.form1.endTime = val2;
        this.specificTime = '';
      } else {
        this.specificTime = '1';
        this.formData1.beginTime = '';
        this.formData1.endTime = '';
        this.form1.beginTime = '';
        this.form1.endTime = '';
      }
    },
    //选择（今日，昨天，近一年时 置空时间范围框的值）
    handleChangeTimeOptions: function () {
      this.datePluginValueList.datePluginValue = '';
      this.formData1.beginTime = '';
      this.formData1.endTime = '';
      this.form1.beginTime = '';
      this.form1.endTime = '';
    },

    //获取分页的每页数量
    handleSizeChange1(size) {
      this.formData1.pageSize = size;
      this.getdate1();
    },
    //获取分页的第几页
    handleCurrentChange1(currentPage) {
      this.formData1.currentPage = currentPage;
      this.getdate1();
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].smsReplyId)
      }
      this.selectId = selectId.join(); //批量操作选中id
    },
    // 单条加入黑名单
    handelAddblack(row) {
      this.AddBlacks = true;
      this.AddBlacksStatus = true;
      this.AddBlackVal = row.smsReplyId;
    },
    // 关闭弹窗
    handleClose() {
      this.AddBlacks = false;
    },
    //批量加入黑名单
    batchAddblack() {
      // this.$confirms.confirmation('post','确定加入黑名单?',this.API.cpus+'consumerclientblacknumber/blacklistBatch',{idStr:this.AddBlacksStatus==false?this.selectId:this.AddBlackVal,remark:this.AddBlackform.remark},res =>{
      //   this.AddBlacks=false    
      //   this.getdate1(); 
      // })
    }
  },
  watch: {
    //监听查询框的值是否改变
    formData1: {
      handler() {
        this.getdate1();
      },
      deep: true
    },
    //监听(今天，昨天，近一年)的改变
    specificTime: {
      handler(val) {
        this.getdate1();
      },
      deep: true,
      immediate: true
    },
    AddBlacks(val) {
      if (val == false) {
        this.AddBlacksStatus = false;
        if (this.$refs.AddBlackformRef) {
          this.$refs.AddBlackformRef.resetFields();
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* ImsReplyRecord 特有样式 */

/* 手机号单元格样式 */
.phone-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.phone-text {
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

/* 内容单元格样式 */
.content-cell {
  word-break: break-all;
  line-height: 1.5;
  color: #606266;
}

/* 时间文本样式 */
.time-text {
  color: #909399;
  font-size: 13px;
}

/* 状态文本样式 */
.status-text {
  color: #909399;
  font-size: 12px;
}

/* 表格操作按钮样式 */
.action-btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &.primary {
    color: #409eff;
    &:hover {
      color: #66b1ff;
      background: rgba(64, 158, 255, 0.1);
    }
  }
}

/* 对话框表单样式 */
.dialog-form {
  padding: 0 20px;

  .el-textarea {
    font-family: inherit;
  }
}

/* 时间选择器特殊样式 */
.time-radio-group {
  margin-right: 12px;

  /deep/ .el-radio-button__inner {
    padding: 7px 15px;
  }

  /deep/ .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0 4px 4px 0;
  }
}

.search-date {
  /deep/ .el-range-editor {
    border-radius: 4px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .phone-text {
    font-size: 12px;
  }

  .time-text {
    font-size: 12px;
  }

  .content-cell {
    font-size: 13px;
  }
}
</style>

<style>
@media screen and (max-width: 1200px) {
  .el-pagination {
    white-space: pre-wrap;
  }
}
</style>
