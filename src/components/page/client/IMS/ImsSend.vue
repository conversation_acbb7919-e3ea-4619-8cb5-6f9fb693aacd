<template>
  <div class="modern-signature-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="el-icon-message"></i>
            国际短信发送
          </h1>
        </div>
        <div class="header-right">
          <el-tag type="primary" size="medium">
            国际短信
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <el-row class="send-form-layout">
          <el-col :span="15" class="form-section">
            <!-- 国际短信配置表单卡片 -->
            <el-card shadow="hover" class="form-card sms-config-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-setting"></i>
                  国际短信配置
                </span>
              </div>

              <el-form
                :model="formData"
                :rules="formRule"
                ref="formRef"
                label-width="95px"
                class="modern-form"
              >
                <el-form-item label="短信类型" prop="type" class="form-item-modern">
                  <div class="sms-type-selector">
                    <el-select
                      v-model="formData.type"
                      placeholder="请选择短信类型"
                      size="large"
                      class="modern-select"
                    >
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                        <span style="float: left">{{ item.label }}</span>
                        <span style="float: right; color: #8cc5ff; font-size: 13px">
                          {{ item.value === 'YZM' ? '验证类' : '营销类' }}
                        </span>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>

                <el-form-item label="短信内容" prop="content" class="form-item-modern">
                  <div class="sms-content-editor">
                    <el-input
                      v-model="formData.content"
                      type="textarea"
                      placeholder="请输入国际短信内容"
                      :rows="6"
                      maxlength="1500"
                      show-word-limit
                      class="modern-textarea content-textarea"
                      @input="handleInput"
                    />
                  </div>
                </el-form-item>

                <el-form-item label="发送方式" class="form-item-modern">
                  <div class="send-type-selector">
                    <el-radio-group v-model="formData.sendType" @change="handelSend" class="modern-radio-group">
                      <el-radio label="0" class="send-type-radio">
                        <span class="radio-label">
                          <i class="el-icon-edit-outline"></i>
                          号码发送
                        </span>
                        <span class="radio-desc">手动输入手机号码</span>
                      </el-radio>
                      <el-radio label="1" class="send-type-radio">
                        <span class="radio-label">
                          <i class="el-icon-document"></i>
                          文件发送
                        </span>
                        <span class="radio-desc">批量上传号码文件</span>
                      </el-radio>
                    </el-radio-group>
                  </div>
                </el-form-item>

                <el-form-item v-if="formData.sendType == '1'" label="文件上传" class="form-item-modern">
                  <div class="upload-section">
                    <el-upload
                      v-permission
                      class="upload-demo modern-upload"
                      drag
                      :action="API.cpus + 'v3/file/upload'"
                      :headers="header"
                      :on-remove="handleRemove"
                      :on-success="handleAvatarSuccess"
                      :before-upload="beforeAvatarUpload"
                      multiple
                      :limit="1"
                      :file-list="fileList"
                    >
                      <i class="el-icon-upload active"></i>
                      <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                      </div>
                      <div class="el-upload__tip" slot="tip">
                        支持 .xlsx .xls .txt 格式，文件大小不超过300M
                      </div>
                    </el-upload>
                    <el-button
                      type="info"
                      icon="el-icon-download"
                      @click="Variabledownload"
                      class="template-download-btn"
                      plain
                    >
                      模板下载
                    </el-button>
                  </div>
                </el-form-item>

                <el-form-item v-if="formData.sendType == '0'" label="手机号码" prop="mobile" class="form-item-modern">
                  <div class="mobile-input-section">
                    <el-input
                      type="textarea"
                      v-model="formData.mobile"
                      placeholder="请输入国际手机号码，例如：86158********"
                      :rows="4"
                      class="modern-textarea mobile-textarea"
                    />
                    <div class="mobile-tip">
                      <i class="el-icon-info"></i>
                      格式示例：86158********（国家代码+手机号）
                    </div>
                  </div>
                </el-form-item>

                <el-form-item label="发送外显" prop="from" class="form-item-modern">
                  <el-input
                    v-model="formData.from"
                    placeholder="请输入发送外显号码"
                    size="large"
                    class="modern-input"
                  >
                    <i slot="prefix" class="el-icon-phone"></i>
                  </el-input>
                </el-form-item>

                <el-form-item class="form-item-modern send-action">
                  <el-button
                    v-permission
                    type="primary"
                    @click="send"
                    size="large"
                    class="send-btn"
                    icon="el-icon-s-promotion"
                  >
                    立即发送
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>

          <el-col :span="9" class="preview-section">
            <!-- 短信预览卡片 -->
            <el-card shadow="hover" class="form-card preview-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-mobile-phone"></i>
                  国际短信预览
                </span>
                <div class="preview-actions">
                  <el-tooltip content="刷新预览" placement="top">
                    <el-button type="text" @click="refreshPreview" class="refresh-btn">
                      <i class="el-icon-refresh"></i>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>

              <div class="phone-preview">
                <!-- 手机外观容器 -->
                <div class="phone-mockup">
                  <div class="phone-frame">
                    <div class="phone-screen">
                      <!-- 手机状态栏 -->
                      <div class="status-bar">
                        <div class="status-left">
                          <span class="time">{{ currentTime }}</span>
                        </div>
                        <div class="status-right">
                          <i class="signal-icon"></i>
                          <i class="wifi-icon"></i>
                          <span class="battery">100%</span>
                        </div>
                      </div>

                      <!-- 短信应用界面 -->
                      <div class="sms-app">
                        <div class="sms-header">
                          <div class="contact-info">
                            <div class="contact-avatar">
                              <i class="el-icon-message"></i>
                            </div>
                            <div class="contact-details">
                              <div class="contact-name">国际短信</div>
                              <div class="contact-number">{{ formData.from || '+86138****8888' }}</div>
                            </div>
                          </div>
                        </div>

                        <!-- 短信内容区域 -->
                        <div class="sms-conversation">
                          <div class="message-container">
                            <div class="message-bubble received">
                              <div class="message-content">
                                <div v-if="!formData.content" class="placeholder-text">
                                  请输入国际短信内容进行预览...
                                </div>
                                <div v-else class="sms-text">
                                  {{ formData.content }}
                                </div>
                              </div>
                              <div class="message-time">{{ messageTime }}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 手机按钮 -->
                    <div class="phone-button"></div>
                  </div>
                </div>

                <!-- 预览统计信息 -->
                <div class="preview-stats">
                  <div class="stats-grid">
                    <div class="stat-item">
                      <div class="stat-label">字数统计</div>
                      <div class="stat-value">
                        <span class="stat-number" :class="{ 'over-limit': formData.content.length > 1500 }">
                          {{ formData.content.length }}
                        </span>
                        <span class="stat-unit">字</span>
                      </div>
                    </div>

                    <div class="stat-item">
                      <div class="stat-label">预计条数</div>
                      <div class="stat-value">
                        <span class="stat-number">{{ smsCount }}</span>
                        <span class="stat-unit">条</span>
                      </div>
                    </div>
                  </div>

                  <!-- <div class="stats-note">
                    <el-alert
                      type="info"
                      :closable="false"
                      show-icon
                    >
                      <template slot="default">
                        <div class="note-content">
                          <div class="note-item">
                            • 纯英文内容140字内（含140字）计1条，超过140字按140字/条计费
                          </div>
                          <div class="note-item">
                            • 中文或中英文混合内容70字内（含70字）计1条，超过70字按70字/条计费
                          </div>
                        </div>
                      </template>
                    </el-alert>
                  </div> -->
                </div>
              </div>
            </el-card>

            <!-- 发送规则卡片 -->
            <el-card shadow="hover" class="form-card rules-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-warning-outline"></i>
                  国际短信计费规则
                </span>
              </div>
              <div class="rules-content">
                <el-alert
                  title="重要规则"
                  type="warning"
                  :closable="false"
                  show-icon
                >
                  <template slot="default">
                    <div class="rules-list">
                      <div class="rule-item">•纯英文内容<span class="highlight">140字内（含140字）</span>按每140字计一条</div>
                      <div class="rule-item">•中文内容<span class="highlight">70字内（含70字）</span>按每70字计一条</div>
                      <div class="rule-item">•中英文内容超过<span class="highlight">70字（含70字）</span>，按每70字计一条</div>
                    </div>
                  </template>
                </el-alert>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 实名认证弹框 -->
    <el-dialog title="实名认证" :visible.sync="dialogSmrzFlag" width="30%" center>
      <div class="auth-notice">
        <i class="el-icon-warning-outline auth-icon"></i>
        <p>尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goSmrz" size="large">前往实名认证</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "ImsSend",
  data() {
    var content = (rule, value, callback) => {
      if (!/^\d{4,6}$/.test(value)) {
        callback(new Error("请输入4-6位的验证码"));
      } else {
        callback();
      }
    };
    var mobile = (rule, value, callback) => {
      if (value != "") {
        if (!/^((00){1}[1-9]{1}[0-9]{1,3}|86|\+86)?1[3458]\d{9}$/.test(value)) {
          callback(new Error("请输入正确的手机号"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入手机号"));
      }
    };
    return {
      dialogSmrzFlag: false,
      endingName: "",
      fileList: [],
      fileStyle: {
        size: 245678943234,
        style: ["xlsx", "xls", "txt"],
      },
      smsCount: 0,
      tip: "仅支持.xlsx .xls.txt 等格式",
      // 预览相关数据
      currentTime: "",
      messageTime: "",
      formData: {
        content: "",
        mobile: "",
        type: "",
        sendType: "0",
        group: "",
        path: "",
        fileName: "",
        from: ""
      },
      // flag: true,
      header: {},
      fileList: [],
      options: [
        {
          value: "YZM",
          label: "验证码",
        },
        {
          value: "YX",
          label: "营销",
        },
      ],
      formRule: {
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
        type: [{ required: true, message: "请选择短信类型", trigger: "blur" }],
        mobile: [
          {
            required: true,
            message: "请输入手机号",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data);
        if (res.data.certificate == 0) {
          this.dialogSmrzFlag = true;
        }
        // this.certificate = res.data.certificate;
      }
    );
    
    // 初始化时间显示
    this.updateTime();
  },
  // activated(){
  //     this.$api.get(
  //     this.API.cpus + "consumerclientinfo/getClientInfo",
  //     null,
  //   (res) => {
  //     // console.log(res.data);
  //     if(res.data.certificate == 0){
  //         this.dialogSmrzFlag = true
  //     }
  //     // this.certificate = res.data.certificate;

  //   }
  // );
  // },
  methods: {
    goSmrz() {
      this.dialogSmrzFlag = false;
      this.$router.push("/authentication");
    },
    send() {
      console.log(this.formData.sendType, 'llthis.formData');
      this.$refs["formRef"].validate((valid, value) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            "确认发送短信？",
            this.API.cpus + "v1/consumerIms/send",
            this.formData,
            (res) => {
              if (res.code == 200) {
                // this.$refs["formRef"].resetFields();
                this.$router.push("/ImsSendDetails");
              }
            }
          );
        }
      });
    },
    handleInput(val) {
      const englishPattern = /^[a-zA-Z\s]*$/; // 仅英文字符
      const chinesePattern = /^[\u4e00-\u9fa5\s]*$/; // 仅中文字符
      // 混合文本：仅包含字母、汉字、数字和空格，但可包含特殊字符
      const mixedPattern = /[a-zA-Z\u4e00-\u9fa5\s\d\W]/;
      if (englishPattern.test(val)) {
        this.smsCount = Math.ceil(val.length / 140);
      } else if (chinesePattern.test(val)) {
        this.smsCount = Math.ceil(val.length / 70);
      } else if (mixedPattern.test(val)) {
        this.smsCount = Math.ceil(val.length / 70);
      }
    },
    Variabledownload() {
      this.$File.export(
        this.API.cpus + "v3/consumersms/templateZipDownload",
        {},
        `发送文件模板（变量模板可用）.zip`
      );
    },
    //限制用户上传文件格式和大小
    beforeAvatarUpload(file) {
      let endingCode = file.name;//结尾字符
      this.endingName = endingCode.slice(endingCode.lastIndexOf('.') + 1, endingCode.length);
      let isStyle = false; //文件格式
      const isSize = file.size / 1024 / 1024 < this.fileStyle.size; //文件大小
      console.log(isSize)
      for (let i = 0; i < this.fileStyle.style.length; i++) {
        if (this.endingName === this.fileStyle.style[i]) {
          isStyle = true;
          break;
        }
      }
      //不能重复上传文件
      let fileArr = this.fileList;
      let fileNames = [];
      if (fileArr.length > 0) {
        for (let k = 0; k < fileArr.length; k++) {
          fileNames.push(fileArr[k].name)
        }
      }
      if (fileNames.indexOf(endingCode) !== -1) {
        this.$message.error('不能重复上传文件');
        return false;
      } else if (!isStyle) { //文件格式判断
        this.$message.error(this.tip);
        return false;
      } else {
        //文件大小判断
        if (!isSize) {
          this.$message.error('上传文件大小不能超过' + this.fileStyle.size);
          return false;
        }
      }
    },
    handleAvatarSuccess(res, fileList) {
      if (res.code == 200) {
        // this.flag = false;
        this.formData.group = res.data.group;
        this.formData.path = res.data.path;
        this.formData.fileName = res.data.fileName;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemove() {
      // this.flag = true;
      this.formData.group = "";
      this.formData.path = "";
      this.formData.fileName = "";
      this.fileList = []
    },
    handelSend(val) {
      if (val == 0) {
        this.handleRemove()
      } else {
        this.formData.mobile = ''
      }
    },
    
    // 预览相关方法
    updateTime() {
      const now = new Date();
      this.currentTime = now.toTimeString().slice(0, 5);
      this.messageTime = now.toTimeString().slice(0, 5);
    },
    
    refreshPreview() {
      this.updateTime();
      this.$message.success('预览已刷新');
    },
  },
  mounted() { },
  watch: {},
};
</script>
<style lang="less" scoped>
// 引入发送短信通用样式
@import '~@/styles/send-details-common.less';

/* ImsSend 特有样式 */

/* 发送方式选择器 */
.send-type-selector {
  .modern-radio-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    width: 100%;

    .send-type-radio {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 20px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-color: #409eff;
        background: #f0f8ff;
      }

      /deep/ .el-radio__input.is-checked + .el-radio__label {
        color: #409eff;
      }

      /deep/ .el-radio__input.is-checked {
        .el-radio__inner {
          background: #409eff;
          border-color: #409eff;
        }
      }

      .radio-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;

        i {
          font-size: 18px;
          color: #409eff;
        }
      }

      .radio-desc {
        font-size: 13px;
        color: #666;
        line-height: 1.4;
      }
    }
  }
}

/* 文件上传区域 */
.upload-section {
  display: flex;
  align-items: flex-start;
  gap: 20px;

  .modern-upload {
    flex: 1;

    /deep/ .el-upload-dragger {
      border-radius: 12px;
      border: 2px dashed #d9d9d9;
      background: #fafafa;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        background: #f0f8ff;
      }

      .active {
        font-size: 48px !important;
        color: #c0c4cc !important;
        margin: 20px 0 12px !important;
        line-height: 1 !important;
      }

      .el-upload__text {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;

        em {
          color: #409eff;
          font-style: normal;
        }
      }
    }

    /deep/ .el-upload__tip {
      font-size: 12px;
      color: #999;
      margin-top: 8px;
    }
  }

  .template-download-btn {
    align-self: flex-start;
    margin-top: 20px;
    border-radius: 8px;
  }
}

/* 手机号码输入区域 */
.mobile-input-section {
  width: 100%;

  .mobile-textarea {
    width: 100%;

    /deep/ .el-textarea__inner {
      border-radius: 8px;
      border: 1px solid #dcdfe6;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .mobile-tip {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 8px;
    padding: 8px 12px;
    background: #f0f8ff;
    border-radius: 6px;
    font-size: 12px;
    color: #666;

    i {
      color: #409eff;
      font-size: 14px;
    }
  }
}

/* 计费规则样式 */
.billing-rules {
  .billing-rules-content {
    /deep/ .el-alert {
      border-radius: 8px;
      
      .el-alert__content {
        .billing-list {
          margin: 8px 0 0 0;
          padding-left: 20px;
          list-style: disc;

          li {
            margin-bottom: 8px;
            font-size: 13px;
            line-height: 1.5;

            .highlight {
              color: #f56c6c;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

/* 发送按钮样式 */
.send-action {
  margin-top: 32px;
  text-align: center;

  .send-btn {
    min-width: 150px;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

/* 手机预览区域 */
.preview-section {
  .preview-card {
    margin-bottom: 24px;
    
    /deep/ .el-card__body {
      padding: 24px;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .preview-actions {
        .refresh-btn {
          color: #409eff;
          padding: 4px;
          
          &:hover {
            background: #f0f8ff;
            border-radius: 4px;
          }
        }
      }
    }
  }

  .phone-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* 现代化手机外观 */
  .phone-mockup {
    margin-bottom: 24px;
    
    .phone-frame {
      width: 280px;
      height: 560px;
      background: #333;
      border-radius: 32px;
      padding: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      
      .phone-screen {
        width: 100%;
        height: 536px;
        background: #000;
        border-radius: 24px;
        overflow: hidden;
        position: relative;
        
        /* 状态栏 */
        .status-bar {
          height: 44px;
          background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 16px;
          color: #fff;
          font-size: 14px;
          font-weight: 600;
          
          .status-left {
            .time {
              font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            }
          }
          
          .status-right {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .signal-icon, .wifi-icon {
              width: 16px;
              height: 12px;
              background: #fff;
              border-radius: 2px;
              position: relative;
              
              &::before {
                content: '';
                position: absolute;
                width: 4px;
                height: 4px;
                background: #667eea;
                border-radius: 50%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              }
            }
            
            .battery {
              font-size: 12px;
            }
          }
        }
        
        /* 短信应用界面 */
        .sms-app {
          height: calc(100% - 44px);
          background: #f8f9fa;
          
          .sms-header {
            height: 60px;
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            padding: 0 16px;
            
            .contact-info {
              display: flex;
              align-items: center;
              gap: 12px;
              
              .contact-avatar {
                width: 40px;
                height: 40px;
                background: #409eff;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                font-size: 18px;
              }
              
              .contact-details {
                .contact-name {
                  font-size: 16px;
                  font-weight: 600;
                  color: #333;
                  line-height: 1.2;
                }
                
                .contact-number {
                  font-size: 12px;
                  color: #999;
                  line-height: 1.2;
                }
              }
            }
          }
          
          .sms-conversation {
            height: calc(100% - 60px);
            padding: 16px;
            overflow-y: auto;
            
            .message-container {
              display: flex;
              justify-content: flex-start;
              
              .message-bubble {
                &.received {
                  max-width: 200px;
                  background: #e3f2fd;
                  border-radius: 18px 18px 18px 6px;
                  padding: 12px 16px;
                  position: relative;
                  
                  .message-content {
                    .placeholder-text {
                      color: #999;
                      font-style: italic;
                      font-size: 13px;
                    }
                    
                    .sms-text {
                      color: #333;
                      font-size: 14px;
                      line-height: 1.4;
                      // word-wrap: break-word;
                      // white-space: pre-wrap;
                    }
                  }
                  
                  .message-time {
                    font-size: 11px;
                    color: #666;
                    margin-top: 4px;
                    text-align: right;
                  }
                }
              }
            }
          }
        }
      }
      
      .phone-button {
        position: absolute;
        bottom: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 4px;
        background: #666;
        border-radius: 2px;
      }
    }
  }

  /* 预览统计信息 */
  .preview-stats {
    width: 100%;
    
    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
      
      .stat-item {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 16px;
        text-align: center;
        border: 1px solid #e9ecef;
        
        .stat-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 8px;
        }
        
        .stat-value {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
          
          .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #409eff;
            
            &.over-limit {
              color: #f56c6c;
            }
          }
          
          .stat-unit {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
    
    .stats-note {
      /deep/ .el-alert {
        border-radius: 8px;
        
        .el-alert__content {
          .note-content {
            .note-item {
              font-size: 12px;
              line-height: 1.5;
              margin-bottom: 4px;
              
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }

  /* 发送规则卡片 */
  .rules-card {
    .rules-content {
      /deep/ .el-alert {
        border-radius: 8px;
        
        .el-alert__content {
          .rules-list {
            .rule-item {
              font-size: 13px;
              line-height: 1.6;
              margin-bottom: 8px;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              .highlight {
                color: #f56c6c;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
}

/* 实名认证弹框样式 */
.auth-notice {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fff7e6;
  border-radius: 8px;
  border: 1px solid #ffd591;

  .auth-icon {
    color: #fa8c16;
    font-size: 20px;
    margin-top: 2px;
    flex-shrink: 0;
  }

  p {
    color: #333;
    line-height: 1.6;
    margin: 0;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .send-form-layout {
    .form-section {
      span: 24;
      margin-bottom: 24px;
    }

    .preview-section {
      span: 24;
    }
  }

  .send-type-selector .modern-radio-group {
    grid-template-columns: 1fr;
  }

  .upload-section {
    flex-direction: column;
    align-items: stretch;

    .template-download-btn {
      align-self: center;
      margin-top: 16px;
    }
  }
}

@media (max-width: 768px) {
  .preview-section {
    .send-mobel-box {
      width: 240px;

      .sms-content-exhibition {
        width: 140px;
        left: 50px;
        padding: 10px;
        font-size: 11px;
      }
    }

    .content-stats {
      padding: 16px;

      .stats-item {
        font-size: 12px;

        .stats-value {
          font-size: 16px;
        }
      }
    }
  }

  .send-type-selector .modern-radio-group .send-type-radio {
    padding: 16px;

    .radio-label {
      font-size: 14px;
    }

    .radio-desc {
      font-size: 12px;
    }
  }
}
</style>

<style>
.el-textarea__inner {
  transition: all 0.3s ease;
}

/* 对话框增强 */
.el-dialog {
  border-radius: 12px;

  .el-dialog__header {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    color: #fff;
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;

    .el-dialog__title {
      color: #fff;
      font-weight: 600;
    }

    .el-dialog__close {
      color: #fff;
      font-size: 20px;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .el-dialog__body {
    padding: 32px 24px;
  }

  .el-dialog__footer {
    padding: 16px 24px 24px;
    text-align: center;

    .el-button {
      border-radius: 6px;
    }
  }
}
</style>