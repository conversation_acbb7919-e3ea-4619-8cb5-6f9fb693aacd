<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  v-permission
                  type="danger"
                  @click="batchDeletion"
                  v-if="selectId.length"
                  class="action-btn danger"
                  icon="el-icon-delete"
                >
                  批量取消
                </el-button>
                <el-button
                  @click="InquireList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ formInlines.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="formInline" :inline="true" ref="formInline" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="执行状态" prop="timingStatus" class="search-item">
                    <el-select
                      v-model="formInline.timingStatus"
                      placeholder="请选择状态"
                      class="search-input"
                      clearable
                    >
                      <el-option label="全部" value=""></el-option>
                      <el-option label="未执行" value="1"></el-option>
                      <el-option label="正在执行" value="2"></el-option>
                      <el-option label="取消" value="3"></el-option>
                      <el-option label="超时未执行" value="4"></el-option>
                      <el-option label="执行完成" value="5"></el-option>
                      <el-option label="执行失败" value="6"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="发送时间" prop="time" class="search-item date-item">
                    <el-date-picker
                      v-model="formInline.time"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      :clearable="false"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item label="提交时间" prop="time1" class="search-item date-item">
                    <el-date-picker
                      v-model="formInline.time1"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      :clearable="false"
                      @change="getTimeOperating1"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button
                      type="primary"
                      @click="ListSearch"
                      class="search-btn primary"
                      icon="el-icon-search"
                    >
                      查询
                    </el-button>
                    <el-button
                      @click="Reset('formInline')"
                      class="search-btn"
                      icon="el-icon-refresh"
                    >
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 国际短信任务列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">国际短信任务列表</h3>
            <span class="table-subtitle">可刷新页面查看最新发送进度</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载任务列表..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无任务数据"
              @selection-change="handelSelection"
            >
              <!-- 选择列 -->
              <el-table-column type="selection" width="50" align="center"></el-table-column>

              <!-- 提交时间 -->
              <el-table-column label="提交时间" width="180" align="center">
                <template slot-scope="scope">
                  {{ scope.row.createTime }}
                </template>
              </el-table-column>

              <!-- 短信内容 -->
              <el-table-column label="短信内容" min-width="300">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <div class="content-text">
                      <span class="signature">{{ scope.row.signature }}</span>
                      <span class="message-content">{{ scope.row.content }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 提交数量 -->
              <el-table-column prop="mobileNumber" label="提交数量" width="100" align="center">
                <template slot-scope="scope">
                  <span class="quantity-text">{{ scope.row.mobileNumber }}</span>
                </template>
              </el-table-column>

              <!-- 定时时间 -->
              <el-table-column prop="sendTime" label="定时时间" width="180" align="center">
                <template slot-scope="scope">
                  {{ scope.row.sendTime }}
                </template>
              </el-table-column>

              <!-- 提交状态 -->
              <el-table-column label="提交状态" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getStatusTagType(scope.row.timingStatus)"
                    size="small"
                  >
                    {{ getStatusText(scope.row.timingStatus) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 操作列 -->
              <el-table-column label="操作" width="100" align="center">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-tooltip content="取消任务" placement="top" v-if="scope.row.timingStatus == '1'">
                      <el-button
                        v-permission
                        type="text"
                        @click="cancel(scope.row)"
                        class="action-btn-small cancel"
                        icon="el-icon-error"
                      >
                        取消
                      </el-button>
                    </el-tooltip>
                    <span v-else class="no-action">-</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
              class="simple-pagination"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
import getNoce from '../../../../plugins/getNoce';
export default {
    name:'ImsSendTask',
    components:{
        DatePlugin,
        TableTem
    },
    data() {
        return {
            name:'ImsSendTask',
            //复选框值
            selectId:'',
            // 搜索数据
            formInline: {
                timingStatus:'',
                beginTime:'',
                endTime:'',
                startTime:'',
                stopTime:'',
                time: [],
                time1: [],
                pageSize:10,
                currentPage:1,
            },
            // 存储搜索数据
            formInlines:{
                timingStatus:'',
                beginTime:'',
                endTime:'',
                startTime:'',
                stopTime:'',
                time: [],
                time1: [],
                pageSize:10,
                currentPage:1,
            },
            //用户列表数据
            tableDataObj: {
                loading2:false,
                tablecurrent:{ //分页参数
                    total:0,
                },
                tableData: [],
            }
        };
    },
    methods: {
        //批量取消
        batchDeletion(){
            this.$confirms.confirmation('post','确定取消定时发送？',this.API.cpus+'consumertimingims/cancelTimingIms',{ids:this.selectId},res =>{
                this.InquireList()
            })
        },
                //列表复选框的值
        handelSelection(val){
            let selectId = [];
            for(let i=0;i<val.length;i++){
                selectId.push(val[i].taskSmsId)
            }
            this.selectId = selectId; //批量操作选中id
        },
        // 发送请求方法
        InquireList(){
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumertimingims/selectTimingPage',this.formInlines,res=>{
                this.tableDataObj.loading2=false;
                this.tableDataObj.tableData=res.data.records
                this.tableDataObj.tablecurrent.total=res.data.total
            })
        },
        // 编辑
        edit(val){
            if (Date.parse(val.sendTime) - Date.now()<1800000) {
                this.$message({
                    message: '定时小于现在30分钟无法编辑',
                    type: 'warning'
                });
            }else{
                if(val.timingStatus==3){
                    this.$router.push({ path: '/PersonalizedSendEditing?SmSId='+val.taskSmsId})
                }else{
                    this.$router.push({ path: '/SendSMSEdit?SmSId='+val.taskSmsId})
                }
            }
        },
        // 取消
        cancel(val){
            this.$confirms.confirmation('post','确定取消定时？',this.API.cpus+'consumertimingims/cancelTimingIms',{ids:[val.id]},res =>{
                this.InquireList()                    
            })
        },
        // 下载
       async download(val){
            // 时间过滤
            const nonce = await getNoce.useNonce();
            Date.prototype.format = function (format) {
            var args = {
                "M+": this.getMonth() + 1,
                "d+": this.getDate(),
                "h+": this.getHours(),
                "m+": this.getMinutes(),
                "s+": this.getSeconds(),
                "q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
                "S": this.getMilliseconds()
            };
            if (/(y+)/.test(format))
                format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var i in args) {
                var n = args[i];
                if (new RegExp("(" + i + ")").test(format))
                    format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length));
            }
            return format;
            };
            var that = this
            filedownload()
            function filedownload() {
            fetch(that.API.cpus +'v3/file/download?fileName='+val.fileMobile+'&group=group1&path='+val.filePath.slice(7), {
                method: 'get',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization':"Bearer "  + window.Vue.$common.getCookie('ZTGlS_TOKEN'),
                    'Once': nonce,
                },
                // body: JSON.stringify({
                //     batchNo: val.row.batchNo,
                // })
            })
            .then(res => res.blob())
            .then(data => {
                let blobUrl = window.URL.createObjectURL(data);
                download(blobUrl);
            });
            }
            function download(blobUrl) {
                var a = document.createElement('a');
                a.style.display = 'none';
                a.download ="("+ new Date().format("yyyy-MM-dd hh:mm:ss")+") "+val.fileMobile
                a.href = blobUrl;
                a.click();
            }
        },
        // 查询
        ListSearch(){
            Object.assign(this.formInlines,this.formInline);
            this.InquireList()
        },
        // 重置
        Reset(formName){
            this.$refs[formName].resetFields();
            this.formInline.time = [],
            this.formInline.beginTime = '';
            this.formInline.endTime = '';
            this.formInline.time1 = [],
            this.formInline.startTime=""
            this.formInline.stopTime=""
            Object.assign(this.formInlines,this.formInline)
            this.InquireList()
        },
        // 定时时间
        getTimeOperating(val){
            if(val){
                this.formInline.beginTime=val[0]+" 00:00:00"
                this.formInline.endTime=val[1]+" 23:59:59"
            }else{
                this.formInline.beginTime=""
                this.formInline.endTime=""
            }
        },
        // 提交时间
        getTimeOperating1(val){
            if(val){
                this.formInline.startTime=val[0]+" 00:00:00"
                this.formInline.stopTime=val[1]+" 23:59:59"
            }else{
                this.formInline.startTime=""
                this.formInline.stopTime=""
            }
        },
        // 分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
            this.InquireList()
        },
        handleCurrentChange: function(currentPage){
            this.formInlines.currentPage = currentPage;
            this.InquireList()
        },
        
        // 获取状态标签类型
        getStatusTagType(status) {
            const statusMap = {
                '1': 'info',      // 未执行
                '2': 'warning',   // 正在执行
                '3': 'danger',    // 取消
                '4': 'danger',    // 超时未执行
                '5': 'success',   // 执行完成
                '6': 'danger'     // 执行失败
            };
            return statusMap[status] || 'info';
        },
        
        // 获取状态文本
        getStatusText(status) {
            const statusMap = {
                '1': '未执行',
                '2': '正在执行',
                '3': '取消',
                '4': '超时未执行',
                '5': '执行完成',
                '6': '执行失败'
            };
            return statusMap[status] || '未知';
        },
    },
    created(){
        this.InquireList()
    },
    // activated(){
    //     this.InquireList()
    // },
    watch:{
        // 监听搜索/分页数据
        // formInlines:{
        //     handler() {
        //         this.InquireList()
        //     },
        //     deep: true,
        //     immediate: true,
        // },
    },
}
</script>
<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* ImsSendTask 特有样式 */

/* 内容单元格样式 */
.content-cell {
  word-break: break-all;
  line-height: 1.4;
}

.content-text {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .signature {
    font-weight: 600;
    color: #409eff;
    font-size: 13px;
  }

  .message-content {
    color: #333;
    font-size: 14px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
  }
}

/* 数量文本样式 */
.quantity-text {
  font-weight: 600;
  color: #409eff;
  font-size: 14px;
}

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  align-items: center;
  justify-content: center;

  .action-btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &.cancel {
      color: #f56c6c;
      &:hover {
        color: #f78989;
        background: rgba(245, 108, 108, 0.1);
      }
    }
  }

  .no-action {
    color: #c0c4cc;
    font-size: 14px;
  }
}

/* 危险按钮样式 */
.action-btn.danger {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  border: none;
  color: #fff;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);

  &:hover {
    background: linear-gradient(135deg, #f78989 0%, #f9a3a4 100%);
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

/* 搜索项特殊样式 */
.search-section {
  .search-row {
    .date-item {
      min-width: 240px;

      .search-date {
        width: 100%;
      }
    }
  }
}

/* 状态标签增强 */
/deep/ .el-tag {
  font-weight: 500;
  border-radius: 12px;
  padding: 0 12px;
  height: 24px;
  line-height: 22px;
  border: none;

  &.el-tag--info {
    background: #e8f4fd;
    color: #409eff;
  }

  &.el-tag--warning {
    background: #fdf6ec;
    color: #e6a23c;
  }

  &.el-tag--success {
    background: #f0f9ff;
    color: #67c23a;
  }

  &.el-tag--danger {
    background: #fef0f0;
    color: #f56c6c;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-section {
    .search-row {
      .search-item {
        margin-bottom: 16px;
      }

      .date-item {
        min-width: 200px;
      }
    }
  }

  .content-text {
    .signature {
      font-size: 12px;
    }

    .message-content {
      font-size: 13px;
      -webkit-line-clamp: 1;
    }
  }
}

@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .stats-info {
      font-size: 12px;

      .stats-text {
        font-size: 12px;
      }
    }
  }

  .search-section {
    .search-row {
      .date-item {
        min-width: auto;
        width: 100%;
      }

      .search-buttons {
        width: 100%;

        .search-btn {
          flex: 1;
        }
      }
    }
  }

  .table-actions {
    .action-btn-small {
      font-size: 11px;
      padding: 2px 6px;
    }
  }

  .quantity-text {
    font-size: 12px;
  }

  /deep/ .el-tag {
    font-size: 11px;
    height: 20px;
    line-height: 18px;
    padding: 0 8px;
  }
}
</style>
