<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="$router.push({ path: '/videoTemplateCreate' })"
                  class="action-btn primary"
                  icon="el-icon-plus"
                >
                  创建模板
                </el-button>
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ formInlines.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="formInline" :inline="true" ref="formInline" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="标题" prop="title" class="search-item">
                    <el-input
                      v-model="formInline.title"
                      placeholder="请输入标题"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="签名" prop="signature" class="search-item">
                    <el-input
                      v-model="formInline.signature"
                      placeholder="请输入签名"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="备注" prop="remark" class="search-item">
                    <el-input
                      v-model="formInline.remark"
                      placeholder="请输入备注"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="创建时间" prop="time" class="search-item date-item">
                    <el-date-picker
                      v-model="formInline.time"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button
                      type="primary"
                      @click="ListSearch"
                      class="search-btn primary"
                      icon="el-icon-search"
                    >
                      查询
                    </el-button>
                    <el-button
                      @click="Reset('formInline')"
                      class="search-btn"
                      icon="el-icon-refresh"
                    >
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 视频模板列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">视频模板列表</h3>
            <span class="table-subtitle">可刷新页面查看最新审核状态</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载视频模板..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无视频模板数据"
            >
              <!-- 模板ID -->
              <el-table-column prop="videoId" label="模板ID" width="80" align="center">
                <template slot-scope="scope">
                  {{ scope.row.videoId }}
                </template>
              </el-table-column>

              <!-- 签名 -->
              <el-table-column label="签名" width="120">
                <template slot-scope="scope">
                  <div class="content-cell">
                    {{ scope.row.signature }}
                  </div>
                </template>
              </el-table-column>

              <!-- 标题 -->
              <el-table-column label="标题" min-width="300">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <div class="content-text">{{ scope.row.title }}</div>
                  </div>
                </template>
              </el-table-column>

              <!-- 备注 -->
              <el-table-column label="备注" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    {{ scope.row.remark || '-' }}
                  </div>
                </template>
              </el-table-column>

              <!-- 变量模板 -->
              <el-table-column label="变量模板" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.variableTemplate ? 'success' : 'info'"
                    size="small"
                  >
                    {{ scope.row.variableTemplate ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 预览 -->
              <el-table-column label="预览" width="90" align="center">
                <template slot-scope="scope">
                  <el-tooltip content="预览视频模板" placement="top">
                    <el-button
                      type="text"
                      @click="View(scope.row)"
                      class="action-btn-small preview"
                      icon="el-icon-view"
                    >
                      预览
                    </el-button>
                  </el-tooltip>
                </template>
              </el-table-column>

              <!-- 创建时间 -->
              <el-table-column label="创建时间" width="180" align="center">
                <template slot-scope="scope">
                  {{ scope.row.createTime }}
                </template>
              </el-table-column>

              <!-- 审核状态 -->
              <el-table-column label="审核状态" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getStatusTagType(scope.row.status)"
                    size="small"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 运营商状态 -->
              <el-table-column label="运营商状态" width="140" align="center">
                <template slot-scope="scope">
                  <div class="carrier-status">
                    <el-tooltip content="移动" placement="top">
                      <i
                        class="iconfont icon-yidong carrier-icon"
                        :class="getCarrierStatusClass(scope.row.ydChannelStatus)"
                      ></i>
                    </el-tooltip>
                    <el-tooltip content="联通" placement="top">
                      <i
                        class="iconfont icon-liantong carrier-icon"
                        :class="getCarrierStatusClass(scope.row.ltChannelStatus)"
                      ></i>
                    </el-tooltip>
                    <el-tooltip content="电信" placement="top">
                      <i
                        class="iconfont icon-dianxin carrier-icon"
                        :class="getCarrierStatusClass(scope.row.dxChannelStatus)"
                      ></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 操作列 -->
              <el-table-column label="操作" width="180" fixed="right">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-tooltip content="复制模板" placement="top">
                      <el-button
                        v-permission
                        type="text"
                        @click="copyVideo(scope.row)"
                        class="action-btn-small copy"
                        icon="el-icon-document-copy"
                      >
                        复制
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="编辑模板" placement="top" v-if="scope.row.status != 2">
                      <el-button
                        v-permission
                        type="text"
                        @click="editVideo(scope.row)"
                        class="action-btn-small edit"
                        icon="el-icon-edit"
                      >
                        编辑
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="删除模板" placement="top">
                      <el-button
                        v-permission
                        type="text"
                        @click="deleteVideo(scope.row.videoId)"
                        class="action-btn-small delete"
                        icon="el-icon-delete"
                      >
                        删除
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
              class="simple-pagination"
            />
          </div>
        </div>
      </div>
    </div>
        <!-- 预览手机弹框   -->
        <el-dialog title="预览" :visible.sync="dialogVisible" width="40%">
            <div>
                <div class="send-mobel-box">
                    <img src="../../../../assets/images/phone.png" alt="">
                    <div class="mms-content-exhibition">
                        <el-scrollbar class="sms-content-exhibition">
                            <div style="width: 253px;">
                                <span
                                    style="display: inline-block;padding: 5px;border-radius: 5px;background: #e2e2e2;margin-top: 5px;">{{ title }}</span>
                            </div>
                            <div style="overflow-wrap: break-word;width: 234px;background: #e2e2e2;padding: 10px;border-radius: 5px;margin-top: 5px;"
                                v-for="(item, index) in viewData" :key=index>
                                <img v-if="item.media == 'jpg' || item.media == 'gif' || item.media == 'png' || item.media == 'jpeg'"
                                    :src="API.imgU + item.mediaGroup + '/' + item.mediaPath" style="width: 235px;"
                                    class="avatar video-avatar" ref="avatar">
                                <video v-if="item.type == 'video'" style="width: 235px;"
                                    v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath" class="avatar video-avatar"
                                    controls="controls">
                                </video>
                                <audio v-if="item.type == 'audio'" style="width: 235px;" autoplay="autoplay"
                                    controls="controls" preload="auto"
                                    v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath">
                                </audio>
                                <div style="white-space: pre-line;" v-html="item.txt"></div>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import TableTem from '@/components/publicComponents/TableTem'
import { mapState, mapMutations, mapActions } from "vuex";

export default {
    name: 'videoTemplate',
    components: {
        TableTem
    },
    data() {
        return {
            dialogVisible: false,
            viewData: [],// 查看内容
            title: '',
            // 搜索数据
            formInline: {
                title: '',
                signature:"",
                remark: "",
                beginTime: '',
                endTime: '',
                time: [],
                pageSize: 10,
                currentPage: 1,
            },
            // 存储搜索数据
            formInlines: {
                title: '',
                signature:"",
                remark: "",
                beginTime: '',
                endTime: '',
                time: [],
                pageSize: 10,
                currentPage: 1,
            },
            //用户列表数据
            tableDataObj: {
                loading2: false,
                tablecurrent: { //分页参数
                    total: 0,
                },
                tableData: [],
            }
        };
    },
    methods: {
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveMMS'
        ]),
        // 发送请求方法
        InquireList() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'v1/consumervideo', this.formInlines, res => {
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tableData = res.data.records
                this.tableDataObj.tablecurrent.total = res.data.total
            })
        },
        // 预览
        View(val) {
            this.viewData = val.contents
            this.title = val.title
            this.dialogVisible = true
        },
        // 查询
        ListSearch() {
            Object.assign(this.formInlines, this.formInline);
            this.InquireList()
        },
        // 重置
        Reset(formName) {
            this.$refs[formName].resetFields();
            this.formInline.time = [],
                this.formInline.beginTime = '';
            this.formInline.endTime = '';
            this.formInline.time1 = [],
                this.formInline.startTime = ""
            this.formInline.stopTime = ""
            Object.assign(this.formInlines, this.formInline)
        },
        // 分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
        },
        handleCurrentChange: function (currentPage) {
            this.formInlines.currentPage = currentPage;
        },
        // 创建时间
        getTimeOperating(val) {
            if (val) {
                this.formInline.beginTime = val[0] + " 00:00:00"
                this.formInline.endTime = val[1] + " 23:59:59"
            } else {
                this.formInline.beginTime = ""
                this.formInline.endTime = ""
            }
        },
        // 编辑模板
        editVideo(val) {
            // console.log(val, '编辑模板');
            
            // let contentMMS = {
            //     contentMMS: {
            //         contents: []
            //     }
            // }
            // contentMMS.contentMMS.contents = val.contents
            // contentMMS.contentMMS.title = val.title
            // localStorage.setItem('contentMMS', JSON.stringify(contentMMS))
            // this.saveMMS(contentMMS);
            this.$router.push({ path: '/videoTemplateCreate', query: { id: val.videoId } })
        },
        // 删除模板
        deleteVideo(val) {
            this.$confirms.confirmation('delete', '确定删除当前模板？', this.API.cpus + 'v1/consumervideo/' + val, {}, res => {
                this.InquireList()
            })
        },
        copyVideo(val) {
            if (val) {
                // let contentMMS = {
                //     contentMMS: {
                //         contents: []
                //     }
                // }
                // contentMMS.contentMMS.contents = val.contents
                // contentMMS.contentMMS.title = val.title
                // this.saveMMS(contentMMS);
                this.$confirms.confirmation('post', '确定复制当前模板？', this.API.cpus + `v1/consumervideo/copy?videoId=${val.videoId}`, {}, res => {
                    // this.InquireList()
                    if (res.code == 200) {
                        this.$router.push({ path: '/videoTemplateCreate', query: { id: res.data.videoId } })
                    }

                })
            }
        },
        
        // 刷新列表
        refreshList() {
            this.InquireList();
        },
        
        // 获取审核状态标签类型
        getStatusTagType(status) {
            const statusMap = {
                0: 'info',      // 编辑中
                1: 'warning',   // 未审核
                2: 'success',   // 通过
                3: 'danger'     // 未通过
            };
            return statusMap[status] || 'info';
        },
        
        // 获取审核状态文本
        getStatusText(status) {
            const statusMap = {
                0: '编辑中',
                1: '未审核',
                2: '通过',
                3: '未通过'
            };
            return statusMap[status] || '未知';
        },
        
        // 获取运营商状态样式类
        getCarrierStatusClass(status) {
            const statusMap = {
                0: 'status-default',
                1: 'status-success',
                2: 'status-error'
            };
            return statusMap[status] || 'status-default';
        }
    },
    // activated(){
    //     this.InquireList()
    // },
    watch: {
        // 监听搜索/分页数据
        formInlines: {
            handler() {
                this.InquireList()
            },
            deep: true,
            immediate: true,
        },
    },
}
</script>
<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* VideoTemplate 特有样式 */

/* 内容单元格样式 */
.content-cell {
  word-break: break-all;
  line-height: 1.4;
}

.content-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}

/* 表格操作按钮样式 */
.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;

  .action-btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &.copy {
      color: #409eff;
      &:hover {
        color: #66b1ff;
        background: rgba(64, 158, 255, 0.1);
      }
    }

    &.edit {
      color: #67c23a;
      &:hover {
        color: #85ce61;
        background: rgba(103, 194, 58, 0.1);
      }
    }

    &.delete {
      color: #f56c6c;
      &:hover {
        color: #f78989;
        background: rgba(245, 108, 108, 0.1);
      }
    }

    &.preview {
      color: #e6a23c;
      &:hover {
        color: #ebb563;
        background: rgba(230, 162, 60, 0.1);
      }
    }
  }
}

/* 运营商状态样式 */
.carrier-status {
  display: flex;
  justify-content: center;
  gap: 8px;

  .carrier-icon {
    font-size: 20px;
    transition: all 0.3s ease;

    &.status-default {
      color: #ddd;
    }

    &.status-success {
      color: #67c23a;
    }

    &.status-error {
      color: #f56c6c;
    }
  }
}

/* 预览对话框样式 */
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin: 0 auto 35px;
  display: flex;
  justify-content: center;
}

.send-mobel-box img {
  width: 300px;
}

.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 10px 0px 45px;
  overflow: auto;
  overflow-y: auto;
}

/* 预览对话框增强 */
/deep/ .el-dialog {
  border-radius: 12px;

  .el-dialog__header {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    color: #fff;
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;

    .el-dialog__title {
      color: #fff;
      font-weight: 600;
    }

    .el-dialog__close {
      color: #fff;
      font-size: 20px;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .el-dialog__body {
    padding: 32px 24px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .carrier-status {
    flex-direction: column;
    gap: 4px;

    .carrier-icon {
      font-size: 16px;
    }
  }

  .table-actions {
    flex-direction: column;
    gap: 4px;

    .action-btn-small {
      font-size: 11px;
      padding: 2px 6px;
    }
  }

  .send-mobel-box {
    width: 250px;

    img {
      width: 250px;
    }

    .mms-content-exhibition {
      width: 250px;
      margin: 113px 21px 0px 21px;
    }
  }
}
</style>
