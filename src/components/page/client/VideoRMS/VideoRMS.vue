<template>
  <div class="simple-videorms-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="refreshPage"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新页面
                </el-button>
                <el-button
                  type="primary"
                  @click="addMMS"
                  class="action-btn"
                  icon="el-icon-plus"
                  v-show="active==2"
                >
                  新建视频短信
                </el-button>
                <el-button
                  @click="WebTask"
                  class="action-btn"
                  icon="el-icon-view"
                  v-show="active==2"
                >
                  查看待发
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">视频短信创建</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-step">步骤 {{ active + 1 }}/3</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 使用流程卡片 -->
        <div class="workflow-section">
          <div class="workflow-card">
            <div class="workflow-header">
              <h3 class="workflow-title">
                <i class="el-icon-guide"></i>
                使用流程
              </h3>
            </div>
            <div class="workflow-content">
              <el-steps :active="active" class="workflow-steps">
                <el-step
                  title="选择已审核模板"
                  icon="el-icon-edit"
                  description="选择已通过审核的视频短信模板"
                />
                <el-step
                  title="录入手机号"
                  icon="el-icon-phone"
                  description="输入或上传接收视频短信的手机号码"
                />
                <el-step
                  title="发送提交"
                  icon="el-icon-s-promotion"
                  description="确认信息并提交发送任务"
                />
              </el-steps>
            </div>
          </div>
        </div>

        <!-- 步骤内容区域 -->
        <div class="step-content-section">
          <!-- 创建模板 -->
          <div v-show="active==0" class="step-content">
            <create @childrenNextStep="childrenNextStep" :key="Reorganization" />
          </div>

          <!-- 号码录入 -->
          <div v-show="active==1" class="step-content">
            <EnterTheNumber @childrenPrevious="childrenPrevious" :header='header' />
          </div>

          <!-- 提交完成 -->
          <div v-show="active==2" class="step-content">
            <el-row class="send-preview-layout">
              <!-- 左侧预览区域 -->
              <el-col :span="10" class="preview-section">
                <el-card shadow="hover" class="preview-card">
                  <div slot="header" class="card-header">
                    <span class="card-title">
                      <i class="el-icon-mobile-phone"></i>
                      发送预览
                    </span>
                  </div>
                  
                  <div class="mobile-preview">
                    <div class="mobile-device">
                      <div class="mobile-screen">
                        <div class="mobile-header">
                          <span class="mobile-time">{{ getCurrentTime() }}</span>
                          <div class="mobile-status">
                            <i class="el-icon-wifi"></i>
                            <i class="el-icon-battery-full"></i>
                          </div>
                        </div>
                        
                        <div class="message-container">
                          <div class="message-bubble">
                            <div class="message-content">
                              <div class="video-message">
                                <div class="video-icon">
                                  <i class="el-icon-video-play"></i>
                                </div>
                                <div class="video-info">
                                  <div class="video-title">{{ title || '视频短信标题' }}</div>
                                  <div class="video-desc">点击查看视频内容</div>
                                </div>
                              </div>
                            </div>
                            <div class="message-time">刚刚</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>

              <!-- 右侧信息区域 -->
              <el-col :span="14" class="info-section">
                <el-card shadow="hover" class="info-card">
                  <div slot="header" class="card-header">
                    <span class="card-title">
                      <i class="el-icon-success"></i>
                      发送信息
                    </span>
                    <el-tag type="success" size="medium">提交成功</el-tag>
                  </div>

                  <div class="send-info-content">
                    <!-- 基本信息 -->
                    <div class="info-section-block">
                      <h4 class="section-title">基本信息</h4>
                      <div class="info-list">
                        <div class="info-item">
                          <span class="info-label">视频短信标题</span>
                          <span class="info-value">{{ title }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">视频短信ID</span>
                          <span class="info-value">{{ videoId }}</span>
                        </div>
                        <div class="info-item">
                          <span class="info-label">创建时间</span>
                          <span class="info-value">{{ time }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- 发送进度 -->
                    <div class="info-section-block">
                      <h4 class="section-title">发送进度</h4>
                      <div v-if="getTepId()!=null" class="process-timeline">
                        <el-timeline>
                          <el-timeline-item
                            timestamp="已完成"
                            placement="top"
                            color="#67c23a"
                            icon="el-icon-success"
                          >
                            <h4>创建视频短信</h4>
                            <p>视频短信创建完成，准备发送</p>
                          </el-timeline-item>
                          <el-timeline-item
                            timestamp="进行中"
                            placement="top"
                            color="#409eff"
                            icon="el-icon-loading"
                          >
                            <h4>发送中</h4>
                            <p>任务已提交，正在发送中</p>
                          </el-timeline-item>
                        </el-timeline>
                      </div>
                      <div v-else class="process-timeline">
                        <el-timeline>
                          <el-timeline-item
                            timestamp="已完成"
                            placement="top"
                            color="#67c23a"
                            icon="el-icon-success"
                          >
                            <h4>创建视频短信</h4>
                            <p>视频短信创建完成</p>
                          </el-timeline-item>
                          <el-timeline-item
                            timestamp="进行中"
                            placement="top"
                            color="#e6a23c"
                            icon="el-icon-view"
                          >
                            <h4>平台审核</h4>
                            <p>等待平台审核，预计2小时内完成</p>
                          </el-timeline-item>
                          <el-timeline-item
                            timestamp="待处理"
                            placement="top"
                            color="#909399"
                          >
                            <h4>发送完成</h4>
                            <p>审核通过后自动发送</p>
                          </el-timeline-item>
                        </el-timeline>
                      </div>
                    </div>

                    <!-- 操作提示 -->
                    <div class="info-section-block">
                      <el-alert
                        title="温馨提示"
                        type="info"
                        :closable="false"
                        show-icon
                      >
                        <template slot="default">
                          <div class="alert-content">
                            <p>• 视频短信已成功创建，您可以在"查看待发"中查看发送状态</p>
                            <p>• 审核通过后，系统将自动发送视频短信到目标手机号</p>
                            <p>• 如需修改内容，请重新创建视频短信</p>
                          </div>
                        </template>
                      </el-alert>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <!-- 实名认证弹窗 -->
    <el-dialog
      title="实名认证"
      :visible.sync="dialogSmrzFlag"
      width="480px"
      center
      class="auth-dialog"
    >
      <div class="auth-content">
        <div class="auth-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="auth-message">
          <p class="auth-title">需要完成实名认证</p>
          <p class="auth-desc">
            尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。
          </p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogSmrzFlag = false">稍后认证</el-button>
        <el-button type="primary" @click="goSmrz">
          <i class="el-icon-right"></i>
          前往实名认证
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import create from './components/create.vue'
import EnterTheNumber from './components/EnterTheNumber.vue'
import { mapActions } from "vuex";
export default {
    name: "VideoRMS",
    components:{
        create,
        EnterTheNumber
    },
    data(){
        return{
            header:{},
            active:0,
            time:'',
            title:'',
            videoId:'',
            Reorganization:'',
            dialogSmrzFlag :false,
        }
    },
    created(){
        this.header = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
        this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
      (res) => {
        // console.log(res.data);
        if(res.data.certificate == 0){
            this.dialogSmrzFlag = true
        }
        // this.certificate = res.data.certificate;

      }
    );
    },
    // activated(){
    //     this.$api.get(
    //     this.API.cpus + "consumerclientinfo/getClientInfo",
    //     null,
    //   (res) => {
    //     // console.log(res.data);
    //     if(res.data.certificate == 0){
    //         this.dialogSmrzFlag = true
    //     }
    //     // this.certificate = res.data.certificate;

    //   }
    // );
    // },
    methods:{
        goSmrz(){
            this.dialogSmrzFlag = false
            this.$router.push("/authentication")
        },
        // 获取当前时间
        getCurrentTime() {
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        },
        childrenNextStep(){
            this.active=1
        },
        childrenPrevious(val){
            if(val){
                this.time=val.createTime
                this.videoId=val.videoId
                this.title=val.title
                this.active=2
            }else{
                this.active=0
            }
        },
        addMMS(){
            this.Reorganization = new Date().getTime()
            this.active=0
        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveUrls'
        ]),
        WebTask(){
            // 存储点击的菜单
            let logUrls={
                logUrls:'VideoWebTask'
            } 
            this.saveUrls(logUrls);
            window.sessionStorage.setItem('logUrls','VideoWebTask')
            this.$router.push({ path: '/VideoWebTask'})
        },
        getTepId(){
            return window.sessionStorage.getItem('tpeId')
        },

        // 刷新页面
        refreshPage() {
            this.Reorganization = new Date().getTime();
            // 可以根据需要重置到第一步或保持当前步骤
        }
    },
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* VideoRMS 特有样式 */
.simple-videorms-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

/* 使用流程卡片样式 */
.workflow-section {
  margin-bottom: 24px;
}

.workflow-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.workflow-header {
  // background: #409eff;
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  padding: 20px 24px;
  color: #fff;

  .workflow-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 20px;
    }
  }
}

.workflow-content {
  padding: 32px 24px;
}

.workflow-steps {
  /deep/ .el-step__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  /deep/ .el-step__description {
    font-size: 13px;
    color: #666;
    margin-top: 4px;
  }

  /deep/ .el-step__icon {
    width: 32px;
    height: 32px;

    .el-step__icon-inner {
      font-size: 16px;
    }
  }

  /deep/ .el-step.is-process .el-step__icon {
    background: #409eff;
    border-color: #409eff;
  }

  /deep/ .el-step.is-finish .el-step__icon {
    background: #67c23a;
    border-color: #67c23a;
  }
}

/* 步骤内容样式 */
.step-content-section {
  margin-top: 24px;
}

.step-content {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 发送预览布局 */
.send-preview-layout {
  gap: 24px;
  padding: 24px;
}

.preview-section,
.info-section {
  .el-card {
    height: 100%;
    border-radius: 12px;
    border: none;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;

  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 20px;
      color: #409eff;
    }
  }
}

/* 手机预览样式 */
.mobile-preview {
  display: flex;
  justify-content: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.mobile-device {
  width: 320px;
  height: 640px;
  background: #000;
  border-radius: 36px;
  padding: 8px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 25px;
    background: #000;
    border-radius: 0 0 20px 20px;
  }
}

.mobile-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 28px;
  overflow: hidden;
  position: relative;
}

.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  font-size: 12px;

  .mobile-time {
    font-weight: 600;
    color: #333;
  }

  .mobile-status {
    display: flex;
    gap: 4px;
    color: #666;

    i {
      font-size: 14px;
    }
  }
}

.message-container {
  padding: 16px;
  background: #f5f5f5;
  min-height: 500px;
}

.message-bubble {
  background: #fff;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 80%;
  margin-left: auto;
}

.message-content {
  .video-message {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f2f5;
    }

    .video-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 24px;
        color: #fff;
      }
    }

    .video-info {
      flex: 1;

      .video-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .video-desc {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

.message-time {
  font-size: 11px;
  color: #999;
  text-align: right;
  margin-top: 8px;
}

/* 发送信息样式 */
.send-info-content {
  padding: 24px;
}

.info-section-block {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
    display: inline-block;
  }
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .info-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      width: 120px;
      font-size: 14px;
      color: #666;
      flex-shrink: 0;
    }

    .info-value {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }
}

/* 时间线样式 */
.process-timeline {
  padding: 16px 0;

  /deep/ .el-timeline {
    padding-left: 20px;
  }

  /deep/ .el-timeline-item__wrapper {
    padding-left: 32px;
  }

  /deep/ .el-timeline-item__timestamp {
    font-size: 12px;
    color: #666;
  }

  /deep/ .el-timeline-item__content {
    h4 {
      font-size: 15px;
      font-weight: 600;
      color: #333;
      margin: 0 0 4px 0;
    }

    p {
      font-size: 13px;
      color: #666;
      margin: 0;
    }
  }

  /deep/ .el-timeline-item__node {
    width: 16px;
    height: 16px;
  }

  /deep/ .el-timeline-item__node--normal {
    width: 12px;
    height: 12px;
    left: 2px;
  }

  /deep/ .el-timeline-item__icon {
    font-size: 12px;
  }
}

/* 提示内容样式 */
.alert-content {
  p {
    margin: 4px 0;
    font-size: 13px;
    line-height: 1.6;
  }
}

/* 实名认证弹窗样式 */
.auth-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;
  }

  /deep/ .el-dialog__header {
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  /deep/ .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  /deep/ .el-dialog__body {
    padding: 24px;
  }
}

.auth-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;

  .auth-icon {
    flex-shrink: 0;

    i {
      font-size: 32px;
      color: #e6a23c;
    }
  }

  .auth-message {
    flex: 1;

    .auth-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    .auth-desc {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      margin: 0;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workflow-content {
    padding: 24px 16px;
  }

  .send-preview-layout {
    padding: 16px;
  }

  .preview-section,
  .info-section {
    margin-bottom: 16px;
  }

  .mobile-device {
    width: 280px;
    height: 560px;
  }

  .auth-content {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<style>
/* 全局样式覆盖 */
.simple-videorms-page .el-step__icon-inner {
  font-size: 16px !important;
  font-weight: 400 !important;
  font-style: normal;
}

.simple-videorms-page .el-steps--horizontal .el-step__line {
  top: 16px;
}
</style>

