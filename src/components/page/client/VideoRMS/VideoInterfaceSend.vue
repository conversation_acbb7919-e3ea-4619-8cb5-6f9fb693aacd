<template>
    <div class="simple-sendtask-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <el-button
                                    v-permission
                                    v-if="selectId.length"
                                    @click="batchDeletion"
                                    class="action-btn danger"
                                    icon="el-icon-error"
                                >
                                    批量取消
                                </el-button>
                                <el-button
                                    @click="InquireList"
                                    class="action-btn"
                                    icon="el-icon-refresh"
                                >
                                    刷新列表
                                </el-button>
                            </div>

                            <!-- 统计信息 -->
                            <div class="stats-info">
                                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                                <el-divider direction="vertical"></el-divider>
                                <span class="stats-text">当前第 {{ formInlines.currentPage }} 页</span>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <el-form :model="formInline" :inline="true" ref="formInline" class="advanced-search-form">
                                <div class="search-row">
                                    <el-form-item label="发送状态" prop="timingStatus" class="search-item">
                                        <el-select v-model="formInline.timingStatus" placeholder="请选择状态" class="search-select" clearable>
                                            <el-option label="全部" value=""></el-option>
                                            <el-option label="未执行" value="1"></el-option>
                                            <el-option label="正在执行" value="2"></el-option>
                                            <el-option label="取消" value="3"></el-option>
                                            <el-option label="超时未执行" value="4"></el-option>
                                            <el-option label="执行完成" value="5"></el-option>
                                            <el-option label="执行失败" value="6"></el-option>
                                        </el-select>
                                    </el-form-item>

                                    <el-form-item label="消息ID" prop="msgid" class="search-item">
                                        <el-input
                                            v-model="formInline.msgid"
                                            placeholder="请输入消息ID"
                                            class="search-input"
                                            clearable
                                        />
                                    </el-form-item>

                                    <el-form-item label="标题" prop="title" class="search-item">
                                        <el-input
                                            v-model="formInline.title"
                                            placeholder="请输入标题"
                                            class="search-input"
                                            clearable
                                        />
                                    </el-form-item>

                                    <el-form-item label="发送时间" prop="time" class="search-item date-item">
                                        <el-date-picker
                                            v-model="formInline.time"
                                            value-format="yyyy-MM-dd"
                                            type="daterange"
                                            range-separator="-"
                                            @change="getTimeOperating"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            class="search-date"
                                            :clearable="false"
                                        />
                                    </el-form-item>

                                    <el-form-item class="search-buttons">
                                        <el-button
                                            type="primary"
                                            @click="ListSearch"
                                            class="search-btn primary"
                                            icon="el-icon-search"
                                        >
                                            查询
                                        </el-button>
                                        <el-button
                                            @click="Reset('formInline')"
                                            class="search-btn"
                                            icon="el-icon-refresh"
                                        >
                                            重置
                                        </el-button>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>

                <!-- 接口发送任务列表 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3 class="table-title">接口发送任务列表</h3>
                        <span class="table-subtitle">可刷新页面查看最新发送进度</span>
                    </div>

                    <div class="table-container">
                        <el-table
                            v-loading="tableDataObj.loading2"
                            element-loading-text="正在加载发送任务..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(255, 255, 255, 0.9)"
                            ref="multipleTable"
                            border
                            :data="tableDataObj.tableData"
                            class="enhanced-table"
                            stripe
                            :header-cell-style="{ background: '#fafafa', color: '#333' }"
                            :row-style="{ height: '60px' }"
                            @selection-change="handelSelection"
                            empty-text="暂无发送任务数据"
                        >
                            <!-- 选择列 -->
                            <el-table-column type="selection" width="46"></el-table-column>

                            <!-- 发送ID -->
                            <el-table-column prop="id" label="发送ID" width="140" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.id }}
                                </template>
                            </el-table-column>

                            <!-- 消息ID -->
                            <el-table-column label="消息ID" width="240">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <span class="msgid-text">{{ scope.row.msgid }}</span>
                                        <el-tooltip content="复制消息ID" placement="top">
                                            <i 
                                                class="el-icon-document-copy copy-icon" 
                                                @click="handleCopy(scope.row.msgid, $event)"
                                            ></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 标题 -->
                            <el-table-column label="标题" min-width="200">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <Tooltip 
                                            v-if="scope.row.title" 
                                            :content="scope.row.title" 
                                            className="wrapper-text" 
                                            effect="light"
                                        />
                                        <span v-else>-</span>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 预览 -->
                            <el-table-column label="预览" width="100" align="center">
                                <template slot-scope="scope">
                                    <el-tooltip content="预览内容" placement="top">
                                        <el-button
                                            type="text"
                                            @click="View(scope.row)"
                                            class="action-btn-small preview"
                                            icon="el-icon-view"
                                        >
                                            预览
                                        </el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>

                            <!-- 文件名/手机号 -->
                            <el-table-column label="文件名/手机号" width="150">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <span v-if="scope.row.filePath" :title="scope.row.fileOriginalName">
                                            {{ scope.row.fileOriginalName }}
                                        </span>
                                        <span v-else :title="scope.row.mobile" class="mobile-text">
                                            {{ scope.row.mobile }}
                                        </span>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 发送时间 -->
                            <el-table-column label="发送时间" width="170" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.sendTime }}
                                </template>
                            </el-table-column>

                            <!-- 发送状态 -->
                            <el-table-column label="发送状态" width="140" align="center">
                                <template slot-scope="scope">
                                    <el-tag
                                        :type="getStatusTagType(scope.row.timingStatus)"
                                        size="small"
                                    >
                                        {{ getStatusText(scope.row.timingStatus) }}
                                    </el-tag>
                                </template>
                            </el-table-column>

                            <!-- 运营商状态 -->
                            <el-table-column label="运营商状态" width="140" align="center">
                                <template slot-scope="scope">
                                    <div class="carrier-status">
                                        <el-tooltip content="移动" placement="top">
                                            <i
                                                class="iconfont icon-yidong carrier-icon"
                                                :class="getCarrierStatusClass(scope.row.ydChannelStatus)"
                                            ></i>
                                        </el-tooltip>
                                        <el-tooltip content="联通" placement="top">
                                            <i
                                                class="iconfont icon-liantong carrier-icon"
                                                :class="getCarrierStatusClass(scope.row.ltChannelStatus)"
                                            ></i>
                                        </el-tooltip>
                                        <el-tooltip content="电信" placement="top">
                                            <i
                                                class="iconfont icon-dianxin carrier-icon"
                                                :class="getCarrierStatusClass(scope.row.dxChannelStatus)"
                                            ></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 操作列 -->
                            <el-table-column label="操作" width="140" fixed="right">
                                <template slot-scope="scope">
                                    <div class="table-actions">
                                        <el-tooltip content="取消发送" placement="top" v-if="scope.row.timingStatus == 1">
                                            <el-button
                                                v-permission
                                                type="text"
                                                @click="cancel(scope.row)"
                                                class="action-btn-small cancel"
                                                icon="el-icon-error"
                                            >
                                                取消
                                            </el-button>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                    <!-- 简约分页 -->
                    <div class="pagination-section">
                        <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="formInlines.currentPage"
                            :page-size="formInlines.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="tableDataObj.tablecurrent.total"
                            class="simple-pagination"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- 预览手机弹框 -->
        <el-dialog title="预览" :visible.sync="dialogVisible" width="40%">
            <div>
                <div class="send-mobel-box">
                    <img src="../../../../assets/images/phone.png" alt="">
                    <div class="mms-content-exhibition">
                        <el-scrollbar class="sms-content-exhibition">
                            <div style="width: 253px;">
                                <span
                                    style="display: inline-block;padding: 5px;border-radius: 5px;background: #e2e2e2;margin-top: 5px;">{{ title }}</span>
                            </div>
                            <div style="overflow-wrap: break-word;width: 234px;background: #e2e2e2;padding: 10px;border-radius: 5px;margin-top: 5px;"
                                v-for="(item, index) in viewData" :key="index">
                                <img v-if="item.media == 'jpg' || item.media == 'gif' || item.media == 'png' || item.media == 'jpeg'"
                                    :src="API.imgU + item.mediaGroup + '/' + item.mediaPath" style="width: 235px;"
                                    class="avatar video-avatar" ref="avatar">
                                <video v-if="item.type == 'video'" style="width: 235px;"
                                    v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath" class="avatar video-avatar"
                                    controls="controls">
                                </video>
                                <audio v-if="item.type == 'audio'" style="width: 235px;" autoplay="autoplay"
                                    controls="controls" preload="auto"
                                    v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath">
                                </audio>
                                <div style="white-space: pre-line;">
                                    {{ item.txt }}
                                </div>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
import Tooltip from "@/components/publicComponents/tooltip";
import clip from '../../utils/clipboard'
import getNoce from '../../../../plugins/getNoce';

export default {
    name: 'VideoInterfaceSend',
    components: {
        DatePlugin,
        TableTem,
        Tooltip
    },
    data() {
        return {
            name: 'VideoInterfaceSend',
            // 定时时间
            datePluginValueList: { //日期参数配置
                type: "datetime",
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    }
                },
                defaultTime: '', //默认起始时刻
                datePluginValue: ''
            },
            sendTime: '',
            taskSmsId: '',
            dialogVisible: false,
            viewData: [],// 查看内容
            title: '',
            dialogVisibleTime: false,
            //复选框值
            selectId: [],
            // 搜索数据
            formInline: {
                timingStatus: '',
                msgid: '',
                title: '',
                beginTime: '',
                endTime: '',
                time: [],
                flag: 0,
                pageSize: 10,
                currentPage: 1,
            },
            // 存储搜索数据
            formInlines: {
                timingStatus: '',
                msgid: '',
                title: '',
                beginTime: '',
                endTime: '',
                time: [],
                flag: 0,
                pageSize: 10,
                currentPage: 1,
            },
            //用户列表数据
            tableDataObj: {
                loading2: false,
                tablecurrent: { //分页参数
                    total: 0,
                },
                tableData: []
            }
        };
    },
    methods: {
        //批量取消
        batchDeletion() {
            this.$confirms.confirmation('post', '确定取消定时发送？', this.API.cpus + 'consumertimingvideo/cancelTimingMms', { ids: this.selectId }, res => {
                this.InquireList()
            })
        },
        //列表复选框的值
        handelSelection(val) {
            let selectId = [];
            for (let i = 0; i < val.length; i++) {
                selectId.push(val[i].id)
            }
            this.selectId = selectId; //批量操作选中id
        },
        // 发送请求方法
        InquireList() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumertimingvideo/selectTimingPage', this.formInlines, res => {
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tableData = res.data.records
                this.tableDataObj.tablecurrent.total = res.data.total
            })
        },
        // 预览
        View(val) {
            this.viewData = val.contents
            this.title = val.title
            this.dialogVisible = true
        },
        // 取消
        cancel(val) {
            this.$confirms.confirmation('post', '确定取消定时彩信？', this.API.cpus + 'consumertimingvideo/cancelTimingMms', { ids: [val.id] }, res => {
                this.InquireList()
            })
        },
        // 下载
        download(val) {
            // 时间过滤
            Date.prototype.format = function (format) {
                var args = {
                    "M+": this.getMonth() + 1,
                    "d+": this.getDate(),
                    "h+": this.getHours(),
                    "m+": this.getMinutes(),
                    "s+": this.getSeconds(),
                    "q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
                    "S": this.getMilliseconds()
                };
                if (/(y+)/.test(format))
                    format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
                for (var i in args) {
                    var n = args[i];
                    if (new RegExp("(" + i + ")").test(format))
                        format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length));
                }
                return format;
            };
            var that = this
            filedownload()
            async function filedownload() {
                const nonce = await getNoce.useNonce();
                fetch(that.API.cpus + 'v3/file/download?fileName=' + val.fileOriginalName + '&group=group1&path=' + val.filePath.slice(7), {
                    method: 'get',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': "Bearer " + window.Vue.$common.getCookie('ZTGlS_TOKEN'),
                        'Once': nonce,
                    },
                    // body: JSON.stringify({
                    //     batchNo: val.row.batchNo,
                    // })
                })
                    .then(res => res.blob())
                    .then(data => {
                        let blobUrl = window.URL.createObjectURL(data);
                        download(blobUrl);
                    });
            }
            function download(blobUrl) {
                var a = document.createElement('a');
                a.style.display = 'none';
                a.download = "(" + new Date().format("yyyy-MM-dd hh:mm:ss") + ") " + val.fileOriginalName
                a.href = blobUrl;
                a.click();
            }
        },
        // 查询
        ListSearch() {
            Object.assign(this.formInlines, this.formInline);
            this.InquireList()
        },
        handleCopy(name, event) {
            clip(name, event)
        },
        // 重置
        Reset(formName) {
            this.$refs[formName].resetFields();
            this.formInline.time = [],
                this.formInline.beginTime = '';
            this.formInline.endTime = '';
            this.formInline.time1 = [],
                this.formInline.startTime = ""
            this.formInline.stopTime = ""
            Object.assign(this.formInlines, this.formInline)
        },
        // 发送时间
        getTimeOperating(val) {
            if (val) {
                this.formInline.beginTime = val[0] + " 00:00:00"
                this.formInline.endTime = val[1] + " 23:59:59"
            } else {
                this.formInline.beginTime = ""
                this.formInline.endTime = ""
            }
        },
        // 提交时间
        getTimeOperating1(val) {
            if (val) {
                this.formInline.startTime = val[0] + " 00:00:00"
                this.formInline.stopTime = val[1] + " 23:59:59"
            } else {
                this.formInline.startTime = ""
                this.formInline.stopTime = ""
            }
        },
        // 定时时间
        handledatepluginVal: function (val1, val2) { //日期
            this.sendTime = val1
        },
        // 分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
        },
        handleCurrentChange: function (currentPage) {
            this.formInlines.currentPage = currentPage;
        },
        
        // 获取发送状态标签类型
        getStatusTagType(status) {
            const statusMap = {
                1: 'info',      // 未执行
                2: 'warning',   // 正在执行
                3: 'info',      // 取消
                4: 'danger',    // 超时未执行
                5: 'success',   // 执行完成
                6: 'danger'     // 执行失败
            };
            return statusMap[status] || 'info';
        },
        
        // 获取发送状态文本
        getStatusText(status) {
            const statusMap = {
                1: '未执行',
                2: '正在执行',
                3: '取消',
                4: '超时未执行',
                5: '执行完成',
                6: '执行失败'
            };
            return statusMap[status] || '未知';
        },
        
        // 获取运营商状态样式类
        getCarrierStatusClass(status) {
            const statusMap = {
                0: 'status-default',
                1: 'status-success',
                2: 'status-error'
            };
            return statusMap[status] || 'status-default';
        }
    },
    watch: {
        // 监听搜索/分页数据
        formInlines: {
            handler() {
                this.InquireList()
            },
            deep: true,
            immediate: true,
        },
    },
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* VideoInterfaceSend 特有样式 */

/* 内容单元格样式 */
.content-cell {
    word-break: break-all;
    line-height: 1.4;
    
    .msgid-text {
        margin-right: 8px;
    }
    
    .copy-icon {
        color: #409eff;
        cursor: pointer;
        font-size: 14px;
        transition: color 0.3s ease;
        
        &:hover {
            color: #66b1ff;
        }
    }
    
    .mobile-text {
        max-width: 130px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
    }
}

/* 表格操作按钮样式 */
.table-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;

    .action-btn-small {
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &.cancel {
            color: #f56c6c;
            &:hover {
                color: #f78989;
                background: rgba(245, 108, 108, 0.1);
            }
        }

        &.preview {
            color: #e6a23c;
            &:hover {
                color: #ebb563;
                background: rgba(230, 162, 60, 0.1);
            }
        }
    }
}

/* 运营商状态样式 */
.carrier-status {
    display: flex;
    justify-content: center;
    gap: 8px;

    .carrier-icon {
        font-size: 20px;
        transition: all 0.3s ease;

        &.status-default {
            color: #ddd;
        }

        &.status-success {
            color: #67c23a;
        }

        &.status-error {
            color: #f56c6c;
        }
    }
}

/* 预览对话框样式 */
.send-mobel-box {
    width: 300px;
    overflow: hidden;
    position: relative;
    margin: 0 auto 35px;
    display: flex;
    justify-content: center;
}

.send-mobel-box img {
    width: 300px;
}

.mms-content-exhibition {
    position: absolute;
    top: 0;
    width: 300px;
    height: 375px;
    margin: 135px 10px 0px 45px;
    overflow: auto;
    overflow-y: auto;
}

/* 预览对话框增强 */
/deep/ .el-dialog {
    border-radius: 12px;

    .el-dialog__header {
        background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
        color: #fff;
        padding: 20px 24px;
        border-radius: 12px 12px 0 0;

        .el-dialog__title {
            color: #fff;
            font-weight: 600;
        }

        .el-dialog__close {
            color: #fff;
            font-size: 20px;

            &:hover {
                color: rgba(255, 255, 255, 0.8);
            }
        }
    }

    .el-dialog__body {
        padding: 32px 24px;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .carrier-status {
        flex-direction: column;
        gap: 4px;

        .carrier-icon {
            font-size: 16px;
        }
    }

    .table-actions {
        flex-direction: column;
        gap: 4px;

        .action-btn-small {
            font-size: 11px;
            padding: 2px 6px;
        }
    }

    .send-mobel-box {
        width: 250px;

        img {
            width: 250px;
        }

        .mms-content-exhibition {
            width: 250px;
            margin: 113px 21px 0px 21px;
        }
    }
}
</style>
