<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
                <el-button
                  v-if="selectId.length"
                  type="danger"
                  @click="batchDeletion"
                  class="action-btn danger"
                  icon="el-icon-delete"
                >
                  批量取消
                </el-button>
                <el-button
                  type="primary"
                  @click="exportNums()"
                  class="action-btn primary"
                  icon="el-icon-download"
                >
                  导出数据
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ formInlines.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="formInline" :inline="true" ref="formInline" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="发送类型" prop="timingSend" class="search-item">
                    <el-select v-model="formInline.timingSend" placeholder="请选择类型" class="search-input" clearable>
                      <el-option label="全部" value=""></el-option>
                      <el-option label="立即发送" value="0"></el-option>
                      <el-option label="定时发送" value="1"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="消息ID" prop="msgid" class="search-item">
                    <el-input
                      v-model="formInline.msgid"
                      placeholder="请输入消息ID"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="标题" prop="title" class="search-item">
                    <el-input
                      v-model="formInline.title"
                      placeholder="请输入标题"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="来源" prop="source" class="search-item">
                    <el-input
                      v-model="formInline.source"
                      placeholder="请输入来源"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="任务名称" prop="taskName" class="search-item">
                    <el-input
                      v-model="formInline.taskName"
                      placeholder="请输入任务名称"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="发送时间" prop="time" class="search-item date-item">
                    <el-date-picker
                      v-model="formInline.time"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button
                      type="primary"
                      @click="ListSearch"
                      class="search-btn primary"
                      icon="el-icon-search"
                    >
                      查询
                    </el-button>
                    <el-button
                      @click="Reset('formInline')"
                      class="search-btn"
                      icon="el-icon-refresh"
                    >
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- Web任务列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">
              <i class="el-icon-video-camera"></i>
              Web任务列表
            </h3>
            <span class="table-subtitle">可刷新页面查看最新发送进度</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载任务数据..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无Web任务数据"
              @selection-change="handelSelection"
            >
              <!-- 选择列 -->
              <el-table-column type="selection" width="55" align="center"></el-table-column>

              <!-- 发送ID -->
              <el-table-column prop="id" label="发送ID" width="80" align="center">
                <template slot-scope="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column>

              <!-- 消息ID -->
              <el-table-column label="消息ID" width="220">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="msgid-text">{{ scope.row.msgid }}</span>
                    <el-tooltip content="复制消息ID" placement="top">
                      <i 
                        class="el-icon-document-copy copy-icon"
                        @click="handleCopy(scope.row.msgid, $event)"
                      ></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 任务名称 -->
              <el-table-column label="任务名称" width="120">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <Tooltip v-if="scope.row.taskName" :content="scope.row.taskName" className="wrapper-text" effect="light">
                    </Tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 发送类型 -->
              <el-table-column label="发送类型" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.timingSend == 0 ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ scope.row.timingSend == 0 ? '立即发送' : '定时发送' }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 预览 -->
              <el-table-column label="预览" width="90" align="center">
                <template slot-scope="scope">
                  <el-tooltip content="预览视频内容" placement="top">
                    <el-button
                      type="text"
                      @click="View(scope.row)"
                      class="action-btn-small preview"
                      icon="el-icon-view"
                    >
                      预览
                    </el-button>
                  </el-tooltip>
                </template>
              </el-table-column>

              <!-- 文件名/手机号 -->
              <el-table-column label="文件名/手机号" width="160">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <div v-if="scope.row.filePath" class="file-info">
                      <Tooltip v-if="scope.row.fileOriginalName" :content="scope.row.fileOriginalName" className="wrapper-text" effect="light">
                      </Tooltip>
                    </div>
                    <div v-if="scope.row.mobile" class="mobile-info">
                      <Tooltip :content="scope.row.mobile" className="wrapper-text" effect="light">
                      </Tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 发送时间 -->
              <el-table-column label="发送时间" width="160" align="center">
                <template slot-scope="scope">
                  {{ scope.row.sendTime }}
                </template>
              </el-table-column>

              <!-- 提交号码数 -->
              <el-table-column label="提交号码数" width="120" align="center">
                <template slot-scope="scope">
                  <span class="number-badge">{{ scope.row.totalNum }}</span>
                </template>
              </el-table-column>

              <!-- 有效号码 -->
              <el-table-column label="有效号码" width="120" align="center">
                <template slot-scope="scope">
                  <span class="number-badge success">{{ scope.row.effectiveNum }}</span>
                </template>
              </el-table-column>

              <!-- 无效号码 -->
              <el-table-column label="无效号码" width="120" align="center">
                <template slot-scope="scope">
                  <span 
                    v-if="scope.row.invalidNum > 0 && scope.row.invalidFilePath" 
                    class="number-badge danger clickable"
                    @click="download(scope.row, '2')"
                  >
                    <el-tooltip content="点击下载无效号码文件" placement="top">
                      <span>{{ scope.row.invalidNum }}</span>
                    </el-tooltip>
                  </span>
                  <span v-else class="number-badge">{{ scope.row.invalidNum }}</span>
                </template>
              </el-table-column>

              <!-- 发送状态 -->
              <el-table-column label="发送状态" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getStatusTagType(scope.row.status)"
                    size="small"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 发送成功数 -->
              <el-table-column label="发送成功数" width="120" align="center">
                <template slot-scope="scope">
                  <span class="number-badge success">{{ scope.row.sendSuccessNum }}</span>
                </template>
              </el-table-column>

              <!-- 成功率 -->
              <el-table-column label="成功率" width="80" align="center">
                <template slot-scope="scope">
                  <span class="success-rate">{{ scope.row.successRate }}</span>
                </template>
              </el-table-column>

              <!-- 待返回数 -->
              <el-table-column label="待返回数" width="100" align="center">
                <template slot-scope="scope">
                  <span class="number-badge warning">{{ scope.row.waiteNum }}</span>
                </template>
              </el-table-column>

              <!-- 失败数 -->
              <el-table-column label="失败数" width="80" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.failNum" class="number-badge danger">{{ scope.row.failNum }}</span>
                  <span v-else class="no-data">--</span>
                </template>
              </el-table-column>

              <!-- 来源 -->
              <el-table-column label="来源" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag size="mini" type="info">{{ scope.row.source }}</el-tag>
                </template>
              </el-table-column>

              <!-- 操作 -->
              <el-table-column label="操作" width="120" fixed="right" align="center">
                <template slot-scope="scope">
                  <div class="action-buttons-cell">
                    <el-tooltip content="编辑定时时间" placement="top">
                      <el-button
                        v-permission
                        v-if="scope.row.status != 2 &&
                          scope.row.status != 3 &&
                          scope.row.status != -1 &&
                          scope.row.auditStatus != 2 &&
                          scope.row.auditStatus != 3 &&
                          scope.row.timingSend != 0"
                        type="text"
                        @click="edit(scope.row)"
                        class="action-btn-small edit"
                        icon="el-icon-edit"
                      >
                        编辑
                      </el-button>
                    </el-tooltip>
                    
                    <el-tooltip content="取消发送" placement="top">
                      <el-button
                        v-permission
                        v-if="scope.row.status == 0"
                        type="text"
                        @click="cancel(scope.row)"
                        class="action-btn-small danger"
                        icon="el-icon-close"
                      >
                        取消
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination
                class="simple-pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInlines.currentPage"
                :page-size="formInlines.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.tablecurrent.total"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览手机弹框 -->
    <el-dialog 
      title="视频内容预览" 
      :visible.sync="dialogVisible" 
      width="500px"
      class="preview-dialog"
    >
      <div class="preview-container">
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="phone-header">
              <span class="phone-time">{{ getCurrentTime() }}</span>
              <div class="phone-status">
                <i class="el-icon-wifi"></i>
                <i class="el-icon-battery-full"></i>
              </div>
            </div>
            <div class="message-container">
              <el-scrollbar class="message-scrollbar">
                <div class="message-content-wrapper">
                  <div class="message-bubble title-bubble">
                    {{ title || '视频短信标题' }}
                  </div>
                  <div
                    v-for="(item, index) in viewData"
                    :key="index"
                    class="message-bubble content-bubble"
                  >
                    <img
                      v-if="item.media == 'jpg' || item.media == 'gif' || item.media == 'png' || item.media == 'jpeg'"
                      :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                      class="media-content image-content"
                    />
                    <video
                      v-if="item.type == 'video'"
                      :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                      class="media-content video-content"
                      controls
                    />
                    <audio
                      v-if="item.type == 'audio'"
                      :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                      class="media-content audio-content"
                      controls
                      preload="auto"
                    />
                    <div class="text-content" v-if="item.txt">
                      {{ item.txt }}
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 编辑定时时间弹框 -->
    <el-dialog 
      title="编辑定时时间" 
      :visible.sync="dialogVisibleTime" 
      width="450px"
      class="time-edit-dialog"
    >
      <div class="time-edit-content">
        <date-plugin 
          class="time-picker"
          :datePluginValueList="datePluginValueList"
          @handledatepluginVal="handledatepluginVal"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleTime = false">取 消</el-button>
        <el-button type="primary" @click="determine()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem";
import Tooltip from "@/components/publicComponents/tooltip";
import clip from '../../utils/clipboard'
import getNoce from "../../../../plugins/getNoce";
export default {
  name: "VideoWebTask",
  components: {
    DatePlugin,
    TableTem,
    Tooltip
  },
  data() {
    return {
      name: "VideoWebTask",
      // 定时时间
      datePluginValueList: {
        //日期参数配置
        type: "datetime",
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          },
        },
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
      sendTime: "",
      taskSmsId: "",
      dialogVisible: false,
      viewData: [], // 查看内容
      title: "",
      dialogVisibleTime: false,
      //复选框值
      selectId: "",
      // 搜索数据
      formInline: {
        timingSend: "",
        msgid: "",
        title: "",
        source: "",
        taskName: "",
        beginTime: "",
        endTime: "",
        time: [],
        decode:false,
        // flag: 0,
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        timingSend: "",
        msgid: "",
        title: "",
        source: "",
        taskName: "",
        beginTime: "",
        endTime: "",
        time: [],
        decode:false,
        // flag: 0,
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    };
  },
  methods: {
    // 刷新列表
    refreshList() {
      this.InquireList();
    },

    // 获取当前时间
    getCurrentTime() {
      const now = new Date();
      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 0: return 'info';      // 待处理
        case 1: return 'warning';   // 处理中
        case 2: return 'success';   // 已完成
        case 3: return 'danger';    // 取消发送
        case -1: return 'danger';   // 处理异常
        default: return 'info';
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 0: return '待处理';
        case 1: return '处理中';
        case 2: return '已完成';
        case 3: return '取消发送';
        case -1: return '处理异常';
        default: return '未知';
      }
    },

    //批量取消
    batchDeletion() {
      this.$confirms.confirmation(
        "post",
        "确定取消定时发送？",
        this.API.cpus + "v1/video/cancel",
        { ids: this.selectId },
        (res) => {
          this.InquireList();
        }
      );
    },
    handleCopy(name, event) {
      clip(name, event)
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].taskSmsId);
      }
      this.selectId = selectId; //批量操作选中id
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "v1/video/consumerWebTask",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tablecurrent.total = res.data.total;
        }
      );
    },
    // 编辑
    edit(val) {
      if (Date.parse(val.sendTime) - Date.now() < 1800000) {
        this.$message({
          message: "定时小于现在30分钟无法编辑",
          type: "warning",
        });
      } else {
        this.taskSmsId = val.id;
        this.dialogVisibleTime = true;
      }
    },
    // 预览
    View(val) {
      this.viewData = val.contents;
      this.title = val.title;
      this.dialogVisible = true;
    },
    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        "post",
        "确定取消定时视频短信？",
        this.API.cpus + "v1/video/cancel",
        { ids: [val.id] },
        (res) => {
          this.InquireList();
        }
      );
    },
    // 下载
    download(val, tag) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          "M+": this.getMonth() + 1,
          "d+": this.getDate(),
          "h+": this.getHours(),
          "m+": this.getMinutes(),
          "s+": this.getSeconds(),
          "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        };
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var i in args) {
          var n = args[i];
          if (new RegExp("(" + i + ")").test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
            );
        }
        return format;
      };
      var that = this;
      filedownload();
      async function filedownload() {
        const nonce = await getNoce.useNonce();
        if (tag == "2") {
          let group = val.invalidFilePath.substring(0, 6);
          let invalidFilePath = val.invalidFilePath.slice(7);
          fetch(
            "/gateway/client-cpus/v3/file/download?fileName=" +
            new Date().getTime() +
            "&group=" +
            group +
            "&path=" +
            invalidFilePath,
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
              // body: JSON.stringify({
              //     batchNo: val.row.batchNo,
              // })
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        } else {
          fetch(
            that.API.cpus +
            "v3/file/download?fileName=" +
            val.fileOriginalName +
            "&group=group1&path=" +
            val.filePath.slice(7),
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        }
      }
      function download(blobUrl) {
        if (tag == "2") {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            new Date().getTime() +
            ".txt";
          a.href = blobUrl;
          a.click();
        } else {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            val.fileOriginalName;
          a.href = blobUrl;
          a.click();
        }
      }
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      (this.formInline.time = []), (this.formInline.beginTime = "");
      this.formInline.endTime = "";
      (this.formInline.time1 = []), (this.formInline.startTime = "");
      this.formInline.stopTime = "";
      this.formInline.source = "";
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    exportFn(obj) {
      this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        if (res.code == 200) {
          this.exportShow = false
          this.$message({
            type: "success",
            duration: "2000",
            message: "已加入到文件下载中心!",
          });
          this.$router.push('/FileExport');
        } else {
          this.$message({
            type: "error",
            duration: "2000",
            message: res.msg,
          });
        }
      });
    },
    exportNums() {
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
        return;
      }
      let data = Object.assign({}, this.formInlines);
      data.productType = 13;
      this.exportFn(data)
    },
    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    // 提交时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.startTime = val[0] + " 00:00:00";
        this.formInline.stopTime = val[1] + " 23:59:59";
      } else {
        this.formInline.startTime = "";
        this.formInline.stopTime = "";
      }
    },
    // 定时时间
    handledatepluginVal: function (val1, val2) {
      //日期
      this.sendTime = val1;
    },
    // 确定定时时间
    determine() {
      if (this.sendTime == "") {
        this.$message({
          message: "请选大于当前30分钟的定时时间！",
          type: "warning",
        });
      } else {
        let nowTiem = new Date(this.sendTime).getTime();
        if (nowTiem < Date.now() + 1800000) {
          this.$message({
            message: "定时时间应大于当前时间30分钟，需重新设置！",
            type: "warning",
          });
          this.datePluginValueList.datePluginValue = "";
          this.sendTime = "";
        } else {
          this.$confirms.confirmation(
            "put",
            "确定修改定时？",
            this.API.cpus + "v1/video/consumerWebTask",
            { sendTime: this.sendTime, taskSmsId: this.taskSmsId },
            (res) => {
              this.InquireList();
              this.dialogVisibleTime = false;
            }
          );
        }
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
  },
  // activated(){
  //     this.InquireList()
  // },
  created() {
    if (this.$route.query.source) {
      this.formInline.source = this.$route.query.source;
      this.formInlines.source = this.$route.query.source;
    }
    this.InquireList();
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
};
</script>
<style lang="less" scoped>
// 引入公用样式
@import "~@/styles/template-common.less";

/* 视频内容预览弹窗样式 */
.preview-dialog {
  /deep/ .el-dialog {
    border-radius: 16px;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.176);
  }

  /deep/ .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px 16px 0 0;
    padding: 24px;
    border-bottom: none;

    .el-dialog__title {
      color: #fff;
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #fff;
        font-size: 20px;
        
        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  /deep/ .el-dialog__body {
    padding: 32px;
    background: #f8f9fa;
  }
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.phone-mockup {
  width: 320px;
  height: 580px;
  background: #e8e8e8;
  border-radius: 24px;
  padding: 8px;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.1),
    0 0 0 1px #d0d0d0;
  position: relative;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
}

.phone-header {
  background: #f8f9fa;
  color: #333;
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 36px;
  font-size: 13px;
  font-weight: 500;
  border-bottom: 1px solid #e9ecef;

  .phone-time {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
  }

  .phone-status {
    display: flex;
    gap: 6px;
    
    i {
      font-size: 13px;
      color: #666;
    }
  }
}

.message-container {
  flex: 1;
  background: #fafbfc;
  position: relative;
}

.message-scrollbar {
  height: 100%;
  
  /deep/ .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  /deep/ .el-scrollbar__bar.is-vertical {
    width: 4px;
    
    .el-scrollbar__thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }
  }
}

.message-content-wrapper {
  padding: 20px 16px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-bubble {
  max-width: 85%;
  align-self: flex-end;
  animation: slideInRight 0.3s ease-out;

  &.title-bubble {
    background: #007AFF;
    color: #fff;
    padding: 10px 14px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0, 122, 255, 0.2);
  }

  &.content-bubble {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

.media-content {
  width: 100%;
  border-radius: 8px;
  
  &.image-content {
    max-width: 220px;
    max-height: 280px;
    object-fit: cover;
    display: block;
    border: 1px solid #e0e0e0;
  }

  &.video-content {
    max-width: 220px;
    max-height: 280px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    
    &::-webkit-media-controls {
      border-radius: 6px;
    }
    
    &::-webkit-media-controls-panel {
      background-color: rgba(0, 0, 0, 0.7);
    }
  }

  &.audio-content {
    width: 100%;
    height: 36px;
    margin: 6px 8px;
    border-radius: 6px;
    
    &::-webkit-media-controls {
      border-radius: 6px;
    }
    
    &::-webkit-media-controls-panel {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}

.text-content {
  color: #333;
  padding: 10px 12px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: pre-wrap;
}

// 动画效果
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计覆盖 */
@media (max-width: 1200px) {
  .fixed-toolbar .toolbar-container {
    padding: 16px;
  }
  
  .table-section {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .preview-dialog /deep/ .el-dialog {
    margin: 0 !important;
    width: 100% !important;
    height: 100vh;
    border-radius: 0;
  }

  .preview-container {
    min-height: 400px;
  }

  .phone-mockup {
    width: 280px;
    height: 500px;
  }
}
</style>
