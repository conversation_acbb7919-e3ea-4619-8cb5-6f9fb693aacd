<template>
    <div>
        <!-- 视频短信标题 -->
        <!-- <div class="fillet shortChain-box" style="margin-top: 20px;">
            <p style="font-size: 20px;font-weight: 600;padding: 20px;border-bottom: 1px solid #eaeaea;">视频短信标题</p>
            <div style="margin: 0px 50px;padding: 20px 0px;">
                 <el-input v-model="title" maxlength="20"></el-input>
            </div>
        </div> -->
        <!-- 视频短信内容 -->
        <div class="fillet shortChain-box" style="margin-top: 20px;">
            <div style="margin: 0px 50px;padding: 20px 0px;">
                <el-tabs v-model="activeName" type="card">
                    <!-- <el-tab-pane :label="'视频短信内容'+mmsSize+'KB/2048KB'" name="MMScontent">
                        <div class="LoopUpload_div" v-for="(item,index) in LoopNum" :key=index>
                            <div style="height: 30px;">
                                <span style="font-size: 18px;">第 {{index+1}} 帧</span>
                                <span :style="Math.ceil(item.size+(sizeof(item.textarea)))>2048?'color: #ff00009e;':''">({{Math.ceil(item.size+(sizeof(item.textarea)))}}KB)</span>
                            </div>
                            <Loop-upload :children='item' @childrenChange="childrenChange(index)"></Loop-upload>
                        </div>
                        <div class="LoopUpload_div">
                            <div style="height: 30px;"></div>
                            <div class="AddTo" @click="AddToLoop" style="border: 1px dashed #ccc;position: relative;border-radius: 5px;cursor: pointer;">
                                <i class="el-icon-plus" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);font-size: 60px;font-weight: 100;color: #eaeaea;"></i>
                            </div>
                        </div>
                    </el-tab-pane> -->
                    <el-tab-pane label="我的模板" name="MMStemplate">
                        <Template-library @childrenTemplateCreation="childrenTemplateCreation"></Template-library>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <!-- 预览，下一步 -->
        <!-- <div v-if="activeName=='MMScontent'" class="fillet shortChain-box" style="margin-top: 20px;">
            <div style="margin: 0px 50px;padding: 20px 0px;text-align: center;">
                <el-button style="margin-right:10px;" @click="Preview">预览</el-button>
                <el-button type="primary" style="margin-right:10px;" @click="NextStep()">下一步</el-button>
            </div>
        </div> -->
        <!-- 预览手机弹框   -->
        <el-dialog
            title="预览"
            :visible.sync="dialogVisible"
            width="40%"
            >
            <div>
                <div class="send-mobel-box">
                    <img src="../../../../../assets/images/phone.png" alt="">
                    <div class="mms-content-exhibition">
                    <el-scrollbar  class="sms-content-exhibition" >
                        <div style="width: 253px;">
                            <span style="display: inline-block;padding: 5px;border-radius: 5px;background: #e2e2e2;margin-top: 5px;">{{this.title}}</span>
                        </div>
                        <div style="overflow-wrap: break-word;width: 234px;background: #e2e2e2;padding: 10px;border-radius: 5px;margin-top: 5px;" v-for="(item,index) in LoopNum" :key=index>
                            <img v-if="item.imageUrl" :src="item.imageUrl" style="width: 235px;" class="avatar video-avatar"  ref="avatar">
                            <video v-if="item.showVideoPath"
                                style="width: 235px;"
                                v-bind:src="item.showVideoPath"
                                class="avatar video-avatar"
                                controls="controls">
                            </video>
                            <audio
                                v-if="item.showaudioPath"
                                style="width: 235px;" 
                                autoplay="autoplay"
                                controls="controls"
                                preload="auto"
                                v-bind:src="item.showaudioPath">
                            </audio>
                            <div>
                                <pre>{{item.textarea}}</pre>
                            </div>
                        </div>
                    </el-scrollbar>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapState, mapMutations,mapActions } from "vuex";
import LoopUpload from './components/LoopUpload.vue'
import TemplateLibrary from './components/TemplateLibrary.vue'
export default {
    name: "create",
    data () {
        return {
            activeName: 'MMStemplate',
            dialogVisible:false,//预览手机
            title:'',//视频短信标题
            LoopNum:[
                {
                    showVideoPath:'',//视频url
                    showaudioPath: '',//音频url
                    imageUrl:'',//图片url
                    media:'',//媒体格式
                    type:'',//文件类型
                    mediaGroup:'',//返回路径
                    mediaPath:'',//返回路径
                    textarea:'',//文本框
                    time:0,//选择时间
                    size:0,//文件大小
                }
            ],
            mmsSize:0
        }
    },
    components:{
        LoopUpload,
        TemplateLibrary
    },
    methods:{
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveMMS'
        ]),
        // 添加帧
       AddToLoop(){
            if(this.LoopNum.length<10){
                let flag=true
                for(let i=0;i<this.LoopNum.length;i++){
                    if(this.LoopNum[i].showVideoPath||this.LoopNum[i].showaudioPath||this.LoopNum[i].imageUrl||this.LoopNum[i].textarea){
                         flag=true
                    }else{
                         flag=false
                    }
                }
                if(flag){
                    let obj={
                        showVideoPath:'',//视频url
                        showaudioPath: '',//音频url
                        imageUrl:'',//图片url
                        media:'',//媒体格式
                        type:'',//文件类型
                        mediaGroup:'',//返回路径
                        mediaPath:'',//返回路径
                        textarea:'',//文本框
                        time:0,//选择时间
                        size:0,//大小
                    }
                    this.LoopNum.push(obj)
                }else{
                    this.$message({
                        type: 'warning',
                        message:'请先编辑前面的内容'
                    });
                }
            }else{
                this.$message({
                    type: 'warning',
                    message:'最多添加10帧'
                });
            }
       },
    //    删除帧
        childrenChange(index) {
            this.LoopNum.splice(index,1)
        },
    //    预览
        Preview(){
            for(let i=0;i<this.LoopNum.length;i++){
                if(this.LoopNum[i].showVideoPath||this.LoopNum[i].showaudioPath||this.LoopNum[i].imageUrl||this.LoopNum[i].textarea){
                    if(!this.title){
                        this.$message({
                            type: 'warning',
                            message:'请编辑视频短信标题'
                        });  
                        return false
                    }
                    this.dialogVisible=true
                }else{
                    this.$message({
                        type: 'warning',
                        message:'请先编辑视频短信内容'
                    }); 
                }
            }
        },
        //下一步
        NextStep(val){
            let contentMMS={
                contentMMS:{
                    contents:[]
                }
            }
            if(this.mmsSize>2048){
                this.$message({
                    type: 'warning',
                    message:'发送内容已超过2MB上线,请适当缩减内容'
                }); 
                return false
            }
            for(let i=0;i<this.LoopNum.length;i++){
                if(this.LoopNum[i].showVideoPath||this.LoopNum[i].showaudioPath||this.LoopNum[i].imageUrl||this.LoopNum[i].textarea){
                    if(!this.title){
                        this.$message({
                            type: 'warning',
                            message:'请编辑视频短信标题'
                        });  
                        return false
                    }
                    let a={}
                    if(this.LoopNum[i].imageUrl||this.LoopNum[i].showVideoPath||this.LoopNum[i].showaudioPath){
                        a.mediaPath=this.LoopNum[i].mediaPath
                        a.mediaGroup=this.LoopNum[i].mediaGroup
                        a.media=this.LoopNum[i].media
                        a.type=this.LoopNum[i].type
                    }
                    a.time=this.LoopNum[i].time
                    a.txt=this.LoopNum[i].textarea
                    a.size=this.LoopNum[i].size
                    contentMMS.contentMMS.contents.push(a)
                }else{
                    this.$message({
                        type: 'warning',
                        message:'请先编辑视频短信内容'
                    }); 
                    return false
                }
            }
            contentMMS.contentMMS.title=this.title
            this.saveMMS(contentMMS);
            // 跳过编辑 直接提交 清空LoopNum
            if(val=='template'){
                this.LoopNum=[{
                    showVideoPath:'',//视频url
                    showaudioPath: '',//音频url
                    imageUrl:'',//图片url
                    media:'',//媒体格式
                    type:'',//文件类型
                    mediaGroup:'',//返回路径
                    mediaPath:'',//返回路径
                    textarea:'',//文本框
                    time:0,//选择时间
                    size:0,//文件大小
                }]
                this.title=''
            }
            this.$emit('childrenNextStep', '')
        },
        childrenTemplateCreation(val){
            let data=val.contents
            this.LoopNum=[]
            for(let i=0;i<data.length;i++){
                let a={}
                if(data[i].type=='img'){
                    a.imageUrl=this.API.imgU+data[i].mediaGroup+'/'+data[i].mediaPath
                }else if(data[i].type=='video'){
                    a.showVideoPath=this.API.imgU+data[i].mediaGroup+'/'+data[i].mediaPath
                }else if(data[i].type=='audio'){
                    a.showaudioPath=this.API.imgU+data[i].mediaGroup+'/'+data[i].mediaPath
                }else{
                    a.imageUrl=a.showVideoPath=a.showaudioPath=''
                }
                a.media=data[i].media
                a.mediaGroup=data[i].mediaGroup
                a.mediaPath=data[i].mediaPath
                a.textarea=data[i].txt
                a.time=data[i].time
                a.size=data[i].size
                a.type=data[i].type
                this.LoopNum.push(a)
            }
            this.title=val.title
            let contentMMS={
                contentMMS:{
                    contents:[]
                }
            }
            // 赋值
            contentMMS.contentMMS=val
            // 转换id
            contentMMS.contentMMS.templateId=val.id
            // 删除多余属性
           delete contentMMS.contentMMS.createTime 
           delete contentMMS.contentMMS.status 
           delete contentMMS.contentMMS.userId
           delete contentMMS.contentMMS.username
           delete contentMMS.contentMMS.content
           delete contentMMS.contentMMS.id

            this.saveMMS(contentMMS);
            // this.activeName='MMScontent'
            this.activeName='MMStemplate'//改为直接返回模板
            this.NextStep('template')
        },
        // 计算文本字节
        sizeof(str, charset){
            var total = 0,
                charCode,
                i,
                len;
            charset = charset ? charset.toLowerCase() : '';
            if(charset === 'utf-16' || charset === 'utf16'){
                for(i = 0, len = str.length; i < len; i++){
                    charCode = str.charCodeAt(i);
                    if(charCode <= 0xffff){
                        total += 2;
                    }else{
                        total += 4;
                    }
                }
            }else{
                for(i = 0, len = str.length; i < len; i++){
                    charCode = str.charCodeAt(i);
                    if(charCode <= 0x007f) {
                        total += 1;
                    }else if(charCode <= 0x07ff){
                        total += 2;
                    }else if(charCode <= 0xffff){
                        total += 3;
                    }else{
                        total += 4;
                    }
                }
            }
            return total / 1024 ;
        },
    },
    mounted(){
        window.sessionStorage.removeItem('tpeId')
    },
    watch:{
        'LoopNum':{
            handler:function(newValue,oldValue){
                let a =0
                    for(let i=0;i<this.LoopNum.length;i++){
                        // sizeof(item.textarea)
                        a+=(parseInt(Math.ceil(this.LoopNum[i].size+this.sizeof(this.LoopNum[i].textarea))))
                    }
                    this.mmsSize=a
                },
            deep:true,
        }
    }
}
</script>
<style scoped>
.AddTo{
    padding: 10px;
    height: 530px;
}
.AddTo:hover{
    background: #ecf5ff70;
}
.LoopUpload_div{
    width: 25%;
    float: left;
    margin-right: 100px;
    height: 700px;
    min-width: 325px;
}
.send-mobel-box{
    width: 300px;
    overflow: hidden;
    position: relative;
    margin-bottom: 35px;
    left: 28%;
}
.send-mobel-box img{
    width:300px;
}
.mms-content-exhibition{
    position: absolute;
    top: 0;
    width: 300px;
    height: 375px;
    margin: 135px 25px 0px 25px;
    overflow: auto;
    overflow-y: auto;
}
.el-scrollbar__wrap{
    margin-bottom:0;
    margin-right:0
}
</style>