<template>
  <div id="phoneLR" style="min-width: 1150px">
    <el-row style="padding-bottom: 20px; background: #fff; margin-top: 20px">
      <el-col :span="9" style="border-left: 1px dashed #ccc; padding-top: 32px">
        <div class="send-mobel-box">
          <img src="../../../../../assets/images/phone.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div>
                <span
                  style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  "
                  >{{ contentMMS.title }}</span
                >
              </div>
              <div
                style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                "
                v-for="(item, index) in contentMMS.contents"
                :key="index"
              >
                <img
                  v-if="
                    item.media == 'jpg' ||
                    item.media == 'gif' ||
                    item.media == 'png' ||
                    item.media == 'jpeg'
                  "
                  :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  style="width: 235px"
                  class="avatar video-avatar"
                  ref="avatar"
                />
                <video
                  v-if="item.type == 'video'"
                  style="width: 235px"
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  class="avatar video-avatar"
                  controls="controls"
                ></video>
                <audio
                  v-if="item.type == 'audio'"
                  style="width: 235px"
                  autoplay="autoplay"
                  controls="controls"
                  preload="auto"
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                ></audio>
                <div style="white-space: pre-line">
                  {{ item.txt }}
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </el-col>
      <el-col :span="15" style="padding-right: 40px">
        <el-form
          :model="formData"
          ref="configurations"
          label-width="95px"
          style="padding: 20px 8px 0px; margin: 100px 0px"
        >
          <el-form-item label="标签字段" prop="label">
            <input-tag v-on:childLabelEvent="handChildLabel" />
          </el-form-item>
          <el-form-item label="任务名称" prop="taskName">
            <el-input
              v-model="formData.taskName"
              style="width: 420px"
            ></el-input>
          </el-form-item>
          <el-form-item label="发送方式">
              <el-radio-group v-model="formData.type" @change="handelSend">
                <el-radio label="0">号码发送</el-radio>
                <el-radio label="1">文件发送</el-radio>
              </el-radio-group>
            </el-form-item>
          <div v-if="formData.type=='1'" style="position: relative">
            <span
              style="
                display: inline-block;
                width: 83px;
                text-align: right;
                padding-right: 11px;
                position: absolute;
              "
              >发送对象</span
            >
            <el-upload
              v-permission
              class="upload-demo"
              drag
              style="display: inline-block; margin-left: 97px"
              :action="this.API.cpus + 'v3/file/upload'"
              :headers="header"
              :limit="1"
              :on-remove="fileup"
              :before-upload="beforeAvatarUpload"
              :on-success="fileupres"
              :file-list="fileList"
              multiple
            >
              <i class="el-icon-upload active"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <!-- <div class="el-upload__tip" slot="tip">
                只能上传jpg/png文件，且不超过500kb
              </div> -->
            </el-upload>
            <!-- <file-upload
              style="display: inline-block; margin-left: 97px"
              :action="this.API.cpus + 'v3/file/upload'"
              :limit="1"
              :showfileList="true"
              :fileStyle="fileStyle"
              :del="del1"
              :istip="false"
              @fileup="fileup"
              @fileupres="fileupres"
              :key="Reorganization"
              >选择上传文件</file-upload
            > -->
            <span
              v-if="RowNum"
              style="
                font-size: 12px;
                color: #aaa;
                position: relative;
                top: -9px;
                left: 10px;
              "
              >本次共上传 {{ RowNum }} 行</span
            >
          </div>
          <div v-if="formData.type=='1'" class="send-upload-tips">
            格式要求：支持.xlsx .xls.txt 等格式,文件大小不超过300M<span
              style="color: rgb(210, 7, 7)"
              >(如上传文件将会自动清除下面手动填写的手机号码)</span
            >
          </div>
          <div v-if="formData.type=='1'" class="send-upload-tips">TXT格式：只支持一行一个号码。</div>
          <div v-if="formData.type=='1'" class="send-upload-tips" style="margin-bottom: 10px">
            <a
              style="color: #409eff"
              href="https://doc.zthysms.com/Public/Uploads/2021-09-17/614447e093f40.xlsx"
              >变量模板下载</a
            >
          </div>
          <el-form-item
            label=""
            prop=""
            v-if="formData.type=='0'"
            
          >
          <!-- v-if="textareaShow == true && downloadtem != '1' && variableTp" -->
            <div class="box-textareas">
              <el-input
                type="textarea"
                @blur="FilterNumber"
                class="textareas"
                @input="textChange"
                style="width: 380px"
                placeholder="手动最多输入200个手机号码，号码之间用英文逗号隔开"
                v-model="formData.mobile"
              ></el-input>
            </div>
            <div
              style="
                width: 695px;
                text-align: center;
                position: absolute;
                bottom: 30px;
                height: 0;
              "
              v-show="limit <= 200"
            >
              <span>{{ limit }}/200</span>
            </div>
            <div
              style="
                width: 695px;
                text-align: center;
                position: absolute;
                bottom: 30px;
                height: 0;
              "
              v-show="limit > 200"
            >
              <span style="color: red">{{ limit }}</span
              ><span>/200</span>
            </div>
          </el-form-item>
          <!-- 发送时间 -->
          <el-form-item label="发送时间">
            <el-radio-group v-model="formData.timingSend">
              <el-radio label="0">立即发送</el-radio>
              <el-radio label="1">定时发送</el-radio>
            </el-radio-group>
          </el-form-item>
          <div
            v-if="formData.timingSend == 1"
            style="padding-bottom: 18px"
            class="send-time-sel"
            prop="sendTime"
          >
            <span
              style="
                display: inline-block;
                width: 83px;
                text-align: right;
                padding-right: 10px;
              "
              >定时时间</span
            >
            <date-plugin
              class="Mail-search-date"
              :datePluginValueList="datePluginValueList"
              @handledatepluginVal="handledatepluginVal"
              style="width: 364px"
            ></date-plugin>
          </div>
        </el-form>
      </el-col>
    </el-row>
    <!-- 上一步，发送 -->
    <div class="fillet shortChain-box" style="margin-top: 0px">
      <div style="margin: 0px 50px; padding: 20px 0px; text-align: center">
        <el-button style="margin-right: 10px" @click="Previous"
          >上一步</el-button
        >
        <el-button v-permission type="primary" style="margin-right: 10px" @click="send"
          >发送</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { mapState, mapMutations, mapActions } from "vuex";
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
import DatePlugin from "@/components/publicComponents/DatePlugin"; //日期
import inputTag from "@/components/publicComponents/InputTag";
export default {
  name: "EnterTheNumber",
  components: {
    FileUpload,
    DatePlugin,
    inputTag,
  },
  props:['header'],
  data() {
    var time = "任务" + "-" + moment(new Date()).format("YYYYMMDD");
    return {
      Reorganization: "",
      endingName:"",
      fileList:[],
      fileFlag: true,
      message: "",
      formData: {
        taskName: time,
        timingSend: "0", //选择立即发送还是定时发送
        type:"0",
        sendTime: "", //发送时间的值
        fileName: "",
        files: "",
        group: "",
        path: "",
        mobile: "",
        label: "",
      },
      // 手机号录入
      fileStyle: {
        size: 245678943234,
        style: ["xlsx", "xls", "txt"],
      },
      del1: true, //关闭弹框时清空图片
      tip: "仅支持.xlsx .xls.txt 等格式",
      textareaShow: true,
      downloadtem: "2", //模板下载（全文，自定义还是变量）（自定义为2）
      variableTp: true,
      limit: 0, //号码限制
      RowNum: null, //上传行数

      // 定时时间
      datePluginValueList: {
        //日期参数配置
        type: "datetime",
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          },
        },
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      contentMMS: (state) => state.contentMMS,
    }),
  },
  methods: {
    //上一步
    Previous() {
      window.sessionStorage.removeItem("tpeId");
      this.$emit("childrenPrevious", "");
    },
    //移除文件
    fileup(val) {
      this.RowNum = null;
      this.formData.group = "";
      this.formData.path = "";
      this.formData.fileName = "";
      this.formData.files = "";
      this.textareaShow = true;
      this.formData.mobile = "";
      this.limit = 0; //号码限制
      this.SuccessfullySubmitted = 0; //成功提交号
      this.filter = 0; //过滤
      this.invalid = 0; //无效
      this.fileList = []
    },
    //限制用户上传文件格式和大小
        beforeAvatarUpload(file) { 
            let endingCode = file.name;//结尾字符
            this.endingName = endingCode.slice(endingCode.lastIndexOf('.')+1,endingCode.length);
            let isStyle = false; //文件格式
            const isSize = file.size / 1024 / 1024 < this.fileStyle.size; //文件大小
            console.log(isSize)
            for(let i=0;i<this.fileStyle.style.length;i++){
                if(this.endingName === this.fileStyle.style[i]){
                    isStyle = true;
                    break; 
                }
            }
            //不能重复上传文件
            let fileArr=this.fileList;
            let fileNames=[];
            if(fileArr.length>0){
                    for(let k=0;k<fileArr.length;k++){
                    fileNames.push(fileArr[k].name)
                } 
            }
            if(fileNames.indexOf(endingCode)!==-1){
                this.$message.error('不能重复上传文件');
                return false;
            }else if (!isStyle) { //文件格式判断
                this.$message.error(this.tip);
                return false;
            }else{
                //文件大小判断
                if (!isSize) {
                    this.$message.error('上传文件大小不能超过'+this.fileStyle.size);
                    return false;
                }
            }
        },
    //文件上传成功
    fileupres(val, val2) {
      if (val.code == 200) {
        this.fileFlag = true;
        this.RowNum = val.data.total;
        this.formData.fileName = val.data.fileName;
        this.formData.group = val.data.group;
        this.formData.path = val.data.path;
        this.formData.files = val.data.fileName;
        this.formData.mobile = "";
        this.textareaShow = false;
        this.del1 = true;
        this.$message({
          message: val.msg,
          type: "success",
        });
      } else {
        this.fileFlag = false;
        this.message = val.msg;
        this.$message({
          message: val.msg,
          type: "error",
        });
      }
    },
    //下载模板
    downloadTems() {
      if (this.downloadtem == "2") {
        this.$File.export(
          this.API.cpus + "v3/consumersms/templateZipDownload",
          {},
          `发送文件模板（自定义内容，全文模板可用）.zip`
        );
      } else {
        this.$File.export(
          this.API.cpus + "v3/consumersms/templateZipDownload",
          {},
          `发送文件模板（变量模板可用）.zip`
        );
      }
    },
    handChildLabel(data) {
      this.formData.label = data;
      // console.log(this.configurationItem.formData.label,'data');
    },
    // 过滤号码
    FilterNumber() {
      this.formData.mobile = this.formData.mobile.replace(/\D/g, ",");
      let NumberFilter = this.formData.mobile.split(",");
      let arrNumber = [];
      let hash = [];
      let reg = /^(?:\+?86)?1\d{10}$/;
      for (var i = 0; i < NumberFilter.length; i++) {
        for (var j = i + 1; j < NumberFilter.length; j++) {
          if (NumberFilter[i] === NumberFilter[j]) {
            ++i;
          }
        }
        arrNumber.push(NumberFilter[i]);
      }
      for (var i = 0; i < arrNumber.length; i++) {
        if (reg.test(arrNumber[i])) {
          hash.push(arrNumber[i]);
        }
      }
      this.formData.mobile = hash.join(",");
      this.SuccessfullySubmitted = hash.length; //成功提交号
      this.filter = NumberFilter.length - arrNumber.length; //过滤
      if (arrNumber[0] == "") {
        this.invalid == 0;
      } else {
        this.invalid = arrNumber.length - hash.length; //无效
      }
      this.limit = hash.length;
    },
    textChange() {
      if (this.formData.mobile[this.formData.mobile.length - 1] == ",") {
        this.limit = this.formData.mobile.split(",").length - 1;
      } else {
        this.limit = this.formData.mobile.split(",").length;
      }
      if (
        this.formData.mobile.split(",").length == 1 &&
        this.formData.mobile.split(",")[0] == ""
      ) {
        this.limit = 0;
      }
    },
    handledatepluginVal: function (val1, val2) {
      //日期
      this.formData.sendTime = val1;
    },
    handelSend(val){
      if(val == 0){
        this.fileup()
      }else{
        this.formData.mobile = ''
      }
    },
    // 发送
    send() {
      if (!this.fileFlag) {
        this.$message({
          message: this.message,
          type: "error",
        });
        return;
      }
      if (this.limit > 200) {
        this.$message({
          message: "手机号超出填写个数",
          type: "warning",
        });
        return false;
      }
      //判断是否上传文件
      if (!(this.formData.files != "" || this.formData.mobile != "")) {
        if (this.downloadtem == "2") {
          this.$message({
            message: "上传发送对象文件或手动填写手机号!",
            type: "warning",
          });
        } else {
          this.$message({
            message: "上传发送对象文件!",
            type: "warning",
          });
        }
        return false;
      }
      let flags;
      if (this.formData.timingSend === "1") {
        //判断是否定时时间 ，定时时间范围
        if (this.formData.sendTime == "") {
          this.$message({
            message: "请选大于当前30分钟的定时时间！",
            type: "warning",
          });
        } else {
          let nowTiem = new Date(this.formData.sendTime).getTime();
          if (nowTiem < Date.now() + 1800000) {
            flags = false;
            this.$message({
              message: "定时时间应大于当前时间30分钟，需重新设置！",
              type: "warning",
            });
            this.datePluginValueList.datePluginValue = "";
            this.formData.sendTime = "";
          } else {
            flags = true;
          }
        }
      } else {
        flags = true;
      }
      if (flags) {
        // this.contentMMS.timingSend=this.formData.timingSend
        // this.contentMMS.sendTime=this.formData.sendTime
        // this.contentMMS.fileName=this.formData.fileName
        // this.contentMMS.group=this.formData.group
        // this.contentMMS.path=this.formData.path
        // this.contentMMS.mobile=this.formData.mobile
        // if(window.sessionStorage.getItem('tpeId')){
        //     this.contentMMS.videoId=window.sessionStorage.getItem('tpeId')
        // }
        this.formData.videoId = window.sessionStorage.getItem("tpeId");
        this.$confirms.confirmation(
          "post",
          "确定发送当前视频短信模版？",
          this.API.cpus + "v1/consumervideo/send",
          this.formData,
          (res) => {
            if (res.code == 200) {
              this.fileup();
              this.datePluginValueList.datePluginValue = "";
              this.contentMMS.sendTime = "";
              this.formData.timingSend = "0";
              this.Reorganization = new Date().getTime();
              this.$emit("childrenPrevious", res.data);
            }
          }
        );
      }
    },
  },
  watch: {
    downloadtem(val) {
      if (val == "1") {
        this.fileStyle.style = ["xlsx", "xls"];
        this.tip = "仅支持.xlsx .xls 等格式";
        this.formData.mobile = "";
      } else {
        this.fileStyle.style = ["xlsx", "xls", "txt"];
        this.tip = "仅支持.xlsx .xls.txt 等格式";
      }
    },
  },
};
</script>
<style scoped>
.active{
        font-size: 67px !important; 
    color: #C0C4CC !important;
    margin: 40px 0 16px !important;
    line-height: 50px !important;
}
.send-upload-tips {
  padding-left: 96px;
  padding-top: 5px;
  font-size: 12px;
}
.send-upload-tips span {
  display: inline-block;
  padding-left: 8px;
  color: #0066ff;
}
.el-textarea {
  width: 50%;
}
.textareas textarea {
  height: 100px;
  resize: none;
}
.textareas textarea::-webkit-scrollbar {
  display: none;
}
.box-textareas {
  width: 380px;
  height: 125px;
  border-radius: 5px;
  border: 1px solid rgb(162, 219, 208);
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
.el-scrollbar__wrap {
  margin-bottom: 0px !important;
}
/* .el-upload--text {
    height: 40px !important;
} */
</style>
<style>
#phoneLR .textareas > .el-textarea__inner {
  height: 100px !important;
  border: none !important;
  resize: none !important;
}
#phoneLR .el-scrollbar__wrap {
  margin-bottom: 0px !important;
}
</style>