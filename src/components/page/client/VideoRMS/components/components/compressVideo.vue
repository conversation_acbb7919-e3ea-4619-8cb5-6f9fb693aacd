<template>
  <div class="modern-compress-video">
    <!-- 上传区域 -->
    <div class="upload-section">
      <div class="upload-container">
        <el-upload
          :class="{ 'hide-upload': hideUpload }"
          :action="action"
          :headers="token"
          list-type="picture-card"
          :data="{ FoldPath: '上传目录', SecretKey: '安全验证' }"
          :file-list="fileList"
          :limit="1"
          :on-progress="uploadVideoProcess"
          :before-upload="beforeUploadVideo"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          class="video-uploader"
        >
          <i slot="default" class="el-icon-upload upload-icon"></i>
          <div slot="file" slot-scope="{ file }" class="upload-preview">
            <video :src="path" controls class="preview-video"></video>
            <span class="el-upload-list__item-actions">
              <span
                v-if="!disabled"
                class="el-upload-list__item-delete"
                @click="handleRemove"
              >
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
        
        <div v-if="!formInline.url" class="upload-hint">
          <h3 class="hint-title">上传视频文件</h3>
          <p class="hint-text">支持格式：MP4、WEBM</p>
          <p class="hint-text">文件大小：不超过50MB</p>
        </div>
      </div>
    </div>

    <!-- 视频信息和控制面板 -->
    <div v-if="formInline.url" class="control-section">
      <el-card shadow="hover" class="info-card">
        <div slot="header" class="card-header">
          <span class="card-title">
            <i class="el-icon-info"></i>
            视频信息
          </span>
        </div>
        
        <div class="info-list">
          <div class="info-item">
            <span class="info-label">文件名称：</span>
            <span class="info-value">{{ formInline.fileName }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">原始大小：</span>
            <span class="info-value highlight">{{ formInline.size }} MB</span>
          </div>
          
          <div class="info-item slider-item">
            <span class="info-label">清晰度：</span>
            <div class="slider-container">
              <span class="slider-value">{{ formInline.videoBitRate }}</span>
              <el-slider
                v-model="videoBitRate"
                :step="1"
                :format-tooltip="() => editFormInline.videoBitRate"
                @input="handelChange($event, 'videoBitRate')"
                class="quality-slider"
              />
            </div>
          </div>
          
          <div class="info-item">
            <span class="info-label">预估大小：</span>
            <span class="info-value estimate">{{ formInline.videoChangeSize || '计算中...' }} MB</span>
          </div>
        </div>
      </el-card>

      <!-- 操作提示 -->
      <el-alert
        type="info"
        :closable="false"
        show-icon
        class="compress-tips"
      >
        <template slot="default">
          <strong>温馨提示：</strong>
          视频短信容量最大不超过1.8MB，压缩视频需注意图片和文字大小。
          如：图片300KB，文字50KB，则视频建议在1.5MB内。
        </template>
      </el-alert>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          v-if="!pressLoding"
          type="primary"
          size="large"
          icon="el-icon-s-operation"
          @click="compressVideo"
          class="compress-btn"
        >
          开始压缩
        </el-button>
        
        <el-button
          v-else
          type="primary"
          size="large"
          :loading="true"
          class="compress-btn"
        >
          {{ showTip || '压缩中...' }}
        </el-button>

        <el-button
          v-if="videoPath"
          type="success"
          size="large"
          icon="el-icon-download"
          @click="downVideo(videoPath)"
          class="download-btn"
        >
          下载视频
        </el-button>
      </div>
    </div>

    <!-- 压缩结果展示 -->
    <transition name="fade">
      <div v-if="videoPath" class="result-section">
        <el-card shadow="hover" class="result-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-success"></i>
              压缩完成
            </span>
            <el-tag type="success" size="small">
              压缩后大小：{{ pressedData.size }} MB
            </el-tag>
          </div>
          
          <div class="video-preview">
            <video :src="videoPath" controls class="result-video"></video>
          </div>
        </el-card>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    compressVideoDia: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      action: this.API.fiveWeb + "/compress/upload",
      token: {},
      fileList: [],
      videoFlag: false,
      videoUploadPercent: "",
      loadingFlag: false,
      titleW: "",
      path: "",
      disabled: false,
      formInline: {},
      formInlineCopy: {},
      editFormInline: {},
      videoPath: "",
      pressLoding: false,
      videoWidth: 100,
      videoHeight: 100,
      videoFrameRate: 100,
      videoBitRate: 100,
      audioBitRate: 100,
      compressId: "",
      pressTimmer: "",
      showTip: "",
      pressedData: {},
      hideUpload: false,
      videoInpSizeFlag: "",
      videoChangeSize: ""
    };
  },
  methods: {
    // 清除素材地址
    handleRemove() {
      this.fileList = [];
      this.path = "";
      this.formInline = {};
      this.formInlineCopy = {};
      this.editFormInline = {};
      this.videoPath = "";
      this.hideUpload = false;
      this.videoWidth = 100;
      this.videoHeight = 100;
      this.videoFrameRate = 100;
      this.videoBitRate = 100;
      this.audioBitRate = 100;
      this.compressId = "";
      this.showTip = "";
      this.pressedData = {};
      this.videoChangeSize = "";
      if (this.pressTimmer) {
        window.clearInterval(this.pressTimmer);
        this.pressTimmer = "";
      }
    },

    // 上传成功钩子函数
    handleSuccess(res, fileList) {
      if (res.code == 200) {
        this.videoFlag = false;
        this.videoUploadPercent = 0;
        this.path = res.data.url;
        this.formInline = res.data;
        this.formInlineCopy = JSON.parse(JSON.stringify(res.data));
        let obj = JSON.stringify(res.data);
        this.editFormInline = JSON.parse(obj);
        this.formInline.size = res.data.size;
        this.hideUpload = true;
        
        this.$message({
          type: "success",
          message: "视频上传成功！"
        });
      } else {
        this.$message({
          type: "error",
          message: res.msg
        });
        this.fileList = [];
        this.path = "";
        this.loadingFlag = false;
        this.hideUpload = false;
      }
    },

    uploadVideoProcess(event, file, fileList) {
      this.videoFlag = true;
      this.videoUploadPercent = file.percentage.toFixed(0) * 1;
      this.loadingFlag = true;
      this.hideUpload = true;
    },

    beforeUploadVideo(file) {
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (
        ["video/mp4"].indexOf(file.type) == -1 &&
        ["video/WEBM"].indexOf(file.type) == -1
      ) {
        this.$message({
          type: "warning",
          message: "请上传正确的视频格式"
        });
        return false;
      }
      if (!isLt50M) {
        this.$message({
          type: "warning",
          message: "视频大小不能超过50MB"
        });
        return false;
      }
    },

    // 压缩
    compressVideo() {
      this.pressLoding = true;
      this.editFormInline.width = 640;
      this.editFormInline.height = 360;
      
      this.$api.post(
        this.API.fiveWeb + "/compress/asynVideo",
        this.editFormInline,
        (res) => {
          if (res.code == 200) {
            if (res.data) {
              this.compressId = res.data;
              this.getCompressVideoResult();
            } else {
              this.$message.error("压缩错误，请重试");
              this.pressLoding = false;
            }
          } else {
            this.$message.error(res.msg);
            this.pressLoding = false;
          }
        }
      );
    },

    getCompressVideoResult() {
      if (!this.pressTimmer) {
        this.pressTimmer = window.setInterval(() => {
          this.$api.get(
            this.API.fiveWeb + "/compress/result",
            { compressId: this.compressId },
            (res) => {
              if (res.code === 200) {
                window.clearInterval(this.pressTimmer);
                this.pressLoding = false;
                this.videoPath = res.data.url;
                this.pressedData = res.data;
                this.pressTimmer = "";
                
                this.$message({
                  type: "success",
                  message: "视频压缩完成！"
                });
              } else if (res.code === 1) {
                this.showTip = res.msg;
              } else {
                this.$message.error(res.msg);
                window.clearInterval(this.pressTimmer);
                this.pressTimmer = "";
                this.pressLoding = false;
              }
            }
          );
        }, 3000);
      }
    },

    // 下载
    downVideo(url) {
      window.open(`${url}?response-content-type=application%2Foctet-stream`);
    },

    calculatePercentage(originalValue, percentage) {
      return (originalValue * (percentage / 100)).toFixed(0);
    },

    handelChange(e, tag) {
      switch (tag) {
        case "videoBitRate":
          this.formInline.videoBitRate = JSON.parse(
            JSON.stringify(this.formInlineCopy.videoBitRate)
          );
          this.editFormInline.videoBitRate = this.calculatePercentage(
            this.formInline.videoBitRate,
            e
          );
          this.formInline.videoBitRate = this.editFormInline.videoBitRate;
          this.formInline.videoChangeSize = (
            this.formInline.size *
            (e / 100)
          ).toFixed(2);
          break;
        default:
          break;
      }
    }
  },
  mounted() {
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN")
    };
  },
  watch: {
    compressVideoDia: {
      handler(newValue) {
        if (!newValue) {
          this.handleRemove();
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.modern-compress-video {
  padding: 20px;
}

/* 上传区域 */
.upload-section {
  margin-bottom: 24px;
  
  .upload-container {
    text-align: center;
    
    .video-uploader {
      display: inline-block;
      
      /deep/ .el-upload--picture-card {
        width: 200px;
        height: 200px;
        line-height: 200px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #409eff;
          background: #f5f7fa;
        }
        
        .upload-icon {
          font-size: 48px;
          color: #c0c4cc;
        }
      }
      
      /deep/ .el-upload-list__item {
        width: 200px;
        height: 200px;
        border-radius: 8px;
        
        .upload-preview {
          width: 100%;
          height: 100%;
          
          .preview-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
          }
        }
      }
    }
    
    &.hide-upload {
      /deep/ .el-upload--picture-card {
        display: none;
      }
    }
    
    .upload-hint {
      margin-top: 16px;
      
      .hint-title {
        font-size: 16px;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .hint-text {
        font-size: 14px;
        color: #909399;
        margin: 4px 0;
      }
    }
  }
}

/* 控制面板 */
.control-section {
  .info-card {
    margin-bottom: 16px;
    border-radius: 8px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        
        i {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }
    
    .info-list {
      .info-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #ebeef5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .info-label {
          width: 100px;
          color: #606266;
          font-size: 14px;
        }
        
        .info-value {
          flex: 1;
          color: #303133;
          font-size: 14px;
          
          &.highlight {
            color: #409eff;
            font-weight: 600;
          }
          
          &.estimate {
            color: #67c23a;
            font-weight: 600;
          }
        }
        
        &.slider-item {
          .slider-container {
            flex: 1;
            display: flex;
            align-items: center;
            
            .slider-value {
              width: 60px;
              text-align: center;
              color: #409eff;
              font-weight: 600;
            }
            
            .quality-slider {
              flex: 1;
              margin: 0 16px;
            }
          }
        }
      }
    }
  }
  
  .compress-tips {
    margin-bottom: 20px;
    border-radius: 8px;
  }
  
  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    
    .compress-btn,
    .download-btn {
      min-width: 140px;
      height: 48px;
      font-size: 16px;
      border-radius: 24px;
    }
    
    .compress-btn {
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      border: none;
      
      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }
    
    .download-btn {
      background: linear-gradient(135deg, #67c23a 0%, #409eff 100%);
      border: none;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
      }
    }
  }
}

/* 结果展示 */
.result-section {
  margin-top: 24px;
  
  .result-card {
    border-radius: 8px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        
        i {
          margin-right: 8px;
          color: #67c23a;
        }
      }
    }
    
    .video-preview {
      display: flex;
      justify-content: center;
      padding: 20px;
      
      .result-video {
        max-width: 100%;
        max-height: 400px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

/* 隐藏上传按钮 */
.hide-upload {
  /deep/ .el-upload--picture-card {
    display: none;
  }
}
</style>