<template>
  <div class="modern-loop-upload">
    <!-- 媒体上传区域 -->
    <div
      class="upload-container"
      v-loading="loadingFlag"
      :element-loading-text="titleW"
      @mouseover="mouseOver"
      @mouseleave="mouseLeave"
    >
      <!-- 删除按钮 -->
      <transition name="fade">
        <el-button
          v-if="hasContent && styleShow"
          type="danger"
          icon="el-icon-delete"
          circle
          size="small"
          class="delete-btn"
          @click="FileRemoval"
        />
      </transition>

      <!-- 媒体内容展示 -->
      <div
        class="media-display"
        v-if="hasContent"
      >
        <!-- 图片展示 -->
        <div v-if="children.imageUrl" class="media-wrapper">
          <img :src="children.imageUrl" class="media-content image-content" />
        </div>

        <!-- 视频展示 -->
        <div v-if="children.showVideoPath" class="media-wrapper">
          <video
            :src="children.showVideoPath"
            class="media-content video-content"
            controls
          >
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 音频展示 -->
        <div v-if="children.showaudioPath" class="media-wrapper audio-wrapper">
          <audio
            :src="children.showaudioPath"
            class="media-content audio-content"
            controls
            preload="auto"
          />
        </div>
      </div>

      <!-- 上传区域 -->
      <div v-if="!hasContent && divFlag" class="upload-area">
        <div class="upload-options">
          <!-- 图片上传 -->
          <el-upload
            class="upload-item"
            :auto-upload="true"
            :action="this.API.cpus+'v3/file/uploadAndCompress'"
            :data="{FoldPath:'上传目录',SecretKey:'安全验证',fileType:'pic'}"
            :headers="header"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <div class="upload-btn">
              <i class="el-icon-picture-outline upload-icon"></i>
              <span class="upload-text">添加图片</span>
            </div>
          </el-upload>

          <!-- 视频上传 -->
          <el-upload
            class="upload-item"
            :action="this.API.cpus+'v3/file/uploadAndCompress'"
            :headers="header"
            :data="{FoldPath:'上传目录',SecretKey:'安全验证',fileType:'video'}"
            :on-progress="uploadVideoProcess"
            :on-success="handleVideoSuccess"
            :before-upload="beforeUploadVideo"
            :show-file-list="false"
          >
            <div class="upload-btn">
              <i class="el-icon-video-camera upload-icon"></i>
              <span class="upload-text">添加视频</span>
            </div>
          </el-upload>

          <!-- 音频上传 -->
          <el-upload
            class="upload-item"
            :action="this.API.cpus+'v3/file/uploadAndCompress'"
            :headers="header"
            :data="{FoldPath:'上传目录',SecretKey:'安全验证',fileType:'audio'}"
            :on-success="handleAudioSuccess"
            :before-upload="beforeUploadAudio"
            :show-file-list="false"
          >
            <div class="upload-btn">
              <i class="el-icon-headset upload-icon"></i>
              <span class="upload-text">添加音频</span>
            </div>
          </el-upload>
        </div>

        <div class="upload-tips">
          <p class="tip-title">点击上传文件</p>
          <p class="tip-format">支持格式：.jpg .png .gif .mp3 .mp4 .3gp</p>
          <p class="tip-size">文件大于1.8MB将自动压缩</p>
        </div>
      </div>

      <!-- 压缩进度 -->
      <div v-if="videoFlag" class="compress-progress">
        <el-progress
          type="circle"
          :percentage="0"
          :format="format"
          :width="80"
        />
      </div>
    </div>

    <!-- 文本输入区域 -->
    <div class="text-input-container">
      <el-input
        type="textarea"
        v-model="children.txt"
        placeholder="请输入文本内容"
        :rows="4"
        class="modern-textarea"
      />
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-tooltip content="设置播放时长" placement="top">
        <el-button
          type="text"
          icon="el-icon-time"
          @click="timeShow"
          class="toolbar-btn"
        >
          时长
        </el-button>
      </el-tooltip>
      <el-tooltip content="删除此帧" placement="top">
        <el-button
          type="text"
          icon="el-icon-delete"
          @click="deleteLoop"
          class="toolbar-btn delete"
        >
          删除
        </el-button>
      </el-tooltip>
    </div>

    <!-- 时间滑块 -->
    <transition name="slide-fade">
      <div v-if="timeFlag" class="time-slider">
        <span class="slider-label">播放时长：</span>
        <el-slider
          v-model="children.time"
          :max="15"
          :step="1"
          :show-tooltip="true"
          :format-tooltip="formatTooltip"
          class="modern-slider"
        />
      </div>
    </transition>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";

export default {
  name: "Loopupload",
  props: {
    children: Object
  },
  data() {
    return {
      header: {},
      videoFlag: false,
      videoUploadPercent: "",
      isShowUploadVideo: false,
      timeFlag: false,
      styleShow: false,
      timer: null,
      loadingFlag: false,
      divFlag: true,
      titleW: ""
    };
  },
  computed: {
    hasContent() {
      return this.children.imageUrl || this.children.showVideoPath || this.children.showaudioPath;
    }
  },
  methods: {
    format() {
      return '视频加载中···';
    },
    
    formatTooltip(val) {
      return `${val} 秒`;
    },

    // 视频上传前回调
    beforeUploadVideo(file) {
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (['video/mp4'].indexOf(file.type) == -1 && ['video/3gpp'].indexOf(file.type) == -1) {
        this.$message({
          type: 'warning',
          message: "请上传正确的视频格式"
        });
        return false;
      }
      if (!isLt30M) {
        this.$message({
          type: 'warning',
          message: "视频大小不能超过30MB"
        });
        return false;
      }
      this.isShowUploadVideo = false;
    },

    // 进度条
    uploadVideoProcess(event, file, fileList) {
      this.loadingFlag = true;
      this.divFlag = false;
      this.titleW = '视频压缩中，大约需要30-60秒···';
    },

    settimer(data, file, type) {
      this.$api.get(this.API.cpus + 'v3/file/' + data.fileId, {}, res => {
        if (!res.data.fullpath) {
          this.timer = setTimeout(() => {
            this.settimer(data, file, type);
          }, 3000);
        } else {
          this.loadingFlag = false;
          clearTimeout(this.timer);
          if (type == 1) {
            // 音频
            this.children.showaudioPath = this.API.imgU + res.data.fullpath;
            this.children.size = res.data.fileSize / 1024;
            this.children.media = res.data.fileName.split('.')[res.data.fileName.split(".").length - 1];
            this.children.mediaGroup = res.data.group;
            this.children.mediaPath = res.data.path;
            this.children.type = 'audio';
          } else {
            // 视频
            this.children.showVideoPath = this.API.imgU + res.data.fullpath;
            this.children.media = res.data.fileName.split('.')[res.data.fileName.split(".").length - 1];
            this.children.size = res.data.fileSize / 1024;
            this.children.mediaGroup = res.data.group;
            this.children.mediaPath = res.data.path;
            this.children.type = 'video';
          }
        }
      });
    },

    // 上传成功回调
    handleVideoSuccess(res, file) {
      if (res.code == 200) {
        this.isShowUploadVideo = true;
        if (res.data.fullpath) {
          this.loadingFlag = false;
          this.children.showVideoPath = this.API.imgU + res.data.fullpath;
          this.children.media = file.name.split('.')[file.name.split(".").length - 1];
          this.children.size = res.data.fileSize / 1024;
          this.children.mediaGroup = res.data.group;
          this.children.mediaPath = res.data.path;
          this.children.type = 'video';
        } else {
          this.settimer(res.data, file, 2);
        }
      } else {
        this.loadingFlag = false;
        this.divFlag = true;
        this.$message({
          type: 'warning',
          message: '上传失败，请重新上传'
        });
      }
    },

    // 时间展示
    timeShow() {
      this.timeFlag = !this.timeFlag;
    },

    // 图片上传成功
    handleAvatarSuccess(res, file) {
      this.titleW = '图片压缩中，大约需要30-60秒···';
      this.loadingFlag = true;
      this.divFlag = false;
      if (res.code == 200) {
        this.loadingFlag = false;
        this.children.imageUrl = URL.createObjectURL(file.raw);
        this.children.size = res.data.fileSize / 1024;
        this.children.media = file.name.split('.')[file.name.split(".").length - 1];
        this.children.mediaGroup = res.data.group;
        this.children.mediaPath = res.data.path;
        this.children.type = 'img';
      } else {
        this.loadingFlag = false;
        this.divFlag = true;
        this.$message({
          type: 'warning',
          message: '上传失败，请重新上传'
        });
      }
    },

    beforeAvatarUpload(file) {
      const siJPGGIF = file.name.split('.')[file.name.split(".").length - 1];
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (siJPGGIF != 'jpg' && siJPGGIF != 'gif' && siJPGGIF != 'png') {
        this.$message.warning('上传图片只能是 jpg、gif、png格式!');
        return false;
      }
      if (!isLt30M) {
        this.$message.warning('上传图片大小不能超过 30MB!');
        return false;
      }
    },

    // 音频上传前回调
    beforeUploadAudio(file) {
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (['audio/mpeg'].indexOf(file.type) == -1 && ['audio/mp3'].indexOf(file.type) == -1) {
        this.$message({
          type: 'warning',
          message: "请上传正确的音频格式"
        });
        return false;
      }
      if (!isLt30M) {
        this.$message({
          type: 'warning',
          message: "音频大小不能超过30MB"
        });
        return false;
      }
      this.isShowUploadVideo = false;
    },

    // 音频上传成功回调
    handleAudioSuccess(res, file) {
      this.isShowUploadVideo = true;
      this.titleW = '音频压缩中，大约需要30-60秒···';
      this.loadingFlag = true;
      this.divFlag = false;
      this.audioUploadPercent = 0;
      if (res.code == 200) {
        if (res.data.fullpath) {
          this.loadingFlag = false;
          this.children.showaudioPath = this.API.imgU + res.data.fullpath;
          this.children.size = res.data.fileSize / 1024;
          this.children.media = res.data.fileName.split('.')[res.data.fileName.split(".").length - 1];
          this.children.mediaGroup = res.data.group;
          this.children.mediaPath = res.data.path;
          this.children.type = 'audio';
        } else {
          this.settimer(res.data, file, 1);
        }
      } else {
        this.loadingFlag = false;
        this.divFlag = true;
        this.$message({
          type: 'warning',
          message: '上传失败，请重新上传'
        });
      }
    },

    // 组件删除
    deleteLoop() {
      this.$emit('childrenChange', '');
    },

    // 鼠标事件
    mouseOver() {
      this.styleShow = true;
    },

    mouseLeave() {
      this.styleShow = false;
    },

    // 文件移除
    FileRemoval() {
      this.$confirm('确认移除当前文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.children.showVideoPath = '';
        this.children.showaudioPath = '';
        this.children.imageUrl = '';
        this.children.mediaGroup = '';
        this.children.mediaPath = '';
        this.children.media = '';
        this.children.size = 0;
        this.divFlag = true;
      });
    }
  },
  mounted() {
    this.header = { Authorization: "Bearer" + this.$common.getCookie('ZTGlS_TOKEN') };
  }
};
</script>

<style lang="less" scoped>
.modern-loop-upload {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 上传容器 */
.upload-container {
  position: relative;
  background: #fff;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  min-height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    border-color: #c0c4cc;
  }

  /* 删除按钮 */
  .delete-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

/* 媒体展示 */
.media-display {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;

  .media-wrapper {
    max-width: 100%;
    max-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.audio-wrapper {
      width: 100%;
    }
  }

  .media-content {
    max-width: 100%;
    max-height: 260px;
    border-radius: 4px;

    &.image-content {
      object-fit: contain;
    }

    &.video-content {
      width: 100%;
    }

    &.audio-content {
      width: 100%;
    }
  }
}

/* 上传区域 */
.upload-area {
  width: 100%;
  text-align: center;
  padding: 24px;

  .upload-options {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 24px;
  }

  .upload-item {
    .upload-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 24px;
      border: 1px solid #dcdfe6;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafafa;

      &:hover {
        border-color: #409eff;
        background: #f5f7fa;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);

        .upload-icon {
          color: #409eff;
        }
      }

      .upload-icon {
        font-size: 32px;
        color: #909399;
        margin-bottom: 8px;
        transition: color 0.3s ease;
      }

      .upload-text {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .upload-tips {
    .tip-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }

    .tip-format,
    .tip-size {
      font-size: 12px;
      color: #909399;
      margin: 4px 0;
    }
  }
}

/* 压缩进度 */
.compress-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 文本输入容器 */
.text-input-container {
  margin-top: 16px;

  .modern-textarea {
    /deep/ .el-textarea__inner {
      border-radius: 8px;
      border-color: #dcdfe6;
      font-size: 14px;
      line-height: 1.5;
      resize: none;

      &:focus {
        border-color: #409eff;
      }
    }
  }
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  padding: 0 4px;

  .toolbar-btn {
    font-size: 14px;
    padding: 4px 12px;

    &:hover {
      color: #409eff;
    }

    &.delete:hover {
      color: #f56c6c;
    }
  }
}

/* 时间滑块 */
.time-slider {
  margin-top: 12px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
  display: flex;
  align-items: center;

  .slider-label {
    font-size: 14px;
    color: #606266;
    margin-right: 12px;
    white-space: nowrap;
  }

  .modern-slider {
    flex: 1;
  }
}

/* 过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter, .slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}
</style>