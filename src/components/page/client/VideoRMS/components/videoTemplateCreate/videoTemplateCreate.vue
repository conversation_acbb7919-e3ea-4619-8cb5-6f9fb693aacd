<template>
  <div class="modern-video-create-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            type="text"
            @click="$router.push({ path: '/videoTemplate' })"
            class="back-btn"
          >
            <i class="el-icon-arrow-left"></i>
            返回
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <h1 class="page-title">
            <i class="el-icon-video-camera"></i>
            {{ Vtitle }}
          </h1>
        </div>
        <div class="header-right">
          <el-tag :type="videoId ? 'warning' : 'success'" size="medium">
            {{ videoId ? '编辑模式' : '新建模板' }}
          </el-tag>
          <el-tooltip effect="dark" content="视频压缩工具" placement="left">
            <el-button
              type="primary"
              icon="el-icon-picture-outline"
              circle
              @click="compressVideoDia = true"
              class="compress-btn"
            />
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 温馨提醒卡片 -->
        <el-card shadow="hover" class="tips-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-info"></i>
              温馨提醒
            </span>
          </div>
          <div class="tips-content">
            <el-alert
              type="info"
              :closable="false"
              show-icon
            >
              <template slot="default">
                <div class="tip-item">
                  <strong>模板报备：</strong>支持变量模板，变量请使用英文{}进行包裹，例如：尊敬的{user}。网页端发送时变量模板必须使用excel表格提交，表头必须与大括号{}内的变量名保持一致。
                </div>
                <div class="tip-item">
                  <strong>格式要求：</strong>支持.xlsx .xls等格式；手机号码需在A列，变量参数则为英文字母代替
                </div>
                <div class="tip-item">
                  <strong>变量支持：</strong>最多只支持9个变量。
                </div>
                <div class="tip-item">
                  <strong>模板签名：</strong>第一帧的文本开头以【】包住。
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>

        <!-- 视频短信标题卡片 -->
        <el-card shadow="hover" class="form-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-edit"></i>
              视频短信标题
            </span>
            <span class="card-subtitle">最多20个字符</span>
          </div>
          <div class="card-body">
            <el-input
              v-model="title"
              maxlength="20"
              show-word-limit
              placeholder="请输入视频短信标题"
              size="large"
              class="title-input"
            >
              <!-- <i slot="prefix" class="el-icon-document"></i> -->
            </el-input>
          </div>
        </el-card>

        <!-- 视频短信内容卡片 -->
        <el-card shadow="hover" class="content-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-film"></i>
              视频短信内容
            </span>
            <span class="card-subtitle">
              {{ mmsSize }}KB / 1843KB
            </span>
          </div>
          <div class="card-body">
            <div class="frames-container">
              <!-- 帧内容 -->
              <div
                v-for="(item, index) in LoopNum"
                :key="index"
                class="frame-item"
              >
                <div class="frame-header">
                  <span class="frame-title">第 {{ index + 1 }} 帧</span>
                  <span
                    class="frame-size"
                    :class="{ 'size-warning': Math.ceil(item.size + sizeof(item.txt)) > 2048 }"
                  >
                    {{ Math.ceil(item.size + sizeof(item.txt)) }}KB
                  </span>
                </div>
                <div class="frame-content">
                  <Loop-upload
                    :children="item"
                    @childrenChange="childrenChange(index)"
                    class="loop-upload-component"
                  />
                </div>
              </div>

              <!-- 添加新帧 -->
              <div class="frame-item add-frame">
                <div
                  class="add-frame-btn"
                  @click="AddToLoop"
                >
                  <i class="el-icon-plus add-icon"></i>
                  <span class="add-text">添加新帧</span>
                  <span class="add-subtitle">最多10帧</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <el-button
            size="large"
            @click="Preview"
            icon="el-icon-view"
            class="preview-btn"
          >
            预览效果
          </el-button>
          <el-button
            v-permission
            type="primary"
            size="large"
            @click="NextStep()"
            icon="el-icon-check"
            class="submit-btn"
          >
            提交审核
          </el-button>
        </div>
      </div>
    </div>

    <!-- 视频压缩弹窗 -->
    <el-dialog
      :visible.sync="compressVideoDia"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="60%"
      title="视频压缩工具"
      class="compress-dialog"
    >
      <CompressVideo :compressVideoDia="compressVideoDia" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="compressVideoDia = false" size="medium">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 预览手机弹框 -->
    <el-dialog
      title="预览效果"
      :visible.sync="dialogVisible"
      width="500px"
      class="preview-dialog"
    >
      <div class="preview-container">
        <div class="phone-mockup">
          <div class="phone-screen">
            <div class="phone-header">
              <span class="phone-time">{{ getCurrentTime() }}</span>
              <div class="phone-status">
                <i class="el-icon-wifi"></i>
                <i class="el-icon-battery-full"></i>
              </div>
            </div>
            <div class="message-container">
              <el-scrollbar class="message-scrollbar" ref="messageScrollbar">
                <div class="message-content-wrapper">
                  <div class="message-bubble title-bubble">
                    {{ this.title || '视频短信标题' }}
                  </div>
                  <div
                    v-for="(item, index) in LoopNum"
                    :key="index"
                    class="message-bubble content-bubble"
                  >
                    <img
                      v-if="item.imageUrl"
                      :src="item.imageUrl"
                      class="media-content image-content"
                      @click="showFullImage(item.imageUrl)"
                    />
                    <video
                      v-if="item.showVideoPath"
                      :src="item.showVideoPath"
                      class="media-content video-content"
                      controls
                    />
                    <audio
                      v-if="item.showaudioPath"
                      :src="item.showaudioPath"
                      class="media-content audio-content"
                      controls
                    />
                    <div class="text-content" v-if="item.txt">
                      {{ item.txt }}
                    </div>
                  </div>
                  <!-- 底部占位，确保最后的内容能滚动到可见区域 -->
                  <div class="scroll-padding"></div>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 图片放大查看弹窗 -->
    <el-dialog
      :visible.sync="imageViewerVisible"
      width="90%"
      class="image-viewer-dialog"
      append-to-body
    >
      <img :src="currentImage" class="full-image" />
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import LoopUpload from "../components/LoopUpload.vue";
import TemplateLibrary from "../components/TemplateLibrary.vue";
import CompressVideo from "../components/compressVideo.vue";

export default {
  name: "videoTemplateCreate",
  components: {
    LoopUpload,
    TemplateLibrary,
    CompressVideo,
  },
  data() {
    return {
      activeName: "MMScontent",
      dialogVisible: false,
      Vtitle: "创建视频短信模板",
      title: "",
      LoopNum: [
        {
          showVideoPath: "",
          showaudioPath: "",
          imageUrl: "",
          media: "",
          type: "",
          mediaGroup: "",
          mediaPath: "",
          txt: "",
          time: 0,
          size: 0,
        },
      ],
      mmsSize: 0,
      videoId: "",
      compressVideoDia: false,
      imageViewerVisible: false,
      currentImage: "",
    };
  },
  computed: {
    ...mapState({
      contentMMS: (state) => state.contentMMS,
    }),
  },
  methods: {
    ...mapActions(["saveMMS"]),
    
    // 获取当前时间
    getCurrentTime() {
      const now = new Date();
      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    },
    
    // 显示大图
    showFullImage(imageUrl) {
      this.currentImage = imageUrl;
      this.imageViewerVisible = true;
    },
    
    // 添加帧
    AddToLoop() {
      if (this.LoopNum.length < 10) {
        let flag = true;
        for (let i = 0; i < this.LoopNum.length; i++) {
          if (
            this.LoopNum[i].showVideoPath ||
            this.LoopNum[i].showaudioPath ||
            this.LoopNum[i].imageUrl ||
            this.LoopNum[i].txt
          ) {
            flag = true;
          } else {
            flag = false;
          }
        }
        if (flag) {
          let obj = {
            showVideoPath: "",
            showaudioPath: "",
            imageUrl: "",
            media: "",
            type: "",
            mediaGroup: "",
            mediaPath: "",
            txt: "",
            time: 0,
            size: 0,
          };
          this.LoopNum.push(obj);
        } else {
          this.$message({
            type: "warning",
            message: "请先编辑前面的内容",
          });
        }
      } else {
        this.$message({
          type: "warning",
          message: "最多添加10帧",
        });
      }
    },
    
    // 删除帧
    childrenChange(index) {
      this.LoopNum.splice(index, 1);
    },
    
    // 预览
    Preview() {
      let a = true;
      for (let i = 0; i < this.LoopNum.length; i++) {
        if (
          this.LoopNum[i].showVideoPath ||
          this.LoopNum[i].showaudioPath ||
          this.LoopNum[i].imageUrl ||
          this.LoopNum[i].txt
        ) {
          if (!this.title) {
            this.$message({
              type: "warning",
              message: "请编辑视频短信标题",
            });
            a = false;
            return false;
          }
        } else {
          this.$message({
            type: "warning",
            message: "请先编辑视频短信内容",
          });
          a = false;
          return false;
        }
      }
      if (a) {
        this.dialogVisible = true;
        // 确保滚动条在顶部
        this.$nextTick(() => {
          if (this.$refs.messageScrollbar) {
            this.$refs.messageScrollbar.wrap.scrollTop = 0;
          }
        });
      }
    },
    
    // 提交审核
    NextStep(val) {
      let a = true;
      for (let i = 0; i < this.LoopNum.length; i++) {
        if (
          this.LoopNum[i].showVideoPath ||
          this.LoopNum[i].showaudioPath ||
          this.LoopNum[i].imageUrl ||
          this.LoopNum[i].txt
        ) {
          if (!this.title) {
            this.$message({
              type: "warning",
              message: "请编辑视频短信标题",
            });
            a = false;
            return false;
          }
        } else {
          this.$message({
            type: "warning",
            message: "请先编辑视频短信内容",
          });
          a = false;
          return false;
        }
      }
      if (a) {
        this.$prompt("请输入备注", "确认当前模板提交审核？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        })
          .then(({ value }) => {
            this.$api.post(
              this.API.cpus + "v1/consumervideo/custom/create",
              {
                contents: this.LoopNum,
                title: this.title,
                videoId: this.videoId || null,
                remark: value,
              },
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    type: "success",
                    message: res.msg,
                  });
                  this.$router.push({ path: "/videoTemplate" });
                } else {
                  this.$message({
                    type: "error",
                    message: res.msg,
                  });
                }
              }
            );
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "取消输入",
            });
          });
      }
    },
    
    // 计算文本字节
    sizeof(str, charset) {
      var total = 0,
        charCode,
        i,
        len;
      charset = charset ? charset.toLowerCase() : "";
      if (charset === "utf-16" || charset === "utf16") {
        for (i = 0, len = str.length; i < len; i++) {
          charCode = str.charCodeAt(i);
          if (charCode <= 0xffff) {
            total += 2;
          } else {
            total += 4;
          }
        }
      } else {
        for (i = 0, len = str.length; i < len; i++) {
          charCode = str.charCodeAt(i);
          if (charCode <= 0x007f) {
            total += 1;
          } else if (charCode <= 0x07ff) {
            total += 2;
          } else if (charCode <= 0xffff) {
            total += 3;
          } else {
            total += 4;
          }
        }
      }
      return total / 1024;
    },
  },
  mounted() {
    window.sessionStorage.removeItem("tpeId");
    if (this.$route.query.id) {
      this.Vtitle = "编辑视频短信模板";
      this.$api.get(
        this.API.cpus + "v1/consumervideo/getOne?videoId=" + this.$route.query.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.videoId = this.$route.query.id;
            this.LoopNum = res.data.contents;
            this.title = res.data.title;
            for (let i = 0; i < this.LoopNum.length; i++) {
              if (this.LoopNum[i].type == "img") {
                this.LoopNum[i].imageUrl =
                  this.API.imgU +
                  this.LoopNum[i].mediaGroup +
                  "/" +
                  this.LoopNum[i].mediaPath;
              } else if (this.LoopNum[i].type == "video") {
                this.LoopNum[i].showVideoPath =
                  this.API.imgU +
                  this.LoopNum[i].mediaGroup +
                  "/" +
                  this.LoopNum[i].mediaPath;
              } else if (this.LoopNum[i].type == "audio") {
                this.LoopNum[i].showaudioPath =
                  this.API.imgU +
                  this.LoopNum[i].mediaGroup +
                  "/" +
                  this.LoopNum[i].mediaPath;
              }
            }
          }
        }
      );
    } else {
      this.Vtitle = "创建视频短信模板";
    }
  },
  watch: {
    LoopNum: {
      handler: function (newValue, oldValue) {
        let a = 0;
        for (let i = 0; i < this.LoopNum.length; i++) {
          a += parseInt(
            Math.ceil(this.LoopNum[i].size + this.sizeof(this.LoopNum[i].txt))
          );
        }
        this.mmsSize = a;
      },
      deep: true,
    },
  },
};
</script>

<style lang="less" scoped>
/* 现代化视频模板创建页面样式 */
.modern-video-create-page {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 页面头部样式 */
.page-header {
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  
  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    
    .back-btn {
      padding: 8px 16px;
      font-size: 14px;
      color: #606266;
      
      &:hover {
        color: #409eff;
      }
    }
    
    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .compress-btn {
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      border: none;
      
      &:hover {
        transform: scale(1.1);
      }
    }
  }
}

/* 主要内容区域 */
.page-content {
  padding: 24px 0;
  
  .content-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

/* 卡片通用样式 */
.tips-card,
.form-card,
.content-card {
  margin-bottom: 24px;
  border-radius: 8px;
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 8px;
        color: #409eff;
      }
    }
    
    .card-subtitle {
      font-size: 14px;
      color: #909399;
    }
  }
  
  .card-body {
    padding: 24px;
  }
}

/* 提示卡片样式 */
.tips-card {
  .tips-content {
    .tip-item {
      margin-bottom: 8px;
      line-height: 1.6;
      font-size: 14px;
      color: #606266;
      
      strong {
        color: #303133;
      }
    }
  }
}

/* 标题输入样式 */
.title-input {
  /deep/ .el-input__inner {
    height: 48px;
    font-size: 16px;
  }
}

/* 帧容器样式 */
.frames-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

/* 帧项目样式 */
.frame-item {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }
  
  .frame-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .frame-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .frame-size {
      font-size: 14px;
      color: #909399;
      
      &.size-warning {
        color: #f56c6c;
        font-weight: 600;
      }
    }
  }
  
  .frame-content {
    min-height: 400px;
  }
}

/* 添加新帧样式 */
.add-frame {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 480px;
  cursor: pointer;
  border: 2px dashed #dcdfe6;
  background: #fafafa;
  
  &:hover {
    border-color: #409eff;
    background: #f5f7fa;
    
    .add-frame-btn {
      transform: scale(1.05);
    }
  }
  
  .add-frame-btn {
    text-align: center;
    transition: transform 0.3s ease;
    
    .add-icon {
      font-size: 48px;
      color: #c0c4cc;
      display: block;
      margin-bottom: 12px;
    }
    
    .add-text {
      font-size: 16px;
      color: #606266;
      display: block;
      margin-bottom: 4px;
    }
    
    .add-subtitle {
      font-size: 14px;
      color: #909399;
    }
  }
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .preview-btn,
  .submit-btn {
    min-width: 160px;
    height: 48px;
    font-size: 16px;
    border-radius: 24px;
  }
  
  .preview-btn {
    &:hover {
      color: #409eff;
      border-color: #409eff;
    }
  }
  
  .submit-btn {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    border: none;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  }
}

/* 预览对话框样式 */
.preview-dialog {
  /deep/ .el-dialog__header {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    color: #fff;
    padding: 16px 20px;
    
    .el-dialog__title {
      color: #fff;
    }
    
    .el-dialog__close {
      color: #fff;
    }
  }
  
  .preview-container {
    display: flex;
    justify-content: center;
    padding: 24px;
  }
}

/* 手机模拟器样式 */
.phone-mockup {
  width: 375px;
  height: 667px;
  background: #f5f5f5;
  border-radius: 36px;
  padding: 10px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  
  .phone-screen {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 26px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .phone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    background: #f8f8f8;
    font-size: 14px;
    flex-shrink: 0;
    
    .phone-time {
      font-weight: 600;
    }
    
    .phone-status {
      display: flex;
      gap: 8px;
      
      i {
        font-size: 16px;
      }
    }
  }
  
  .message-container {
    flex: 1;
    background: #e5e5e5;
    overflow: hidden;
    position: relative;
  }
  
  .message-scrollbar {
    height: 100%;
    
    /deep/ .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    
    /deep/ .el-scrollbar__view {
      min-height: 100%;
    }
  }
  
  .message-content-wrapper {
    padding: 16px;
    min-height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .message-bubble {
    background: #fff;
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;
    max-width: 85%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    word-break: break-word;
    
    &.title-bubble {
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      color: #fff;
      font-weight: 600;
    }
    
    .media-content {
      width: 100%;
      border-radius: 8px;
      margin-bottom: 8px;
      display: block;
      
      &.image-content {
        cursor: pointer;
        transition: transform 0.2s;
        
        &:hover {
          transform: scale(1.02);
        }
      }
      
      &.video-content {
        max-height: 200px;
        object-fit: contain;
        background: #000;
      }
      
      &.audio-content {
        width: 100%;
      }
    }
    
    .text-content {
      white-space: pre-line;
      line-height: 1.5;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
  }
  
  .scroll-padding {
    height: 20px;
    flex-shrink: 0;
  }
}

/* 压缩对话框样式 */
.compress-dialog {
  /deep/ .el-dialog__header {
    background: #f5f7fa;
    padding: 20px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
}

/* 图片查看弹窗样式 */
.image-viewer-dialog {
  /deep/ .el-dialog__header {
    background: #000;
    padding: 10px 20px;
    
    .el-dialog__title {
      color: #fff;
    }
    
    .el-dialog__close {
      color: #fff;
      font-size: 24px;
      
      &:hover {
        color: #ccc;
      }
    }
  }
  
  /deep/ .el-dialog__body {
    padding: 0;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
  }
  
  .full-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .frames-container {
    grid-template-columns: 1fr;
  }
  
  .phone-mockup {
    transform: scale(0.8);
  }
}
</style>