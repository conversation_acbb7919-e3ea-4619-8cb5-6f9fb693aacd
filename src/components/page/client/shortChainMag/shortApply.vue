<template>
    <div class="simple-signature-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 标题信息区域 -->
                        <div class="action-section">
                            <!-- <div class="action-buttons">
                                <div class="page-info">
                                    <i class="el-icon-lx-emoji"></i>
                                    <span class="page-title">白名单申请</span>
                                </div>
                            </div> -->
                            
                            <!-- <div class="stats-info">
                                <el-alert
                                    title="温馨提示：请确保域名可正常访问，审核通过后将加入白名单"
                                    type="info"
                                    :closable="false"
                                    show-icon
                                    class="notice-alert">
                                </el-alert>
                            </div> -->
                            
                            <div class="action-buttons">
                                <el-button
                                    v-permission
                                    :disabled="shortStatus == 1" 
                                    @click="addWhiteList('add')"
                                    type="primary"
                                    class="action-btn primary"
                                    icon="el-icon-plus">
                                    添加白名单
                                </el-button>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <el-form 
                                ref="queryForm" 
                                :model="formInline" 
                                class="search-form">
                                <div class="search-row">
                                    <el-form-item label="域名" prop="domain" class="search-item" label-width="80px">
                                        <el-input 
                                            v-model="formInline.domain" 
                                            placeholder="请输入域名" 
                                            class="search-input"
                                            clearable>
                                        </el-input>
                                    </el-form-item>
                                    
                                    <el-form-item label="审核状态" prop="auditStatus" class="search-item" label-width="80px">
                                        <el-select 
                                            v-model="formInline.auditStatus" 
                                            placeholder="请选择状态"
                                            class="search-select"
                                            clearable>
                                            <el-option label="全部" value=""></el-option>
                                            <el-option label="审核中" value="1"></el-option>
                                            <el-option label="审核通过" value="2"></el-option>
                                            <el-option label="审核未通过" value="3"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    
                                    <el-form-item label="创建时间" prop="time" class="search-item" label-width="80px">
                                        <el-date-picker 
                                            type="daterange" 
                                            v-model="formInline.time" 
                                            range-separator="至"
                                            start-placeholder="开始日期" 
                                            end-placeholder="结束日期" 
                                            class="search-date"
                                            @change="handleTimeChange">
                                        </el-date-picker>
                                    </el-form-item>

                                    <div class="search-buttons">
                                        <el-button 
                                            type="primary" 
                                            @click="Query()" 
                                            class="action-btn primary"
                                            icon="el-icon-search">
                                            查询
                                        </el-button>
                                        <el-button 
                                            @click="Reload('queryForm')" 
                                            class="action-btn"
                                            icon="el-icon-refresh">
                                            重置
                                        </el-button>
                                    </div>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>

                <!-- 表格内容 -->
                <div class="table-container">
                    <el-table
                        v-loading="tableDataObj.loading2"
                        element-loading-text="加载中..."
                        element-loading-spinner="el-icon-loading"
                        element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable"
                        border
                        :stripe="true"
                        :data="tableDataObj.tableData"
                        class="enhanced-table">
                        
                        <el-table-column label="域名" min-width="200" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div class="domain-info">
                                    <i class="el-icon-link"></i>
                                    <span class="domain-text">{{ scope.row.domain }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="审核状态" width="140" align="center">
                            <template slot-scope="scope">
                                <div class="status-info">
                                    <el-tag 
                                        size="small" 
                                        :type="getStatusType(scope.row.auditStatus)"
                                        :icon="getStatusIcon(scope.row.auditStatus)">
                                        {{ getStatusText(scope.row.auditStatus) }}
                                    </el-tag>
                                    <el-tooltip 
                                        v-if="scope.row.auditStatus == '3' && scope.row.auditReason" 
                                        effect="dark" 
                                        :content="scope.row.auditReason" 
                                        placement="top">
                                        <i class="el-icon-question failure-reason" @click.stop></i>
                                    </el-tooltip>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="备注信息" min-width="150" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div class="remark-info">
                                    <span v-if="scope.row.remark" class="remark-text">{{ scope.row.remark }}</span>
                                    <span v-else class="no-remark">暂无备注</span>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column prop="createName" label="创建人" width="120" align="center" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <div class="creator-info">
                                    <i class="el-icon-user"></i>
                                    <span>{{ scope.row.createName || '-' }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="创建时间" width="180" align="center">
                            <template slot-scope="scope">
                                <div class="create-time">
                                    <i class="el-icon-time"></i>
                                    <span>{{ scope.row.createTime ? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="操作" width="150" align="center" fixed="right">
                            <template slot-scope="scope">
                                <div class="table-actions" v-if="scope.row.auditStatus == '3'">
                                    <el-button 
                                        v-permission
                                        type="text" 
                                        size="small"
                                        class="action-btn-small edit"
                                        icon="el-icon-edit"
                                        @click="editWhiteList(scope.row)">
                                        修改
                                    </el-button>
                                    <el-button 
                                        v-permission
                                        type="text" 
                                        size="small"
                                        class="action-btn-small delete"
                                        icon="el-icon-delete"
                                        @click="handelDeleta(scope.row)">
                                        删除
                                    </el-button>
                                </div>
                                <span v-else class="no-operation">-</span>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div class="pagination-container">
                        <el-pagination 
                            @size-change="handleSizeChange" 
                            @current-change="handleCurrentChange" 
                            :current-page="formInline.currentPage"  
                            :page-size="formInline.pageSize" 
                            :page-sizes="[10, 20, 50, 100]"  
                            layout="total, sizes, prev, pager, next, jumper" 
                            :total="tableDataObj.total"
                            class="enhanced-pagination">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加白名单对话框 -->
        <el-dialog 
            title="添加白名单" 
            :visible.sync="dialogVisible" 
            width="700px"
            :before-close="handleClose"
            :close-on-click-modal="false"
            class="whitelist-dialog">
            
            <div class="dialog-content">
                <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="whitelist-form">
                    
                    <div v-if="ruleForm.domainList.length" class="domain-list">
                        <div v-for="(row, index) in ruleForm.domainList" :key="index" class="domain-item">
                            <div class="domain-form-row">
                                <el-form-item 
                                    :rules="rules.domain" 
                                    label="域名" 
                                    :prop="'domainList.' + index + '.domain'"
                                    label-width="80px"
                                    class="domain-input-item">
                                    <el-input 
                                        v-model="row.domain" 
                                        placeholder="请输入域名，例如：www.example.com"
                                        @change="(val) => handleDomainChange(val, index)">
                                        <template slot="prepend">http://</template>
                                    </el-input>
                                </el-form-item>
                                
                                <el-button 
                                    type="danger" 
                                    icon="el-icon-delete" 
                                    size="small"
                                    circle
                                    @click="ruleForm.domainList.splice(index, 1)"
                                    class="remove-btn">
                                </el-button>
                            </div>
                            
                            <el-form-item 
                                :rules="rules.remark" 
                                label="备注" 
                                :prop="'domainList.' + index + '.remark'"
                                label-width="80px"
                                class="remark-input-item">
                                <el-input 
                                    v-model="row.remark" 
                                    placeholder="请输入域名用途或备注信息"
                                    type="textarea"
                                    :rows="2"
                                    maxlength="200"
                                    show-word-limit>
                                </el-input>
                            </el-form-item>
                        </div>
                        
                        <div class="add-more-btn" @click="addWhiteListAction">
                            <i class="el-icon-plus"></i>
                            <span>继续添加域名</span>
                        </div>
                    </div>
                    
                    <div v-else class="empty-state" @click="addWhiteListAction">
                        <div class="empty-icon">
                            <i class="el-icon-plus"></i>
                        </div>
                        <div class="empty-text">点击添加白名单域名</div>
                        <div class="empty-desc">支持批量添加多个域名</div>
                    </div>
                </el-form>
                
                <div class="domain-tips">
                    <h4><i class="el-icon-info"></i> 域名格式说明</h4>
                    <ul>
                        <li>支持主域名：example.com</li>
                        <li>支持子域名：www.example.com</li>
                        <li>系统会自动提取域名，无需输入协议前缀</li>
                        <li>请确保域名可正常访问</li>
                    </ul>
                </div>
            </div>
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false" class="action-btn">取 消</el-button>
                <el-button v-permission type="primary" @click="handleSubmit('ruleForm')" :loading="submitLoading" class="action-btn primary">
                    <span v-if="!submitLoading">确 定</span>
                    <span v-else>提 交 中...</span>
                </el-button>
            </span>
        </el-dialog>

        <!-- 修改白名单对话框 -->
        <el-dialog 
            title="修改白名单" 
            :visible.sync="dialogEditVisible" 
            width="600px"
            :before-close="handleClose"
            :close-on-click-modal="false"
            class="whitelist-dialog">
            
            <el-form :model="editForm" :rules="rules" ref="editForm" class="edit-form" label-width="80px">
                <el-form-item label="域名" prop="domain">
                    <el-input 
                        v-model="editForm.domain"
                        placeholder="请输入域名，例如：www.example.com">
                        <template slot="prepend">http://</template>
                    </el-input>
                </el-form-item>
                
                <el-form-item label="备注" prop="remark">
                    <el-input 
                        v-model="editForm.remark" 
                        placeholder="请输入域名用途或备注信息"
                        type="textarea"
                        :rows="3"
                        maxlength="200"
                        show-word-limit>
                    </el-input>
                </el-form-item>
            </el-form>
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogEditVisible = false" class="action-btn">取 消</el-button>
                <el-button type="primary" @click="handleSubmitEdit('editForm')" :loading="editLoading" class="action-btn primary">
                    <span v-if="!editLoading">确 定</span>
                    <span v-else>保 存 中...</span>
                </el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import clip from '../../utils/clipboard'
import moment from 'moment'
export default {
    name: "shortApply",
    data() {
        var domain = (rule, value, callback) => {
            if (!value) {
                callback(new Error('域名不能为空'));
            } else {
                var domain = /^(?!-)[A-Za-z0-9-]+([-.]{1}[A-Za-z0-9]+)*.[A-Za-z]{2,6}$/;
                if (domain.test(value)) {
                    callback();
                } else {
                    callback(new Error('域名格式不正确'));
                }
            }
        }
        return {
            shortStatus: 1, //短链状态
            dialogVisible: false,
            dialogEditVisible: false,
            submitLoading: false,
            editLoading: false,
            actionStauts: "add",
            ruleForm: {
                domainList: []
            },
            editForm: {
                id: "",
                domain: "",
                remark: ""
            },
            apiUrl: this.API.cpus + 'v3/file/upload',
            headers: {},
            fileList: [],
            rules: {
                domain: [
                    { required: true, validator: domain, trigger: 'blur' }
                ],
                remark: [
                    { required: true, message: '备注不能为空', trigger: 'blur' }
                ]
            },
            formInline: {
                domain: "",
                auditStatus: "",
                time: [],
                beginTime: "",
                endTime: "",
                currentPage: 1,
                pageSize: 10,
            },
            tableDataObj: {
                //列表数据
                loading2: false, //loading动画
                tableData: [],
                total: 0, //总条数
            },
            selectId: [],
        }
    },
    created() {
        this.headers = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
        this.getShortStatus(); //获取短链状态
        this.getTableData(); //获取列表数据
    },
    methods: {
        // 获取状态类型
        getStatusType(status) {
            const statusMap = {
                '1': 'warning',  // 审核中
                '2': 'success',  // 审核通过
                '3': 'danger'    // 审核未通过
            };
            return statusMap[status] || 'info';
        },
        // 获取状态图标
        getStatusIcon(status) {
            const iconMap = {
                '1': 'el-icon-time',      // 审核中
                '2': 'el-icon-check',     // 审核通过
                '3': 'el-icon-close'      // 审核未通过
            };
            return iconMap[status] || '';
        },
        // 获取状态文本
        getStatusText(status) {
            const textMap = {
                '1': '审核中',
                '2': '审核通过',
                '3': '审核未通过'
            };
            return textMap[status] || '-';
        },
        //开通短链
        openShort() {
            this.$api.post(this.API.slms + 'shortLink/apply', {}, res => {
                if (res.code == 200) {
                    this.$message.success('短链申请中，请耐心等待审核结果！');
                    this.getShortStatus()
                } else {
                    this.$message.error(res.msg);
                }
            })
        },
        //开通状态
        getShortStatus(val) {
            let data = JSON.parse(localStorage.getItem('userInfo'));
            this.$api.get(this.API.cpus + 'consumerclientshortlink/userShortLinkInfo/' + data.userId, {}, res => {
                if (res.code == 200) {
                    if (res.data) {
                        this.shortStatus = res.data.productOpen;
                    } else {
                        this.shortStatus = 1
                    }
                }
            })
        },
        //查询
        Query() {
            this.formInline.currentPage = 1;
            this.getTableData()
        },
        //重置
        Reload(formName) {
            this.$refs[formName].resetFields(); //清空查询表单
            this.formInline.beginTime = "";
            this.formInline.endTime = "";
            this.formInline.currentPage = 1;
            this.getTableData()
        },
        //-----复制功能
        handleCopy(name, event) {
            clip(name, event)
        },
        //-----表格数据
        getTableData() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.slms + 'shortLink/domainApply/page', this.formInline, res => {
                if (res.code == 200) {
                    this.tableDataObj.tableData = res.data.records;
                    this.tableDataObj.total = res.data.total;
                    this.tableDataObj.loading2 = false;
                }
            })
        },
        //-----翻页操作
        handleSizeChange(size) {
            this.formInline.pageSize = size;
            this.formInline.currentPage = 1;
            this.getTableData()
        },
        handleCurrentChange: function (currentPage) {
            this.formInline.currentPage = currentPage;
            this.getTableData()
        },
        handleTimeChange(val) {
            if (val && val.length === 2) {
                this.formInline.beginTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss');
                this.formInline.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss');
            } else {
                this.formInline.beginTime = "";
                this.formInline.endTime = "";
            }
        },
        //全选
        handleSelectionChange(val) {
            let selectId = [];
            for (let i = 0; i < val.length; i++) {
                selectId.push(val[i].id);
            }
            this.selectId = selectId;
        },
        batchDelete() {
            this.$confirms.confirmation(
                "post",
                "确认是否删除白名单？",
                this.API.slms + "shortLink/domain/delete",
                { ids: this.selectId },
                (res) => {
                    if (res.code == 200) {
                        this.getTableData();
                    }
                }
            );
        },
        addWhiteListAction() {
            let obj = {
                domain: "",
                remark: "",
            }
            this.ruleForm.domainList.push(obj)
        },
        handelDeleta(row) {
            this.$confirms.confirmation(
                "post",
                "确定要删除该白名单域名吗？",
                this.API.slms + "shortLink/domain/delete",
                { ids: [row.id] },
                (res) => {
                    if (res.code == 200) {
                        this.$message.success('删除成功');
                        this.getTableData()
                    }
                }
            );
        },
        addWhiteList(type, row) {
            this.dialogVisible = true;
        },
        editWhiteList(row) {
            this.dialogEditVisible = true;
            this.editForm.id = row.id;
            this.editForm.domain = row.domain;
            this.editForm.remark = row.remark;
        },
        beforeAvatarUpload(file) {
            const isLt5M = file.size / 1024 / 1024 < 2;
            const fileType = ["jpg", "jpeg", "png"];

            if (!isLt5M) {
                this.$message.error("上传文件大小不能超过 2MB!");
                return false;
            }
            if (!fileType.includes(file.type.split("/")[1])) {
                this.$message.error("上传文件仅支持 jpg、jpeg、png格式!");
                return false;
            }
            return true;
        },
        handleRemove(file, fileList) {
            this.fileList = fileList;
            this.ruleForm.relationFile = "";
        },
        handleAvatarSuccess(res, file, fileList) {
            this.ruleForm.relationFile = res.data.fullpath;
            this.fileList = fileList;
        },
        handleClose() {
            this.ruleForm.domainList = [];
            this.editForm.domain = "";
            this.editForm.remark = "";
            this.fileList = [];
            this.submitLoading = false;
            this.editLoading = false;
        },
        handleSubmit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.submitLoading = true;
                    this.$api.post(this.API.slms + 'shortLink/domainApply', this.ruleForm, res => {
                        this.submitLoading = false;
                        if (res.code == 200) {
                            this.$message.success('添加成功，请等待审核');
                            this.dialogVisible = false;
                            this.getTableData()
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                } else {
                    console.log('error submit!!');
                }
            })
        },
        handleSubmitEdit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.editLoading = true;
                    this.$api.post(this.API.slms + 'shortLink/domain/update', this.editForm, res => {
                        this.editLoading = false;
                        if (res.code == 200) {
                            this.$message.success('修改成功，请等待审核');
                            this.dialogEditVisible = false;
                            this.getTableData()
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                } else {
                    console.log('error submit!!');
                }
            })
        },
        getDomain(url) {
            try {
                // 创建 URL 对象
                const urlObj = new URL(url);
                // 返回主机名（域名）
                return urlObj.hostname;
            } catch (error) {
                var domain = /^(?!-)[A-Za-z0-9-]+([-.]{1}[A-Za-z0-9]+)*.[A-Za-z]{2,6}$/;
                if (domain.test(url)) {
                    return url;
                } else {
                    this.$message.error("提取域名中链接必须带有http://或https://！");
                }
                return url;
            }
        },
        handleDomainChange(val, index) {
            this.ruleForm.domainList[index].domain = this.getDomain(val);
        },
    },
    watch: {
        dialogVisible(val) {
            if (!val) {
                this.$nextTick(() => {
                    this.$refs.ruleForm && this.$refs.ruleForm.resetFields();
                    this.ruleForm.domainList = [];
                });
            }
        },
        dialogEditVisible(val) {
            if (!val) {
                this.$nextTick(() => {
                    this.$refs.editForm && this.$refs.editForm.resetFields();
                });
            }
        }
    }
}
</script>

<style lang="less" scoped>
@import "~@/styles/template-common.less";

.page-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333;
    
    i {
        font-size: 18px;
        color: #409eff;
    }
    
    .page-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }
}

.notice-alert {
    max-width: 600px;
    ::v-deep .el-alert__content {
        font-size: 13px;
    }
}

.search-section {
    margin-top: 16px;

    .search-form {
        .search-row {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;

            .search-item {
                margin-bottom: 0;

                ::v-deep .el-form-item__label {
                    color: #333;
                    font-weight: 500;
                    white-space: nowrap;
                }

                .search-input,
                .search-select {
                    min-width: 180px;
                }
                
                .search-date {
                    min-width: 280px;
                }
            }

            .search-buttons {
                display: flex;
                gap: 12px;
                margin-left: auto;

                .action-btn {
                    min-width: 80px;
                    height: 36px;
                    border-radius: 6px;
                    font-weight: 500;
                    transition: all 0.3s ease;

                    &.primary {
                        background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
                        border: none;
                        color: #ffffff;

                        &:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
                        }
                    }

                    &:not(.primary) {
                        background: #ffffff;
                        border: 1px solid #d9d9d9;
                        color: #333;

                        &:hover {
                            border-color: #409eff;
                            color: #409eff;
                        }
                    }
                }
            }
        }
    }
}

.table-container {
    margin-top: 20px;

    .enhanced-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .domain-info {
            display: flex;
            align-items: center;
            gap: 8px;

            i {
                color: #409eff;
                font-size: 14px;
            }

            .domain-text {
                font-weight: 500;
                color: #333;
            }
        }

        .status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            
            .failure-reason {
                color: #f56c6c;
                font-size: 16px;
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                    color: #f78989;
                    transform: scale(1.1);
                }
            }
        }

        .remark-info {
            text-align: left;
            padding: 0 8px;

            .remark-text {
                color: #333;
            }

            .no-remark {
                color: #c0c4cc;
                font-style: italic;
                font-size: 13px;
            }
        }

        .creator-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;

            i {
                color: #666;
                font-size: 14px;
            }
        }

        .create-time {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;

            i {
                color: #409eff;
                font-size: 14px;
            }

            span {
                font-size: 13px;
                color: #333;
            }
        }

        .no-operation {
            color: #c0c4cc;
            font-size: 13px;
        }
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        background: #ffffff;
        padding: 16px 0;

        .enhanced-pagination {
            ::v-deep .el-pagination__total {
                color: #409eff;
                font-weight: 500;
            }

            ::v-deep .el-pager li.active {
                background: #409eff;
                color: #ffffff;
            }

            ::v-deep .el-pagination__jump {
                color: #666;
            }
        }
    }
}

// 对话框样式
.whitelist-dialog {
    ::v-deep .el-dialog__header {
        background: #f8f9fa;
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
    }

    ::v-deep .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
    }

    ::v-deep .el-dialog__body {
        padding: 24px;
    }
}

.dialog-content {
    .whitelist-form,
    .edit-form {
        max-height: 60vh;
        overflow-y: auto;
    }

    .domain-list {
        margin-bottom: 20px;

        .domain-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s;

            &:hover {
                border-color: #409eff;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
            }

            .domain-form-row {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                margin-bottom: 16px;

                .domain-input-item {
                    flex: 1;
                    margin-bottom: 0 !important;
                }

                .remove-btn {
                    margin-top: 32px;
                    flex-shrink: 0;
                }
            }

            .remark-input-item {
                margin-bottom: 0 !important;
            }
        }
    }

    .add-more-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        height: 50px;
        border: 2px dashed #409eff;
        border-radius: 8px;
        color: #409eff;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;

        &:hover {
            background: #ecf5ff;
            border-color: #66b1ff;
        }
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        background: #fafafa;

        &:hover {
            border-color: #409eff;
            background: #ecf5ff;
        }

        .empty-icon {
            width: 60px;
            height: 60px;
            background: #409eff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;

            i {
                font-size: 24px;
                color: white;
            }
        }

        .empty-text {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .empty-desc {
            font-size: 14px;
            color: #909399;
        }
    }

    .domain-tips {
        background: #f0f9ff;
        border: 1px solid #b3d8ff;
        border-radius: 8px;
        padding: 16px;
        margin-top: 20px;

        h4 {
            margin: 0 0 12px 0;
            color: #2c3e50;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;

            i {
                color: #409eff;
            }
        }

        ul {
            margin: 0;
            padding-left: 16px;

            li {
                color: #606266;
                font-size: 13px;
                line-height: 1.6;
                margin-bottom: 4px;
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .search-section {
        .search-form {
            .search-row {
                flex-direction: column;
                align-items: stretch;

                .search-item {
                    width: 100%;

                    .search-input,
                    .search-select,
                    .search-date {
                        width: 100%;
                    }
                }

                .search-buttons {
                    margin-left: 0;
                    justify-content: center;

                    .action-btn {
                        flex: 1;
                        max-width: 120px;
                    }
                }
            }
        }
    }

    .action-section {
        flex-direction: column;
        gap: 12px;

        .stats-info {
            order: -1;
        }
    }

    .dialog-content {
        .domain-item {
            .domain-form-row {
                flex-direction: column;
                gap: 8px;

                .remove-btn {
                    align-self: flex-end;
                    margin-top: 8px;
                }
            }
        }
    }
}
</style>