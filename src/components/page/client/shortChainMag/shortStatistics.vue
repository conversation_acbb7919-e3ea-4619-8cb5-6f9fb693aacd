<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 标题信息区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <div class="page-info">
                  <el-button 
                    type="text" 
                    icon="el-icon-arrow-left" 
                    @click="goBack()"
                    class="back-btn">
                    返回
                  </el-button>
                  <el-divider direction="vertical"></el-divider>
                  <span class="page-title">短链详细统计</span>
                </div>
              </div>
              
              <div class="stats-info">
                <span class="stats-text">查看短链的访问详情和用户行为数据</span>
              </div>
              
              <div class="action-buttons">
                <el-button 
                  type="primary" 
                  @click="exportNums1()"
                  class="action-btn primary"
                  icon="el-icon-download">
                  导出数据
                </el-button>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form 
                ref="shortFrom" 
                :model="shortFrom" 
                class="search-form">
                <div class="search-row">
                  <el-form-item label="手机号码" prop="mobile" class="search-item" label-width="80px">
                    <el-input 
                      v-model="shortFrom.mobile" 
                      placeholder="请输入手机号" 
                      class="search-input"
                      clearable>
                    </el-input>
                  </el-form-item>
                  
                  <el-form-item label="打开时间" prop="time" class="search-item" label-width="80px">
                    <el-date-picker
                      v-model="shortFrom.time"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="至"
                      :clearable="false"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date">
                    </el-date-picker>
                  </el-form-item>

                  <div class="search-buttons">
                    <el-button 
                      type="primary" 
                      @click="querySending()" 
                      class="action-btn primary"
                      icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button 
                      @click="resetSending('shortFrom')" 
                      class="action-btn"
                      icon="el-icon-refresh">
                      重置
                    </el-button>
                  </div>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="table-container">
          <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="tableDataObj.tableData"
            class="enhanced-table">
            
            <el-table-column label="手机号码" width="160" align="center">
              <template slot-scope="scope">
                <div class="mobile-info">
                  <div v-if="scope.row.mobile">
                    <div v-if="pIndex == scope.$index" class="revealed-mobile">
                      <i class="el-icon-unlock"></i>
                      <span class="mobile-text">{{ scope.row.mobile || "-" }}</span>
                    </div>
                    <div v-else class="masked-mobile" @click="phoneClickTable(scope.$index, scope.row)">
                      <i class="el-icon-lock"></i>
                      <span class="masked-text">{{ scope.row.maskMobile || "-" }}</span>
                      <el-button type="text" size="mini" class="reveal-btn">
                        <i class="el-icon-view"></i>
                      </el-button>
                    </div>
                  </div>
                  <div v-else class="unknown-mobile">
                    <i class="el-icon-question"></i>
                    <span>未知</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="短链接" width="180" align="center">
              <template slot-scope="scope">
                <div class="short-link">
                  <i class="el-icon-share"></i>
                  <span class="short-code">{{ scope.row.shortCode }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="用户代理(UA)" min-width="200" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="user-agent">
                  <i class="el-icon-monitor"></i>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="scope.row.userAgent"
                    placement="top">
                    <span class="ua-text">{{ scope.row.userAgent }}</span>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="IP地址" width="150" align="center">
              <template slot-scope="scope">
                <div class="ip-address">
                  <i class="el-icon-position"></i>
                  <span class="ip-text">{{ scope.row.ip }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="访问时间" width="180" align="center">
              <template slot-scope="scope">
                <div class="click-time">
                  <i class="el-icon-time"></i>
                  <span>{{ moment(scope.row.clickTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination 
              @size-change="handleSizeChange" 
              @current-change="handleCurrentChange" 
              :current-page="formData.currentPage"  
              :page-size="formData.pageSize" 
              :page-sizes="[10, 20, 50, 100]"  
              layout="total, sizes, prev, pager, next, jumper" 
              :total="tableDataObj.totalRow"
              class="enhanced-pagination">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出数据对话框 -->
    <el-dialog 
      title="导出数据" 
      :visible.sync="exportShow" 
      width="500px" 
      :before-close="handleClose"
      class="export-dialog">
      
      <div class="export-content">
        <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="export-form">
          <el-form-item label="导出类型" prop="decode">
            <el-radio-group v-model="ruleForm.decode" class="export-radio-group">
              <el-radio label="0" class="export-radio">
                <div class="radio-content">
                  <div class="radio-title">
                    <i class="el-icon-lock"></i>
                    掩码导出
                  </div>
                  <div class="radio-desc">手机号码将显示为掩码格式</div>
                </div>
              </el-radio>
              <el-radio label="1" class="export-radio">
                <div class="radio-content">
                  <div class="radio-title">
                    <i class="el-icon-unlock"></i>
                    明码导出
                  </div>
                  <div class="radio-desc">完整显示手机号码</div>
                </div>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <div class="email-notice" v-if="ruleForm.decode == '1'">
            <el-alert
              :title="`明码文件将发送至您的邮箱：${emailInfo.email}`"
              type="warning"
              :closable="false"
              show-icon>
              <template slot="default">
                为了保护用户隐私，明码文件不支持直接下载，将通过邮件方式发送
              </template>
            </el-alert>
          </div>
        </el-form>
      </div>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="exportShow = false" class="action-btn">取消</el-button>
        <el-button type="primary" @click="submitExport" class="action-btn primary">
          <i :class="ruleForm.decode == '0' ? 'el-icon-download' : 'el-icon-message'"></i>
          {{ ruleForm.decode == '0' ? '立即下载' : '发送邮件' }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import DownLoadExport from "@/components/publicComponents/downLodExport";
export default {
  components: {
    DownLoadExport,
  },
  name:"shortStatistics",
  data() {
    return {
      phoneList: [],
      pIndex: -1,
      exportFlag: false,
      shortFrom: {
        mobile: "",
        time: [],
        beginTime: "",
        endTime: "",
        shortCode: "",
        currentPage: 1,
        pageSize: 10,
      },
      formData: {
        mobile: "",
        time: [],
        beginTime: "",
        endTime: "",
        shortCode: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
      },
      emailInfo: {
        email: "",
        username: ""
      },
      exportShow: false,
      ruleForm: {
        decode: "0",
      },
    };
  },
  created() {
    this.formData.shortCode = this.$route.query.shortCode;
    this.shortFrom.shortCode = this.$route.query.shortCode;
    this.getList();
  },
  methods: {
    goBack() {
      this.$router.push({ path: "Shortchainstatistics" });
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.shortFrom.beginTime = this.moment(val[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.shortFrom.endTime = this.moment(val[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      } else {
        this.shortFrom.beginTime = "";
        this.shortFrom.endTime = "";
      }
    },
    getList() {
      this.pIndex = -1;
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.slms + "shortLinkClick/page",
        this.formData,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tableData.forEach(item=>{
            item.maskMobile = item.mobile
          })
          this.tableDataObj.totalRow = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    querySending() {
      this.formData.currentPage = 1;
      Object.assign(this.formData, this.shortFrom);
      this.getList();
    },
    resetSending(formName) {
      this.$refs[formName].resetFields();
      this.shortFrom.beginTime = "";
      this.shortFrom.endTime = "";
      this.formData.currentPage = 1;
      Object.assign(this.formData, this.shortFrom);
      this.getList();
    },
    //获取分页的每页数量
    handleSizeChange(size) {
      this.formData.pageSize = size;
      this.formData.currentPage = 1;
      this.getList();
    },
    //获取分页的第几页
    handleCurrentChange(currentPage) {
      this.formData.currentPage = currentPage;
      this.getList();
    },
    handleClose() {
      this.exportFlag = false;
    },
    phoneClickTable(index, row) {
      this.pIndex = index;
      this.$api.post(
        this.API.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          cipherMobile: row.cipherMobile,
          smsInfoId:row.cipherMobile
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    getLoginPhone() {
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data;
          }
        }
      );
    },
    exportFn(obj) {
      this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        if (res.code == 200) {
          this.exportShow = false
          this.$message({
            type: "success",
            duration: "2000",
            message: "已加入到文件下载中心!",
          });
          this.$router.push('/FileExport');
        } else {
          this.$message({
            type: "error",
            duration: "2000",
            message: res.msg,
          });
        }
      });
    },
    exportNums1() {
      if(this.tableDataObj.tableData.length==0){
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
      }else{
        this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
          if (res.code == 200) {
            this.emailInfo.email = res.data.email;
            this.emailInfo.username = res.data.username;
            if (res.data.decode) {
              this.exportShow = true
            } else {
              let data = {};
              Object.assign(data, this.formData);
              data.productType = 11;
              this.exportFn(data)
            }
          }
        })
      }
    },
    submitExport() {
      let data = Object.assign({}, this.formData);
      data.productType = 11;
      data.decode = this.ruleForm.decode == 0 ? false : true;
      this.exportFn(data)
    }
  },
};
</script>

<style lang="less" scoped>
@import "~@/styles/template-common.less";

.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  
  .back-btn {
    color: #409eff;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s ease;
    
    &:hover {
      background: #ecf5ff;
      color: #66b1ff;
    }
    
    i {
      margin-right: 4px;
    }
  }
  
  .page-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #2c3e50;
  }
  
  .el-divider--vertical {
    height: 20px;
    margin: 0 12px;
    border-left-color: #e4e7ed;
  }
}

.search-section {
  margin-top: 16px;

  .search-form {
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;

      .search-item {
        margin-bottom: 0;

        ::v-deep .el-form-item__label {
          color: #333;
          font-weight: 500;
          white-space: nowrap;
        }

        .search-input {
          min-width: 200px;
        }
        
        .search-date {
          min-width: 280px;
        }
      }

      .search-buttons {
        display: flex;
        gap: 12px;
        margin-left: auto;

        .action-btn {
          min-width: 80px;
          height: 36px;
          border-radius: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

          &.primary {
            background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
            border: none;
            color: #ffffff;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
            }
          }

          &:not(.primary) {
            background: #ffffff;
            border: 1px solid #d9d9d9;
            color: #333;

            &:hover {
              border-color: #409eff;
              color: #409eff;
            }
          }
        }
      }
    }
  }
}

.table-container {
  margin-top: 20px;

  .enhanced-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .mobile-info {
      .revealed-mobile {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #67c23a;
        
        i {
          font-size: 14px;
        }
        
        .mobile-text {
          font-family: 'Courier New', monospace;
          font-weight: 600;
        }
      }
      
      .masked-mobile {
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: pointer;
        color: #e6a23c;
        transition: all 0.3s;
        
        &:hover {
          color: #f7ba2a;
          transform: scale(1.02);
        }
        
        i {
          font-size: 14px;
        }
        
        .masked-text {
          font-family: 'Courier New', monospace;
          font-weight: 500;
        }
        
        .reveal-btn {
          color: #409eff;
          padding: 2px;
          margin-left: 4px;
          
          &:hover {
            background: #ecf5ff;
          }
        }
      }
      
      .unknown-mobile {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #909399;
        
        i {
          font-size: 14px;
        }
      }
    }

    .short-link {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      i {
        color: #409eff;
        font-size: 14px;
      }

      .short-code {
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #2c3e50;
        font-weight: 500;
      }
    }

    .user-agent {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #6c757d;
        font-size: 14px;
        flex-shrink: 0;
      }

      .ua-text {
        color: #333;
        font-size: 13px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: help;
      }
    }

    .ip-address {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      i {
        color: #409eff;
        font-size: 14px;
      }

      .ip-text {
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #333;
        font-weight: 500;
      }
    }

    .click-time {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      i {
        color: #409eff;
        font-size: 14px;
      }

      span {
        font-size: 13px;
        color: #333;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    background: #ffffff;
    padding: 16px 0;

    .enhanced-pagination {
      ::v-deep .el-pagination__total {
        color: #409eff;
        font-weight: 500;
      }

      ::v-deep .el-pager li.active {
        background: #409eff;
        color: #ffffff;
      }

      ::v-deep .el-pagination__jump {
        color: #666;
      }
    }
  }
}

// 导出对话框样式
.export-dialog {
  ::v-deep .el-dialog__header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
  }

  ::v-deep .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  ::v-deep .el-dialog__body {
    padding: 24px;
  }
}

.export-content {
  .export-form {
    .export-radio-group {
      display: flex;
      flex-direction: column;
      gap: 16px;
      
      .export-radio {
        width: 100%;
        margin: 0;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        padding: 16px;
        transition: all 0.3s;
        
        &:hover {
          border-color: #409eff;
          background: #f8f9ff;
        }
        
        ::v-deep .el-radio__input.is-checked + .el-radio__label {
          color: #409eff;
        }
        
        ::v-deep .el-radio__input.is-checked {
          .el-radio__inner {
            background: #409eff;
            border-color: #409eff;
          }
        }
        
        .radio-content {
          margin-left: 24px;
          
          .radio-title {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
            
            i {
              font-size: 16px;
            }
          }
          
          .radio-desc {
            font-size: 13px;
            color: #64748b;
            line-height: 1.4;
          }
        }
      }
    }
  }
  
  .email-notice {
    margin-top: 16px;
    
    ::v-deep .el-alert {
      border-radius: 6px;
      
      .el-alert__title {
        font-weight: 500;
        font-size: 13px;
      }
      
      .el-alert__description {
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .search-section {
    .search-form {
      .search-row {
        flex-direction: column;
        align-items: stretch;

        .search-item {
          width: 100%;

          .search-input,
          .search-date {
            width: 100%;
          }
        }

        .search-buttons {
          margin-left: 0;
          justify-content: center;

          .action-btn {
            flex: 1;
            max-width: 120px;
          }
        }
      }
    }
  }
  
  .export-content {
    .export-form {
      .export-radio-group {
        .export-radio {
          padding: 12px;
          
          .radio-content {
            margin-left: 20px;
          }
        }
      }
    }
  }
}
</style>