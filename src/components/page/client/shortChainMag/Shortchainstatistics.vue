<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 标题信息区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <div class="page-info">
                  <!-- <i class="el-icon-lx-emoji"></i> -->
                  <span class="page-title">短链统计</span>
                </div>
              </div>
              
              <div class="stats-info">
                <span class="stats-text">短链使用统计与监控管理</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form 
                ref="formInline" 
                :model="formInline" 
                class="search-form">
                <div class="search-row">
                  <el-form-item label="短链名称" prop="name" class="search-item" label-width="80px">
                    <el-input 
                      v-model="formInline.name" 
                      placeholder="请输入短链名称" 
                      class="search-input"
                      clearable>
                    </el-input>
                  </el-form-item>
                  
                  <el-form-item label="短链" prop="shortCode" class="search-item" label-width="60px">
                    <el-input 
                      v-model="formInline.shortCode" 
                      placeholder="请输入短链" 
                      class="search-input"
                      clearable>
                    </el-input>
                  </el-form-item>
                  
                  <el-form-item label="创建时间" prop="time" class="search-item" label-width="80px">
                    <el-date-picker
                      v-model="formInline.time"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="至"
                      :clearable="false"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date">
                    </el-date-picker>
                  </el-form-item>

                  <div class="search-buttons">
                    <el-button 
                      type="primary" 
                      @click="ListSearch()" 
                      class="action-btn primary"
                      icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button 
                      @click="Reset('formInline')" 
                      class="action-btn"
                      icon="el-icon-refresh">
                      重置
                    </el-button>
                  </div>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="table-container">
          <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="tableDataObj.tableData"
            class="enhanced-table">
            
            <el-table-column label="短链名称" min-width="150" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="name-info">
                  <i class="el-icon-edit-outline"></i>
                  <span class="name-text">{{ scope.row.name || '-' }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="短链接" width="180" align="center">
              <template slot-scope="scope">
                <div class="short-link">
                  <i class="el-icon-share"></i>
                  <span class="short-code">{{ scope.row.shortCode }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="长链接" min-width="300" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="original-url">
                  <i class="el-icon-link"></i>
                  <span class="url-text">{{ scope.row.originalUrl }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="打开次数" width="120" align="center">
              <template slot-scope="scope">
                <div class="click-count">
                  <span
                    v-if="tableDataObj.tablecurrent.open == 0 && scope.row.countNum != 0"
                    class="clickable-count"
                    @click="checkShort(scope.row)">
                    <i class="el-icon-view"></i>
                    {{ scope.row.countNum }}
                  </span>
                  <span v-else class="normal-count">
                    <i class="el-icon-view"></i>
                    {{ scope.row.countNum }}
                  </span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="可用状态" width="100" align="center">
              <template slot-scope="scope">
                <div class="status-info">
                  <el-tag 
                    size="small" 
                    :type="scope.row.available == 1 ? 'danger' : 'success'">
                    <i :class="scope.row.available == 1 ? 'el-icon-close' : 'el-icon-check'"></i>
                    {{ scope.row.available == 1 ? '失效' : '生效' }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="有效时间" width="180" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.expireTime" class="expire-time">
                  <div v-if="scope.row.remainingDays > 0 && scope.row.remainingDays < 7" class="warning-expire">
                    <div class="time-text warning">
                      <i class="el-icon-time"></i>
                      {{ moment(scope.row.expireTime).format('YYYY-MM-DD HH:mm:ss') }}
                    </div>
                    <div class="expire-tip warning">
                      <i class="el-icon-warning"></i>
                      即将到期，剩余{{ scope.row.remainingDays }}天
                    </div>
                  </div>
                  <div v-else-if="scope.row.remainingDays <= 0" class="expired">
                    <div class="time-text danger">
                      <i class="el-icon-time"></i>
                      {{ moment(scope.row.expireTime).format('YYYY-MM-DD HH:mm:ss') }}
                    </div>
                    <div class="expire-tip danger">
                      <i class="el-icon-circle-close"></i>
                      已到期
                    </div>
                  </div>
                  <div v-else class="normal-expire">
                    <div class="time-text">
                      <i class="el-icon-time"></i>
                      {{ moment(scope.row.expireTime).format('YYYY-MM-DD HH:mm:ss') }}
                    </div>
                  </div>
                </div>
                <span v-else class="no-expire">永久有效</span>
              </template>
            </el-table-column>
            
            <el-table-column label="创建时间" width="180" align="center">
              <template slot-scope="scope">
                <div class="create-time">
                  <i class="el-icon-time"></i>
                  <span>{{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="备注" min-width="150" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="remark-info">
                  <span v-if="scope.row.reason" class="remark-text">{{ scope.row.reason }}</span>
                  <span v-else class="no-remark">暂无备注</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <div class="table-actions">
                  <el-button 
                    v-permission
                    type="text" 
                    size="small"
                    class="action-btn-small delete"
                    icon="el-icon-delete"
                    @click="deleteShort(scope.row)">
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination 
              @size-change="handleSizeChange" 
              @current-change="handleCurrentChange" 
              :current-page="formInlines.currentPage"  
              :page-size="formInlines.pageSize" 
              :page-sizes="[10, 20, 50, 100]"  
              layout="total, sizes, prev, pager, next, jumper" 
              :total="tableDataObj.tablecurrent.total"
              class="enhanced-pagination">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem";
export default {
  name: "Shortchainstatistics",
  components: {
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      name: "Shortchainstatistics",
      // 搜索数据
      formInline: {
        shortCode: "",
        name: "",
        time: [],
        beginTime: "",
        endTime: "",
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        shortCode: "",
        name: "",
        time: [],
        beginTime: "",
        endTime: "",
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
          open: 1,
        },
        tableData: [],
        tableLabel: [
          {
            prop: "shortCode",
            showName: "短链接",
            fixed: false,
          },
          {
            prop: "originalUrl",
            showName: "长链接",
            fixed: false,
          },
          {
            prop: "countNum",
            showName: "链接打开次数",
            fixed: false,
          },
          {
            prop: "available",
            showName: "长链接",
            fixed: false,
            formatData: function (val) {
              return val == "1" ? "可用" : "不可用";
            },
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "120", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
    };
  },
  created() {
    if (this.$route.query.shortCode) {
      this.formInline.shortCode = this.$route.query.shortCode;
      Object.assign(this.formInlines, this.formInline);
    }
    this.InquireList();
  },
  methods: {
    // 发送请求方法
    InquireList() {
      const currentDate = new Date();

      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.slms + "v3/shortLink/report",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.list;
          this.tableDataObj.tableData.forEach((item) => {
            if (item.expireTime) {
              item.expirationDate = new Date(item.expireTime);
              item.remainingTime = item.expirationDate - currentDate;
              item.remainingDays = Math.ceil(
                item.remainingTime / (1000 * 60 * 60 * 24)
              );
            }
          });
          console.log(
            this.tableDataObj.tableData,
            "this.tableDataObj.tableData"
          );

          this.tableDataObj.tablecurrent.total = res.data.total;
          this.tableDataObj.tablecurrent.open = res.data.open;
        }
      );
    },
    handelOptionButton(val) {
      if (val.methods == "exportNums") {
        //查看详情
        this.$router.push({ path: "/detailShort?code=" + val.row.shortCode });
      }
    },
    // 查询
    ListSearch() {
      this.formInlines.currentPage = 1;
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      this.formInline.beginTime = "";
      this.formInline.endTime = "";
      this.formInlines.currentPage = 1;
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.formInline.endTime = this.moment(val[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
      this.formInlines.currentPage = 1;
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
    //查看链接打开次数
    checkShort(row) {
      this.$router.push({
        path: "shortStatistics",
        query: { shortCode: row.originShortCode },
      });
    },
    //删除短链
    deleteShort(row) {
      this.$confirms.confirmation(
        "delete", 
        "确定删除该短链吗？", 
        this.API.slms + "v3/shortLink/delete/" + row.id, 
        {}, 
        (res) => {
          if (res.code == 200) {
            this.$message.success("删除成功");
            this.InquireList();
          }
        }
      );
    },
  },

  watch: {
    "$route.query.shortCode"(newValue, oldValue) {
      console.log(newValue, "newValue");
      if (newValue) {
        this.formInline.shortCode = newValue;
        Object.assign(this.formInlines, this.formInline);
        this.InquireList();
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@/styles/template-common.less";

.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  
  i {
    font-size: 18px;
    color: #409eff;
  }
  
  .page-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }
}

.search-section {
  margin-top: 16px;

  .search-form {
    .search-row {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;

      .search-item {
        margin-bottom: 0;

        ::v-deep .el-form-item__label {
          color: #333;
          font-weight: 500;
          white-space: nowrap;
        }

        .search-input {
          min-width: 180px;
        }
        
        .search-date {
          min-width: 280px;
        }
      }

      .search-buttons {
        display: flex;
        gap: 12px;
        margin-left: auto;

        .action-btn {
          min-width: 80px;
          height: 36px;
          border-radius: 6px;
          font-weight: 500;
          transition: all 0.3s ease;

          &.primary {
            background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
            border: none;
            color: #ffffff;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
            }
          }

          &:not(.primary) {
            background: #ffffff;
            border: 1px solid #d9d9d9;
            color: #333;

            &:hover {
              border-color: #409eff;
              color: #409eff;
            }
          }
        }
      }
    }
  }
}

.table-container {
  margin-top: 20px;

  .enhanced-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .name-info {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #409eff;
        font-size: 14px;
      }

      .name-text {
        color: #333;
        font-weight: 500;
      }
    }

    .short-link {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      i {
        color: #409eff;
        font-size: 14px;
      }

      .short-code {
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #2c3e50;
        font-weight: 500;
      }
    }

    .original-url {
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #6c757d;
        font-size: 14px;
        flex-shrink: 0;
      }

      .url-text {
        color: #333;
        font-size: 13px;
        word-break: break-all;
      }
    }

    .click-count {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      .clickable-count {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #409eff;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s;

        &:hover {
          color: #66b1ff;
          transform: scale(1.05);
        }

        i {
          font-size: 14px;
        }
      }

      .normal-count {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #333;
        font-weight: 500;

        i {
          color: #6c757d;
          font-size: 14px;
        }
      }
    }

    .status-info {
      display: flex;
      justify-content: center;

      .el-tag {
        display: flex;
        align-items: center;
        gap: 4px;

        i {
          font-size: 12px;
        }
      }
    }

    .expire-time {
      .warning-expire {
        .time-text.warning {
          color: #e6a23c;
          font-size: 13px;
          display: flex;
          align-items: center;
          gap: 4px;

          i {
            font-size: 12px;
          }
        }

        .expire-tip.warning {
          color: #e6a23c;
          font-size: 11px;
          margin-top: 2px;
          display: flex;
          align-items: center;
          gap: 4px;

          i {
            font-size: 10px;
          }
        }
      }

      .expired {
        .time-text.danger {
          color: #f56c6c;
          font-size: 13px;
          display: flex;
          align-items: center;
          gap: 4px;

          i {
            font-size: 12px;
          }
        }

        .expire-tip.danger {
          color: #f56c6c;
          font-size: 11px;
          margin-top: 2px;
          display: flex;
          align-items: center;
          gap: 4px;

          i {
            font-size: 10px;
          }
        }
      }

      .normal-expire {
        .time-text {
          color: #333;
          font-size: 13px;
          display: flex;
          align-items: center;
          gap: 4px;

          i {
            color: #409eff;
            font-size: 12px;
          }
        }
      }
    }

    .no-expire {
      color: #67c23a;
      font-size: 13px;
      font-weight: 500;
    }

    .create-time {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      i {
        color: #409eff;
        font-size: 14px;
      }

      span {
        font-size: 13px;
        color: #333;
      }
    }

    .remark-info {
      text-align: left;
      padding: 0 8px;

      .remark-text {
        color: #333;
      }

      .no-remark {
        color: #c0c4cc;
        font-style: italic;
        font-size: 13px;
      }
    }

    .table-actions {
      display: flex;
      justify-content: center;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    background: #ffffff;
    padding: 16px 0;

    .enhanced-pagination {
      ::v-deep .el-pagination__total {
        color: #409eff;
        font-weight: 500;
      }

      ::v-deep .el-pager li.active {
        background: #409eff;
        color: #ffffff;
      }

      ::v-deep .el-pagination__jump {
        color: #666;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .search-section {
    .search-form {
      .search-row {
        flex-direction: column;
        align-items: stretch;

        .search-item {
          width: 100%;

          .search-input,
          .search-date {
            width: 100%;
          }
        }

        .search-buttons {
          margin-left: 0;
          justify-content: center;

          .action-btn {
            flex: 1;
            max-width: 120px;
          }
        }
      }
    }
  }
}
</style>
