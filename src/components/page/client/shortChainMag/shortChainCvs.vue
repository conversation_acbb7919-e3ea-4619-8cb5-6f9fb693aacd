<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 标题信息区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <div class="page-info">
                  <i class="el-icon-lx-emoji"></i>
                  <span class="page-title">短链转换</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能说明区域 -->
        <div class="notice-section">
          <h3 class="notice-title">
            <i class="el-icon-info"></i>
            功能说明
          </h3>
          <div class="notice-list">
            <div class="notice-item">
              <span class="notice-label">1、功能介绍：</span>
              短链接，通俗来说就是将长的URL网址，通过程序计算等方式，转换为简短的网址字符串
            </div>
            <div class="notice-item">
              <span class="notice-label">2、使用优势：</span>
              使用短链接可节省字符数空间，输入更多的短信内容
            </div>
            <div class="notice-item">
              <span class="notice-label">3、转换服务：</span>
              链转换服务，可以帮助您快速生成短链，提升营销效果
            </div>
          </div>
        </div>

        <!-- 短链转换表单 -->
        <div class="conversion-container">
          <div class="conversion-form">
            <!-- 短链名称 -->
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">
                  <i class="el-icon-edit"></i>
                  短链名称
                </h4>
                <span class="section-desc">为您的短链设置一个便于识别的名称</span>
              </div>
              <div class="section-content">
                <el-input 
                  placeholder="请输入链接名称（可选）" 
                  v-model="name" 
                  class="conversion-input"
                  clearable>
                  <template slot="prepend">
                    <i class="el-icon-edit-outline"></i>
                  </template>
                </el-input>
              </div>
            </div>

            <!-- 长网址链接 -->
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">
                  <i class="el-icon-link"></i>
                  长网址链接
                </h4>
                <span class="section-desc">输入需要转换的完整网址链接</span>
              </div>
              <div class="section-content">
                <el-input
                  placeholder="请输入完整的长链接，如：https://www.example.com"
                  v-model="originalUrl"
                  class="conversion-input">
                  <template slot="prepend">
                    <i class="el-icon-link"></i>
                  </template>
                  <el-button
                    v-permission
                    style="z-index: 1000;"
                    slot="append"
                    type="primary"
                    icon="el-icon-refresh"
                    @click="transformation()"
                    class="action-btn primary">
                    转换
                  </el-button>
                </el-input>
                <div class="input-tips">
                  <div class="tip-item">
                    <i class="el-icon-warning"></i>
                    <span>我们可以帮您把长链接压缩，让您可以输入更多的内容</span>
                  </div>
                  <div class="tip-item">
                    <i class="el-icon-info"></i>
                    <span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 短网址链接 -->
            <div class="form-section">
              <div class="section-header">
                <h4 class="section-title">
                  <i class="el-icon-share"></i>
                  短网址链接
                </h4>
                <span class="section-desc">转换后生成的短链接，可直接复制使用</span>
              </div>
              <div class="section-content">
                <el-input
                  v-model="shortConUrl"
                  oncut="return false;"
                  class="conversion-input result-input"
                  :disabled="true"
                  placeholder="转换成功后将在此显示短链接">
                  <template slot="prepend">
                    <i class="el-icon-share"></i>
                  </template>
                  <el-button
                    slot="append"
                    type="success"
                    @click="handlePreview()"
                    icon="el-icon-view"
                    class="action-btn success">
                    预览
                  </el-button>
                  <el-button
                    slot="append"
                    type="primary"
                    @click="handleCopy(shortConUrl, $event)"
                    icon="el-icon-document-copy"
                    class="action-btn primary">
                    复制
                  </el-button>
                </el-input>
                <div class="result-status" v-if="shortConUrl">
                  <el-alert
                    title="短链接生成成功！"
                    type="success"
                    :closable="false"
                    show-icon>
                    <template slot="default">
                      短链默认有效期为30天，请及时使用
                    </template>
                  </el-alert>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import clip from "../../utils/clipboard";
export default {
  name: "shortChainCvs",
  data() {
    return {
      originalUrl: "", //长链接的值
      shortConUrl: "", //短链接的值
      name: "",
    };
  },
  methods: {
    //转换
    transformation() {
      if (this.originalUrl != "") {
        this.$api.post(
          this.API.slms + "v3/shortLink/add",
          { originalUrl: this.originalUrl, name: this.name },
          (res) => {
            if (res.code == 200) {
              this.shortConUrl = res.data.shortLinkUrl;
              this.$api.get(this.API.slms + "v3/shortLink/count", {}, (res) => {
                if (res.code == 200) {
                  if (res.data <= 1) {
                    this.$message({
                      message: "短链接转换成功,短链默认有效期为30天！",
                      type: "success",
                    });
                  } else {
                    this.$message({
                      message: "短链接转换成功！",
                      type: "success",
                    });
                  }
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              });
            } else if (res.code === 4100001) {
                                          this.$message({
                                type: "error",
                                duration: "3000",
                                message:
                                    "该链接不在白名单内，请到'短链白名单'进行申请、或联系客服处理",
                            });
            } else {
              this.originalUrl = "";
              this.$message({
                message: res.msg,
                type: "warning",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "长链接不可为空",
          type: "warning",
        });
      }
    },
    //预览
    handlePreview() {
      if (this.shortConUrl != "") {
        window.open("https://" + this.shortConUrl, "_blank");
      } else {
        this.$message({
          message: "短连接为空，无法预览",
          type: "warning",
        });
      }
    },
    handleCopy(text, event) {
      clip(text, event);
      console.log("clicp");
    },
  },
  watch: {},
};
</script>

<style lang="less" scoped>
@import "~@/styles/template-common.less";

.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  
  i {
    font-size: 18px;
    color: #409eff;
  }
  
  .page-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }
}

.notice-section {
  margin-bottom: 24px;
  
  .notice-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    
    i {
      color: #409eff;
      font-size: 18px;
    }
  }
  
  .notice-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .notice-item {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      border: 1px solid #bae6fd;
      border-radius: 8px;
      padding: 16px;
      line-height: 1.6;
      font-size: 14px;
      color: #334155;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }
      
      .notice-label {
        font-weight: 600;
        color: #1e40af;
      }
    }
  }
}

.conversion-container {
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .conversion-form {
    padding: 24px;
    
    .form-section {
      margin-bottom: 32px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .section-header {
        margin-bottom: 16px;
        
        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
          
          i {
            color: #409eff;
            font-size: 16px;
          }
        }
        
        .section-desc {
          font-size: 13px;
          color: #64748b;
          line-height: 1.5;
        }
      }
      
      .section-content {
        .conversion-input {
          width: 100%;
          max-width: 600px;
          
          ::v-deep .el-input__inner {
            height: 44px;
            line-height: 44px;
            font-size: 14px;
            border-radius: 6px;
            transition: all 0.3s ease;
            
            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
          }
          
          ::v-deep .el-input-group__prepend {
            background: #f8f9fa;
            border-color: #e9ecef;
            color: #6c757d;
            
            i {
              font-size: 14px;
            }
          }
          
          ::v-deep .el-input-group__append {
            padding: 0;
            border: none;
            
            .el-button {
              border-radius: 0;
              border-left: 1px solid #dcdfe6;
              margin: 0;
              
              &:first-child {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
              }
              
              &:last-child {
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
              }
              
              &.success {
                background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
                border-color: #67c23a;
                color: white;
                
                &:hover {
                  background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
                  border-color: #85ce61;
                }
              }
            }
          }
          
          &.result-input {
            ::v-deep .el-input__inner {
              background: #f8f9fa;
              color: #2c3e50;
              font-weight: 500;
              font-family: 'Courier New', monospace;
            }
          }
        }
        
        .input-tips {
          margin-top: 12px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          
          .tip-item {
            display: flex;
            align-items: flex-start;
            gap: 6px;
            font-size: 12px;
            color: #64748b;
            line-height: 1.5;
            
            i {
              margin-top: 2px;
              font-size: 12px;
              color: #f59e0b;
              flex-shrink: 0;
            }
          }
        }
        
        .result-status {
          margin-top: 12px;
          
          ::v-deep .el-alert {
            border-radius: 6px;
            
            .el-alert__title {
              font-weight: 500;
            }
            
            .el-alert__description {
              font-size: 13px;
              color: #059669;
            }
          }
        }
      }
    }
  }
}

.action-btn {
  min-width: 80px;
  height: 44px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  
  &.primary {
    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
    border: none;
    color: #ffffff;
    
    &:hover {
      background: linear-gradient(135deg, #66b1ff 0%, #5dade2 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
    }
  }
  
  &.success {
    background: linear-gradient(135deg, #67c23a 0%, #5daf34 100%);
    border: none;
    color: #ffffff;
    
    &:hover {
      background: linear-gradient(135deg, #85ce61 0%, #73c73e 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .conversion-container {
    .conversion-form {
      padding: 16px;
      
      .form-section {
        margin-bottom: 24px;
        
        .section-content {
          .conversion-input {
            max-width: 100%;
            
            ::v-deep .el-input-group__append {
              .el-button {
                padding: 0 12px;
                font-size: 13px;
                
                span {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .notice-section {
    .notice-list {
      .notice-item {
        padding: 12px;
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 480px) {
  .conversion-container {
    .conversion-form {
      .form-section {
        .section-content {
          .conversion-input {
            ::v-deep .el-input-group__append {
              .el-button {
                width: 44px;
                padding: 0;
                
                i {
                  margin: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>