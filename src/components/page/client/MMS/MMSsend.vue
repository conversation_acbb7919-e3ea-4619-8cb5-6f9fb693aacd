<template>
    <div id="MMSCJ" class="MM_s">
        <!-- <div class="crumbs" style="margin:10px">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 创建彩信</el-breadcrumb-item>
            </el-breadcrumb>
        </div> -->
        <div class="fillet shortChain-box" style="padding-bottom: 1px;">
            <p style="font-size: 20px;font-weight: 600;padding: 20px;border-bottom: 1px solid #eaeaea;">使用流程</p>
            <div style="margin: 20px 50px;border: 1px solid #ccc;padding: 20px 50px;border-radius: 20px;">
                <el-steps :active="active">
                    <el-step class="icon iconfont" title="创建彩信" icon="el-icon-edit"></el-step>
                    <el-step class="icon iconfont" title="录入手机号" icon="icon-shoujilianxiren"></el-step>
                    <el-step class="icon iconfont" title="发送提交" icon="icon-fasong"></el-step>
                </el-steps>
            </div>
        </div> 
        <div class="Templat-matter">
            <p style="font-weight:bolder">温馨提醒：</p>
            <p>模板报备：支持变量模板，变量请使用英文{}进行包裹，例如：尊敬的{user}。网页端发送时变量模板必须使用excel表格提交，表头必须与大括号{}内的变量名保持一致。</p>
            <p>格式要求：支持.xlsx .xls等格式；手机号码需在A列，变量参数则为 英文字母 代替</p>
            <p>变量支持：最多只支持9个变量。</p>
            <p>模板签名：第一帧的文本开头以【】包住。</p>
        </div>
        <!-- 创建模板 -->
        <create @childrenNextStep="childrenNextStep()" v-show="active==0" :key="Reorganization"></create>
        <!-- 号码录入 -->
        <EnterTheNumber @childrenPrevious="childrenPrevious" :header='header' v-show="active==1"></EnterTheNumber>
        <!-- 短信审核 -->
        <div v-show="active==2" style="background: rgb(255, 255, 255);margin: 20px 0px;padding-bottom: 50px;">
            <div class="fillet shortChain-box" style="padding-bottom: 1px;">
                <p style="font-size: 20px;font-weight: 600;padding: 20px;border-bottom: 1px solid #eaeaea;">提交发送</p>
            </div>
            <div>
                <div style="text-align: center;">
                    <p style="font-size: 18px;font-weight: 600;margin: 30px;">提交成功</p>
                    <p>彩信提交成功，需等待平台审核，请耐心等候</p>
                </div>
                <div style="text-align: center;margin: 70px 0;">
                    <span style="font-size: 15px;font-weight: 600;">彩信标题：</span><span style="color: #a4a4a4;">{{title}}</span>
                    <span style="font-size: 15px;font-weight: 600;margin-left:20%;">彩信ID：</span><span style="color: #a4a4a4;">{{MMSID}}</span>
                    <span style="font-size: 15px;font-weight: 600;margin-left:20%;">创建时间：</span><span style="color: #a4a4a4;">{{time}}</span>
                </div>
                <div style="margin: 20px 50px;padding: 20px 50px;">
                    <el-steps :active="1">
                        <el-step title="创建彩信" ></el-step>
                        <el-step title="平台审核" ></el-step>
                        <el-step title="发送完成" ></el-step>
                    </el-steps>
                </div>
            </div>
        </div>
        <!-- 上一步，发送 -->
        <div v-show="active==2" class="fillet shortChain-box" style="margin-top: 20px;">
            <div style="margin: 0px 50px;padding: 20px 0px;text-align: center;">
                <el-button style="margin-right:10px;" @click="WebTask">查看待发</el-button>
                <el-button type="primary" style="margin-right:10px;" @click="addMMS">新建彩信</el-button>
            </div>
        </div>
        <el-dialog
            title="实名认证"
            :visible.sync="dialogSmrzFlag"
            width="30%"
            center>
            <span>尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
            </span>
        </el-dialog>
    </div>    
</template>

<script>
import create from './components/create.vue'
import EnterTheNumber from './components/EnterTheNumber.vue'
import { mapState, mapMutations,mapActions } from "vuex";
export default {
    name: "MMSsend",
    components:{
        create,
        EnterTheNumber
    },
    data(){
        return{
            header:{},
            active:0,
            time:'',
            title:'',
            MMSID:'',
            Reorganization:'',
            dialogSmrzFlag :false,
        }
    },
    created(){
        this.header = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
         this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
      (res) => {
        // console.log(res.data);
        if(res.data.certificate == 0){
            this.dialogSmrzFlag = true
        }
        // this.certificate = res.data.certificate;

      }
    );
    },
    // activated(){
    //     this.$api.get(
    //     this.API.cpus + "consumerclientinfo/getClientInfo",
    //         null,
    //     (res) => {
    //         // console.log(res.data);
    //         if(res.data.certificate == 0){
    //             this.dialogSmrzFlag = true
    //         }
    //         // this.certificate = res.data.certificate;

    //     }
    //     );
    // },
    methods:{
        childrenNextStep(){
            this.active=1
        },
        goSmrz(){
            this.dialogSmrzFlag = false
            this.$router.push("/authentication")
        },
        childrenPrevious(val){
            if(val){
                this.time=val.createTime
                this.MMSID=val.mmsid
                this.title=val.title
                this.active=2
            }else{
                this.active=0
            }
        },
        addMMS(){
            this.Reorganization = new Date().getTime()
            this.active=0
        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveUrls'
        ]),
        WebTask(){
            // 存储点击的菜单
            let logUrls={
                logUrls:'MMSWebTask'
            } 
            this.saveUrls(logUrls);
            window.sessionStorage.setItem('logUrls','MMSWebTask')
            this.$router.push({ path: '/MMSWebTask'})
        }
    },
}
</script>

<style scoped>
    .MM_s{
        background: #fff;
    }
    .Templat-matter{
    border:1px solid #66CCFF;
    background: #E5F0FF;
    padding:10px 14px;
    border-radius: 5px;
    font-size: 12px;
    margin: 0 50px;
}
.Templat-matter > p{
  padding:5px 0;
}
</style>
<style>
#MMSCJ .el-step__icon-inner{
    font-size: 25px !important;
    font-weight: 400 !important;
    font-style:normal
}
</style>

