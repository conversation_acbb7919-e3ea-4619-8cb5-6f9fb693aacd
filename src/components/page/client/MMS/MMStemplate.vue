<template>
    <div class="login_cell_phone" style="background: #fff">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 草稿箱管理</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet Statistics-box">
            <div class="OuterFrame fillet" style="height: 100%;">
                <div>
                    <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
                        <el-form-item label="标题" label-width="82px"  prop="title">
                            <el-input v-model="formInline.title" placeholder="" class="input-w"></el-input>
                        </el-form-item>
                        <el-form-item label="创建时间" label-width="80px" prop="time">
                            <el-date-picker class="input-time"
                            v-model="formInline.time"
                            value-format="yyyy-MM-dd"
                            type="daterange"
                            range-separator="-"
                            :clearable='false'
                            @change="getTimeOperating"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="boderbottom">
                    <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
                    <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
                    <el-button type="primary" plain style=""  @click="$router.push({ path: '/templateCreate'})">创建草稿</el-button>
                </div>
                <div class="sensitive-fun" style="margin:10px 0">
                    <!-- <span class="sensitive-list-header">草稿管理列表</span> -->
                </div>
                <div class="Mail-table"  style="padding-bottom:40px;">
                    <el-table
                        v-loading="tableDataObj.loading2 "
                        element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading"
                        element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable"
                        border
                        :data="tableDataObj.tableData"
                        style="width: 100%"
                        >
                        <el-table-column label="id">
                            <template slot-scope="scope">{{ scope.row.id}}</template>
                        </el-table-column>
                        <el-table-column label="标题">
                            <template slot-scope="scope">{{ scope.row.title}}</template>
                        </el-table-column>
                        <el-table-column label="预览" >
                            <template slot-scope="scope" >
                                <span style="cursor: pointer;color: #16A589;" @click="View(scope.row)"><i class="el-icon-picture"></i>预览</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建时间" width='170'>
                            <template slot-scope="scope">
                                <span >{{ scope.row.createTime }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width='150'>
                            <template slot-scope="scope">
                                <el-button type="text" style="color: #16A589;" @click="edit(scope.row)">编 辑</el-button>
                                <el-button type="text" style="color: #16A589;" @click="deleteTemplate(scope.row)">删 除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="formInlines.currentPage" :page-size="formInlines.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
                        </el-pagination>
                    </el-col>
                </div>  
            </div>
        </div>
        <!-- 预览手机弹框   -->
        <el-dialog
            title="预览"
            :visible.sync="dialogVisible"
            width="40%"
            >
            <div>
                <div class="send-mobel-box">
                    <img src="../../../../assets/images/phone.png" alt="">
                    <div class="mms-content-exhibition">
                    <el-scrollbar  class="sms-content-exhibition" >
                        <div style="width: 253px;">
                            <span style="display: inline-block;padding: 5px;border-radius: 5px;background: #e2e2e2;margin-top: 5px;">{{title}}</span>
                        </div>
                        <div style="overflow-wrap: break-word;width: 234px;background: #e2e2e2;padding: 10px;border-radius: 5px;margin-top: 5px;" v-for="(item,index) in viewData" :key=index>
                            <img v-if="item.media=='png'||item.media=='jpg'||item.media=='gif'" :src="API.imgU+item.mediaGroup+'/'+item.mediaPath" style="width: 235px;" class="avatar video-avatar"  ref="avatar">
                            <video v-if="item.type=='video'"
                                style="width: 235px;"
                                v-bind:src="API.imgU+item.mediaGroup+'/'+item.mediaPath"
                                class="avatar video-avatar"
                                controls="controls">
                            </video>
                            <audio
                                v-if="item.type=='audio'"
                                style="width: 235px;" 
                                autoplay="autoplay"
                                controls="controls"
                                preload="auto"
                                v-bind:src="API.imgU+item.mediaGroup+'/'+item.mediaPath">
                            </audio>
                            <div style="white-space: pre-line;">
                                {{item.txt}}
                            </div>
                        </div>
                    </el-scrollbar>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import TableTem from '@/components/publicComponents/TableTem'
export default {
    name:'MMStemplate',
    components:{
        TableTem
    },
    data() {
        return {
            dialogVisible:false,
            viewData:[],// 查看内容
            title:'',
            // 搜索数据
            formInline: {
                title:'',
                beginTime:'',
                endTime:'',
                time: [],
                pageSize:10,
                currentPage:1,
            },
            // 存储搜索数据
            formInlines:{
                title:'',
                beginTime:'',
                endTime:'',
                time: [],
                pageSize:10,
                currentPage:1,
            },
            //用户列表数据
            tableDataObj: {
                loading2:false,
                tablecurrent:{ //分页参数
                    total:0,
                },
                tableData: [],
            }
        };
    },
    methods: {
        // 发送请求方法
        InquireList(){
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumermmstemplate/page',this.formInlines,res=>{
                this.tableDataObj.loading2=false;
                this.tableDataObj.tableData=res.data.records
                this.tableDataObj.tablecurrent.total=res.data.total
            })
        },
        // 预览
        View(val){
            this.viewData=val.contents
            this.title=val.title
            this.dialogVisible=true
        },
        // 查询
        ListSearch(){
            Object.assign(this.formInlines,this.formInline);
            this.InquireList()
        },
        // 重置
        Reset(formName){
            this.$refs[formName].resetFields();
            this.formInline.time = [],
            this.formInline.beginTime = '';
            this.formInline.endTime = '';
            this.formInline.time1 = [],
            this.formInline.startTime=""
            this.formInline.stopTime=""
            Object.assign(this.formInlines,this.formInline)
            this.InquireList()
        },
        // 分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
            this.InquireList()
        },
        handleCurrentChange: function(currentPage){
            this.formInlines.currentPage = currentPage;
            this.InquireList()
        },
        // 创建时间
        getTimeOperating(val){
            if(val){
                this.formInline.beginTime=val[0]+" 00:00:00"
                this.formInline.endTime=val[1]+" 23:59:59"
            }else{
                this.formInline.beginTime=""
                this.formInline.endTime=""
            }
        },
        // 编辑草稿
        edit(val){
            this.$router.push({ path: '/templateCreate',query:{id:val.id}})
        },
        // 删除草稿
        deleteTemplate(val){
            this.$confirms.confirmation('delete','确定删除当前草稿？',this.API.cpus+'consumermmstemplate/'+val.id,{},res =>{
                this.InquireList()
            })
        },
    },
    created(){
        this.InquireList()
    },
    // activated(){
    //     this.InquireList()
    // },
    watch:{
        // 监听搜索/分页数据
        // formInlines:{
        //     handler() {
        //         this.InquireList()
        //     },
        //     deep: true,
        //     immediate: true,
        // },
    },
}
</script>
<style scoped>
.Statistics-box{
    padding:20px;
}
.addC .el-select {
    width: 100%;
}
.addC .el-cascader{
    width: 100%;
}
.send-mobel-box{
    width: 300px;
    overflow: hidden;
    position: relative;
    margin-bottom: 35px;
    left: 28%;
}
.send-mobel-box img{
    width:300px;
}
.mms-content-exhibition{
    position: absolute;
    top: 0;
    width: 300px;
    height: 375px;
    margin: 135px 25px 0px 25px;
    overflow: auto;
    overflow-y: auto;
}
</style>
