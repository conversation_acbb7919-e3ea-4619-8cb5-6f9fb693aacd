<template>
    <div class="LoopDiv">
        <div>
            <!-- <p class="type_title">
                <span>视频介绍</span>
            </p> -->
            <div class="pic_img albumvideo" style="border: 1px solid #16A589;border-radius: 5px;">
                <div class="picDiv pic_top" @mouseover="mouseOver" @mouseleave="mouseLeave">
                    <img src="@/assets/images/1395fa6f-07d1-45af-85f9-7adfaed8be48.png" v-if="children.showVideoPath||children.showaudioPath||children.imageUrl" @click="FileRemoval" class="imgX" :style="styleShow?'display: block;':'display: none;'" alt="">
                    <!-- 内容展示 -->
                    <div class="videoF" style="padding: 10px 10px 0px;position: absolute;left: 50%;transform: translate(-50%, 0);">
                        <img v-if="children.imageUrl" :src="children.imageUrl" class="avatar video-avatar"  ref="avatar">
                    </div>
                    <div class="videoF" style="padding: 10px 10px 0 10px;position: absolute;">
                        <video v-if="children.showVideoPath && !videoFlag"
                            v-bind:src="children.showVideoPath"
                            class="avatar video-avatar"
                            style="width: 100%;"
                            controls="controls">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                    <div class="videoF" style="padding: 10px 10px 0px;position: absolute;top: 25%;left: 1%;">
                        <audio 
                            v-if="children.showaudioPath"
                            autoplay="autoplay"
                            controls="controls"
                            preload="auto"
                            v-bind:src="children.showaudioPath">
                        </audio>
                    </div>
                    <el-progress v-if="videoFlag == true"
                        type="circle"
                        v-bind:percentage="videoUploadPercent"
                    style="margin-top: 7px;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                    </el-progress>
                    <!-- 内容展示 -->
                    <!-- 图片上传 -->
                    <div class="pic_img_box">
                        <el-upload 
                            v-if="!children.imageUrl&&children.showVideoPath =='' && !videoFlag&&children.showaudioPath==''" 
                            class="avatar-uploader"
                            :auto-upload="true"
                            :action="this.API.cpus+'v3/file/upload'"
                            :headers="header"
                            :show-file-list="false"
                            :on-success="handleAvatarSuccess"
                            :before-upload="beforeAvatarUpload">
                                <div v-if="!children.imageUrl" class="icon iconfont">
                                    <i v-if="!children.imageUrl" class="iconStyle el-icon-picture"></i>
                                </div>
                                <div v-if="!children.imageUrl">
                                    <span>添加图片</span>
                                </div>
                        </el-upload>
                    </div>
                    <!-- 图片上传 -->
                    <!-- 视频上传 -->
                    <div class="pic_img_box">
                        <el-upload 
                            v-if="!children.imageUrl&&children.showVideoPath =='' && !videoFlag&&children.showaudioPath==''"
                            class="avatar-uploader" 
                            :action="this.API.cpus+'v3/file/upload'"
                            :headers ='header'
                            v-bind:data="{FoldPath:'上传目录',SecretKey:'安全验证'}"
                            v-bind:on-progress="uploadVideoProcess"
                            v-bind:on-success="handleVideoSuccess"
                            v-bind:before-upload="beforeUploadVideo"
                            v-bind:show-file-list="false">
                            <div class="icon iconfont">
                                <i v-if="children.showVideoPath =='' && !videoFlag"
                                class="iconStyle icon-icon-test"></i>
                            </div>
                            <div v-if="children.showVideoPath =='' && !videoFlag">
                                <span class="iconFont">添加视频</span>
                            </div>
                        </el-upload>
                    </div>
                    <!-- 视频上传 -->
                    <!-- 音频上传 -->
                    <div class="pic_img_box">
                        <el-upload 
                            v-if="!children.imageUrl&&children.showVideoPath =='' && !videoFlag&&children.showaudioPath==''"
                            class="avatar-uploader" 
                            :action="this.API.cpus+'v3/file/upload'"
                            :headers ='header'
                            v-bind:data="{FoldPath:'上传目录',SecretKey:'安全验证'}"
                            v-bind:on-success="handleAudioSuccess"
                            v-bind:before-upload="beforeUploadAudio"
                            v-bind:show-file-list="false">
                            <div class="icon iconfont">
                                <i v-if="children.showaudioPath==''"
                                class="iconStyle icon-icon-test"></i>
                            </div>
                            <div v-if="children.showaudioPath==''">
                                <span class="iconFont">添加音频</span>
                            </div>
                        </el-upload>
                    </div>
                    <!-- 音频上传 -->
                    <div style="text-align: center;" v-if="!children.imageUrl&&children.showVideoPath =='' && !videoFlag&&children.showaudioPath==''">
                        <p style="margin: 20px;">点击上传文件</p>
                        <p style="color: #ccc;">支持文件格式：.jpg .gif .midi .png</p>
                    </div>
                </div>
                <div class="picDiv pic_bottom">
                    <el-input type="textarea" v-model="children.textarea" placeholder="请输入内容" class="textarea"></el-input>
                </div>
            </div>
            <div>
                <div class="icon iconfont" style="text-align: center;margin-top: 10px;">
                    <span class="timeDelet" title="时间" @click="timeShow">
                        <i class="iconClass icon-shijian"></i>
                    </span>
                    <span class="timeDelet" title="删除此帧" @click="deleteLoop">
                        <i class="iconClass icon-shanchu"></i>
                    </span>
                </div>
                <div style="padding: 0 10px;" v-if="timeFlag">
                    <el-slider
                        v-model="children.time"
                        :max="15"
                        :step="1">
                    </el-slider>
                </div>
            </div>
        </div>
        <!-- <p class="Upload_pictures">
            <span></span>
            <span>最多可以上传1个视频，建议大小50M，推荐格式mp4</span>
        </p> -->
    </div>
</template>
<script>
import { mapState, mapMutations,mapActions } from "vuex";
export default {
    name: "Loopupload",
    props: {children:Object},
    data () {
        return {
            header:{},//token
            videoFlag: false,//是否显示进度条
            videoUploadPercent: "",//进度条的进度，
            isShowUploadVideo: false,//显示上传按钮
            // showVideoPath: '',//视频url
            // showaudioPath: '',//音频url
            // imageUrl:'',//图片url
            // textarea:'',//文本框
            // time:0,//选择时间
            timeFlag:false,
            // videoSrc:''
            styleShow:false
        }
    },
    components:{

    },
    methods:{
        handleClick(tab, event) {
            console.log(tab, event);
        },
        // 视频上传功能》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
        //上传前回调
        beforeUploadVideo(file) {
            var fileSize = file.size / 1024 < 96;
            // if (['video/mp4'].indexOf(file.type) == -1) {
            if (['audio/mid'].indexOf(file.type) == -1||['audio/midi'].indexOf(file.type) == -1) {
                this.$message({
                    type: 'warning',
                    message:"请上传正确的视频格式"
                });
                return false;
            }
            if (!fileSize) {
                this.$message({
                    type: 'warning',
                    message:"视频大小不能超过96KB"
                });
                return false;
            }
            this.isShowUploadVideo = false;
        },
        //进度条
        uploadVideoProcess(event, file, fileList) {
            this.videoFlag = true;
            this.videoUploadPercent = file.percentage.toFixed(0) * 1;
        },
        //上传成功回调
        handleVideoSuccess(res, file) {
            this.isShowUploadVideo = true;
            this.videoFlag = false;
            this.videoUploadPercent = 0;
            // 前台上传地址
            if (file.status == 'success' ) {
                this.children.showVideoPath =this.API.imgU+file.response.data.fullpath;
                this.children.size=file.size / 1024 
                this.children.media=file.name.split('.')[file.name.split(".").length-1]
                this.children.mediaGroup=res.data.group
                this.children.mediaPath=res.data.path
                this.children.type='video'
            } else {
                this.$message({
                    type: 'warning',
                    message:'上传失败，请重新上传'
                });
            }
            //后台上传地址
            if (res.code == 200) {
                this.children.showVideoPath =this.API.imgU+ res.data.fullpath;
                this.children.size=file.size / 1024 
                this.children.media=file.name.split('.')[file.name.split(".").length-1]
                this.children.mediaGroup=res.data.group
                this.children.mediaPath=res.data.path
            } else {
                this.$message({
                    type: 'warning',
                    message:res.Message
                });
            }
        },

        // 转换为base64    blob:http://kh.mixcloud.com:8080/bbf1c269-5d3b-4f76-9215-0d0c65405f7a

        // uploadVideo(e) {
        // console.log(e)
        //   var that = this;
        //     var video = event.target.files[0];  //选择的文件
        //     var reader = new FileReader();  
        //     var rs = reader.readAsDataURL(video);  
        //     reader.onload = (e) =>{
        //       var videoSrc= e.target.result; 
        //       console.log(videoSrc)
        //       //提交到后台部分略
        //     } 
        // } ,

        //上传视频转为blob格式
        // uploadVideo(e) {
        //     this.handleVideoSuccess()
        //     console.log(e)
        //     debugger
        //     var that = this;
        //             var video = event.target.files[0];  //选择的文件
        //             var reader = new FileReader();  
        //             var rs =  reader.readAsArrayBuffer(video) 
        //             reader.onload = (e) => {
        //             let data
        //             if (typeof e.target.result === 'object') {
        //                 data = window.URL.createObjectURL(new Blob([e.target.result]))
        //             } else {
        //                 data = e.target.result
        //             }
        //             this.children.showVideoPath = data;
        //             // this.videoSrc= data; 
        //             console.log("视频地址" +data)
        //             //提交到后台部分略
        //         } 
        //     },


        //     base64ToBlob ({b64data = '', contentType = '', sliceSize = 512} = {}) {
        //         return new Promise((resolve, reject) => {
        //             // 使用 atob() 方法将数据解码
        //             let byteCharacters = atob(b64data);
        //             let byteArrays = [];
        //             for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        //             let slice = byteCharacters.slice(offset, offset + sliceSize);
        //             let byteNumbers = [];
        //             for (let i = 0; i < slice.length; i++) {
        //                 byteNumbers.push(slice.charCodeAt(i));
        //             }
        //             // 8 位无符号整数值的类型化数组。内容将初始化为 0。
        //             // 如果无法分配请求数目的字节，则将引发异常。
        //             byteArrays.push(new Uint8Array(byteNumbers));
        //             }
        //             let result = new Blob(byteArrays, {
        //             type: contentType
        //             })
        //             result = Object.assign(result,{
        //             // jartto: 这里一定要处理一下 URL.createObjectURL
        //             preview: URL.createObjectURL(result),
        //             name: `图片示例.mp4`
        //             });
        //             resolve(result)
        //         })
        //     },
        // 时间展示
        timeShow(){
            this.timeFlag=!this.timeFlag
        },
        // 视频上传功能《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《


        // 图片上传功能 》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
        // 上传成功
        handleAvatarSuccess(res, file) {
            // console.log(111);
            if(res.code == 200){
                this.children.imageUrl = URL.createObjectURL(file.raw);
            this.children.size=file.size / 1024 
            this.children.media=file.name.split('.')[file.name.split(".").length-1]
            this.children.mediaGroup=res.data.group
            this.children.mediaPath=res.data.path
            this.children.type='img'
            }else{
                this.$message({
                    type: 'warning',
                    message:'上传失败，请重新上传'
                });
            }
            
        },
        beforeAvatarUpload(file) {
            // log
            const siJPGGIF =file.name.split('.')[file.name.split(".").length-1]
            const isLtSize = file.size / 1024  < 96;
            if (siJPGGIF!='jpg'&&siJPGGIF!='gif'&&siJPGGIF!='png') {
                this.$message.warning('上传头像图片只能是 jpg、gif、png格式!');
                return false;
            }
            if (!isLtSize) {
                this.$message.warning('上传头像图片大小不能超过 96KB!');
                return false;
            }
            
            // return isJPG && isLt2M;
        },
        // 图片上传功能 《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《
        // 音频上传功能》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
        //上传前回调
        beforeUploadAudio(file) {
            var fileSize = file.size / 1024 < 96;
            // if (['audio/mpeg'].indexOf(file.type) == -1) {
            if (['audio/mid'].indexOf(file.type) == -1||['audio/midi'].indexOf(file.type) == -1) {
                this.$message({
                    type: 'warning',
                    message:"请上传正确的音频格式"
                });
                return false;
            }
            if (!fileSize) {
                this.$message({
                    type: 'warning',
                    message:"音频大小不能超过96KB"
                });
                return false;
            }
            this.isShowUploadVideo = false;
        },
        //上传成功回调
        handleAudioSuccess(res, file) {
            this.isShowUploadVideo = true;
            this.videoFlag = false;
            this.audioUploadPercent = 0;
            // 前台上传地址
            if (file.status == 'success' ) {
               this.children.showaudioPath =this.API.imgU+file.response.data.fullpath;
                this.children.size=file.size / 1024 
                this.children.media=file.name.split('.')[file.name.split(".").length-1]
                this.children.mediaGroup=res.data.group
                this.children.mediaPath=res.data.path
                this.children.type='audio'
            } else {
                this.$message({
                    type: 'warning',
                    message:'上传失败，请重新上传'
                });
            }
            //后台上传地址
            if (res.code == 200) {
                this.children.showaudioPath =this.API.imgU+ res.data.fullpath;
                this.children.size=file.size / 1024 
                this.children.media=file.name.split('.')[file.name.split(".").length-1]
                this.children.mediaGroup=res.data.group
                this.children.mediaPath=res.data.path
            } else {
                this.$message({
                    type: 'warning',
                    message:res.Message
                });
            }
        },
        // 音频上传功能 《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《

        // 组件删除
        deleteLoop(){
           this.$emit('childrenChange', '')
        },
        // 鼠标事件
        mouseOver(){
            this.styleShow=true
        },
        mouseLeave(){
            this.styleShow=false
        },
        // 文件移除
        FileRemoval(){
            this.$confirm('确认移除当前文件？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.children.showVideoPath=''//视频url
                this.children.showaudioPath=''//音频url
                this.children.imageUrl=''//图片url
                this.children.mediaGroup=''
                this.children.mediaPath=''
                this.children.media=''
                this.children.size=0
            })
        }
    },
    mounted(){
        this.header = {Authorization :"Bearer"  + this.$common.getCookie('ZTGlS_TOKEN')};
        // let base64 = '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'
        // this.base64ToBlob({b64data: base64, contentType: 'video/mp4'}).then(res => {
        //     // 转后后的blob对象
        //     console.log('blob', res)
        // })
            // this.isShowUploadVideo = true;
            // this.videoFlag = false;
            // this.videoUploadPercent = 0;
            // this.children.showVideoPath = 'blob:http://kh.mixcloud.com:8080/bbf1c269-5d3b-4f76-9215-0d0c65405f7a';

    },
}
</script>
<style scoped>
@media screen and (min-width: 1200px){
    .video-avatar{
    height: 200px;
    max-width: 300px;
}
.albumvideo{
    padding: 10px;
    height: 530px;
}
.pic_img_box{
display: inline-block;
margin: 20px;
}
.pic_top{
    position: relative;
    border: 1px dashed #ccc;
    height: 230px;
    width: 100%;
    margin-bottom: 20px;
    text-align: center;
}
.iconStyle{
    font-style: normal;
    font-size: 35px;
    color: darksalmon;
}
.iconClass{
    font-style: normal;
    color: #fff;
    font-size: 25px;
}
.timeDelet{
    margin: 0 20px;
    display: inline-block;
    border: 1px solid #16A589;
    padding: 5px;
    border-radius: 5px;
    background: #16A589;
    cursor: pointer;
}
.LoopDiv{
    width: 100%;;
}
.imgX{
    width: 20px;position: absolute;top: 1px;right: 1px;z-index:100;cursor: pointer;
}
}
@media screen and (max-width: 1200px){
    .video-avatar{
    height: 200px;
    max-width: 300px;
}
.albumvideo{
    padding: 10px;
    width: 200px;
    height: 530px;
}
.pic_img_box{
display: inline-block;
margin: 5px;
}
.pic_top{
    position: relative;
    border: 1px dashed #ccc;
    height: 230px;
    width: 100%;
    margin-bottom: 20px;
    text-align: center;
}
.iconStyle{
    font-style: normal;
    font-size: 35px;
    color: darksalmon;
}
.iconClass{
    font-style: normal;
    color: #fff;
    font-size: 25px;
}
.timeDelet{
    margin: 0 20px;
    display: inline-block;
    border: 1px solid #16A589;
    padding: 5px;
    border-radius: 5px;
    background: #16A589;
    cursor: pointer;
}
.LoopDiv{
    width: 100%;;
}
.imgX{
    width: 20px;position: absolute;top: 1px;right: 1px;z-index:100;cursor: pointer;
}
}

/* .el-upload--text{
    height: 70px !important;
} */
</style>
<style >
.LoopDiv .textarea >textarea{
    height: 270px !important;
    resize: none !important;
    background: white !important;
    border: 1px dashed #CACACA !important;
    border-radius: 5px;
}
@media screen and (max-width: 1200px){
    .el-dialog {
        margin-left: 100px;
    }
    .el-radio-group {
        display: flex;
        padding: 10px;
    }
    .el-textarea__inner {
        height: 100px;
    }
    .el-form-item__content{
        width: 200px;
    }
    .el-message-box{
        width: 200px;
    }
}
/* .el-upload--text{
    height: 70px !important;
} */
</style>