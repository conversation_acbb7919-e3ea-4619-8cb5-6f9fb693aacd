<template>
    <div>
        <div>
            <div class="templateDiv" style="padding: 20px;display: inline-block;position: relative;width: 300px;background: #eaeaea;border-radius: 10px;margin: 20px 20px;" v-for="(item,index) in templateData" :key=index>
                <div style="
                    position: absolute;
                    border: 1px solid #b0aeae;
                    border-radius: 5px;
                    padding: 3px;
                    top: 0;
                    right: 0;
                    color: #fff;
                    background: #000;
                    opacity: 0.7;
                    z-index: 2;
                    margin: 5px;
                ">
                    <span>ID：{{item.id}}</span>
                </div>
                <div style="width: 100%;height: 300px;text-align: center;position: relative;z-index: 1;">
                    <img v-if="item.contents[0].media=='jpg' ||item.contents[0].media=='png' ||item.contents[0].media=='gif'" :src="API.imgU+item.contents[0].mediaGroup+'/'+item.contents[0].mediaPath" style="height: 250px;max-width: 100%;">
                    <video v-else-if="item.contents.media=='mp4'"
                        style="height: 250px;max-width: 100%;"
                        v-bind:src="API.imgU+item.contents[0].mediaGroup+'/'+item.contents[0].mediaPath"
                        class="avatar video-avatar"
                        controls="controls">
                    </video>
                    <audio
                        v-else-if="item.contents[0].media=='mp3'"
                        style="height: 250px;max-width: 100%;" 
                        autoplay="autoplay"
                        controls="controls"
                        preload="auto"
                        v-bind:src="API.imgU+item.contents[0].mediaGroup+'/'+item.contents[0].mediaPath">
                    </audio>
                    <p v-else-if="item.contents[0].txt" style="text-align: left;">{{item.contents[0].txt}}</p>
                    <div class="templateSon" @click="toUse(item)">
                        <div style="
                            height: 100%;
                            display: inline-block;
                            width: 50%;
                            font-size: 18px;
                            line-height: 60px;
                            background: rgb(22, 165, 137);
                            color: #fff;
                        ">
                            <span>点击使用</span>
                        </div>
                    </div>
                </div>
                <div style="padding: 10px 10px;background: #fff;">
                    <span>{{item.title}}</span>
                </div>
                <div style="background: #fff;padding: 10px 10px;position: relative;">
                    <span @click="View(item)" style="cursor: pointer;color: #16A589;">预览</span>
                    <!-- <span @click="deleteTemplate(item)" style="cursor: pointer;position: absolute;right: 10px;"><i class="el-icon-delete"></i></span> -->
                </div>
            </div>
        </div>
        <!-- 预览手机弹框   -->
        <el-dialog
            title="预览"
            :visible.sync="dialogVisible"
            width="40%"
            >
            <div>
                <div class="send-mobel-box">
                    <img src="../../../../../../assets/images/phone.png" alt="">
                    <div class="mms-content-exhibition">
                    <el-scrollbar  class="sms-content-exhibition" >
                        <div>
                            <span style="display: inline-block;padding: 5px;border-radius: 5px;background: #e2e2e2;margin-top: 5px;">{{title}}</span>
                        </div>
                        <div style="overflow-wrap: break-word;width: 234px;background: #e2e2e2;padding: 10px;border-radius: 5px;margin-top: 5px;" v-for="(item,index) in viewData" :key=index>
                            <img v-if="item.media=='jpg' ||item.media=='png' ||item.media=='gif'" :src="API.imgU+item.mediaGroup+'/'+item.mediaPath" style="width: 235px;" class="avatar video-avatar"  ref="avatar">
                            <video v-if="item.type=='video'"
                                style="width: 235px;"
                                v-bind:src="API.imgU+item.mediaGroup+'/'+item.mediaPath"
                                class="avatar video-avatar"
                                controls="controls">
                            </video>
                            <audio
                                v-if="item.type=='audio'"
                                style="width: 235px;" 
                                autoplay="autoplay"
                                controls="controls"
                                preload="auto"
                                v-bind:src="API.imgU+item.mediaGroup+'/'+item.mediaPath">
                            </audio>
                            <div style="white-space: pre-line;">
                                {{item.txt}}
                            </div>
                        </div>
                    </el-scrollbar>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapState, mapMutations,mapActions } from "vuex";
export default {
    name: "DraftLibrary",
    data () {
        return {
            dialogVisible:false,//预览手机
            title:'',//彩信标题
            templateData:[],
            // 预览内容
            viewData:'',
            title:''
        }
    },
    methods:{
        // 模板列表
        templateList(){
            this.$api.post(this.API.cpus + 'v1/consumermms/list',{status:2},res=>{
                this.templateData=res.data.records
            })
        },
        //预览
        Preview(){
            for(let i=0;i<this.LoopNum.length;i++){
                if(this.LoopNum[i].showVideoPath||this.LoopNum[i].showaudioPath||this.LoopNum[i].imageUrl||this.LoopNum[i].textarea){
                    if(!this.title){
                        this.$message({
                            type: 'warning',
                            message:'请编辑彩信标题'
                        });  
                        return false
                    }
                    this.dialogVisible=true
                }else{
                    this.$message({
                        type: 'warning',
                        message:'请先编辑彩信内容'
                    }); 
                }
            }
        },
        // 预览
        View(val){
            this.viewData=val.contents
            this.title=val.title
            this.dialogVisible=true
        },
        // 删除模板
        deleteTemplate(val){
            this.$confirms.confirmation('delete','确定删除当前模板？',this.API.cpus+'consumermmstemplate/'+val.id,{},res =>{
                this.templateList()
            })
        },
        toUse(val){
            window.sessionStorage.tpeId=val.id
            window.sessionStorage.MMStype="draft"
            this.$emit('childrenTemplateCreation', val,'draft')
        }
    },
    mounted(){
        this.templateList()
    },
    // activated(){
    //     this.templateList()
    // },
    watch:{

    }
}
</script>
<style scoped>
.AddTo{
    padding: 10px;
    height: 530px;
}
.AddTo:hover{
    background: #ecf5ff70;
}
.LoopUpload_div{
    width: 25%;
    float: left;
    margin-right: 100px;
    height: 700px;
    min-width: 325px;
}
.send-mobel-box{
    width: 300px;
    overflow: hidden;
    position: relative;
    margin-bottom: 35px;
    left: 28%;
}
.send-mobel-box img{
    width:300px;
}
.mms-content-exhibition{
    position: absolute;
    top: 0;
    width: 300px;
    height: 375px;
    margin: 135px 25px 0px 25px;
    overflow: auto;
    overflow-y: auto;
}
.el-scrollbar__wrap{
    margin-bottom:0;
    margin-right:0
}
.templateSon{
    position: absolute;
    width: 100%;
    height: 60px;
    bottom: 0;
    background: #00000017;
    cursor: pointer;
    display: none;
}
.templateDiv:hover .templateSon{
    position: absolute;
    width: 100%;
    height: 60px;
    bottom: 0;
    background: #00000017;
    cursor: pointer;
    display: block;
}
</style>