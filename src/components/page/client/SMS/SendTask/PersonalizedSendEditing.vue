<template>
    <div>
        <div class="Top_title">
            <!-- <span style="display:inline-block;padding-right:10px; cursor:pointer;color:#16a589;" @click="goBack()"><i class="el-icon-arrow-left"></i> 返回</span>| -->
            <span style="display:inline-block;padding-left:6px;"><span style="display:inline-block;padding-right:10px; cursor:pointer;color:#16a589;" @click="goBack()"><i class="el-icon-arrow-left"></i>返回</span>|<span> 个性化自定义发送编辑</span></span>
        </div>
        <div class="fillet shortChain-box">
            <el-form :model="configurationItem.formData" :rules="configurationItem.formRule" ref="configurations" label-width="95px" style="padding:20px 8px  0 8px;">
                <!-- <div style="position:relative;">
                    <span style="display:inline-block;width:83px;text-align:right;padding-right:11px;position:absolute;">发送对象</span>
                    <file-upload style="display: inline-block; margin-left:97px;"
                        :action="this.API.cpus+'v3/file/upload'"
                        :limit= 1
                        :showfileList=true
                        :fileStyle='fileStyle'
                        :del='del1'
                        :istip='false'
                        :tip='tip'
                        @fileup="fileup"
                        @fileupres="fileupres"
                    >选择上传文件</file-upload>
                </div> -->
                <!-- <div class="send-upload-tips">格式要求：支持.xlsx .xls格式,文件大小不超过300M,表格<span style="color:rgb(210, 7, 7)">第一列手机号、第二列发送内容；</span></div>
                <div class="send-upload-tips" style="margin-bottom: 20px;">建议单次最大上传10万行，内容格式请先下载模板<span style="cursor: pointer;" @click="downloadTems">模板下载</span></div> -->
                <!-- <el-form-item label="" prop="">
                   <div style="width: 507px;padding: 10px;border: 1px solid #bbb;position: sticky;">
                       <div><i style="font-size: 12px;color: #16A589;" class="el-icon-success"></i><span style="margin-left: 4px;font-weight: 600;">成功提交号码9989个</span><span style="margin-left: 20px;font-size: 12px;color: #aaa;">文件：手机号.xlsx</span><el-button type="primary" size="mini" style="position: absolute;top: 12px;right: 12px;">清空</el-button></div>
                        <div style="font-size: 12px;"><i class="el-icon-warning"></i> 单次最大提交<span>7</span>个    成功提交<span>13</span>个 已过滤<span>7</span>个重复 无效号码<span>51</span>个</div>
                   </div>
                </el-form-item> -->
                <el-form-item label="发送时间" prop="isTiming">
                    <el-radio-group v-model="configurationItem.formData.isTiming">
                    <el-radio disabled label="0" >立即发送</el-radio>
                    <el-radio label="1" >定时发送</el-radio>
                    </el-radio-group>
                </el-form-item>
                <div v-if="configurationItem.formData.isTiming == 1" style="padding-bottom:18px;" class="send-time-sel" prop="sendTime">
                    <span style="display:inline-block;width:83px;text-align:right;padding-right:10px;">定时时间</span>
                    <date-plugin class="Mail-search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal" style="width:364px;"></date-plugin>
                </div>
                <div class="sms-first-steps-btns" style="text-align: left;padding-left: 95px;margin-bottom: 30px;">
                    <el-button type="primary" v-if="sendLoading == true" @click="submissionItem('configurations')" style="padding:10px 28px;">发送短信</el-button>
                    <el-button type="primary" v-if="sendLoading == false" :loading="true" style="padding:10px 40px;">提交数据中</el-button>
                    <!-- <el-button @click="sendMsgDialogShow = false" style="padding:10px 20px;">取 消</el-button> -->
                </div>
                <el-form-item label="发送规则" prop="">
                    <ul style="list-style: disc;margin-left: 20px;">
                        <li>发送的短信内容，不能包含变量，且需进入人工审核，待审核完毕后将自动发送。</li>
                        <li>计费规则：<span style="color: red;">70字内（含70字）</span>计一条，超过70字，按<span style="color: red;">67字/条</span>计费</li>
                    </ul>
                </el-form-item>
                <el-form-item label="内容规范" prop="">
                    <ul  style="list-style: disc;margin-left: 20px;">
                        <li>签名内容为：公司或品牌名称，字数要求<span style="color: red;">2-12</span>个字符。位于<span style="color: red;">每条短信内容的首位【】</span></li>
                        <li><span style="color: red;">邀请注册、邀请成为会员、邀请加微信、加QQ群</span>的商业性信息不能发送。</li>
                        <li><span style="color: red;">黄、赌、毒</span>犯法等国家法律法规严格禁止的内容不能发送。</li>
                        <li>包含“股票加群、购物加群、集资贷款、改分、代办大额信用卡、信用卡提额”等疑似诈骗或类似的信息不能发送。</li>
                        <li>超链接地址请写在短信内容中，便于核实，部分安卓系统存在超链接识别问题，需在超链接前后添加空格。</li>
                        <li>变量模板中的变量有长度和个数限制，具体请咨询。</li>
                    </ul>
                </el-form-item>
            </el-form>
        </div> 
        <!-- 短链转换 -->
        <el-dialog title="短链转换" :visible.sync="shortVisible" width="520px" >
            <div class="short-box">
                <p class="short-title" style="padding-top:10px">长网址链接</p> 
                <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l" >
                    <el-button slot="append" type="primary"  icon="el-icon-refresh" @click="transformation()">转换</el-button>
                </el-input>
                <div class="font-sizes font-sizes1"><span style="color:red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。</div>
                <div class="font-sizes"><span style="color:red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。</div>
            </div>
            <div class="short-box">
                <p class="short-title" style="padding-top:20px">短网址链接</p> 
                <el-input  v-model="shortConUrl" class="width-l" :disabled="true">
                    <el-button slot="append" type="primary" @click="handlePreview()"  icon="el-icon-share">预览</el-button>
                </el-input>
            </div>
            <div style="text-align:right;margin-top:16px">
                <el-button @click="HandelCencals()">取 消</el-button>
                <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
            </div>
        </el-dialog>
        <!-- 二次确认弹出 -->
        <el-dialog title="确认发送" :visible.sync="ConfirmSend" width="520px" >
            <div style="padding-bottom:5px;">您本次提交号码数 <span style="color:#16A589;">{{sendNumber}}</span> 个</div>
            <div style="padding-top:10px;">发送内容： <div style="color:#999;padding-top:6px;word-wrap:break-word;">{{sendContent}}</div></div>   
            <div class="sms-seconnd-steps-btns">
                <el-button type="primary" @click="ConfirmSending()" style="padding:10px 20px;" v-if="fullscreenLoading == true">确定发送</el-button>
                <el-button @click="ConfirmSends">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapState, mapMutations,mapActions } from "vuex";
import FileUpload from '@/components/publicComponents/FileUpload' //文件上传
import DatePlugin from '@/components/publicComponents/DatePlugin' //日期

export default {
    name: "PersonalizedSendEditing",
    components: {
        FileUpload,
        DatePlugin
    },
    data () {
        //短信内容的签名格式是否正确，或者是否有签名
        var content = (rule, value, callback) => {
            if(value==""){
                return callback(new Error('请填写自定义内容！'));
            }else{
                let ret = 3;
                let beg = value.indexOf('【');
                let end = value.indexOf('】');
                let lastbeg = value.lastIndexOf('【');
                let lastend = value.lastIndexOf('】');
                let valLength = value.length;
                if(beg >- 1 && end > -1 && end > beg && beg == 0 ){
                    if( beg == 0 && end < 50 && end >2){
                        callback();
                    }else{
                        return callback(new Error('请填写正确的签名！'));
                    }
                }else if(lastbeg > -1 && lastend > -1 && lastend > lastbeg && lastend == valLength -1){
                    if (lastend == (valLength -1) && lastend - lastbeg < 49 && lastend - lastbeg > 2){
                        callback();
                    }else{
                        return callback(new Error('请填写正确的签名！'));
                    }
                }else{
                        return callback(new Error('请填写签名！'));
                }
            }
        };
        var mobile = (rule, value, callback) => {
            if(value==""){
                callback();
            }else{
                if(this.limit>200){
                    callback("已超过填写返回");
                }
            };
        }
        return {
            SmSid:'',
            textareaShow:true,
            ConfirmSend:false,
            limit:0,//号码限制
            SuccessfullySubmitted:0, //成功提交号
            filter:0, //过滤
            invalid:0, //无效 
            shortVisible:false,//短链弹框
            originalUrl:'',
            shortConUrl:'', //短链的URL
            sendLoading:true,//第一步短信发送的loading按钮
            fullscreenLoading: true, //第二步短信发送的loading按钮
            sendNumber:'', //短信发送条数
            sendContent:"", //发送短信
            copysigCenten:"",//复制签名内容
            copytemCenten:"",//复制模板内容
            phoneCenten:'', //手机短信内容
            del1:true,//关闭弹框时清空图片
            tip:'仅支持.xlsx .xls.txt 等格式',
            sigOptions:[], //签名列表
            temOptions:[],//模板列表
            SMScount:{ //手机展示短信内容的下方，短信内容的计数
                smswordNum:'0', //短信内容的个数
                smssTrip:'0' //短信可以分为几条
            },
            downloadtem:'2', //模板下载（全文，自定义还是变量）（自定义为2）
            fileStyle:{ 
                size:245678943234,
                style:['xlsx','xls','txt']
            },
            datePluginValueList: { //日期参数配置
                type:"datetime",
                pickerOptions:{
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    }
                },
                defaultTime:'', //默认起始时刻
                datePluginValue:''
            },
            configurationItem:{ //发送短信弹出框的值
                formData:{
                    isTiming:"1", //选择立即发送还是定时发送
                    sendTime:'', //发送时间的值
                    group:'',
                    path:'',
                    mobile:'',
                    files:'',
                    fileName:'',
                },
                formDatass:{
                    isTiming:"1", //选择立即发送还是定时发送
                    sendTime:'', //发送时间的值
                    group:'',
                    path:'',
                    mobile:'',
                    files:'',
                    fileName:'',
                },
                formRule:{//验证规则
                    content:[
                        { required: true, validator: content, trigger: 'change' },
                        { min: 1, max: 450, message: '长度在 1 到 450 个字符', trigger: 'change' }
                    ],
                    tempId:[
                        { required: true, message: '请选择模板名称', trigger: 'change' },
                    ],
                    signatureId:[
                        { required: true, message: '请选择签名', trigger: 'change' },
                    ],
                    sendTime:[
                        { required: true, message: '请填写定时时间', trigger: 'change' },
                    ],
                    isTiming:[
                        { required: true, message: '请选择发送时间', trigger: 'change' }
                    ],
                    // mobile:[
                    //     {validator: mobile, trigger: 'change'}
                    // ]
                }
            },
        }
    },
    methods: {
        //返回
        goBack(){
            this.$router.go(-1);
        },
        //移除文件
        fileup(val){
            this.configurationItem.formData.files = '';
            this.textareaShow=true
            this.configurationItem.formData.mobile='';
            this.limit=0//号码限制
            this.SuccessfullySubmitted=0//成功提交号
            this.filter=0 //过滤
            this.invalid=0 //无效 
        },
        //文件上传成功
        fileupres(val,val2){
            if(val.code==200){
                this.configurationItem.formData.group=val.data.group
                this.configurationItem.formData.path=val.data.path
                this.configurationItem.formData.files = val2[0].name;
                this.configurationItem.formData.mobile=""
                this.configurationItem.formData.fileName=val.data.fileName
                this.textareaShow=false
                this.del1 = true;
            }
        },
        //下载模板
        downloadTems(){
                this.$File.export(this.API.cpus +'v3/individualization/templateDownload', {},`自定义发送模板.zip`)
        },
        //短信发送提交 (第一步的短信发送)
        submissionItem(formName){
            this.$refs[formName].validate((valid,val) => {
                if (valid) {
                    let falgs = false;
                    let falgss = false;
                    this.configurationItem.formDatass = Object.assign({},this.configurationItem.formData);
                    if(this.configurationItem.formDatass.isTiming === '1'){ //判断发送时间为定时时间
                        if(this.configurationItem.formDatass.sendTime){  //如果是定时时间 ，定时时间范围必填
                            let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
                            if (nowTiem < Date.now()+600000) {
                                falgss = false;
                                this.datePluginValueList.datePluginValue='';
                                this.configurationItem.formData.sendTime = '';
                                this.$message({
                                    message: '定时时间已过期，定时时间应大于当前时间30分钟，需重新设置！',
                                    type: 'warning'
                                });
                            }else{
                                falgss = true;
                            }
                        }else{
                            falgss = false;
                            this.$message({
                                message: '选择定时时间！',
                                type: 'warning'
                            });
                        }
                    }else{
                        falgss = true;
                    }
                    if(falgss == true ){
                    this.configurationItem.formData.taskSmsId=this.SmSid
                        this.$confirms.confirmation("post",'确认编辑自定义短信？',this.API.cpus + 'v3/updateConsumerWebTask',this.configurationItem.formData ,res=>{
                            if(res.code==200){
                                this.$message({
                                    message: '定时编辑成功！',
                                    type: 'success'
                                });
                                this.$router.push({ path: '/SendTask'})
                            }else{
                                this.$message({
                                    message: res.msg,
                                    type: 'error'
                                });
                            }
                        })   
                    }
                }
            })
            
        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveUrl',
        ]),
        logUrl(val){
        let logUrl={
            logUrl:val
        } 
        this.saveUrl(logUrl);
        window.sessionStorage.setItem('logUrl',val)            
        },
        // 取消发送
        ConfirmSends(){
            this.ConfirmSend=false
        },
        handledatepluginVal: function(val1,val2){ //日期
            this.configurationItem.formData.sendTime = val1
        },
    },
    mounted(){
        this.SmSid=this.$route.query.SmSId
        if(this.SmSid){
            this.$api.get(this.API.cpus+'v3/queryConsumerWebTaskById/'+this.SmSid,{},res=>{
                this.configurationItem.formData.sendTime=res.sendTime
                this.datePluginValueList.datePluginValue=new Date(Date.parse(res.sendTime))
            })   
        }
    },
    watch:{
        downloadtem(val){
            if(val=="1"){
                this.fileStyle.style=['xlsx','xls']
                this.tip='仅支持.xlsx .xls 等格式'
                this.configurationItem.formData.mobile=""
            }else{
                this.fileStyle.style=['xlsx','xls','txt']
                this.tip='仅支持.xlsx .xls.txt 等格式'
            }
        },
    },
}
</script>
<style scoped>
.el-textarea{
    width: 50%;
}
.shortChain-box{
    padding:20px;
}
.sendMsg-box{
    padding:20px;
}
.sendMsg-title{
    margin:10px 0;
    padding-left:5px;
    color: #16a589;
}
.sendMsg-table{
    margin-top:12px;
}
.sendMsg-list-header{
    padding-top:30px;
    font-weight: bold;
}
.el-steps--simple{
    background: #fff;
    border-radius: 0px;
    border-bottom: 1px solid #f3efef;
}
.send-mobel-box{
    width: 260px;
    overflow: hidden;
    position: relative;
    margin-bottom: 35px;
    left: 28%;
}
.send-mobel-box img{
    width:255px;
}
.el-select{
    width:50%;
}
.send-upload-tips{
    padding-left:96px;
    padding-top:5px;
    font-size: 12px;
}
.send-upload-tips span{
    display: inline-block;
    padding-left:8px;
    color:#0066FF;
}
.sms-content-exhibition{
    width: 170px;
    height: 32%;
    border-radius: 10px;
    position: absolute;
    padding: 8px 9px;
    background: #e2e2e2;
    top: 146px;
    left: 31px;
    font-size: 12px;
    line-height: 18px;
    color: #000;
    overflow: hidden;
}
/* .sms-first-steps-btns{
    width:517px;
    position: absolute;
    text-align: center;
    bottom: 10px;
} */
.sms-seconnd-steps-box{
    padding:30px 20px 30px 55px;
}
.sms-seconnd-steps-btns{
    text-align: right;
}
.goTiming{
    padding-right:14px;
    cursor: pointer;
    color:#16a589;
}
.goTiming:hover{
    color:#03886e;
}
.shortChain-box{
    padding:20px;
}
.shortChain-matter{
    border:1px solid #66CCFF;
    padding:10px 14px;
}

.short-title{
    font-weight: bolder;
    padding-bottom: 5px;
}
.font-sizes{
    padding-top:2px;
    font-size:12px;
    color:rgb(163, 163, 163);
}
.font-sizes1{
    margin-top:10px;
}
</style>
<style>
.sendMsg-box .el-dialog__body{
    padding:10px 20px;
}
.sendMsg-box .el-form-item__label{
    text-align:left;
}
.sendMsg-box .el-step__head.is-process{
    color:#989898;
}
.sendMsg-box .el-step__title.is-process{
    color:#989898;
}
.sms-content-exhibition .el-scrollbar__wrap{
    overflow-x: hidden;
    /* overflow-y: scroll !important;  */
    margin-right: -27px !important;
}
 .el-picker-panel .el-button--text{
    display: none ;
}
.textareas textarea{
    height:100px;
    resize:none;

}
.textareas textarea::-webkit-scrollbar {
    display: none;
    
}
/* .box-textareas{
    width: 380px;
    height: 125px;
    border-radius: 5px;
    border: 1px solid rgb(162, 219, 208);
}
.box-textareas textarea{
    border:none
} */
</style>