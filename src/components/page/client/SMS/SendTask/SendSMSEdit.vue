<template>
  <div class="simple-template-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="el-icon-message"></i>
            发送短信编辑
          </h1>
          <p class="page-subtitle">编辑和配置短信发送任务</p>
        </div>
        <div class="header-right">
          <el-tag type="warning" size="medium">编辑模式</el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 返回导航 -->
        <div class="navigation-section">
          <span class="back-link" @click="goBack()">
            <i class="el-icon-arrow-left"></i>返回
          </span>
          <span class="page-indicator">发送短信编辑</span>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
          <el-row :gutter="20">
            <el-col :span="15">
              <!-- 短信配置表单卡片 -->
              <el-card shadow="hover" class="form-card">
                <div slot="header" class="card-header">
                  <span class="card-title">
                    <i class="el-icon-setting"></i>
                    短信配置
                  </span>
                </div>

                <el-form
                  :model="configurationItem.formData"
                  :rules="configurationItem.formRule"
                  ref="configurations"
                  label-width="95px"
                  class="modern-form"
                >
                  <el-form-item label="短信类型" prop="tempId" class="form-item-modern">
                    <div class="sms-type-selector">
                      <el-select
                        v-model="configurationItem.formData.tempId"
                        placeholder="请选择"
                        size="large"
                        class="modern-select"
                      >
                        <el-option disabled label="自定义发送内容" value="-1"></el-option>
                        <el-option disabled label="模板发送" value="2"></el-option>
                      </el-select>
                    </div>
                  </el-form-item>

                  <el-form-item
                    label="选择签名"
                    v-if="!(TemplateContent.indexOf('】') > 0 && configurationItem.formData.tempId != '-1') && sendTypes == '2'"
                    class="form-item-modern"
                  >
                    <div class="signature-selector">
                      <el-select 
                        v-model="configurationItem.formData.signatureId" 
                        @change="changeLocationValue"
                        class="modern-select"
                        clearable 
                        filterable
                      >
                        <el-option 
                          v-for="item in sigOptions" 
                          :key="item.signatureId" 
                          :label="item.signature"
                          :value="item.signatureId"
                        ></el-option>
                      </el-select>
                      <router-link :to="'CreateSign?i=1'">
                        <el-button
                          type="success"
                          icon="el-icon-plus"
                          size="large"
                          class="action-btn"
                          @click="logUrl('/SignatureManagement')"
                        >
                          添加签名
                        </el-button>
                      </router-link>
                    </div>
                  </el-form-item>

                  <el-form-item label="短信内容" prop="content" v-if="configurationItem.formData.tempId == '-1'" class="form-item-modern">
                    <div class="sms-content-editor">
                      <el-input
                        type="textarea"
                        placeholder="【短信签名】请在此处输入短信内容"
                        v-model="configurationItem.formData.content"
                        maxlength="1500"
                        :rows="6"
                        show-word-limit
                        class="modern-textarea content-textarea"
                      />

                      <div class="content-tips">
                        <el-alert
                          type="info"
                          :closable="false"
                          show-icon
                        >
                          <template slot="default">
                            <div class="tip-item">
                              <i class="el-icon-info"></i>
                              已输入 <span :class="{ 'text-danger': number_zs > 1500 }">{{ number_zs }}</span> 字，最多 1500 字（含签名），70字内（含70字）计一条，超过70字，按67字/条计费
                            </div>
                            <div class="tip-item">
                              <i class="el-icon-warning"></i>
                              短信内容中，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况
                            </div>
                          </template>
                        </el-alert>
                      </div>
                    </div>
                  </el-form-item>

                  <el-form-item label="短信内容" prop="content" v-if="configurationItem.formData.tempId != '-1'" class="form-item-modern">
                    <div class="template-content-editor">
                      <div class="template-content-wrapper" @mouseover="button_QC = true" @mouseout="button_QC = false">
                        <el-button
                          type="danger"
                          size="small"
                          plain
                          class="clear-content-btn"
                          @click="button_QCNR"
                          v-if="button_QC"
                          icon="el-icon-delete"
                        >
                          清除内容
                        </el-button>
                        <el-input
                          type="textarea"
                          placeholder="请选择您需要发送的短信模板，选择完模板内容将会显示"
                          readonly
                          v-model="configurationItem.formData.content"
                          :rows="6"
                          class="modern-textarea template-textarea"
                        />
                      </div>

                      <div class="content-tips">
                        <el-alert
                          type="info"
                          :closable="false"
                          show-icon
                        >
                          <template slot="default">
                            <div class="tip-item">
                              <i class="el-icon-info"></i>
                              已输入 <span :class="{ 'text-danger': number_zs > 1500 }">{{ number_zs }}</span> 字，最多 1500 字（含签名），70字内（含70字）计一条，超过70字，按67字/条计费
                            </div>
                          </template>
                        </el-alert>
                      </div>
                    </div>
                  </el-form-item>

                  <el-button 
                    type="primary" 
                    v-show="configurationItem.formData.tempId == '-1' && !SmSid"
                    icon="el-icon-refresh" 
                    plain 
                    @click="shortReset()"
                    class="action-btn primary-btn"
                  >
                    短链转换
                  </el-button>

                  <el-form-item label="发送时间" class="form-item-modern">
                    <el-radio-group v-model="configurationItem.formData.isTiming">
                      <el-radio disabled label="0">立即发送</el-radio>
                      <el-radio label="1">定时发送</el-radio>
                    </el-radio-group>

                    <div v-if="configurationItem.formData.isTiming == 1" class="send-time-picker">
                      <el-date-picker
                        v-model="configurationItem.formData.sendTime"
                        type="datetime"
                        placeholder="选择日期时间"
                        size="large"
                        class="modern-datepicker"
                      ></el-date-picker>
                    </div>
                  </el-form-item>

                  <el-form-item label="发送对象" class="form-item-modern">
                    <div class="recipient-uploader">
                      <file-upload
                        v-permission
                        v-if="!SmSid"
                        :action="this.API.cpus + 'v3/file/upload'"
                        :limit=1
                        :showfileList=true
                        :fileStyle='fileStyle'
                        :del='del1'
                        :istip='false'
                        :tip='tip'
                        @fileup="fileup"
                        @fileupres="fileupres"
                        class="modern-file-upload"
                      >
                        选择上传文件
                      </file-upload>

                      <div class="upload-tips">
                        <el-alert
                          type="info"
                          :closable="false"
                          show-icon
                          class="small-alert"
                        >
                          <template slot="default">
                            <div class="tip-item">
                              <i class="el-icon-info"></i>
                              格式要求：支持.xlsx .xls.txt等格式，文件大小不超过300M
                            </div>
                            <div class="tip-item">
                              <i class="el-icon-warning"></i>
                              如上传文件将会自动清除下面手动填写的手机号码
                            </div>
                          </template>
                        </el-alert>
                      </div>
                    </div>
                  </el-form-item>

                  <div class="upload-notice">
                    <p>TXT格式：只支持一行一个号码，且只适用于自定义与普通模板发送，建议单次最大上传10万行，内容格式请先下载模板</p>
                    <p>建议单次最大上传10万行，内容格式请参考模板<span class="download-link" @click="downloadTems">模板下载</span></p>
                  </div>

                  <el-form-item label="手机号码" prop="mobile" v-if="textareaShow == true && downloadtem != '1' && variableTp" class="form-item-modern">
                    <div class="mobile-input-container">
                      <el-input 
                        type="textarea" 
                        disabled 
                        @blur="FilterNumber" 
                        class="mobile-textarea"
                        @input="textChange" 
                        placeholder="手动最多输入200个手机号码，号码之间用英文逗号隔开"
                        v-model="configurationItem.formData.mobile"
                      ></el-input>
                      <div class="mobile-counter" v-show="limit <= 200">
                        <span>{{ limit }}/200</span>
                      </div>
                      <div class="mobile-counter error" v-show="limit > 200">
                        <span class="text-danger">{{ limit }}</span><span>/200</span>
                      </div>
                    </div>
                  </el-form-item>

                  <!-- <el-form-item label="统计信息" v-if="textareaShow == true && downloadtem != '1' && variableTp" class="form-item-modern">
                    <div class="mobile-stats">
                      <span class="stat-item">成功提交<i class="text-success">{{ SuccessfullySubmitted }}</i>个</span>
                      <span class="stat-item">已过滤<i class="text-danger">{{ filter }}</i>个重复</span>
                      <span class="stat-item">无效号码<i class="text-danger">{{ invalid }}</i>个</span>
                    </div>
                  </el-form-item> -->

                  <div class="form-actions">
                    <el-button
                      v-permission
                      type="primary"
                      :loading="!sendLoading"
                      @click="ConfirmSending()"
                      size="large"
                      class="action-btn primary-btn"
                    >
                      发送短信
                    </el-button>
                    <el-button
                      type="default"
                      size="large"
                      class="action-btn default-btn"
                      @click="goBack()"
                    >
                      取消
                    </el-button>
                  </div>

                  <el-form-item label="发送规则" class="form-item-modern">
                    <ul class="rule-list">
                      <li>从模板库中导入的短信可实时发送。</li>
                      <li>您在本页面上输入的短信内容，需进入人工审核，待审核完毕后将自动发送。</li>
                      <li>发送会员营销短信，不能包含变量，且需进入人工审核，待审核完毕后将自动发送。</li>
                      <li>发送行业通知短信，选择模板发送不需要人工审核直接发送；输入内容最少需添加1个参数，最多可添加16个参数。</li>
                      <li>计费规则：<span class="text-danger">70字内（含70字）</span>计一条，超过70字，按<span class="text-danger">67字/条</span>计费。</li>
                    </ul>
                  </el-form-item>

                  <el-form-item label="内容规范" class="form-item-modern">
                    <ul class="rule-list">
                      <li>签名内容为：公司或品牌名称，字数要求<span class="text-danger">1-50</span>个字符。</li>
                      <li><span class="text-danger">邀请注册、邀请成为会员、邀请加微信、加QQ群</span>的商业性信息不能发送。</li>
                      <li><span class="text-danger">黄、赌、毒</span>犯法等国家法律法规严格禁止的内容不能发送。</li>
                      <li>包含"股票加群、购物加群、集资贷款、改分、代办大额信用卡、信用卡提额"等疑似诈骗或类似的信息不能发送。</li>
                      <li>超链接地址请写在短信内容中，便于核实，部分安卓系统存在超链接识别问题，需在超链接前后添加空格。</li>
                      <li>变量模板中的变量有长度和个数限制，具体请咨询。</li>
                    </ul>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-col>

            <el-col :span="9">
              <!-- 短信预览卡片 -->
              <el-card shadow="hover" class="preview-card">
                <div slot="header" class="card-header">
                  <span class="card-title">
                    <i class="el-icon-mobile-phone"></i>
                    短信预览
                  </span>
                  <div class="preview-actions">
                    <el-button 
                      type="text" 
                      size="small" 
                      @click="refreshPreview"
                      class="refresh-btn"
                    >
                      <i class="el-icon-refresh"></i>
                    </el-button>
                  </div>
                </div>
                
                <!-- 手机预览区域 -->
                <div class="sms-preview-container">
                  <div class="phone-mockup">
                    <div class="phone-screen">
                      <div class="phone-header">
                        <div class="status-bar">
                          <span class="time">9:41</span>
                          <div class="status-icons">
                            <i class="el-icon-wifi"></i>
                            <i class="el-icon-mobile-phone"></i>
                            <i class="el-icon-battery"></i>
                          </div>
                        </div>
                        <div class="message-header">
                          <i class="el-icon-arrow-left"></i>
                          <span class="contact-name">短信</span>
                          <i class="el-icon-more"></i>
                        </div>
                      </div>
                      
                      <div class="message-content">
                        <div class="message-bubble" v-if="configurationItem.formData.content">
                          <div class="message-text">{{ configurationItem.formData.content }}</div>
                          <div class="message-time">{{ getCurrentTime() }}</div>
                        </div>
                        <div class="empty-message" v-else>
                          <i class="el-icon-message"></i>
                          <p>请输入短信内容</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 统计信息 -->
                <div class="preview-stats">
                  <div class="stats-grid">
                    <div class="stat-item">
                      <div class="stat-icon">
                        <i class="el-icon-document"></i>
                      </div>
                      <div class="stat-content">
                        <div class="stat-value" :class="{ 'text-danger': SMScount.smswordNum > 1500 }">
                          {{ SMScount.smswordNum }}
                        </div>
                        <div class="stat-label">当前字数</div>
                      </div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-icon">
                        <i class="el-icon-s-promotion"></i>
                      </div>
                      <div class="stat-content">
                        <div class="stat-value text-primary">
                          {{ SMScount.smssTrip }}
                        </div>
                        <div class="stat-label">预计条数</div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 字数限制提示 -->
                  <div class="word-limit-tips" v-if="SMScount.smswordNum > 0">
                    <div class="limit-bar">
                      <div 
                        class="limit-progress" 
                        :class="{ 'warning': SMScount.smswordNum > 1000, 'danger': SMScount.smswordNum > 1500 }"
                        :style="{ width: Math.min((SMScount.smswordNum / 1500) * 100, 100) + '%' }"
                      ></div>
                    </div>
                    <div class="limit-text">
                      <span v-if="SMScount.smswordNum <= 70" class="text-success">
                        <i class="el-icon-success"></i> 单条短信
                      </span>
                      <span v-else-if="SMScount.smswordNum <= 1500" class="text-warning">
                        <i class="el-icon-warning"></i> 多条短信
                      </span>
                      <span v-else class="text-danger">
                        <i class="el-icon-error"></i> 超出限制
                      </span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 模板列表对话框 -->
    <el-dialog title="短信模板选择" :visible.sync="templateDialog" :closeOnClickModal="false" width="920px">
      <div class="dialog-toolbar">
        <el-input 
          v-model="tabelAlllist.param" 
          placeholder="请输入内容"
          class="search-input"
        ></el-input>
        <el-button type="primary" plain @click="getTableData()" class="action-btn primary-btn">查询</el-button>
        <router-link :to="'CreateTemplate?i=1'">
          <el-button type="primary" plain @click="logUrl('/TemplateManagement')" class="action-btn primary-btn">添加模版</el-button>
        </router-link>
      </div>
      <el-table 
        v-loading="tableDataObj.loading2" 
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading" 
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable" 
        border 
        :data="tableDataObj.tableData" 
        class="enhanced-table"
      >
        <el-table-column prop="temId" label="ID" width="60"></el-table-column>
        <el-table-column label="模板类型" width="90">
          <template slot-scope="scope">
            <span v-if="scope.row.temType == '1'">验证码</span>
            <span v-else-if="scope.row.temType == '2'">通知</span>
            <span v-else-if="scope.row.temType == '3'">营销推广</span>
          </template>
        </el-table-column>
        <el-table-column label="内容">
          <template slot-scope="scope">
            <span>{{ scope.row.temContent }}</span>
            <span v-if="scope.row.checkReason" class="reject-reason">( 驳回原因：{{ scope.row.checkReason }} )</span>
          </template>
        </el-table-column>
        <el-table-column label="字数" width="90">
          <template slot-scope="scope">
            <span>{{ scope.row.temContent.length }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width='80'>
          <template slot-scope="scope">
            <el-button 
              v-permission 
              type="text" 
              @click="delTem(scope.row.temContent, scope.row.temId)"
              class="action-link info"
            >
              <i class="el-icon-circle-plus"></i>&nbsp;选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <div class="pagination-section">
        <el-pagination 
          class="simple-pagination" 
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" 
          :current-page="tabelAlllist.currentPage"
          :page-size='tabelAlllist.pageSize' 
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" 
          :total="pageTotal"
        >
        </el-pagination>
      </div>
    </el-dialog>

    <!-- 短链转换对话框 -->
    <el-dialog title="短链转换" :visible.sync="shortVisible" width="520px">
      <div class="short-chain-section">
        <div class="short-box">
          <p class="short-title">长网址链接</p>
          <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
            <el-button v-permission slot="append" type="primary" icon="el-icon-refresh" @click="transformation()" class="action-btn primary-btn">转换</el-button>
          </el-input>
          <div class="font-sizes font-sizes1"><span class="text-danger">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。</div>
          <div class="font-sizes"><span class="text-danger">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。</div>
        </div>
        <div class="short-box">
          <p class="short-title">短网址链接</p>
          <el-input v-model="shortConUrl" class="width-l" :disabled="true">
            <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share" class="action-btn primary-btn">预览</el-button>
          </el-input>
        </div>
      </div>
      <div class="dialog-actions">
        <el-button @click="HandelCencals()" class="action-btn default-btn">取 消</el-button>
        <el-button v-permission type="primary" @click="shortConDetermine()" class="action-btn primary-btn">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 二次确认对话框 -->
    <el-dialog title="确认发送" :visible.sync="ConfirmSend" width="520px">
      <div class="confirm-content">
        <div class="confirm-item">您本次提交号码数 <span class="text-success">{{ sendNumber }}</span> 个</div>
        <div class="confirm-item">发送内容： <div class="send-content">{{ sendContent }}</div></div>
      </div>
      <div class="dialog-actions">
        <el-button 
          v-permission 
          type="primary" 
          @click="ConfirmSending()" 
          v-if="fullscreenLoading == true"
          class="action-btn primary-btn"
        >
          确定发送
        </el-button>
        <el-button @click="ConfirmSends()" class="action-btn default-btn">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import FileUpload from '@/components/publicComponents/FileUpload' //文件上传
export default {
    name: "SendSMSEdit",
    components: {
        FileUpload
    },
    data() {
        //短信内容的签名格式是否正确，或者是否有签名
        var content = (rule, value, callback) => {
            if (value == "") {
                return callback(new Error('请填写短信内容！'));
            } else {
                let ret = 3;
                let beg = value.indexOf('【');
                let end = value.indexOf('】');
                let lastbeg = value.lastIndexOf('【');
                let lastend = value.lastIndexOf('】');
                let valLength = value.length;
                if (beg > - 1 && end > -1 && end > beg && beg == 0) {
                    if (beg == 0 && end < 50 && end > 2) {
                        let index = value.split('】').length
                        for (let i = 0; i < index; i++) {
                            if (i != 0) {
                                let c = value.split('【')[i]
                                let a = c.substring(0, c.indexOf("】"))
                                if (!/^[\u4e00-\u9fa5_a-zA-Z0-9]+$/.test(a) || a.length < 2 || a.length > 20) {
                                    return callback(new Error('请填写正确的签名！'));
                                }
                            }
                        }
                        callback();
                    } else {
                        return callback(new Error('请填写正确的签名！'));
                    }
                } else if (lastbeg > -1 && lastend > -1 && lastend > lastbeg && lastend == valLength - 1) {
                    if (lastend == (valLength - 1) && lastend - lastbeg < 49 && lastend - lastbeg > 2) {
                        callback();
                    } else {
                        return callback(new Error('请填写正确的签名！'));
                    }
                } else {
                    return callback(new Error('请填写签名！'));
                }
            }
        };
        var mobile = (rule, value, callback) => {
            if (value == "") {
                callback();
            } else {
                if (this.limit > 200) {
                    callback("已超过填写返回");
                }
            };
        }
        return {
            sendTypes: '',
            // 编辑id
            SmSid: '',
            TemplateStatus: false,
            variableTp: true,
            tempIdVal: '',
            TemplateContent: "",//选择模板存储内容用
            number_zs: 0,
            button_QC: false,
            textareaShow: true,
            ConfirmSend: false,
            limit: 0,//号码限制
            SuccessfullySubmitted: 0, //成功提交号
            filter: 0, //过滤
            invalid: 0, //无效 
            shortVisible: false,//短链弹框
            originalUrl: '',
            shortConUrl: '', //短链的URL
            sendLoading: true,//第一步短信发送的loading按钮
            fullscreenLoading: true, //第二步短信发送的loading按钮
            sendNumber: '', //短信发送条数
            sendContent: "", //发送短信
            copysigCenten: "",//复制签名内容
            copytemCenten: "",//复制模板内容
            phoneCenten: '', //手机短信内容
            del1: true,//关闭弹框时清空图片
            tip: '仅支持.xlsx .xls.txt 等格式',
            sigOptions: [], //签名列表
            loadingcomp: false, //文件上传loading
            temOptions: [],//模板列表
            templateDialog: false,
            // 模板列表
            tableDataObj: { //列表数据
                tableData: []
            },
            tabelAlllist: {//------发送表格请求的对象
                param: '',
                currentPage: 1,//当前页
                pageSize: 10,//每一页条数
            },
            pageTotal: 0,//总共条数
            SMScount: { //手机展示短信内容的下方，短信内容的计数
                smswordNum: '0', //短信内容的个数
                smssTrip: '0' //短信可以分为几条
            },
            downloadtem: '2', //模板下载（全文，自定义还是变量）（自定义为2）
            fileStyle: {
                size: 245678943234,
                style: ['xlsx', 'xls', 'txt']
            },
            datePluginValueList: { //日期参数配置
                type: "datetime",
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    }
                },
                defaultTime: '', //默认起始时刻
                datePluginValue: ''
            },
            configurationItem: { //发送短信弹出框的值
                formData: {
                    tempId: "-1",
                    content: '',
                    signatureId: '',
                    isTiming: "1", //选择立即发送还是定时发送
                    sendTime: '', //发送时间的值
                    files: '',
                    group: '',
                    path: '',
                    mobile: '',
                    templateId: '',
                    taskSmsId: ''
                },
                formDatass: {
                    tempId: "-1",
                    content: '',
                    signatureId: '',
                    isTiming: "1", //选择立即发送还是定时发送
                    sendTime: '', //发送时间的值
                    files: '',
                    group: '',
                    path: '',
                    templateId: '',
                    taskSmsId: ''
                },
                formRule: {//验证规则
                    content: [
                        { required: true, validator: content, trigger: 'change' },
                        { min: 1, max: 450, message: '长度在 1 到 450 个字符', trigger: 'change' }
                    ],
                    tempId: [
                        { required: true, message: '请选择模板名称', trigger: 'change' },
                    ],
                    signatureId: [
                        { required: true, message: '请选择模板', trigger: 'change' },
                    ],
                    sendTime: [
                        { required: true, message: '请填写定时时间', trigger: 'change' },
                    ],
                    isTiming: [
                        { required: true, message: '请选择发送时间', trigger: 'change' }
                    ],
                    // mobile:[
                    //     {validator: mobile, trigger: 'change'}
                    // ]
                }
            },
        }
    },
    methods: {
        // 刷新预览
        refreshPreview() {
            this.$message({
                message: '预览已刷新',
                type: 'success',
                duration: 1000
            });
        },
        
        // 获取当前时间
        getCurrentTime() {
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        },
        
        button_QCNR() {
            this.configurationItem.formData.content = ""
        },
        //返回
        goBack() {
            this.$router.go(-1);
        },
        textChange() {
            // this.configurationItem.formData.mobile=this.configurationItem.formData.mobile.replace(/[^\ \d\,]/g,"")
            // this.configurationItem.formData.mobile=this.configurationItem.formData.mobile.replace(/\s+/g,",")
            if (this.configurationItem.formData.mobile[this.configurationItem.formData.mobile.length - 1] == ",") {
                this.limit = this.configurationItem.formData.mobile.split(",").length - 1
            } else {
                this.limit = this.configurationItem.formData.mobile.split(",").length
            }
            if (this.configurationItem.formData.mobile.split(",").length === 1 && this.configurationItem.formData.mobile.split(",")[0] === "") {
                this.limit = 0
            }
        },
        // remoteMethod(query) {
        //     if (query !== '') {
        //         this.loadingcomp = true
        //         this.getSignature(query)
        //         this.loadingcomp = false
        //     } else {
        //         this.compNamelist = []
        //         this.getSignature()
        //     }
        // },
        //获得签名列表
        getSignature(query) {
            this.$api.post(this.API.cpus + 'signature/signatureList', {
                // signature: query || '',
                auditStatus: '2',
                currentPage: 1,
                pageSize: 200
            }, res => {
                this.sigOptions = res.records;
            })
        },
        //获得模板名称列表
        getTemplate() {
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate', {
                currentPage: 1,
                pageSize: 200
            }, res => {
                this.temOptions = res.records;
            })
        },
        //获得模板名称对应的内容
        // getTemplateContent(){
        //     if(this.configurationItem.formData.tempId){
        //         this.$api.get(this.API.cpus + 'v3/consumersmstemplate/get/'+this.configurationItem.formData.tempId,{},res=>{
        //             this.downloadtem = res.data.temFormat; //模板下载类型（1变量，2全文）
        //             this.copytemCenten = res.data.temContent;
        //             this.phoneCenten = this.copysigCenten + this.copytemCenten;
        //         })
        //     }
        // },
        // 过滤号码
        FilterNumber() {
            let NumberFilter = this.configurationItem.formData.mobile.split(",")
            let arrNumber = []
            let hash = []
            let reg = /^1\d{10}$/;
            for (var i = 0; i < NumberFilter.length; i++) {
                for (var j = i + 1; j < NumberFilter.length; j++) {
                    if (NumberFilter[i] === NumberFilter[j]) {
                        ++i;
                    }
                }
                arrNumber.push(NumberFilter[i]);
            }
            for (var i = 0; i < arrNumber.length; i++) {
                if (reg.test(arrNumber[i])) {
                    hash.push(arrNumber[i])
                }
            }
            this.configurationItem.formData.mobile = hash.join(",")
            this.SuccessfullySubmitted = hash.length //成功提交号
            this.filter = (NumberFilter.length) - (arrNumber.length) //过滤
            if (arrNumber[0] == "") {
                this.invalid == 0
            } else {
                this.invalid = (arrNumber.length) - (hash.length) //无效 
            }
            this.limit = hash.length
        },
        //获取签名下拉框选择的 签名内容
        changeLocationValue(val) {
            // if(!val){
            //     this.getSignature()
            // }
            let obj = {};
            obj = this.sigOptions.find((item) => {
                return item.signatureId === val;
            });
            this.copysigCenten = obj.signature;
            //短信内容展示
            if (this.configurationItem.formData.tempId == "-1") {
                let signaIndex = this.configurationItem.formData.content.lastIndexOf("】") + 1
                this.configurationItem.formData.content = this.copysigCenten + this.configurationItem.formData.content.substring(signaIndex, this.configurationItem.formData.content.length);
            } else {
                this.configurationItem.formData.content = this.copysigCenten + this.TemplateContent;
            }
        },
        //移除文件
        fileup(val) {
            this.configurationItem.formData.files = '';
            this.textareaShow = true
            this.configurationItem.formData.mobile = '';
            this.limit = 0//号码限制
            this.SuccessfullySubmitted = 0//成功提交号
            this.filter = 0 //过滤
            this.invalid = 0 //无效 
        },
        //文件上传成功
        fileupres(val, val2) {
            this.configurationItem.formData.group = val.data.group;
            this.configurationItem.formData.path = val.data.path;
            this.configurationItem.formData.files = val2[0].name;
            this.configurationItem.formData.mobile = ""
            this.textareaShow = false
            this.del1 = true;
        },
        //下载模板
        downloadTems() {
            if (this.downloadtem == '2') {
                this.$File.export(this.API.cpus + 'consumersmsinfo/templateZipDownload', {}, `发送文件模板（自定义内容，全文模板可用）.zip`)
            } else {
                this.$File.export(this.API.cpus + 'consumersmsinfo/templateZipDownload', {}, `发送文件模板（变量模板可用）.zip`)
            }
        },
        //短信发送提交 (第一步的短信发送)
        submissionItem(formName) {
            this.$refs[formName].validate((valid, val) => {
                if (this.configurationItem.formData.tempId === '-1' && Object.getOwnPropertyNames(val).length === 1 && val.signatureId[0].message === "请选择签名") {
                    valid = true
                } else if (this.configurationItem.formData.tempId !== '-1' && Object.getOwnPropertyNames(val).length === 1 && val.content[0].message === "请填写短信内容！") {
                    valid = true
                }
                if (this.limit > 200) {
                    valid = false
                    this.$message({
                        message: '手机号超出填写个数',
                        type: 'warning'
                    });
                }
                if (valid) {
                    let falgs = false;
                    let falgss = false;
                    this.configurationItem.formDatass = Object.assign({}, this.configurationItem.formData);
                    if (this.configurationItem.formDatass.isTiming === '1') { //判断发送时间为定时时间
                        if (this.configurationItem.formDatass.sendTime) {  //如果是定时时间 ，定时时间范围必填
                            let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
                            if (nowTiem < Date.now() + 1800000) {
                                falgss = false;
                                this.datePluginValueList.datePluginValue = '';
                                this.configurationItem.formData.sendTime = '';
                                this.$message({
                                    message: '定时时间已过期，定时时间应大于当前时间30分钟，需重新设置！',
                                    type: 'warning'
                                });
                            } else {
                                falgss = true;
                            }
                        } else {
                            falgss = false;
                            this.$message({
                                message: '选择定时时间！',
                                type: 'warning'
                            });
                        }
                    } else {
                        falgss = true;
                    }
                    //判断是否上传文件
                    // if(this.configurationItem.formDatass.files != ''||this.configurationItem.formDatass.mobile!=""){
                    falgs = true;
                    // }else{
                    // falgs = false;
                    //     if(this.downloadtem=="2"){
                    //         this.$message({
                    //             message: '上传发送对象文件或手动填写手机号!',
                    //             type: 'warning'
                    //         });
                    //     }else{
                    //         this.$message({
                    //             message: '上传发送对象文件!',
                    //             type: 'warning'
                    //         });
                    //     }
                    // }
                    //判断是否是 自定义内容
                    if (this.configurationItem.formDatass.tempId === '-1') {
                        this.configurationItem.formDatass.tempId = '';
                    } else {
                        this.configurationItem.formDatass.tempId = this.tempIdVal
                    }
                    if (falgs === true && falgss === true) {
                        this.sendLoading = false;
                        this.$api.post(this.API.cpus + 'consumersmsinfo/submitSms', this.configurationItem.formDatass, res => {
                            this.sendLoading = true;
                            if (res.code == 200) {
                                if (res.data.mobileNum == 0) {
                                    this.$message({
                                        message: '提交成功条数为0 ，需重新设置！',
                                        type: 'warning'
                                    });
                                } else {
                                    this.sendNumber = res.data.mobileNum;
                                    this.sendContent = res.data.content;
                                    this.phoneCenten = res.data.content;
                                    this.ConfirmSend = true
                                }
                            } else {
                                this.$message({
                                    message: res.msg,
                                    type: 'warning'
                                });
                            }
                        })
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            })
        },
        //确定发送短信 (第二步的短信发送)
        ConfirmSending() {
            this.fullscreenLoading = false;
            this.configurationItem.formDatass = Object.assign({}, this.configurationItem.formData);
            let flags = false;
            if (this.configurationItem.formDatass.isTiming === '1' && this.configurationItem.formDatass.sendTime) {  //判断是否定时时间 ，定时时间范围
                let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
                if (nowTiem < Date.now() + 1800000) {
                    flags = false;
                    this.ConfirmSend = false
                    this.fullscreenLoading = true;
                    this.$message({
                        message: "定时时间应大于当前时间，需重新设置！",
                        type: 'warning'
                    });
                    this.datePluginValueList.datePluginValue = '';
                    this.configurationItem.formData.sendTime = '';
                    this.stepsActive = 1;

                } else {
                    flags = true;
                }
            } else {
                flags = true;
            }
            if (flags === true) {
                //自定义内容发送
                if (this.configurationItem.formDatass.tempId === '-1') {
                    this.configurationItem.formDatass.tempId = '';
                    this.configurationItem.formDatass.signatureId = '';
                    if (this.isShort) {
                        this.configurationItem.formDatass.shortCode = this.shortCode;
                    } else {
                        this.configurationItem.formDatass.shortCode = '';
                    }
                    this.$api.post(this.API.cpus + 'v3/updateConsumerWebTask', this.configurationItem.formDatass, res => {
                        if (res.code == 200) {
                            this.$message({
                                message: '短信编辑成功！',
                                type: 'success'
                            });
                            this.$router.go(-1);
                        } else if (res.code == 406) {
                            this.$message({
                                message: '有效短信为0条！',
                                type: 'warning'
                            })
                        } else {
                            this.$message({
                                message: res.msg,
                                type: 'error'
                            });
                        }
                    })
                } else {
                    //模板内容发送
                    this.$api.post(this.API.cpus + 'v3/updateConsumerWebTask', this.configurationItem.formDatass, res => {
                        if (res.code == 200) {
                            this.$message({
                                message: '短信发送成功！',
                                type: 'success'
                            });
                            this.$router.go(-1);
                        } else if (res.code == 406) {
                            this.$message({
                                message: '有效短信为0条！',
                                type: 'warning'
                            })
                        } else {
                            this.$message({
                                message: res.msg,
                                type: 'error'
                            });
                        }
                    })
                }
            }
        },
        // 选择模板
        delTem(val, val2) {
            this.TemplateStatus = true
            this.templateDialog = false
            if (this.TemplateStatus) {
                this.configurationItem.formData.content = val
            } else {
                let contentIndex = this.configurationItem.formData.content.lastIndexOf("】") + 1
                this.configurationItem.formData.content = this.configurationItem.formData.content.substring(0, contentIndex) + val
            }
            this.TemplateContent = val
            this.tempIdVal = val2
        },
        // 取消发送
        ConfirmSends() {
            this.ConfirmSend = false
        },
        handledatepluginVal: function (val1, val2) { //日期
            this.configurationItem.formData.sendTime = val1
        },
        //短链转换
        shortReset() {
            this.shortVisible = true;
        },
        //转换
        transformation() {
            if (this.originalUrl != '') {
                this.$api.post(this.API.cpus + 'shortlink/changeUrl', { originalUrl: this.originalUrl }, res => {
                    if (res.code == 200) {
                        this.shortConUrl = res.data.shortLinkUrl;
                        this.shortCode = res.data.shortCode;
                        this.$message({
                            message: '短链接转换成功！',
                            type: 'success'
                        });
                    } else {
                        this.originalUrl = '';
                        this.$message({
                            message: res.msg,
                            type: 'warning'
                        });
                    }
                })
            } else {
                this.$message({
                    message: '长链接不可为空',
                    type: 'warning'
                });
            }
        },
        //预览
        handlePreview() {
            if (this.shortConUrl != '') {
                window.open('https://' + this.shortConUrl, '_blank');
            } else {
                this.$message({
                    message: '短连接为空，无法预览',
                    type: 'warning'
                });
            }
        },
        //短连接弹框的确定
        shortConDetermine() {
            if (this.shortConUrl != '') {
                this.configurationItem.formData.content += " " + this.shortConUrl + " ";
                this.isShort = true;
                this.shortVisible = false;
            } else {
                this.$message({
                    message: '短链接不可为空',
                    type: 'warning'
                });
            }
        },
        //短链的取消
        HandelCencals() {
            this.shortVisible = false;
            this.isShort = false;
        },
        getTableData() {//获模版取列表数据
            this.tableDataObj.loading2 = true
            let formDatas = this.tabelAlllist;
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate', formDatas, res => {
                this.tableDataObj.tableData = res.records;
                this.tableDataObj.loading2 = false;
                this.pageTotal = res.total;
                this.tabelAlllist.currentPage = res.current;
                this.tabelAlllist.pageSize = res.size
                //                 tabelAlllist:{//------发送表格请求的对象
                //     param:'',
                //     currentPage:1,//当前页
                //     pageSize:10,//每一页条数
                // },
                // pageTotal:0,//总共条数
                // for(var i = 0 ; i<res.records.length ; i++){
                //     if(res.records[i].temFormat==2){
                //         this.tableDataObj.tableData[i].temContent = res.records[i].initialContent
                //     }
                // }
            })
        },
        handleSizeChange(size) { //分页每一页的有几条
            this.tabelAlllist.pageSize = size;
            this.getTableData();
        },
        handleCurrentChange: function (currentPage) {//分页的第几页
            this.tabelAlllist.currentPage = currentPage;
            this.getTableData();
        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveUrl',
        ]),
        logUrl(val) {
            let logUrl = {
                logUrl: val
            }
            this.saveUrl(logUrl);
            window.sessionStorage.setItem('logUrl', val)
        },
    },
    mounted() {
        this.getSignature();
        this.getTemplate();
        this.getTableData();
        this.SmSid = this.$route.query.SmSId
        this.configurationItem.formData.taskSmsId = this.$route.query.SmSId
        if (this.SmSid) {
            this.$api.get(this.API.cpus + 'v3/queryConsumerWebTaskById/' + this.SmSid, {}, res => {
                this.sendTypes = res.sendType
                if (res.sendType == 1) {
                    this.configurationItem.formData.tempId = "2"
                } else {
                    this.configurationItem.formData.tempId = "-1"
                }
                this.configurationItem.formData.mobile = res.mobile
                this.configurationItem.formData.sendTime = res.sendTime
                this.datePluginValueList.datePluginValue = new Date(Date.parse(res.sendTime))
                this.$api.get(this.API.cpus + 'v3/web-task/info/' + this.SmSid, {}, response => {
                    if(response.code == 200){
                        this.configurationItem.formData.content = response.data.content
                    }else{
                        this.$message({
                            message: res.msg,
                            type: 'warning'
                        })
                    }
                   
                })
            })
        }
    },
    watch: {
        templateDialog(val) {
            if (val == false) {
                this.getTableData()
                this.tabelAlllist.param = ""
            }
        },
        downloadtem(val) {
            if (val == "1") {
                this.fileStyle.style = ['xlsx', 'xls']
                this.tip = '仅支持.xlsx .xls 等格式'
                this.configurationItem.formData.mobile = ""
            } else {
                this.fileStyle.style = ['xlsx', 'xls', 'txt']
                this.tip = '仅支持.xlsx .xls.txt 等格式'
            }
        },
        //监听自定义短信的内容
        'configurationItem.formData.content'() {
            this.phoneCenten = this.configurationItem.formData.content
            this.number_zs = this.configurationItem.formData.content.length
            if (this.configurationItem.formData.content.indexOf('{') >= 0 && this.configurationItem.formData.content.indexOf('}') >= 0 && this.configurationItem.formData.tempId != '-1') {
                this.configurationItem.formData.mobile = ''
                this.variableTp = false
            } else {
                this.variableTp = true
            }
        },
        //监听手机框内容的改变
        phoneCenten(val) {
            //模板
            let d1 = /(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}-[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(hh:mm:ss|MM-DD|YYYY-MM-DD|YYYY-MM-DD hh:mm:ss|MM-DD hh:mm:ss)\})/g;
            let a1 = val.match(d1);

            let w1 = val.length;
            let w2 = 0;

            if (a1 == null) {
                this.SMScount.smswordNum = val.length;
            } else {
                let w3 = 0;
                for (let i = 0; i < a1.length; i++) {
                    w2 += a1[i].length;//参数物理长度
                    if (a1[i].substr(-10) == '|hh:mm:ss}') {
                        w3 += 8;
                    } else if (a1[i].substr(-7) == '|MM-DD}') {
                        w3 += 5;
                    } else if (a1[i].substr(-12) == '|YYYY-MM-DD}') {
                        w3 += 10;
                    } else if (a1[i].substr(-21) == '|YYYY-MM-DD hh:mm:ss}') {
                        w3 += 19;
                    } else if (a1[i].substr(-16) == '|MM-DD hh:mm:ss}') {
                        w3 += 14;
                    } else {
                        let num = /[0-9]{1,2}/g;
                        let mun1 = a1[i].match(num);
                        w3 += Number(mun1[mun1.length - 1]);
                    }
                }
                this.SMScount.smswordNum = w1 - w2 + w3;
            }
            //字数和短信条数的显示
            if (this.SMScount.smswordNum == 0) {
                this.SMScount.smssTrip = 0;
            } else if (parseInt(this.SMScount.smswordNum) <= 70 && parseInt(this.SMScount.smswordNum) > 0) {
                this.SMScount.smssTrip = 1;
            } else {
                this.SMScount.smssTrip = Math.ceil((parseInt(this.SMScount.smswordNum)) / 67)
            }
        },
        //短连接弹框是否关闭
        shortVisible(val) {
            if (val == false) {
                this.originalUrl = '';//长链接的值
                this.shortConUrl = '';//短连接的值
            }
        },
    },
}
</script>
<style lang="less" scoped>
/* 基础页面样式 */
.simple-template-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 20px 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
}

.page-content {
  margin: 0 auto;
  padding: 20px 24px;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 按钮样式 */
.action-btn {
  border-radius: 6px;
  font-size: 14px;
  padding: 10px 20px;
  border: 1px solid #d9d9d9;
  background: white;
  color: #333;
  transition: all 0.3s ease;
  font-weight: 500;

  &:hover {
    border-color: #409eff;
    color: #409eff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  }

  &.primary {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border-color: #409eff;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #66b1ff 0%, #85c1ff 100%);
      border-color: #66b1ff;
      color: white;
    }
  }

  &.default-btn {
    &:hover {
      border-color: #d9d9d9;
      color: #333;
      background: #f5f5f5;
    }
  }
}

.action-link {
  color: #409eff;
  margin-right: 8px;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    color: #66b1ff;
  }

  &.info {
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }
}

/* 表格样式 */
.enhanced-table {
  /deep/ .el-table__header th {
    background: #fafafa;
    color: #333;
    font-weight: 500;
    border-bottom: 1px solid #e8e8e8;
    font-size: 14px;
    padding: 12px 0;
  }

  /deep/ .el-table__row:hover {
    background: #f5f7fa;
  }

  /deep/ .el-table__body td {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
  }
}

/* 分页样式 */
.pagination-section {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: center;
}

.simple-pagination {
  /deep/ .el-pagination__total {
    color: #666;
  }

  /deep/ .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
  }

  /deep/ .el-pager li:hover {
    background: #409eff;
    color: white;
  }

  /deep/ .el-pager li.active {
    background: #409eff;
    color: white;
  }
}

/* 导航区域 */
.navigation-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.back-link {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #16a589;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s ease;

  &:hover {
    color: #03886e;
  }
}

.page-indicator {
  color: #666;
  font-size: 14px;
}

/* 表单区域 */
.form-section {
  margin-bottom: 20px;
}

.form-card {
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modern-form {
  padding: 20px;
}

.form-item-modern {
  margin-bottom: 24px;

  /deep/ .el-form-item__label {
    color: #333;
    font-weight: 500;
    font-size: 14px;
  }
}

/* 短信类型选择器 */
.sms-type-selector {
  .modern-select {
    width: 100%;
    max-width: 400px;
  }
}

/* 签名选择器 */
.signature-selector {
  display: flex;
  align-items: center;
  gap: 12px;

  .modern-select {
    width: 364px;
  }
}

/* 短信内容编辑器 */
.sms-content-editor,
.template-content-editor {
  .modern-textarea {
    width: 100%;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #409eff;
    }
  }
}

.template-content-wrapper {
  position: relative;

  .clear-content-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
  }
}

.content-tips {
  margin-top: 12px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

/* 发送时间选择器 */
.send-time-picker {
  margin-top: 12px;

  .modern-datepicker {
    width: 100%;
    max-width: 300px;
  }
}

/* 接收者上传器 */
.recipient-uploader {
  .modern-file-upload {
    margin-bottom: 12px;
  }
}

.upload-tips {
  margin-top: 12px;
}

.upload-notice {
  margin: 16px 0;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;

  p {
    margin: 4px 0;
    font-size: 13px;
    color: #666;
  }

  .download-link {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #66b1ff;
    }
  }
}

/* 手机号码输入 */
.mobile-input-container {
  position: relative;

  .mobile-textarea {
    width: 380px;
    height: 125px;
    border-radius: 6px;
    border: 1px solid #a2dbd0;
    background: #f5f7fa;
    resize: none;

    /deep/ textarea {
      border: none;
      height: 100px;
      resize: none;

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .mobile-counter {
    position: absolute;
    bottom: 8px;
    right: 8px;
    font-size: 12px;
    color: #666;

    &.error {
      color: #f56c6c;
    }
  }
}

.mobile-stats {
  display: flex;
  gap: 16px;
  margin-top: 8px;

  .stat-item {
    font-size: 12px;
    color: #666;

    i {
      font-style: normal;
      margin-left: 4px;
    }
  }
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-left: 95px;
}

/* 规则列表 */
.rule-list {
  list-style: disc;
  margin-left: 20px;
  color: #666;
  font-size: 13px;
  line-height: 1.6;

  li {
    margin-bottom: 8px;
  }
}

/* 预览卡片 */
.preview-card {
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-actions {
  .refresh-btn {
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      color: #409eff;
      background: rgba(64, 158, 255, 0.1);
    }
  }
}

/* 手机预览容器 */
.sms-preview-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.phone-mockup {
  width: 280px;
  height: 500px;
  background: linear-gradient(145deg, #2c3e50, #34495e);
  border-radius: 30px;
  padding: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #1a1a1a;
    border-radius: 2px;
  }
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border-radius: 22px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.phone-header {
  background: #409EFF;
  color: white;
  padding: 0;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px 4px;
  font-size: 12px;
  font-weight: 600;

  .status-icons {
    display: flex;
    gap: 4px;
    font-size: 14px;
  }
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px 12px;
  font-size: 16px;
  font-weight: 600;

  .contact-name {
    font-size: 18px;
  }
}

.message-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  background: #f0f0f0;
}

.message-bubble {
  background: #fff;
  color: #666;
  padding: 12px 16px;
  border-radius: 18px 18px 4px 18px;
  max-width: 85%;
  align-self: flex-end;
  margin-bottom: 8px;
  position: relative;

  .message-text {
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
    margin-bottom: 4px;
  }

  .message-time {
    font-size: 11px;
    opacity: 0.8;
    text-align: right;
  }
}

.empty-message {
  text-align: center;
  color: #999;
  padding: 40px 20px;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

/* 统计信息 */
.preview-stats {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #409eff, #66b1ff);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
  }

  .stat-content {
    flex: 1;

    .stat-value {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #666;
    }
  }
}

/* 字数限制提示 */
.word-limit-tips {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.limit-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 12px;
}

.limit-progress {
  height: 100%;
  background: linear-gradient(90deg, #67c23a, #409eff);
  border-radius: 3px;
  transition: all 0.3s ease;

  &.warning {
    background: linear-gradient(90deg, #409eff, #e6a23c);
  }

  &.danger {
    background: linear-gradient(90deg, #e6a23c, #f56c6c);
  }
}

.limit-text {
  text-align: center;
  font-size: 13px;
  font-weight: 500;

  .text-success {
    color: #67c23a;
  }

  .text-warning {
    color: #e6a23c;
  }

  .text-danger {
    color: #f56c6c;
  }

  i {
    margin-right: 4px;
  }
}

/* 对话框样式 */
.dialog-toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: center;

  .search-input {
    width: 200px;
  }
}

.short-chain-section {
  .short-box {
    margin-bottom: 20px;

    .short-title {
      font-weight: bold;
      padding-bottom: 8px;
      color: #333;
    }

    .width-l {
      margin-bottom: 8px;
    }

    .font-sizes {
      padding-top: 4px;
      font-size: 12px;
      color: #999;
      line-height: 1.4;

      &.font-sizes1 {
        margin-top: 10px;
      }
    }
  }
}

.confirm-content {
  .confirm-item {
    margin-bottom: 12px;
    color: #333;

    .send-content {
      color: #999;
      padding-top: 6px;
      word-wrap: break-word;
    }
  }
}

.dialog-actions {
  text-align: right;
  margin-top: 16px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 文本颜色工具类 */
.text-danger {
  color: #f56c6c !important;
}

.text-success {
  color: #16a589 !important;
}

.text-primary {
  color: #409eff !important;
}

.text-warning {
  color: #e6a23c !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-actions {
    padding-left: 0;
    flex-direction: column;
  }

  .mobile-input-container .mobile-textarea {
    width: 100%;
  }

  .signature-selector {
    flex-direction: column;
    align-items: stretch;

    .modern-select {
      width: 100%;
    }
  }

  .mobile-stats {
    flex-direction: column;
    gap: 8px;
  }

  /* 预览区域响应式 */
  .phone-mockup {
    width: 240px;
    height: 420px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .stat-item {
    padding: 12px;

    .stat-icon {
      width: 32px;
      height: 32px;
      font-size: 14px;
    }

    .stat-content .stat-value {
      font-size: 16px;
    }
  }
}
</style>
