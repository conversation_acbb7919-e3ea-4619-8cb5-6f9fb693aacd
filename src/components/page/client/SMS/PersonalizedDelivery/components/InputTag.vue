<template>
  <div>
    <el-select v-model="value" @change="handChildLabel" placeholder="请选择">
      <el-option
        v-for="item in dynamicTags"
        :key="item.id"
        :label="item.label"
        :value="item.label"
      >
      <span>{{item.label}}</span>
      <span style="float: right;" @click.stop="handleClose(item.id)" ><div style="padding-left: 5px;padding-right: 5px">x</div></span>
      </el-option>
    </el-select>
    <!-- <el-tag
      :key="tag.id"
      v-for="tag in dynamicTags"
      closable
      :disable-transitions="false"
      @close="handleClose(tag.id)"
    >
      {{ tag.label }}
    </el-tag> -->
    <el-input
      class="input-new-tag"
      v-if="inputVisible"
      v-model="inputValue"
      maxlength="20"
      ref="saveTagInput"
      size="small"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
    >
    </el-input>
    <span v-if="inputVisible" style="color:red;font-size:12px;margin-left:10px">标签最多支持20个字符！</span>
    <el-button v-else class="button-new-tag" size="small" @click="showInput"
      >+ 添加标签</el-button
    >
  </div>
</template>

<script>
export default {
  data() {
    return {
      dynamicTags: [],
      inputVisible: false,
      inputValue: "",
      tagsList: [],
      value: "",
    };
  },
  created() {
    this.getLabel();
  },
  methods: {
    getLabel() {
      this.$api.get(this.API.cpus + "consumerlabel/list", {}, (res) => {
        this.dynamicTags = res.data;
        
      });
    },
    handleClose(tag) {
      this.$api.delete(this.API.cpus + "consumerlabel/" + tag, {}, (res) => {
        this.getLabel();
        this.value = ""
      });
    },
  handChildLabel(e){
      this.$emit('childLabelEvent',e)
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue) {
        this.tagsList.push(inputValue);
        this.$api.post(
        this.API.cpus + "consumerlabel",
        {
          label: this.tagsList.join(","),
        },
        (res) => {
          if (res.code == 200) {
            this.getLabel();
            this.$message({
              message: res.msg,
              type: "success",
            });
            this.value = inputValue
            this.$emit('childLabelEvent',this.value)
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        }
      );
      }
      this.inputVisible = false;
    },
  },
  watch: {
    inputVisible(val) {
      if (!val) {
        this.inputValue = "";
        this.tagsList = [];
      }
    },
  },
};
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 180px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>