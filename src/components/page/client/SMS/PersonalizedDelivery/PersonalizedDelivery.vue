<template>
    <div class="personalized-delivery-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-left">
                    <div class="page-title">
                        <i class="el-icon-s-custom page-icon"></i>
                        <span class="title-text">{{ SmSid ? '个性化自定义发送编辑' : '个性化自定义发送' }}</span>
                    </div>
                    <div class="page-description">
                        支持Excel文件批量上传，自定义短信内容模板，实现个性化批量发送
                    </div>
                </div>
                <div class="header-right">
                    <el-tag type="info" size="small">
                        <i class="el-icon-info"></i>
                        个性化发送
                    </el-tag>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <el-row :gutter="24" class="send-form-layout">
                <!-- 左侧表单区域 -->
                <el-col :span="15" class="form-section">
                    <el-card shadow="hover" class="form-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">
                                <i class="el-icon-edit-outline"></i>
                                发送配置
                            </span>
                        </div>

                        <el-form :model="configurationItem.formData" :rules="configurationItem.formRule"
                            ref="configurations" label-width="120px" class="modern-form">
                            <!-- 标签字段 -->
                            <el-form-item label="标签字段" prop="label" class="form-item-modern">
                                <div class="label-field-section">
                                    <input-tag v-on:childLabelEvent="handChildLabel" class="modern-input-tag" />
                                    <!-- <div class="field-tip">
                    <el-alert
                      title="标签字段说明"
                      type="info"
                      :closable="false"
                      show-icon
                    >
                      <template slot="default">
                        用于标识和分类此次发送任务，便于后续查询和管理
                      </template>
</el-alert>
</div> -->
                                </div>
                            </el-form-item>

                            <!-- 任务名称 -->
                            <el-form-item label="任务名称" prop="taskName" class="form-item-modern">
                                <div class="task-name-section">
                                    <el-input v-model="configurationItem.formData.taskName" placeholder="请输入任务名称"
                                        class="modern-input" maxlength="50" show-word-limit />
                                    <div class="task-name-tip">
                                        <i class="el-icon-info"></i>
                                        任务名称将用于发送记录的标识，建议使用有意义的名称
                                    </div>
                                </div>
                            </el-form-item>

                            <!-- 文件上传 -->
                            <el-form-item label="发送对象" class="form-item-modern">
                                <div class="file-upload-section">
                                    <div class="file-upload-wrapper">
                                        <el-upload v-permission class="modern-upload-drag" drag v-if="!SmSid"
                                            :action="this.API.cpus + 'v3/file/upload'" :headers='header' :limit='1'
                                            :on-remove="fileup" :before-upload="beforeAvatarUpload"
                                            :on-success="fileupres" multiple>
                                            <i class="el-icon-upload upload-icon"></i>
                                            <div class="upload-text">将文件拖到此处，或<em>点击上传</em></div>
                                            <div class="upload-hint">支持 .xlsx .xls 格式，文件大小不超过300M</div>
                                        </el-upload>

                                        <div v-if="RowNum" class="upload-result">
                                            <el-alert :title="`本次共上传 ${RowNum} 行数据`" type="success" :closable="false"
                                                show-icon />
                                        </div>
                                    </div>

                                    <div class="upload-tips-section">
                                        <el-alert title="文件格式要求" type="warning" :closable="false" show-icon>
                                            <template slot="default">
                                                <div class="tip-item">• 格式要求：支持 .xlsx .xls 格式，文件大小不超过300M</div>
                                                <div class="tip-item">• 表格格式：<span
                                                        class="highlight">第一列手机号、第二列发送内容</span></div>
                                                <div class="tip-item">• 建议单次最大上传10万行</div>
                                                <div class="tip-item">
                                                    • 内容格式请先下载模板
                                                    <el-button type="text" @click="downloadTems"
                                                        class="download-template-btn">
                                                        <i class="el-icon-download"></i>
                                                        模板下载
                                                    </el-button>
                                                </div>
                                            </template>
                                        </el-alert>
                                    </div>
                                </div>
                            </el-form-item>
                            <!-- 文件内容预览 -->
                            <el-form-item label="文件内容预览" v-if="ArrayData.length != 0" class="form-item-modern">
                                <div class="file-preview-section">
                                    <div class="preview-header">
                                        <div class="preview-title">
                                            <i class="el-icon-view"></i>
                                            数据预览
                                        </div>
                                        <div class="preview-stats">
                                            共 {{ ArrayData.length }} 行数据
                                        </div>
                                    </div>

                                    <div class="preview-table-wrapper">
                                        <el-table element-loading-text="加载中..."
                                            element-loading-spinner="el-icon-loading"
                                            element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
                                            :data="ArrayData" class="modern-table" max-height="300">
                                            <el-table-column label="手机号码" width="150" fixed="left">
                                                <template slot-scope="scope">
                                                    <span class="phone-number">{{ ArrayPhone }}</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column v-for="(item, index) in ArrayData[0]" :key="index"
                                                :label="index" min-width="120">
                                                <template slot-scope="scope">
                                                    <span class="table-content">{{ item }}</span>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </div>
                                </div>
                            </el-form-item>

                            <!-- 短信模板配置 -->
                            <el-form-item label="短信模板" v-if="ArrayData.length != 0" class="form-item-modern">
                                <div class="template-config-section">
                                    <div class="template-preview">
                                        <div class="template-preview-header">
                                            <span class="preview-label">模板预览：</span>
                                        </div>
                                        <el-input v-model="ArrayInput" readonly type="textarea" :rows="3"
                                            class="modern-textarea template-preview-input"
                                            placeholder="请选择字段组合生成短信模板" />
                                    </div>

                                    <div class="template-fields">
                                        <div class="fields-header">
                                            <span class="fields-label">选择字段组合：</span>
                                            <div class="fields-tip">
                                                <i class="el-icon-info"></i>
                                                按选择顺序组合成短信内容
                                            </div>
                                        </div>
                                        <div class="fields-selection">
                                            <el-checkbox-group v-model="ArrayChecked"
                                                @change="CheckedChange(ArrayChecked)" class="modern-checkbox-group">
                                                <div class="checkbox-item">
                                                    <el-checkbox label="手机号码" class="field-checkbox">
                                                        <div class="checkbox-content">
                                                            <span class="checkbox-title">手机号码</span>
                                                            <span class="checkbox-desc">接收短信的手机号码</span>
                                                        </div>
                                                    </el-checkbox>
                                                </div>
                                                <div v-for="(item, index) in ArrayData[0]" :key="index"
                                                    class="checkbox-item">
                                                    <el-checkbox :label="index" class="field-checkbox">
                                                        <div class="checkbox-content">
                                                            <span class="checkbox-title">{{ index }}</span>
                                                            <span class="checkbox-desc">{{ item }}</span>
                                                        </div>
                                                    </el-checkbox>
                                                </div>
                                            </el-checkbox-group>
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                            <!-- 号码去重设置 -->
                            <el-form-item label="号码去重" prop="splitGroup" class="form-item-modern">
                                <div class="deduplication-section">
                                    <el-radio-group v-model="configurationItem.formData.splitGroup"
                                        class="modern-radio-group">
                                        <div class="radio-option">
                                            <el-radio label="0" class="modern-radio">
                                                <div class="radio-content">
                                                    <div class="radio-header">
                                                        <i class="el-icon-check radio-icon"></i>
                                                        <span class="radio-title">启用去重</span>
                                                    </div>
                                                    <!-- <div class="radio-description">相同号码只保留一条记录</div> -->
                                                </div>
                                            </el-radio>
                                        </div>
                                        <div class="radio-option">
                                            <el-radio label="1" class="modern-radio">
                                                <div class="radio-content">
                                                    <div class="radio-header">
                                                        <i class="el-icon-close radio-icon"></i>
                                                        <span class="radio-title">不去重</span>
                                                    </div>
                                                    <!-- <div class="radio-description">保留所有记录，相同号码会拆分为多个文件</div> -->
                                                </div>
                                            </el-radio>
                                        </div>
                                    </el-radio-group>

                                    <div v-if="configurationItem.formData.splitGroup == 1"
                                        class="deduplication-warning">
                                        <el-alert type="warning" :closable="false" show-icon>
                                            <template slot="default">
                                                <i class="el-icon-warning"></i>
                                                相同号码会拆分为多个文件，可能产生重复发送！
                                            </template>
                                        </el-alert>
                                    </div>
                                </div>
                            </el-form-item>

                            <!-- 发送时间 -->
                            <el-form-item label="发送时间" prop="isTiming" class="form-item-modern">
                                <div class="deduplication-section">
                                    <el-radio-group v-model="configurationItem.formData.isTiming"
                                        class="modern-radio-group">
                                        <div class="radio-option">
                                            <el-radio label="0" class="modern-radio">
                                                <div class="radio-content">
                                                    <div class="radio-header">
                                                        <i class="el-icon-position radio-icon"></i>
                                                        <span class="radio-title">立即发送</span>
                                                    </div>
                                                    <div class="radio-description">短信将立即发送到目标手机号码</div>
                                                </div>
                                            </el-radio>
                                        </div>
                                        <div class="radio-option">
                                            <el-radio label="1" class="modern-radio">
                                                <div class="radio-content">
                                                    <div class="radio-header">
                                                        <i class="el-icon-time radio-icon"></i>
                                                        <span class="radio-title">定时发送</span>
                                                    </div>
                                                    <div class="radio-description">设置指定时间发送短信</div>
                                                </div>
                                            </el-radio>
                                        </div>
                                    </el-radio-group>

                                    <div v-if="configurationItem.formData.isTiming == 1" class="timing-picker-section">
                                        <div class="timing-picker-wrapper">
                                            <label class="timing-picker-label">定时时间：</label>
                                            <date-plugin class="modern-date-picker"
                                                :datePluginValueList="datePluginValueList"
                                                @handledatepluginVal="handledatepluginVal" />
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>

                            <!-- 操作按钮 -->
                            <div class="action-buttons">
                                <div class="button-group">
                                    <el-button v-permission type="primary" v-if="sendLoading == true"
                                        @click="submissionItem('configurations')" size="large" class="submit-btn">
                                        <i class="el-icon-s-promotion"></i>
                                        发送短信
                                    </el-button>
                                    <el-button type="primary" v-if="sendLoading == false" :loading="true" size="large"
                                        class="loading-btn">
                                        提交数据中
                                    </el-button>
                                </div>
                            </div>
                        </el-form>
                    </el-card>
                </el-col>
        <!-- 右侧预览区域 -->
        <el-col v-if="isShowBar != 0" :span="9" class="preview-section">
          <!-- 短信预览卡片 -->
          <el-card shadow="hover" class="form-card preview-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-mobile-phone"></i>
                短信预览
              </span>
              <div class="preview-actions">
                <el-tooltip content="刷新预览" placement="top">
                  <el-button type="text" @click="refreshPreview" class="refresh-btn">
                    <i class="el-icon-refresh"></i>
                  </el-button>
                </el-tooltip>
              </div>
            </div>

            <div class="phone-preview">
              <!-- 手机外观容器 -->
              <div class="phone-mockup">
                <div class="phone-frame">
                  <div class="phone-screen">
                    <!-- 手机状态栏 -->
                    <div class="status-bar">
                      <div class="status-left">
                        <span class="time">{{ currentTime }}</span>
                      </div>
                      <div class="status-right">
                        <i class="signal-icon"></i>
                        <i class="wifi-icon"></i>
                        <span class="battery">100%</span>
                      </div>
                    </div>

                    <!-- 短信应用界面 -->
                    <div class="sms-app">
                      <div class="sms-header">
                        <div class="contact-info">
                          <div class="contact-avatar">
                            <i class="el-icon-message"></i>
                          </div>
                          <div class="contact-details">
                            <div class="contact-name">个性化短信</div>
                            <div class="contact-number">{{ previewPhoneNumber }}</div>
                          </div>
                        </div>
                      </div>

                      <!-- 短信内容区域 -->
                      <div class="sms-conversation">
                        <div class="message-container">
                          <div class="message-bubble received">
                            <div class="message-content">
                              <div v-if="!smsPreviewContent" class="placeholder-text">
                                请上传文件并配置短信模板...
                              </div>
                              <div v-else class="sms-text">
                                {{ smsPreviewContent }}
                              </div>
                            </div>
                            <div class="message-time">{{ messageTime }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 手机按钮 -->
                  <div class="phone-button"></div>
                </div>
              </div>

              <!-- 预览统计信息 -->
              <div class="preview-stats">
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-label">字数统计</div>
                    <div class="stat-value">
                      <span class="stat-number" :class="{ 'over-limit': smsPreviewContent.length > 1500 }">
                        {{ smsPreviewContent.length }}
                      </span>
                      <span class="stat-unit">字</span>
                    </div>
                  </div>

                  <div class="stat-item">
                    <div class="stat-label">预计条数</div>
                    <div class="stat-value">
                      <span class="stat-number">{{ estimatedMessages }}</span>
                      <span class="stat-unit">条</span>
                    </div>
                  </div>

                  <!-- <div class="stat-item">
                    <div class="stat-label">数据行数</div>
                    <div class="stat-value">
                      <span class="stat-number">{{ ArrayData.length }}</span>
                      <span class="stat-unit">行</span>
                    </div>
                  </div> -->
                </div>

                <div class="stats-note">
                  <el-alert
                    type="info"
                    :closable="false"
                    show-icon
                  >
                    <template slot="default">
                      <div class="note-content">
                        <div class="note-item">
                          • 70字内（含70字）计1条，超过70字按67字/条计费
                        </div>
                        <div class="note-item">
                          • 只示例展示第一条短信内容，实际发送时每行数据生成对应短信
                        </div>
                      </div>
                    </template>
                  </el-alert>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 发送规则卡片 -->
          <el-card shadow="hover" class="form-card rules-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-warning-outline"></i>
                发送规则
              </span>
            </div>
            <div class="rules-content">
              <el-alert
                title="重要规则"
                type="warning"
                :closable="false"
                show-icon
              >
                <template slot="default">
                  <div class="rules-list">
                    <div class="rule-item">• 发送的短信内容，不能包含变量，且需进入人工审核，待审核完毕后将自动发送</div>
                    <div class="rule-item">• 计费规则：<span class="highlight">70字内（含70字）</span>计一条，超过70字，按<span class="highlight">67字/条</span>计费</div>
                  </div>
                </template>
              </el-alert>
            </div>
          </el-card>

          <!-- 内容规范卡片 -->
          <el-card shadow="hover" class="form-card content-rules-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-document-checked"></i>
                内容规范
              </span>
            </div>
            <div class="content-rules">
              <el-alert
                title="内容规范"
                type="info"
                :closable="false"
                show-icon
              >
                <template slot="default">
                  <div class="rules-list">
                    <div class="rule-item">• 签名内容为：公司或品牌名称，字数要求<span class="highlight">2-12</span>个字符。位于<span class="highlight">每条短信内容的首位【】</span></div>
                    <div class="rule-item">• <span class="highlight">邀请注册、邀请成为会员、邀请加微信、加QQ群</span>的商业性信息不能发送</div>
                    <div class="rule-item">• <span class="highlight">黄、赌、毒</span>犯法等国家法律法规严格禁止的内容不能发送</div>
                    <div class="rule-item">• 包含"股票加群、购物加群、集资贷款、改分、代办大额信用卡、信用卡提额"等疑似诈骗或类似的信息不能发送</div>
                    <div class="rule-item">• 超链接地址请写在短信内容中，便于核实，部分安卓系统存在超链接识别问题，需在超链接前后添加空格</div>
                    <div class="rule-item">• 变量模板中的变量有长度和个数限制，具体请咨询</div>
                  </div>
                </template>
              </el-alert>
            </div>
          </el-card>
        </el-col>
            </el-row>
        </div>
        <!-- <div style="font-size:12px;text-align:center;">当前发送内容 <span style="color:#d20707;">{{SMScount.smswordNum}}</span> 个字，预计发送条数约为 <span style="color:#d20707;">{{ SMScount.smssTrip }}</span> 条短信</div>
        <div style="font-size:12px;text-align:center;color:#d20707;">(实际发送时，如有模板变量会影响计费条数，请注意关注)</div>  -->
        <!-- 短链转换 -->
        <el-dialog title="短链转换" :visible.sync="shortVisible" width="520px">
            <div class="short-box">
                <p class="short-title" style="padding-top:10px">长网址链接</p>
                <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
                    <el-button slot="append" type="primary" icon="el-icon-refresh"
                        @click="transformation()">转换</el-button>
                </el-input>
                <div class="font-sizes font-sizes1"><span style="color:rgb(210, 7, 7)">*
                    </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。</div>
                <div class="font-sizes"><span style="color:rgb(210, 7, 7)">*
                    </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
                </div>
            </div>
            <div class="short-box">
                <p class="short-title" style="padding-top:20px">短网址链接</p>
                <el-input v-model="shortConUrl" class="width-l" :disabled="true">
                    <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share">预览</el-button>
                </el-input>
            </div>
            <div style="text-align:right;margin-top:16px">
                <el-button @click="HandelCencals()">取 消</el-button>
                <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
            </div>
        </el-dialog>
        <!-- 二次确认弹出 -->
        <el-dialog title="确认发送" :visible.sync="ConfirmSend" width="520px">
            <div style="padding-bottom:5px;">您本次提交号码数 <span style="color:#16A589;">{{ sendNumber }}</span> 个</div>
            <div style="padding-top:10px;">发送内容： <div style="color:#999;padding-top:6px;word-wrap:break-word;">
                    {{ sendContent }}
                </div>
            </div>
            <div class="sms-seconnd-steps-btns">
                <el-button type="primary" @click="ConfirmSending()" style="padding:10px 20px;"
                    v-if="fullscreenLoading == true">确定发送</el-button>
                <el-button @click="ConfirmSends">取 消</el-button>
            </div>
        </el-dialog>
        <el-dialog title="实名认证" :visible.sync="dialogSmrzFlag" width="30%" center>
            <span>尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import moment from 'moment'
import { mapActions } from "vuex";
import FileUpload from '@/components/publicComponents/FileUpload' //文件上传
import DatePlugin from '@/components/publicComponents/DatePlugin' //日期
import inputTag from './components/InputTag'
export default {
    name: "PersonalizedDelivery",
    components: {
        FileUpload,
        DatePlugin,
        inputTag
    },
    data() {
        //短信内容的签名格式是否正确，或者是否有签名
        var content = (_, value, callback) => {
            if (value == "") {
                return callback(new Error('请填写自定义内容！'));
            } else {
                // let ret = 3;
                let beg = value.indexOf('【');
                let end = value.indexOf('】');
                let lastbeg = value.lastIndexOf('【');
                let lastend = value.lastIndexOf('】');
                let valLength = value.length;
                if (beg > - 1 && end > -1 && end > beg && beg == 0) {
                    if (beg == 0 && end < 50 && end > 2) {
                        callback();
                    } else {
                        return callback(new Error('请填写正确的签名！'));
                    }
                } else if (lastbeg > -1 && lastend > -1 && lastend > lastbeg && lastend == valLength - 1) {
                    if (lastend == (valLength - 1) && lastend - lastbeg < 49 && lastend - lastbeg > 2) {
                        callback();
                    } else {
                        return callback(new Error('请填写正确的签名！'));
                    }
                } else {
                    return callback(new Error('请填写签名！'));
                }
            }
        };
        // var mobile = (_, value, callback) => {
        //     if (value == "") {
        //         callback();
        //     } else {
        //         if (this.limit > 200) {
        //             callback("已超过填写返回");
        //         }
        //     };
        // }
        return {
            extFlag: false,
            endingName: "",
            fileLists: [],
            fileList: [],
            isShowBar: "0",
            fileFlag: true,
            header: {},
            message: "",
            SmSid: '',
            text: "",
            text1: "",
            exthFlag: false,
            SMScontent: "",//短信内容预览
            ArrayData: [],//文件内容
            ArrayPhone: '',//文件第一列手机号
            ArrayInput: '',
            ArrayChecked: [],
            textareaShow: true,
            dialogSmrzFlag: false,
            ConfirmSend: false,
            limit: 0,//号码限制
            SuccessfullySubmitted: 0, //成功提交号
            filter: 0, //过滤
            invalid: 0, //无效 
            shortVisible: false,//短链弹框
            originalUrl: '',
            shortConUrl: '', //短链的URL
            sendLoading: true,//第一步短信发送的loading按钮
            fullscreenLoading: true, //第二步短信发送的loading按钮
            sendNumber: '', //短信发送条数
            sendContent: "", //发送短信
            copysigCenten: "",//复制签名内容
            copytemCenten: "",//复制模板内容
            phoneCenten: '', //手机短信内容
            del1: true,//关闭弹框时清空图片
            tip: '仅支持.xlsx .xls.txt 等格式',
            sigOptions: [], //签名列表
            temOptions: [],//模板列表
            SMScount: { //手机展示短信内容的下方，短信内容的计数
                smswordNum: '0', //短信内容的个数
                smssTrip: '0' //短信可以分为几条
            },
            RowNum: null,//上传行数
            downloadtem: '2', //模板下载（全文，自定义还是变量）（自定义为2）
            fileStyle: {
                size: 245678943234,
                style: ['xlsx', 'xls', 'txt']
            },
            datePluginValueList: { //日期参数配置
                type: "datetime",
                pickerOptions: {
                    disabledDate(time) {
                        const oneMonthFromNow = Date.now() + 30 * 24 * 60 * 60 * 1000; // 当前时间加上30天
                        return time.getTime() < Date.now() - 8.64e7 || time.getTime() > oneMonthFromNow + 8.64e7; // 禁用掉30天前的时间
                    }
                },
                defaultTime: '', //默认起始时刻
                datePluginValue: ''
            },
            configurationItem: { //发送短信弹出框的值
                formData: {
                    label: "",
                    taskName: "",
                    isTiming: "0", //选择立即发送还是定时发送
                    sendTime: '', //发送时间的值
                    excelIndex: '',//选取的顺序
                    group: '',
                    path: '',
                    mobile: '',
                    files: '',
                    fileName: '',
                    ext: "",
                    splitGroup: "0"
                },
                formDatass: {
                    label: "",
                    taskName: "",
                    isTiming: "0", //选择立即发送还是定时发送
                    sendTime: '', //发送时间的值
                    group: '',
                    path: '',
                    mobile: '',
                    files: '',
                    fileName: '',
                    ext: "",
                    splitGroup: "0"
                },
                formRule: {//验证规则
                    content: [
                        { required: true, validator: content, trigger: 'change' },
                        { min: 1, max: 450, message: '长度在 1 到 450 个字符', trigger: 'change' }
                    ],
                    tempId: [
                        { required: true, message: '请选择模板名称', trigger: 'change' },
                    ],
                    signatureId: [
                        { required: true, message: '请选择签名', trigger: 'change' },
                    ],
                    sendTime: [
                        { required: true, message: '请填写定时时间', trigger: 'change' },
                    ],
                    isTiming: [
                        { required: true, message: '请选择发送时间', trigger: 'change' }
                    ],
                    // mobile:[
                    //     {validator: mobile, trigger: 'change'}
                    // ]
                }
            },
            // 预览相关数据
            currentTime: "",
            messageTime: "",
        }
    },
    computed: {
        // 预计发送条数
        estimatedMessages() {
            const contentLength = this.smsPreviewContent.length;
            if (contentLength <= 70) {
                return 1;
            } else {
                return Math.ceil(contentLength / 67);
            }
        },

        // 短信预览内容
        smsPreviewContent() {
            return this.SMScontent || '';
        },

        // 预览手机号码
        previewPhoneNumber() {
            return this.ArrayPhone || '手机号码';
        }
    },
    created() {
        this.configurationItem.formData.taskName = '任务' + '-' + moment(new Date()).format('YYYYMMDD')
        this.configurationItem.formDatass.taskName = '任务' + '-' + moment(new Date()).format('YYYYMMDD')
        this.header = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
        this.versions()
        this.$api.get(
            this.API.cpus + "consumerclientinfo/getClientInfo",
            null,
            (res) => {
                // console.log(res.data);
                if (res.data.certificate == 0) {
                    this.dialogSmrzFlag = true
                }
                // this.certificate = res.data.certificate;

            }
        );
    },
    methods: {
        // 初始化时间显示
        initTimeDisplay() {
            const now = new Date();
            this.currentTime = now.toTimeString().slice(0, 5); // HH:MM格式
            this.messageTime = now.toTimeString().slice(0, 5);
        },

        // 刷新预览
        refreshPreview() {
            this.initTimeDisplay();
            this.$message.success('预览已刷新');
        },

        versions() {
            //     var u = navigator.userAgent, app = navigator.appVersion;
            //     console.log(u,'u');
            //    var mobile = !!u.match(/AppleWebKit.*Mobile.*/)||!!u.match(/AppleWebKit/)
            //    console.log(mobile,'mobile');
            if ((navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i))) {
                // localStorage.setItem('h5Lg',"0");
                this.isShowBar = 0
                // window.location.href = "mobile.html";
            } else {
                this.isShowBar = 1
                //    localStorage.setItem('h5Lg',"1");
                // window.location.href = "pc.html";
            }
        },
        //返回
        goBack() {
            this.$router.go(-1);
        },
        extflag(val) {
            var r = /^[0-9]*[1-9][0-9]*$/;
            if (!r.test(val)) {
                this.exthFlag = true
                this.text1 = "小号输入有误！"
                if (val == '') {
                    this.text1 = "最大支持4位！"
                }
            } else {
                this.exthFlag = false
                this.text1 = ""
            }
        },
        // cencal(){
        //     this.configurationItem.formData.extType = ''
        //     this.configurationItem.formData.ext = ''
        //     this.exthFlag = false
        // },
        extNum(val) {
            var r = /^[0-9]*[1-9][0-9]*$/;
            // console.log(r.test(val),'r');
            if (val > 4 || !r.test(val)) {
                this.extFlag = true
                if (val > 4 || val == "") {
                    this.text = "仅支持1-4数值之间任意一位扩展"
                } else {
                    this.text = "扩展位数输入有误！"
                }

            } else {
                this.extFlag = false
                this.text = ""
            }
        },
        // extChange(e){
        //     if(e==1){
        //         this.configurationItem.formData.extLength = ''
        //         this.extFlag = false
        //     }else{
        //         this.configurationItem.formData.ext = ''
        //         this.exthFlag = false
        //     }
        //     },
        goSmrz() {
            this.dialogSmrzFlag = false
            this.$router.push("/authentication")
        },
        textChange() {
            this.configurationItem.formData.mobile = this.configurationItem.formData.mobile.replace(/[^\ \d\,]/g, "")
            this.configurationItem.formData.mobile = this.configurationItem.formData.mobile.replace(/\s+/g, ",")
            if (this.configurationItem.formData.mobile[this.configurationItem.formData.mobile.length - 1] == ",") {
                this.limit = this.configurationItem.formData.mobile.split(",").length - 1
            } else {
                this.limit = this.configurationItem.formData.mobile.split(",").length
            }
            if (this.configurationItem.formData.mobile.split(",").length == 1 && this.configurationItem.formData.mobile.split(",")[0] == "") {
                this.limit = 0
            }
        },
        handChildLabel(data) {
            this.configurationItem.formData.label = data
            // console.log(this.configurationItem.formData.label,'data');
        },
        //获得签名列表
        getSignature() {
            this.$api.post(this.API.cpus + 'signature/signatureList', {
                auditStatus: '2',
                currentPage: 1,
                pageSize: 200
            }, res => {
                this.sigOptions = res.records;
            })
        },
        //获得模板名称列表
        getTemplate() {
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate', {
                currentPage: 1,
                pageSize: 200
            }, res => {
                this.temOptions = res.records;
            })
        },
        //获得模板名称对应的内容
        getTemplateContent() {
            if (this.configurationItem.formData.tempId) {
                this.$api.get(this.API.cpus + 'consumersmstemplate/get/' + this.configurationItem.formData.tempId, {}, res => {
                    this.downloadtem = res.data.temFormat; //模板下载类型（1变量，2全文）
                    this.copytemCenten = res.data.temContent;
                    this.phoneCenten = this.copysigCenten + this.copytemCenten;
                })
            }
        },
        // 过滤号码
        FilterNumber() {
            let NumberFilter = this.configurationItem.formData.mobile.split(",")
            let arrNumber = []
            let hash = []
            let reg = /^1\d{10}$/;
            for (var i = 0; i < NumberFilter.length; i++) {
                for (var j = i + 1; j < NumberFilter.length; j++) {
                    if (NumberFilter[i] === NumberFilter[j]) {
                        ++i;
                    }
                }
                arrNumber.push(NumberFilter[i]);
            }
            for (var i = 0; i < arrNumber.length; i++) {
                if (reg.test(arrNumber[i])) {
                    hash.push(arrNumber[i])
                }
            }
            this.configurationItem.formData.mobile = hash.join(",")
            this.SuccessfullySubmitted = hash.length //成功提交号
            this.filter = (NumberFilter.length) - (arrNumber.length) //过滤
            if (arrNumber[0] == "") {
                this.invalid == 0
            } else {
                this.invalid = (arrNumber.length) - (hash.length) //无效 
            }
            this.limit = hash.length
        },
        //获取签名下拉框选择的 签名内容
        changeLocationValue(val) {
            let obj = {};
            obj = this.sigOptions.find((item) => {
                return item.signatureId === val;
            });
            this.copysigCenten = obj.signature;
            //手机短信内容展示
            this.phoneCenten = this.copysigCenten + this.copytemCenten;
        },
        ZH(s) {
            return s.split("").map(function (o) {
                return o.toUpperCase().charCodeAt() - 64;
            }).join("");
        },
        // 选择顺序改变
        CheckedChange(val) {
            this.ArrayInput = val.join(',')
            let a = []
            let b = []
            val.map((item => {
                if (item == "手机号码") {
                    a.push(this.ArrayPhone)
                    b.push('0')
                } else {
                    a.push(this.ArrayData[0][item])
                    b.push(this.ZH(item))
                }
            }))
            this.SMScontent = a.join('')
            this.configurationItem.formData.excelIndex = b.join(',')
        },
        //移除文件
        fileup() {
            this.ArrayData = []
            this.ArrayInput = ''
            this.ArrayChecked = []
            this.RowNum = null
            this.SMScontent = "";//短信预览
            this.configurationItem.formData.files = '';
            this.textareaShow = true
            this.configurationItem.formData.mobile = '';
            this.limit = 0//号码限制
            this.SuccessfullySubmitted = 0//成功提交号
            this.filter = 0 //过滤
            this.invalid = 0 //无效 
        },
        //限制用户上传文件格式和大小
        beforeAvatarUpload(file) {
            let endingCode = file.name;//结尾字符
            this.endingName = endingCode.slice(endingCode.lastIndexOf('.') + 1, endingCode.length);
            let isStyle = false; //文件格式
            const isSize = file.size / 1024 / 1024 < 300
            // const isSize = file.size / 1024 / 1024 < this.fileStyle.size; //文件大小
            console.log(isSize)
            for (let i = 0; i < this.fileStyle.style.length; i++) {
                if (this.endingName === this.fileStyle.style[i]) {
                    isStyle = true;
                    break;
                }
            }
            //不能重复上传文件
            let fileArr = this.fileList;
            let fileNames = [];
            if (fileArr.length > 0) {
                for (let k = 0; k < fileArr.length; k++) {
                    fileNames.push(fileArr[k].name)
                }
            }
            if (fileNames.indexOf(endingCode) !== -1) {
                this.$message.error('不能重复上传文件');
                return false;
            } else if (!isStyle) { //文件格式判断
                this.$message.error(this.tip);
                return false;
            } else {
                //文件大小判断
                if (!isSize) {
                    this.$message.error('上传文件大小不能超过300MB');
                    return false;
                }
            }
        },
        //文件上传成功
        fileupres(val) {
            if (val.code == 200) {
                this.fileFlag = true
                let a = {}
                for (var i = 0; i < val.data.row1.length; i++) {
                    if (i != 0) {
                        a[String.fromCharCode('A'.charCodeAt(0) + i - 1)] = val.data.row1[i]
                    }
                }
                this.ArrayData[0] = a
                this.ArrayPhone = val.data.row1[0]
                if (val.data.total) {
                    this.RowNum = val.data.total
                } else {
                    this.RowNum = null
                }
                // if(val.data.row1.length>1){
                //     this.SMScontent=val.data.row1[1]//短信预览
                // }else{
                //     this.SMScontent=''//短信预览
                // }
                this.configurationItem.formData.group = val.data.group
                this.configurationItem.formData.path = val.data.path
                this.configurationItem.formData.files = val.data.fileName;
                this.configurationItem.formData.mobile = ""
                this.configurationItem.formData.fileName = val.data.fileName
                this.textareaShow = false
                this.del1 = true;
                this.$message({
                    message: val.msg,
                    type: "success",
                });
            } else {
                this.fileFlag = false
                this.message = val.msg
                this.$message({
                    message: val.msg,
                    type: "error",
                });
            }
        },
        //下载模板
        downloadTems() {
            this.$File.export(this.API.cpus + 'v3/consumersms/templateZipDownload', {}, `自定义发送模板.zip`)
        },
        //短信发送提交 (第一步的短信发送)
        submissionItem(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let falgs = false;
                    let falgss = false;
                    let CheckFalg = false;
                    if (!this.fileFlag) {
                        this.$message({
                            message: this.message,
                            type: "error",
                        });
                        return
                    }
                    this.configurationItem.formDatass = Object.assign({}, this.configurationItem.formData);
                    if (this.configurationItem.formDatass.isTiming === '1') { //判断发送时间为定时时间
                        if (this.configurationItem.formDatass.sendTime) {  //如果是定时时间 ，定时时间范围必填
                            let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
                            if (nowTiem < Date.now()) {
                                falgss = false;
                                this.datePluginValueList.datePluginValue = '';
                                this.configurationItem.formData.sendTime = '';
                                this.$message({
                                    message: "定时时间应大于当前时间，需重新设置！",
                                    type: 'warning'
                                });
                            } else {
                                falgss = true;
                            }
                        } else {
                            falgss = false;
                            this.$message({
                                message: '选择定时时间！',
                                type: 'warning'
                            });
                        }
                    } else {
                        falgss = true;
                    }
                    //判断是否上传文件
                    if (this.configurationItem.formDatass.files != '' || this.configurationItem.formDatass.mobile != "") {
                        falgs = true;
                    } else {
                        falgs = false;
                        this.$message({
                            message: '上传发送对象文件!',
                            type: 'warning'
                        });
                    }
                    //判断是否勾选顺序
                    if (this.configurationItem.formData.excelIndex) {
                        CheckFalg = true
                    } else {
                        CheckFalg = false;
                        this.$message({
                            message: '请勾选短信模板!',
                            type: 'warning'
                        });
                    }
                    if (falgs == true && falgss == true && CheckFalg == true && this.extFlag == false && this.exthFlag == false) {
                        this.$confirms.confirmation("post", '确认发送自定义短信？', this.API.cpus + 'v3/consumersms/send/customize/file', this.configurationItem.formData, res => {
                            if (res.code == 200) {
                                this.$message({
                                    message: '短信发送成功！',
                                    type: 'success'
                                });
                                this.logUrl('SendTask')
                                this.$router.push({ path: '/SendTask' });
                                // this.datePluginValueList.datePluginValue='';
                                // this.configurationItem.formData.sendTime = '';
                                // this.configurationItem.formData.isTiming="0";
                                // this.$refs.configurations.resetFields(); //清空表单 
                                // this.configurationItem.formData.mobile=""
                                // this.del1 = false;
                                // this.configurationItem.formData.files='';
                                // this.configurationItem.formData.group='';
                                // this.configurationItem.formData.path='';
                            } else {
                                this.$message({
                                    message: res.msg,
                                    type: 'error'
                                });
                            }
                        })
                    } else {
                        if (this.extFlag == true) {
                            this.$message({
                                message: "扩展位数输入有误！",
                                type: "warning",
                            });
                        }
                        if (this.exthFlag == true) {
                            this.$message({
                                message: "小号输入有误！",
                                type: "warning",
                            })
                        }
                    }
                }
            })

        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveUrl',
        ]),
        logUrl(val) {
            let logUrl = {
                logUrl: val
            }
            this.saveUrl(logUrl);
            window.sessionStorage.setItem('logUrl', val)
        },
        // //确定发送短信 (第二步的短信发送)
        // ConfirmSending(){
        //     this.fullscreenLoading = false;
        //     this.configurationItem.formDatass = Object.assign({},this.configurationItem.formData);
        //     let flags = false;
        //     if(this.configurationItem.formDatass.isTiming === '1' && this.configurationItem.formDatass.sendTime){  //判断是否定时时间 ，定时时间范围
        //         let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
        //         if (nowTiem < Date.now()+600000) {
        //             flags = false;
        //             this.ConfirmSend=false
        //             this.fullscreenLoading = true;
        //             this.$message({
        //                 message: '定时时间已过期，定时时间应大于当前时间10分钟，需重新设置！',
        //                 type: 'warning'
        //             });
        //             this.datePluginValueList.datePluginValue='';
        //             this.configurationItem.formData.sendTime = '';
        //             this.stepsActive = 1;

        //         }else{
        //             flags = true;
        //         }
        //     }else{
        //         flags = true;
        //     }
        //     if(flags === true){
        //         //自定义内容发送
        //         if(this.configurationItem.formDatass.tempId === '-1'){
        //             this.configurationItem.formDatass.tempId = '';
        //             this.configurationItem.formDatass.signatureId = '';
        //             if(this.isShort){
        //                 this.configurationItem.formDatass.shortCode = this.shortCode;
        //             }else{
        //                 this.configurationItem.formDatass.shortCode = '';
        //             }
        //             this.$api.post(this.API.cpus + 'consumersmsinfo/customSendSms',this.configurationItem.formDatass ,res=>{
        //                 this.fullscreenLoading = true;
        //                 // this.sendMsgDialogShow = false;
        //                 this.ConfirmSend = false;
        //                 this.$refs.configurations.resetFields(); //清空表单 
        //                 this.configurationItem.formData.mobile=""
        //                 // this.configurationItem.formDatass.files=""
        //                 this.textareaShow=true
        //                 this.downloadtem='2'
        //                 this.limit=0//号码限制
        //                 this.SuccessfullySubmitted=0//成功提交号
        //                 this.filter=0 //过滤
        //                 this.invalid=0 //无效 
        //                 if(res.code == 200){
        //                     this.$message({
        //                         message: '短信发送成功！',
        //                         type: 'success'
        //                     });
        //                     this.$router.go(-1);
        //                 }else if(res.code == 406){
        //                     this.$message({
        //                         message: '有效短信为0条！',
        //                         type: 'warning'
        //                     })
        //                 }else{
        //                     this.$message({
        //                         message: '短信发送失败！',
        //                         type: 'error'
        //                     });
        //                 }
        //                 this.del1 = false;
        //                 this.configurationItem.formData.files='';
        //                 this.configurationItem.formData.group='';
        //                 this.configurationItem.formData.path='';
        //             })
        //         }else{
        //             //模板内容发送
        //             // this.configurationItem.formDatass.content = '';
        //             this.$api.post(this.API.cpus + 'consumersmsinfo/templateSendSms',this.configurationItem.formDatass ,res=>{
        //                 this.fullscreenLoading = true;
        //                 // this.sendMsgDialogShow = false;
        //                 this.ConfirmSend = false;
        //                 this.$refs.configurations.resetFields(); //清空表单 
        //                 this.configurationItem.formData.mobile=""
        //                 this.textareaShow=true
        //                 this.downloadtem='2'
        //                 this.limit=0//号码限制
        //                 this.SuccessfullySubmitted=0//成功提交号
        //                 this.filter=0 //过滤
        //                 this.invalid=0 //无效 
        //                 if(res.code == 200){
        //                     this.$message({
        //                         message: '短信发送成功！',
        //                         type: 'success'
        //                     });
        //                 this.$router.go(-1);
        //                 }else if(res.code == 406){
        //                     this.$message({
        //                         message: '有效短信为0条！',
        //                         type: 'warning'
        //                     })
        //                 }else{
        //                     this.$message({
        //                         message: '短信发送失败！',
        //                         type: 'error'
        //                     });
        //                 }
        //                 this.del1 = false;
        //                 this.configurationItem.formData.files='';
        //                 this.configurationItem.formData.group='';
        //                 this.configurationItem.formData.path='';
        //             })
        //         }
        //     }
        // },
        // 取消发送
        ConfirmSends() {
            this.ConfirmSend = false
        },
        handledatepluginVal: function (val1) { //日期
            this.configurationItem.formData.sendTime = val1
        },
        //短链转换
        shortReset() {
            this.shortVisible = true;
        },
        //转换
        transformation() {
            if (this.originalUrl != '') {
                this.$api.post(this.API.cpus + 'shortlink/changeUrl', { originalUrl: this.originalUrl }, res => {
                    if (res.code == 200) {
                        this.shortConUrl = res.data.shortLinkUrl;
                        this.shortCode = res.data.shortCode;
                        this.$message({
                            message: '短链接转换成功！',
                            type: 'success'
                        });
                    } else {
                        this.originalUrl = '';
                        this.$message({
                            message: res.msg,
                            type: 'warning'
                        });
                    }
                })
            } else {
                this.$message({
                    message: '长链接不可为空',
                    type: 'warning'
                });
            }
        },
        //预览
        handlePreview() {
            if (this.shortConUrl != '') {
                window.open('https://' + this.shortConUrl, '_blank');
            } else {
                this.$message({
                    message: '短连接为空，无法预览',
                    type: 'warning'
                });
            }
        },
        //短连接弹框的确定
        shortConDetermine() {
            if (this.shortConUrl != '') {
                this.configurationItem.formData.content += " " + this.shortConUrl + " ";
                this.isShort = true;
                this.shortVisible = false;
            } else {
                this.$message({
                    message: '短链接不可为空',
                    type: 'warning'
                });
            }
        },
        //短链的取消
        HandelCencals() {
            this.shortVisible = false;
            this.isShort = false;
        }
    },
    mounted() {
        this.initTimeDisplay();
        this.getSignature();
        this.getTemplate();
        this.SmSid = this.$route.query.SmSId
        if (this.SmSid) {
            this.$api.get(this.API.cpus + 'v3/queryConsumerWebTaskById/' + this.SmSid, {}, () => {

            })
        }

        let a = 'A'
        console.log(a.charCodeAt())
    },
    // activated(){
    //     this.$api.get(
    //     this.API.cpus + "consumerclientinfo/getClientInfo",
    //     null,
    //   (res) => {
    //     // console.log(res.data);
    //     if(res.data.certificate == 0){
    //         this.dialogSmrzFlag = true
    //     }
    //     // this.certificate = res.data.certificate;

    //   }
    // );
    //     this.getSignature();
    //     this.getTemplate();
    // },
    watch: {
        downloadtem(val) {
            if (val == "1") {
                this.fileStyle.style = ['xlsx', 'xls']
                this.tip = '仅支持.xlsx .xls 等格式'
                this.configurationItem.formData.mobile = ""
            } else {
                this.fileStyle.style = ['xlsx', 'xls', 'txt']
                this.tip = '仅支持.xlsx .xls.txt 等格式'
            }
        },
        //监听自定义短信的内容
        'configurationItem.formData.content'() {
            this.phoneCenten = this.configurationItem.formData.content
        },
        //监听是否选择了自定义短信
        'configurationItem.formData.tempId'() {
            //模板
            if (this.configurationItem.formData.tempId != '-1') {
                // if(this.sendMsgDialogShow == true){
                this.getTemplateContent();
                // }
                this.configurationItem.formData.content = '';
            } else {
                //自定义
                this.downloadtem = '2'; //模板下载类型（自定义）
                this.phoneCenten = '';
                this.configurationItem.formData.signatureId = ''; //清除签名id
                this.copysigCenten = ''; //清除签名
            }
        },
        //监听手机框内容的改变
        phoneCenten(val) {
            //模板
            let d1 = /(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}-[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(hh:mm:ss|MM-DD|YYYY-MM-DD|YYYY-MM-DD hh:mm:ss|MM-DD hh:mm:ss)\})/g;
            let a1 = val.match(d1);

            let w1 = val.length;
            let w2 = 0;

            if (a1 == null) {
                this.SMScount.smswordNum = val.length;
            } else {
                let w3 = 0;
                for (let i = 0; i < a1.length; i++) {
                    w2 += a1[i].length;//参数物理长度
                    if (a1[i].substr(-10) == '|hh:mm:ss}') {
                        w3 += 8;
                    } else if (a1[i].substr(-7) == '|MM-DD}') {
                        w3 += 5;
                    } else if (a1[i].substr(-12) == '|YYYY-MM-DD}') {
                        w3 += 10;
                    } else if (a1[i].substr(-21) == '|YYYY-MM-DD hh:mm:ss}') {
                        w3 += 19;
                    } else if (a1[i].substr(-16) == '|MM-DD hh:mm:ss}') {
                        w3 += 14;
                    } else {
                        let num = /[0-9]{1,2}/g;
                        let mun1 = a1[i].match(num);
                        w3 += Number(mun1[mun1.length - 1]);
                    }
                }
                this.SMScount.smswordNum = w1 - w2 + w3;
            }
            //字数和短信条数的显示
            if (this.SMScount.smswordNum == 0) {
                this.SMScount.smssTrip = 0;
            } else if (parseInt(this.SMScount.smswordNum) <= 70 && parseInt(this.SMScount.smswordNum) > 0) {
                this.SMScount.smssTrip = 1;
            } else {
                this.SMScount.smssTrip = Math.ceil((parseInt(this.SMScount.smswordNum)) / 67)
            }
        },
        //短连接弹框是否关闭
        shortVisible(val) {
            if (val == false) {
                this.originalUrl = '';//长链接的值
                this.shortConUrl = '';//短连接的值
            }
        },
    },
}
</script>
<style scoped lang="less">
@media screen and (min-width: 1200px) {
    .el-textarea {
        width: 50%;
    }

    .input_t {
        width: 400px;
    }

    .shortChain-box {
        padding: 20px;
    }

    .sendMsg-box {
        padding: 20px;
    }

    .sendMsg-title {
        margin: 10px 0;
        padding-left: 5px;
        color: #16a589;
    }

    .sendMsg-table {
        margin-top: 12px;
    }

    .sendMsg-list-header {
        padding-top: 30px;
        font-weight: bold;
    }

    .el-steps--simple {
        background: #fff;
        border-radius: 0px;
        border-bottom: 1px solid #f3efef;
    }

    .send-mobel-box {
        width: 260px;
        overflow: hidden;
        position: relative;
        margin-bottom: 35px;
        left: 28%;
    }

    .send-mobel-box img {
        width: 255px;
    }

    .el-select {
        width: 50%;
    }

    .send-upload-tips {
        padding-left: 125px;
        padding-top: 5px;
        font-size: 12px;
    }

    .send-upload-tips span {
        display: inline-block;
        padding-left: 8px;
        color: #0066FF;
    }

    .sms-content-exhibition {
        width: 170px;
        height: 32%;
        border-radius: 10px;
        position: absolute;
        padding: 8px 9px;
        background: #e2e2e2;
        top: 100px;
        left: 31px;
        font-size: 12px;
        line-height: 18px;
        color: #000;
        overflow: hidden;
    }

    /* .sms-first-steps-btns{
    width:517px;
    position: absolute;
    text-align: center;
    bottom: 10px;
} */
    .sms-seconnd-steps-box {
        padding: 30px 20px 30px 55px;
    }

    .sms-seconnd-steps-btns {
        text-align: right;
    }

    .goTiming {
        padding-right: 14px;
        cursor: pointer;
        color: #16a589;
    }

    .goTiming:hover {
        color: #03886e;
    }

    .shortChain-box {
        padding: 20px;
    }

    .shortChain-matter {
        border: 1px solid #66CCFF;
        padding: 10px 14px;
    }

    .short-title {
        font-weight: bolder;
        padding-bottom: 5px;
    }

    .font-sizes {
        padding-top: 2px;
        font-size: 12px;
        color: rgb(163, 163, 163);
    }

    .font-sizes1 {
        margin-top: 10px;
    }
}

@media screen and (max-width: 1200px) {
    .send-upload-tips {
        width: 200px;
        margin-left: 110px;
    }

    .send-upload-tips span {
        display: inline-block;
        padding-left: 8px;
        color: #0066FF;
    }

    .input_t {
        width: 200px;
    }
}

.active {
    font-size: 67px !important;
    color: #C0C4CC !important;
    margin: 40px 0 16px !important;
    line-height: 50px !important;
}
</style>
<style>
@media screen and (min-width: 1200px) {
    .select_s {
        width: 364px;
    }

    .sendMsg-box .el-dialog__body {
        padding: 10px 20px;
    }

    .sendMsg-box .el-form-item__label {
        text-align: left;
    }

    .sendMsg-box .el-step__head.is-process {
        color: #989898;
    }

    .sendMsg-box .el-step__title.is-process {
        color: #989898;
    }

    .sms-content-exhibition .el-scrollbar__wrap {
        overflow-x: hidden;
        margin-bottom: 0 !important;
        margin-right: -27px !important;
    }

    .el-picker-panel .el-button--text {
        display: none;
    }

    .textareas textarea {
        height: 100px;
        resize: none;

    }

    .textareas textarea::-webkit-scrollbar {
        display: none;

    }

    /* .box-textareas{
    width: 500px;
    height: 125px;
    border-radius: 5px;
    border: 1px solid rgb(162, 219, 208);
}
.box-textareas textarea{
    border:none
} */
}

@media screen and (max-width: 1200px) {
    .el-dialog {
        margin-left: 100px;
    }

    .el-radio-group {
        display: flex;
        padding: 10px;
    }

    .el-textarea__inner {
        height: 100px;
    }

    .el-form-item__content {
        width: 200px;
    }

    .el-message-box {
        width: 200px;
    }
}
</style>

<style lang="less" scoped>
// 引入个性化发送通用样式
@import '~@/styles/personalized-delivery-common.less';
</style>