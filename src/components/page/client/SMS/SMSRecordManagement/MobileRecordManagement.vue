<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 简约提醒区域 -->
        <!-- <div class="notice-section">
          <h3 class="notice-title">手机号备案管理</h3>
          <div class="notice-list">
            <div class="notice-item">
              <span class="notice-label">备案要求：</span>发送短信使用的手机号需要进行备案登记，确保手机号实名信息的真实性和有效性。
            </div>
            <div class="notice-item">
              <span class="notice-label">信息完整：</span>请确保填写的姓名、证件类型、证件号码等信息真实有效，与实际持有人信息一致。
            </div>
            <div class="notice-item">
              <span class="notice-label">权限控制：</span>您只能管理自己创建的手机号备案记录，无法操作其他用户的记录。
            </div>
          </div>
        </div> -->

        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button v-permission @click="showAddDialog" class="action-btn primary" icon="el-icon-plus">
                  新增号码备案
                </el-button>
                <el-button v-permission @click="showBatchImportDialog" class="action-btn secondary" icon="el-icon-upload2">
                  批量导入
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ pagination.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ pagination.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="searchForm" :inline="true" ref="searchForm" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="用户名称" prop="clientName" class="search-item">
                    <el-input v-model="searchForm.clientName" placeholder="请输入用户名称" class="search-input" clearable
                      prefix-icon="el-icon-search" />
                  </el-form-item>
                  <el-form-item label="手机号" prop="mobile" class="search-item">
                    <el-input v-model="searchForm.mobile" placeholder="请输入手机号" class="search-input" clearable
                      prefix-icon="el-icon-search" />
                  </el-form-item>
                  <el-form-item label="备案类型" prop="mobileType" class="search-item">
                    <el-select v-model="searchForm.mobileType" placeholder="请选择备案类型" style="width: 100%;" filterable>
                      <el-option label="个人手机" value="1"></el-option>
                      <el-option label="企业手机" value="2"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item class="search-buttons">
                    <el-button type="primary" @click="handleSearch" class="search-btn primary" icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button @click="handleReset" class="search-btn" icon="el-icon-refresh">
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 手机号备案列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">手机号备案列表</h3>
          </div>

          <div class="table-container">
            <el-table v-loading="loading" element-loading-text="正在加载..." element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)" ref="multipleTable" border :data="tableData"
              class="enhanced-table" stripe :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }" empty-text="暂无手机号备案数据">
              <!-- <el-table-column type="index" label="序号" width="90">
                <template slot-scope="scope">
                  {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
                </template>
</el-table-column> -->
              <el-table-column prop="clientName" label="用户名称" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span v-if="scope.row.clientName">
                      {{ scope.row.clientName }}
                    </span>
                    <span v-else>-</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="mobile" label="手机号" width="150">
                <template slot-scope="scope">
                  <div class="content-cell">
                    {{ scope.row.mobile }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="mobileType" label="备案类型" width="100">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <el-tag :type="scope.row.mobileType == 1 ? 'primary' : 'success'" size="small">
                      {{ scope.row.mobileType == 1 ? '个人' : '企业' }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="姓名" width="100">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span>{{ scope.row.name || '-' }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="idType" label="证件" width="100">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span>{{ getIdTypeName(scope.row.idType) }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="idCard" label="证件号" width="100">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span>{{ formatIdCard(scope.row.idCard) }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="主体信息" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <!-- <template v-if="scope.row.mobileType == 1">
                      <div class="info-item">
                        <span class="info-label">姓名：</span>
                        <span>{{ scope.row.name || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="info-label">证件：</span>
                        <span>{{ getIdTypeName(scope.row.idType) }}</span>
                      </div>
                    </template> -->
                    <template>
                      <div class="info-item">
                        <!-- <span class="info-label">公司：</span> -->
                        <span>{{ scope.row.companyName || '-' }}</span>
                      </div>
                      <!-- <div class="info-item">
                        <span class="info-label">联系人：</span>
                        <span>{{ scope.row.contactName || '-' }}</span>
                      </div> -->
                    </template>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="信用代码" min-width="180">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <!-- <template v-if="scope.row.mobileType == '1'">
                      <span v-if="scope.row.idCard">
                        {{ formatIdCard(scope.row.idCard) }}
                      </span>
                      <span v-else>-</span>
                    </template> -->
                    <template>
                      <span v-if="scope.row.creditCode">
                        {{ formatSocialCreditCode(scope.row.creditCode) }}
                      </span>
                      <span v-else>-</span>
                    </template>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="sealImg" label="附件证明" width="180">
                <template slot-scope="scope">
                  <div>
                    <div v-if="getImageArray(scope.row.sealImg).length > 0" class="table-image-container">
                      <el-image 
                        style="width: 50px; height: 50px; cursor: pointer" 
                        :z-index="9999"
                        :src="getImageUrl(getImageArray(scope.row.sealImg)[0])" 
                        fit="cover"
                        :preview-src-list="getImageArray(scope.row.sealImg).map(img => getImageUrl(img))">
                        <template #error>
                          <div class="image-slot">
                            <el-icon>
                              <Picture />
                            </el-icon>
                          </div>
                        </template>
                      </el-image>
                      <div v-if="getImageArray(scope.row.sealImg).length > 1" class="image-count-badge">
                        +{{ getImageArray(scope.row.sealImg).length - 1 }}
                      </div>
                    </div>
                    <span v-else>-</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="创建时间" width="180">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="updateTime" label="更新时间" width="180">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.updateTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="createName" label="创建人" width="120">
                <template slot-scope="scope">
                  <span :class="{ 'current-user': isCurrentUser(scope.row) }">
                    {{ scope.row.createName || '-' }}
                  </span>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="180" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button v-permission type="text" size="small" v-if="canEdit(scope.row)" @click="showEditDialog(scope.row)">
                    <i class="el-icon-edit"></i>
                    编辑
                  </el-button>
                  <el-button v-permission type="text" size="small" style="color: #f56c6c;" v-if="canDelete(scope.row)"
                    @click="handleDelete(scope.row)">
                    <i class="el-icon-delete"></i>
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false"
      @close="handleDialogClose" class="enhanced-dialog">
      <el-form ref="recordForm" :model="recordForm" :rules="formRules" label-width="140px" class="dialog-form">
        <el-form-item label="备案类型" prop="mobileType">
          <el-radio-group v-model="recordForm.mobileType" @change="handleRecordTypeChange">
            <el-radio :label="1">个人号码</el-radio>
            <el-radio :label="2">企业号码</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="号码" prop="mobile">
          <el-input v-model="recordForm.mobile"
            placeholder="请输入号码、固定电话或企业服务号：例如：13800138000 或 01012345678 或 4001234567"
             :disabled="isEdit" />
             <div class="form-item-tip-mobile">
            <i class="el-icon-info"></i>
            <span>企业号码请填写法人姓名和身份证号等信息证明！</span>
          </div>
             <!-- <span  style="font-size: 12px;color: #999;">例如：13800138000 或 010-12345678 或 400-1234567</span> -->
        </el-form-item>
         <!-- 企业信息 -->
         <template v-if="recordForm.mobileType == 2">
          <el-form-item label="公司名称" prop="companyName">
            <el-input v-model="recordForm.companyName" placeholder="请输入公司名称" />
          </el-form-item>

          <el-form-item label="统一社会信用代码" prop="creditCode">
            <el-input v-model="recordForm.creditCode" placeholder="请输入统一社会信用代码" />
          </el-form-item>
        </template>
        <!-- 个人信息 -->
        <template>
          <el-form-item :label="recordForm.mobileType == 1 ? '姓名' : '法人姓名'" prop="name">
            <el-input v-model="recordForm.name" placeholder="请输入姓名" />
          </el-form-item>

          <el-form-item label="证件类型" prop="idType">
            <el-select v-model="recordForm.idType" placeholder="请选择证件类型" style="width: 100%;" filterable>
              <el-option label="A：居民身份证" value="A" />
              <el-option label="B：户口簿" value="B" />
              <el-option label="C：中国人民解放军军人身份证件" value="C" />
              <el-option label="D：中国人民武装警察身份证件" value="D" />
              <el-option label="E：港澳居民来往内地通行证" value="E" />
              <el-option label="F：台湾居民来往大陆通行证" value="F" />
              <el-option label="G：外国人永久居留证" value="G" />
              <el-option label="H：外国公民护照" value="H" />
              <el-option label="I：eID" value="I" />
              <el-option label="J：CTID" value="J" />
              <el-option label="K：前往港澳通行证" value="K" />
              <el-option label="M：往来港澳通行证" value="M" />
              <el-option label="N：电子普通护照" value="N" />
              <el-option label="Q：华侨护照" value="Q" />
              <el-option label="S：港澳居民居住证" value="S" />
              <el-option label="T：台湾居民居住证" value="T" />
            </el-select>
          </el-form-item>

          <el-form-item label="证件号码" prop="idCard">
            <el-input v-model="recordForm.idCard" placeholder="请输入证件号码" />
          </el-form-item>
        </template>

       

        <!-- 备案证明（个人和企业都可以上传） -->
        <el-form-item label="附件证明" prop="sealImg">
          <div class="upload-with-example-mobile">
            <div class="upload-container-enhanced">
              <!-- 已上传的图片列表 -->
              <div v-if="recordForm.sealImg && recordForm.sealImg.length > 0" class="uploaded-images-list">
                <div v-for="(img, index) in recordForm.sealImg" :key="index" class="uploaded-image-card">
                  <div class="image-wrapper">
                    <img :src="getImageUrl(img)" class="uploaded-image" />
                    <!-- <el-image 
                           class="uploaded-image"
                          :z-index="9999"
                          :src="getImageUrl(img)" 
                          fit="cover"
                          :preview-src-list="[getImageUrl(img)]">
                          </el-image> -->
                    <div class="image-overlay">
                      <div class="image-actions">
                        <i class="el-icon-zoom-in" @click.stop="previewAllImages(index)" title="预览"></i>
                        <i class="el-icon-delete" @click.stop="removeImage(index, $event)" title="删除"></i>
                      </div>
                    </div>
                  </div>
                  <div class="image-label">图片 {{ index + 1 }}</div>
                </div>
              </div>

              <!-- 上传按钮 -->
              <div v-if="recordForm.sealImg.length < 3" class="upload-area-enhanced">
                              <el-upload 
                              v-permission
                class="image-uploader-enhanced" 
                :action="uploadUrl" 
                :headers="currentImageHeaders" 
                :show-file-list="false"
                :on-success="handleSealImgSuccess" 
                :on-error="handleUploadError" 
                :before-upload="beforeUpload"
                :on-progress="handleSealImgProgress" 
                accept="image/*">
                  <div class="upload-button-enhanced">
                    <i class="el-icon-plus upload-icon"></i>
                    <div class="upload-text">点击上传</div>
                    <div class="upload-count">{{ recordForm.sealImg.length }}/3</div>
                  </div>
                </el-upload>
              </div>

              <!-- 上传进度 -->
              <div v-if="sealImgUploading" class="upload-progress-wrapper">
                <el-progress :percentage="sealImgProgress" :stroke-width="6" class="upload-progress">
                </el-progress>
                <div class="progress-text">正在上传第{{ recordForm.sealImg.length + 1 }}张图片...</div>
              </div>

              <!-- 提示信息 -->
              <!-- <div class="upload-tips-enhanced">
                <el-alert
                  title="上传要求"
                  type="info"
                  :closable="false"
                  show-icon>
                  <template slot="description">
                    <div class="tips-list">
                      <div>• 支持 JPG、PNG 格式</div>
                      <div>• 单个文件不超过 2MB</div>
                      <div>• 最多可上传 3 张图片</div>
                    </div>
                  </template>
                </el-alert>
              </div> -->
            </div>
            
            <!-- 示例图片区域 -->
            <div class="example-image-container-mobile">
              <div class="example-label">示例图片</div>
              <div class="example-images-grid-mobile">
                <template>
                  <div class="example-item-mobile" @click="showExampleImages('number')">
                    <img src="../../../.././../assets/images/number1.png" alt="号码印章授权证明" class="example-image-mobile" />
                    <div class="example-item-label-mobile">号码印章授权证明</div>
                  </div>
                  <div class="example-item-mobile" @click="showExampleImages('qicc')">
                    <img src="../../../.././../assets/images/QICC2.png" alt="企业信用证明" class="example-image-mobile" />
                    <div class="example-item-label-mobile">企业信用证明</div>
                  </div>
                  <div class="example-item-mobile" @click="showExampleImages('webpc')">
                    <img src="../../../.././../assets/images/WEBPC.png" alt="网站备案证明" class="example-image-mobile" />
                    <div class="example-item-label-mobile">网站备案证明</div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          
          
          <!-- <div class="form-item-tip-mobile" v-else>
            <i class="el-icon-warning"></i>
            <span>企业号码备案需要提供营业执照、统一社会信用代码证书等企业相关证明材料</span>
          </div> -->
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-permission type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="imageDialogVisible" width="800px" append-to-body>
      <el-image style="width: 100%; height: 100%" :src="url" :preview-src-list="srcList">
      </el-image>
    </el-dialog>
    
    <!-- 示例图片查看对话框 -->
    <el-dialog :visible.sync="exampleDialogVisible" width="900px" title="示例图片管理" class="example-dialog-mobile">
      <div class="example-viewer-mobile" v-if="currentExampleImages.length > 0">
        <div class="example-header-mobile">
          <h3 class="example-title-mobile">{{ currentExampleImages[currentExampleIndex].title }}</h3>
          <div class="example-counter-mobile">{{ currentExampleIndex + 1 }} / {{ currentExampleImages.length }}</div>
        </div>
        
        <div class="example-content-mobile">
          <div class="example-image-wrapper-mobile">
            <el-image 
              :src="currentExampleImages[currentExampleIndex].url" 
              fit="contain"
              style="width: 100%; height: 400px;"
              :preview-src-list="currentExampleImages.map(item => item.url)"
              :initial-index="currentExampleIndex"
            />
          </div>
          
          <div class="example-description-mobile">
            <p>{{ currentExampleImages[currentExampleIndex].description }}</p>
          </div>
        </div>
        
        <div class="example-navigation-mobile" v-if="currentExampleImages.length > 1">
          <el-button 
            size="small" 
            :disabled="currentExampleIndex === 0"
            @click="previousExample"
            icon="el-icon-arrow-left"
          >
            上一张
          </el-button>
          <el-button 
            size="small" 
            :disabled="currentExampleIndex === currentExampleImages.length - 1"
            @click="nextExample"
            icon="el-icon-arrow-right"
          >
            下一张
          </el-button>
        </div>
        
        <div class="example-thumbnails-mobile" v-if="currentExampleImages.length > 1">
          <div 
            v-for="(item, index) in currentExampleImages" 
            :key="index"
            class="thumbnail-item-mobile"
            :class="{ active: index === currentExampleIndex }"
            @click="currentExampleIndex = index"
          >
            <img :src="item.url" :alt="item.title" />
            <span class="thumbnail-title-mobile">{{ item.title }}</span>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog title="批量导入号码备案" :visible.sync="batchImportDialogVisible" width="600px" :close-on-click-modal="false" class="enhanced-dialog">
      <div class="batch-import-container">
        <!-- 提示信息 -->
        <el-alert
          title="批量导入说明"
          type="info"
          :closable="false"
          show-icon
          class="import-alert">
          <template slot="description">
            <div class="import-tips">
              <div>• 支持 .xlsx 格式文件，单个文件不超过 10MB</div>
              <div>• 请下载模板文件，按模板格式填写数据</div>
              <div>• 导入前请确保数据格式正确，避免导入失败</div>
            </div>
          </template>
        </el-alert>

        <!-- 模板下载 -->
        <div class="template-download-section">
          <h4 class="section-title">模板下载</h4>
          <div class="download-area">
            <el-button type="primary" icon="el-icon-download" @click="downloadTemplate">
              下载导入模板
            </el-button>
            <span class="download-tip">请下载模板后，按模板格式填写数据再上传</span>
          </div>
        </div>

        <!-- 文件上传 -->
        <div class="file-upload-section">
          <h4 class="section-title">文件上传</h4>
          <el-upload
            ref="batchUpload"
            class="batch-upload"
            drag
            :action="batchUploadUrl"
            :headers="currentBatchHeaders"
            :before-upload="beforeBatchUpload"
            :on-success="handleBatchUploadSuccess"
            :on-error="handleBatchUploadError"
            :on-progress="handleBatchUploadProgress"
            :show-file-list="false"
            accept=".xlsx">
            <div class="upload-content">
              <i class="el-icon-upload upload-icon"></i>
              <div class="upload-text">
                <div class="main-text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="sub-text">只能上传 .xlsx 文件，且不超过 10MB</div>
              </div>
            </div>
          </el-upload>

          <!-- 上传进度 -->
          <div v-if="batchUploading" class="upload-progress-section">
            <el-progress :percentage="batchUploadProgress" :stroke-width="8"></el-progress>
            <div class="progress-text">正在上传文件，请稍候...</div>
          </div>
        </div>

        <!-- 导入结果 -->
        <div v-if="importResult" class="import-result-section">
          <h4 class="section-title">导入结果</h4>
          <div class="result-summary">
            <div class="result-item" :class="importResult.success ? 'success' : 'error'">
              <i :class="importResult.success ? 'el-icon-success' : 'el-icon-error'"></i>
              <span class="result-status">{{ importResult.success ? '导入成功' : '导入失败' }}</span>
            </div>
          </div>
          
          <!-- 结果消息 -->
          <div class="result-message">
            <div class="message-content" :class="importResult.success ? 'success-message' : 'error-message'">
              <p>{{ importResult.message }}</p>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeBatchImportDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment';
import getNoce from '@/plugins/getNoce';

export default {
  name: 'MobileRecordManagement',
  data() {
    // 手机号验证规则
    const validateMobile = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号'));
        return;
      }
      // 号码不能有特殊符号正则
      const specialSymbolPattern = /[^\d]/;
      if (specialSymbolPattern.test(value)) {
        callback(new Error('固定电话号码不支持有特殊符号；例如：010-12345678、（01012345678）等，请更换成01012345678'));
        return;
      }
      // 手机号格式：1开头11位
      // const mobilePattern = /^1[3-9]\d{9}$/;
      // // 固定电话格式：支持带-和不带-的格式
      // const landlinePattern = /^0\d{2,3}-?\d{7,8}$/;
      // // 400/800等企业服务号码：支持多种格式
      // const servicePattern = /^(400|800)(-\d{3}-\d{4}|-\d{7}|\d{7})$/;

      // if (!mobilePattern.test(value) && !landlinePattern.test(value) && !servicePattern.test(value)) {
      //   callback(new Error('请输入正确的手机号、固定电话或企业服务号码'));
      //   return;
      // }

      callback();
    };

    // 证件号码验证规则
    const validateIdCard = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入证件号码'));
        return;
      }

      if (this.recordForm.idType == 'A') {
        // 居民身份证号码验证
        const idCardPattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!idCardPattern.test(value)) {
          callback(new Error('请输入正确的身份证号码'));
          return;
        }
      }
      // 其他证件类型暂不做特殊验证，只验证长度
      if (value && (value.length < 1 || value.length > 30)) {
        callback(new Error('证件号码长度应在1-30位之间'));
        return;
      }
      callback();
    };

    // 统一社会信用代码验证规则
    // const validateSocialCreditCode = (rule, value, callback) => {
    //   if (this.recordForm.mobileType == 2 && !value) {
    //     callback(new Error('请输入统一社会信用代码'));
    //     return;
    //   }

    //   if (this.recordForm.mobileType == 2) {
    //     // 统一社会信用代码格式验证（18位字符，包含数字和字母）
    //     const codePattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
    //     if (!codePattern.test(value)) {
    //       callback(new Error('请输入正确的统一社会信用代码格式'));
    //       return;
    //     }
    //   }
    //   callback();
    // };

    // 公司名称验证规则
    // const validateCompanyName = (rule, value, callback) => {
    //   if (this.recordForm.mobileType == 2 && !value) {
    //     callback(new Error('请输入公司名称'));
    //     return;
    //   }
    //   callback();
    // };

    // // 联系人姓名验证规则
    // const validateContactName = (rule, value, callback) => {
    //   if (this.recordForm.mobileType == 2 && !value) {
    //     callback(new Error('请输入联系人姓名'));
    //     return;
    //   }
    //   callback();
    // };

    return {
      loading: false,
      submitLoading: false,
      dialogVisible: false,
      isEdit: false,
      currentRecord: null,
      headers: {},
      // 上传进度相关
      sealImgUploading: false,
      sealImgProgress: 0,

      // 图片预览相关
      imageDialogVisible: false,
      url: '',
      srcList: [],
      
      // 示例图片相关
      exampleDialogVisible: false,
      currentExampleImages: [],
      currentExampleIndex: 0,

      // 批量导入相关
      batchImportDialogVisible: false,
      batchUploading: false,
      batchUploadProgress: 0,
      importResult: null,
      currentBatchHeaders: null,
      currentImageHeaders: null,
      
      // 示例图片数据
      exampleImagesData: {
        number: [
          {
            url: require('@/assets/images/number1.png'),
            title: '号码印章授权证明',
            description: '号码印章授权证明'
          }
        ],
        qicc: [
          {
            url: require('@/assets/images/QICC2.png'),
            title: '企业信用证明',
            description: '企业信用证明'
          }
        ],
        webpc: [
          {
            url: require('@/assets/images/WEBPC.png'),
            title: '网站备案证明',
            description: '网站备案证明'
          }
        ],
      },

      // 搜索表单
      searchForm: {
        clientName: '',
        mobile: '',
        mobileType: ''
      },

      // 分页信息
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 表格数据
      tableData: [],

      // 表单数据
      recordForm: {
        mobile: '',
        name: '',
        idType: 'A',
        idCard: '',
        mobileType: 1,
        companyName: '',
        creditCode: '',
        sealImg: []
        // contactName: ''
      },

      // 表单验证规则
      formRules: {
        mobileType: [
          { required: true, message: '请选择备案类型', trigger: 'change' }
        ],
        mobile: [
          { required: true, validator: validateMobile, trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 1, max: 50, message: '姓名长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        idType: [
          { required: true, message: '请选择证件类型', trigger: 'change' }
        ],
        idCard: [
          { required: true, validator: validateIdCard, trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入公司名称', trigger: 'blur' },
          { min: 1, max: 100, message: '公司名称长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
        ]
      },

      // 证件类型映射
      idTypeMap: {
        'A': '居民身份证',
        'B': '户口簿',
        'C': '中国人民解放军军人身份证件',
        'D': '中国人民武装警察身份证件',
        'E': '港澳居民来往内地通行证',
        'F': '台湾居民来往大陆通行证',
        'G': '外国人永久居留证',
        'H': '外国公民护照',
        'I': 'eID',
        'J': 'CTID',
        'K': '前往港澳通行证',
        'M': '往来港澳通行证',
        'N': '电子普通护照',
        'Q': '华侨护照',
        'S': '港澳居民居住证',
        'T': '台湾居民居住证'
      }
    };
  },

  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑号码备案' : '新增号码备案';
    },

    // 获取当前用户信息
    currentUserInfo() {
      return JSON.parse(localStorage.getItem('userInfo') || '{}');
    },

    // 获取当前用户ID
    currentUserId() {
      return this.currentUserInfo.userId || this.currentUserInfo.id;
    },

    // 文件上传地址
    uploadUrl() {
      return this.API.cpus + "v3/file/upload";
    },

    // 批量上传地址
    batchUploadUrl() {
      return this.API.cpus + "consumersmstemplatereportmobile/batchUpload";
    }
  },

  created() {
    // 初始化用户名称查询，默认获取本账号数据
    this.initializeUserSearch();
    this.loadData();
    this.initHeaders();
  },

  methods: {
    // 加载数据
    loadData() {
      this.loading = true;
      const params = {
        currentPage: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ...this.searchForm
      };

      this.$api.post(
        this.API.cpus + 'consumersmstemplatereportmobile/page',
        params,
        (res) => {
          this.loading = false;
          if (res.code === 200) {
            this.tableData = res.data.records || [];
            this.pagination.total = res.data.total || 0;
          } else {
            this.$message.error(res.msg || '获取数据失败');
          }
        },
        (error) => {
          this.loading = false;
          console.error('加载数据失败:', error);
          this.$message.error('获取数据失败，请稍后重试');
        }
      );
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.loadData();
    },

    // 重置搜索
    handleReset() {
      const userInfo = this.currentUserInfo;
      this.searchForm = {
        clientName: userInfo.username || '',
        mobile: '',
        mobileType: ''
      };
      this.pagination.currentPage = 1;
      this.loadData();
    },

    // 初始化用户搜索
    initializeUserSearch() {
      // 默认填入当前用户名称，查询本账号的数据
      const userInfo = this.currentUserInfo;
      this.searchForm.clientName = userInfo.username || '';
    },

    // 初始化请求头
    initHeaders() {
      this.headers = { 
        Authorization: "Beare " + this.$common.getCookie("ZTGlS_TOKEN")
      };
    },

    // 动态生成带Once的请求头
    async generateHeadersWithOnce() {
      try {
        const once = await getNoce.useNonce();
        console.log(once,'once');
        
        return {
          Authorization: "Bearer " + this.$common.getCookie("ZTGlS_TOKEN"),
          Once: once
        };
      } catch (error) {
        console.error('获取Once失败:', error);
        // 如果获取Once失败，返回基本的headers
        return {
          Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN")
        };
      }
    },

    // 备案类型变更处理
    handleRecordTypeChange(value) {
      // 清除表单验证错误
      this.$nextTick(() => {
        this.$refs.recordForm.clearValidate();
      });

      // 根据类型重置相应字段
      if (value === '1') {
        this.recordForm.companyName = '';
        this.recordForm.creditCode = '';
        // this.recordForm.contactName = '';
      } else if (value === '2') {
        this.recordForm.name = '';
        this.recordForm.idType = 'A';
        this.recordForm.idCard = '';
      }
    },

    // 显示新增对话框
    async showAddDialog() {
      this.isEdit = false;
      this.currentRecord = null;
      this.recordForm = {
        mobile: '',
        name: '',
        idType: 'A',
        idCard: '',
        mobileType: 1,
        companyName: '',
        creditCode: '',
        sealImg: []
        // contactName: ''
      };
      this.currentImageHeaders = await this.generateHeadersWithOnce();
      this.dialogVisible = true;
    },

    // 显示编辑对话框
    async showEditDialog(record) {
      // 检查权限
      this.currentImageHeaders = await this.generateHeadersWithOnce();
      if (!this.canEdit(record)) {
        this.$message.warning('您没有权限编辑此记录');
        return;
      }

      this.isEdit = true;
      this.currentRecord = record;
      
      // 处理sealImg：如果是字符串则转换为数组，如果是数组则直接使用
      let sealImgArray = [];
      if (record.sealImg) {
        if (typeof record.sealImg === 'string') {
          // 字符串格式，按逗号分割
          sealImgArray = record.sealImg.split(',').filter(img => img.trim() !== '');
        } else if (Array.isArray(record.sealImg)) {
          // 数组格式，直接使用
          sealImgArray = record.sealImg;
        }
      }

      this.recordForm = {
        mobile: record.mobile || '',
        name: record.name || '',
        idType: record.idType || 'A',
        idCard: record.idCard || '',
        id: record.id,
        mobileType: record.mobileType || 1,
        companyName: record.companyName || '',
        creditCode: record.creditCode || '',
        sealImg: sealImgArray
      };
      this.dialogVisible = true;
    },

    // 关闭对话框
    handleDialogClose() {
      this.$refs.recordForm.clearValidate();
    },

    // 提交表单
    handleSubmit() {
      this.$refs.recordForm.validate((valid) => {
        if (!valid) return;

        this.submitLoading = true;
        if (this.isEdit) {
          this.updateRecord();
        } else {
          this.addRecord();
        }
        this.submitLoading = false;
      });
    },

    // 新增记录
    addRecord() {
      // 准备提交的数据，将图片数组转换为逗号分隔的字符串
      const submitData = {
        ...this.recordForm,
        sealImg: this.recordForm.sealImg && this.recordForm.sealImg.length > 0 
          ? this.recordForm.sealImg.join(',') 
          : ''
      };

      this.$api.post(
        this.API.cpus + 'consumersmstemplatereportmobile',
        submitData,
        (res) => {
          if (res.code === 200) {
            this.$message.success('新增成功');
            this.dialogVisible = false;
            this.loadData();
          } else {
            this.$message.error(res.msg || '新增失败');
          }
        },
        (error) => {
          console.error('新增失败:', error);
          this.$message.error('新增失败，请稍后重试');
        }
      );
    },

    // 更新记录
    updateRecord() {
      // 准备提交的数据，将图片数组转换为逗号分隔的字符串
      const submitData = {
        ...this.recordForm,
        sealImg: this.recordForm.sealImg && this.recordForm.sealImg.length > 0 
          ? this.recordForm.sealImg.join(',') 
          : ''
      };

      this.$api.put(
        this.API.cpus + 'consumersmstemplatereportmobile',
        submitData,
        (res) => {
          if (res.code === 200) {
            this.$message.success('更新成功');
            this.dialogVisible = false;
            this.loadData();
          } else {
            this.$message.error(res.msg || '更新失败');
          }
        },
        (error) => {
          console.error('更新失败:', error);
          this.$message.error('更新失败，请稍后重试');
        }
      );
    },

    // 删除记录
    handleDelete(record) {
      // 检查权限
      if (!this.canDelete(record)) {
        this.$message.warning('您没有权限删除此记录');
        return;
      }

      this.$confirm('确认删除这条手机号备案记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.delete(
          this.API.cpus + `consumersmstemplatereportmobile/${record.id}`,
          {},
          (res) => {
            if (res.code === 200) {
              this.$message.success('删除成功');
              this.loadData();
            } else {
              this.$message.error(res.msg || '删除失败');
            }
          },
          (error) => {
            console.error('删除失败:', error);
            this.$message.error('删除失败，请稍后重试');
          }
        );
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadData();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadData();
    },

    // 格式化身份证号（脱敏显示）
    formatIdCard(idCard) {
      if (!idCard) return '-';
      if (idCard.length < 8) return idCard;
      return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4);
    },

    // 格式化统一社会信用代码（脱敏显示）
    formatSocialCreditCode(code) {
      if (!code) return '-';
      if (code.length < 8) return code;
      return code.substring(0, 4) + '****' + code.substring(code.length - 4);
    },

    // 获取证件类型名称
    getIdTypeName(idType) {
      return this.idTypeMap[idType] || '未知';
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-';
      return moment(date).format('YYYY-MM-DD HH:mm:ss');
    },

    // 判断是否可以操作该记录
    canOperate(record) {
      // 基于 allowOperate 字段判断
      return record.allowOperate || false;
    },

    // 判断是否可以编辑
    canEdit(record) {
      // 基于 allowOperate 字段判断
      return record.allowOperate || false;
    },

    // 判断是否可以删除
    canDelete(record) {
      // 基于 allowOperate 字段判断
      return record.allowOperate || false;
    },

    // 判断是否是当前用户创建的记录
    isCurrentUser(record) {
      return this.canOperate(record);
    },

    // 文件上传前校验
    async beforeUpload(file) {
      // 检查图片数量限制
      if (this.recordForm.sealImg.length >= 3) {
        this.$message.error('最多只能上传3张图片!');
        return false;
      }

      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('上传文件只能是图片格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
        return false;
      }

      try {
        // 动态生成带Once的headers并更新
        this.currentImageHeaders = await this.generateHeadersWithOnce();
        
      } catch (error) {
        console.error('生成图片上传headers失败:', error);
        // 即使获取Once失败，也允许上传，使用基本headers
      }

      return true;
    },

    // 处理上传进度
    handleSealImgProgress(event, file, fileList) {
      this.sealImgUploading = true;
      this.sealImgProgress = Math.round(event.percent);
    },

    // 备案证明上传成功
    handleSealImgSuccess(res, file) {
      this.sealImgUploading = false;
      this.sealImgProgress = 0;
      
      // 清理临时headers
      // this.currentImageHeaders = null;
      
      if (res.code === 200) {
        if (!this.recordForm.sealImg) {
          this.recordForm.sealImg = [];
        }
        this.recordForm.sealImg.push(res.data.fullpath);
        this.$message.success('备案证明上传成功');
      } else {
        this.$message.error(res.msg || '图片上传失败');
      }
    },

    // 文件上传失败
    handleUploadError(err, file, fileList) {
      this.sealImgUploading = false;
      this.sealImgProgress = 0;
      
      // 清理临时headers
      // this.currentImageHeaders = null;
      
      this.$message.error('图片上传失败，请重试');
    },

    // 获取图片URL
    getImageUrl(path) {
      if (!path) return '';
      if (path.startsWith('http')) {
        return path;
      }
      return this.API.imgU + path;
    },

    // 预览所有图片
    previewAllImages(startIndex = 0) {
      if (this.recordForm.sealImg && this.recordForm.sealImg.length > 0) {
        this.srcList = this.recordForm.sealImg.map(img => this.getImageUrl(img));
        this.url = this.srcList[startIndex];
        this.imageDialogVisible = true;
      }
    },

    // 删除指定图片
    removeImage(index, event) {
      // 阻止事件冒泡，防止触发上传
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }
      this.recordForm.sealImg.splice(index, 1);
      this.$message.success('图片已移除');
    },

    // 移除备案证明（兼容旧方法）
    removeSealImg(event) {
      // 阻止事件冒泡，防止触发上传
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }
      this.recordForm.sealImg = [];
      this.sealImgUploading = false;
      this.sealImgProgress = 0;
      this.$message.success('备案证明已移除');
    },

    // 获取图片数组（统一处理字符串和数组格式）
    getImageArray(imageData) {
      if (!imageData) return [];
      
      if (typeof imageData === 'string') {
        // 字符串格式，按逗号分割
        return imageData.split(',').filter(img => img.trim() !== '');
      } else if (Array.isArray(imageData)) {
        // 数组格式，直接使用
        return imageData;
      }
      
      return [];
    },
    
    // 显示示例图片管理
    showExampleImages(type, index = 0) {
      this.currentExampleImages = this.exampleImagesData[type] || [];
      this.currentExampleIndex = index;
      this.exampleDialogVisible = true;
    },
    
    // 上一张示例图片
    previousExample() {
      if (this.currentExampleIndex > 0) {
        this.currentExampleIndex--;
      }
    },
    
    // 下一张示例图片
    nextExample() {
      if (this.currentExampleIndex < this.currentExampleImages.length - 1) {
        this.currentExampleIndex++;
      }
    },

    // 显示批量导入对话框
    async showBatchImportDialog() {
      this.batchImportDialogVisible = true;
      this.importResult = null;
      this.batchUploading = false;
      this.batchUploadProgress = 0;
      this.currentBatchHeaders = await this.generateHeadersWithOnce();
    },

    // 关闭批量导入对话框
    closeBatchImportDialog() {
      this.batchImportDialogVisible = false;
      this.importResult = null;
      this.batchUploading = false;
      this.batchUploadProgress = 0;
      // 重新加载数据以显示导入的记录
      this.loadData();
    },

    // 下载导入模板
    downloadTemplate() {
      // 这里可以根据实际情况实现模板下载
      // 方案1: 如果后端提供模板下载接口
      // const downloadUrl = 'https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/26469a9fa240dccdd9c94af11a97e230';
      // const link = document.createElement('a');
      // link.href = downloadUrl;
      // link.download = '号码备案导入模板.xlsx';
      // link.style.display = 'none';
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
      
      // 方案2: 如果是前端本地模板文件
      const templateUrl = 'https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/26469a9fa240dccdd9c94af11a97e230';
      const link = document.createElement('a');
      link.href = templateUrl;
      link.download = '号码备案导入模板.xlsx';
      link.click();
    },

    // 批量上传前校验
    async beforeBatchUpload(file) {
      // 检查文件类型
      const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.name.toLowerCase().endsWith('.xlsx');
      if (!isXlsx) {
        this.$message.error('只能上传 .xlsx 格式的文件!');
        return false;
      }

      // 检查文件大小 (10MB)
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }

      try {
        // 动态生成带Once的headers并更新
        this.currentBatchHeaders = await this.generateHeadersWithOnce();
        
      } catch (error) {
        console.error('生成上传headers失败:', error);
        this.$message.error('准备上传失败，请重试');
        return false;
      }

      this.batchUploading = true;
      this.batchUploadProgress = 0;
      this.importResult = null;
      return true;
    },

    // 批量上传进度
    handleBatchUploadProgress(event, file, fileList) {
      this.batchUploadProgress = Math.round(event.percent);
    },

    // 批量上传成功
    handleBatchUploadSuccess(res, file) {
      this.batchUploading = false;
      this.batchUploadProgress = 0;
      
      // 清理临时headers
      // this.currentBatchHeaders = null;
      
      if (res.code === 200 || res.code === '200') {
        // 导入成功
        this.importResult = {
          success: true,
          message: res.msg || '批量导入成功'
        };
        this.$message.success('批量导入成功！');
      } else {
        // 导入失败
        this.importResult = {
          success: false,
          message: res.msg || '批量导入失败'
        };
        this.$message.error(res.msg || '批量导入失败');
      }
    },

    // 批量上传失败
    handleBatchUploadError(err, file, fileList) {
      this.batchUploading = false;
      this.batchUploadProgress = 0;
      
      // 清理临时headers
      // this.currentBatchHeaders = null;
      
      this.$message.error('文件上传失败，请重试');
      console.error('批量上传失败:', err);
    }
  }
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* MobileRecordManagement 特有样式 */
.search-input {
  width: 200px; // 适合手机号输入的宽度
}

/* 权限控制相关样式 */
.current-user {
  color: #67c23a;
  font-weight: 500;
}

.no-permission-tip {
  color: #f56c6c;
  font-size: 12px;
  font-style: italic;
}

/* 内容单元格样式 */
.content-cell {
  word-break: break-all;
  line-height: 1.4;
}

/* 信息项样式 */
.info-item {
  margin-bottom: 4px;
  font-size: 13px;

  .info-label {
    font-weight: 500;
    color: #606266;
    margin-right: 4px;
  }
}

/* 对话框增强样式 */
.enhanced-dialog {
  /deep/ .el-dialog {
    border-radius: 8px;
    .el-dialog__header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px 24px;
      border-bottom: 1px solid #ebeef5;
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }
  }
}

.dialog-form {
  /deep/ .el-form-item {
    margin-bottom: 24px;

    .el-input__inner,
    .el-textarea__inner,
    .el-select .el-input__inner {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

/* 分页容器样式 */
.pagination-container {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 增强版图片上传样式 */
.upload-container-enhanced {
  width: 100%;
}

.uploaded-images-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.uploaded-image-card {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.15);
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 1;
    }

    .image-actions {
      display: flex;
      gap: 12px;

      i {
        font-size: 18px;
        color: #fff;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }

        &.el-icon-zoom-in:hover {
          color: #409eff;
        }

        &.el-icon-delete:hover {
          color: #f56c6c;
        }
      }
    }
  }
}

.image-label {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
  text-align: center;
  font-weight: 500;
}

.upload-area-enhanced {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.image-uploader-enhanced {
  /deep/ .el-upload {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      background-color: #f5f9ff;
    }
  }
}

.upload-button-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  width: 100%;
  height: 100%;

  .upload-icon {
    font-size: 28px;
    color: #c0c4cc;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .upload-count {
    font-size: 12px;
    color: #999;
    background: #f0f2f5;
    padding: 2px 8px;
    border-radius: 12px;
  }
}

.upload-progress-wrapper {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;

  .upload-progress {
    margin-bottom: 8px;
  }

  .progress-text {
    font-size: 13px;
    color: #606266;
    text-align: center;
  }
}

.upload-tips-enhanced {
  /deep/ .el-alert {
    border-radius: 6px;
    
    .el-alert__description {
      margin-top: 8px;
    }
  }

  .tips-list {
    div {
      margin-bottom: 4px;
      font-size: 13px;
      color: #606266;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

/* 表格图片容器样式 */
.table-image-container {
  position: relative;
  display: inline-block;

  .image-count-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #409eff;
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
  }
}

/* 手机端上传区域带示例图样式 */
.upload-with-example-mobile {
  // display: flex;
  // align-items: flex-start;
  // gap: 20px;
  
  .upload-container-enhanced {
    flex: 1;
  }
  
  .example-image-container-mobile {
    flex-shrink: 0;
    
    .example-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    .example-images-grid-mobile {
      display: flex;
      // flex-direction: column;
      gap: 8px;
      
      .example-item-mobile {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.3s ease;
        
        &:hover {
          background: #f8f9fa;
          transform: translateX(2px);
        }
        
        .example-image-mobile {
          width: 50px;
          height: 50px;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          object-fit: contain;
          transition: all 0.3s ease;
          
          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            border-color: #409eff;
          }
        }
        
        .example-item-label-mobile {
          font-size: 11px;
          color: #666;
          line-height: 1.2;
          max-width: 60px;
          word-break: break-all;
        }
      }
    }
  }
}

/* 手机端表单项提示样式 */
.form-item-tip-mobile {
  margin-top: 8px;
  padding: 10px 12px;
  background: #f0f7ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #1890ff;
  font-size: 12px;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  
  .el-icon-info,
  .el-icon-warning {
    font-size: 14px;
    margin-right: 8px;
    margin-top: 2px;
    flex-shrink: 0;
  }
  
  .el-icon-warning {
    color: #fa8c16;
  }
  
  .el-icon-info {
    color: #1890ff;
  }
  
  span {
    flex: 1;
  }
}

/* 手机端示例图片查看对话框样式 */
.example-dialog-mobile {
  /deep/ .el-dialog {
    .el-dialog__header {
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      color: white;
      
      .el-dialog__title {
        color: white;
        font-weight: 600;
      }
      
      .el-dialog__close {
        color: white;
        
        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
}

.example-viewer-mobile {
  .example-header-mobile {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;
    
    .example-title-mobile {
      margin: 0;
      color: #2c3e50;
      font-size: 18px;
      font-weight: 600;
    }
    
    .example-counter-mobile {
      background: #409eff;
      color: white;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
  }
  
  .example-content-mobile {
    margin-bottom: 20px;
    
    .example-image-wrapper-mobile {
      background: #f8f9fa;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 16px;
    }
    
    .example-description-mobile {
      padding: 12px 16px;
      background: #f0f7ff;
      border-left: 4px solid #409eff;
      border-radius: 4px;
      
      p {
        margin: 0;
        color: #5e6d82;
        font-size: 14px;
        line-height: 1.6;
      }
    }
  }
  
  .example-navigation-mobile {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 20px;
    
    .el-button {
      padding: 8px 16px;
    }
  }
  
  .example-thumbnails-mobile {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    
    .thumbnail-item-mobile {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      padding: 8px;
      border-radius: 8px;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      
      &:hover {
        background: #f8f9fa;
        transform: translateY(-2px);
      }
      
      &.active {
        border-color: #409eff;
        background: #ecf5ff;
      }
      
      img {
        width: 60px;
        height: 60px;
        object-fit: contain;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
      }
      
      .thumbnail-title-mobile {
        font-size: 10px;
        color: #666;
        text-align: center;
        margin-top: 4px;
        max-width: 60px;
        line-height: 1.2;
        word-break: break-all;
      }
    }
  }
}

/* 批量导入按钮样式 */
.action-btn.secondary {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border-color: #67c23a;
  color: white;

  &:hover {
    background: linear-gradient(135deg, #85ce61 0%, #95d475 100%);
    border-color: #85ce61;
    color: white;
  }
}

/* 批量导入相关样式 */
.batch-import-container {
  .import-alert {
    margin-bottom: 20px;
    
    /deep/ .el-alert__description {
      .import-tips {
        div {
          margin-bottom: 4px;
          line-height: 1.5;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  .section-title {
    margin: 20px 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    border-left: 4px solid #409eff;
    padding-left: 12px;
  }
  
  .template-download-section {
    .download-area {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;
      
      .download-tip {
        font-size: 13px;
        color: #666;
        line-height: 1.4;
      }
    }
  }
  
  .file-upload-section {
    .batch-upload {
      /deep/ .el-upload {
        width: 100%;
        
        .el-upload-dragger {
          width: 100%;
          height: 180px;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          background: #fafafa;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #409eff;
            background: #f5f9ff;
          }
          
          .upload-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            
            .upload-icon {
              font-size: 48px;
              color: #c0c4cc;
              margin-bottom: 16px;
              transition: all 0.3s ease;
            }
            
            .upload-text {
              text-align: center;
              
              .main-text {
                font-size: 16px;
                color: #606266;
                margin-bottom: 8px;
                
                em {
                  color: #409eff;
                  font-style: normal;
                  text-decoration: underline;
                }
              }
              
              .sub-text {
                font-size: 13px;
                color: #999;
                line-height: 1.4;
              }
            }
          }
          
          &:hover .upload-icon {
            color: #409eff;
            transform: scale(1.1);
          }
        }
      }
    }
    
    .upload-progress-section {
      margin-top: 16px;
      padding: 16px;
      background: #f0f7ff;
      border-radius: 6px;
      border-left: 4px solid #409eff;
      
      /deep/ .el-progress {
        margin-bottom: 8px;
      }
      
      .progress-text {
        text-align: center;
        font-size: 14px;
        color: #606266;
      }
    }
  }
  
  .import-result-section {
    .result-summary {
      margin-bottom: 16px;
      
      .result-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px 20px;
        border-radius: 8px;
        font-weight: 500;
        font-size: 16px;
        
        &.success {
          background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
          color: #67c23a;
          border: 1px solid #b3e19d;
          
          .el-icon-success {
            font-size: 20px;
          }
        }
        
                  &.error {
          background: linear-gradient(135deg, #fef0f0 0%, #fff2f0 100%);
          color: #f56c6c;
          border: 1px solid #fbc4c4;
          
          .el-icon-error {
            font-size: 20px;
          }
        }
        
        .result-status {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
    
    .result-message {
      .message-content {
        padding: 16px 20px;
        border-radius: 8px;
        border-left: 4px solid;
        
        &.success-message {
          background: #f6ffed;
          border-left-color: #67c23a;
          
          p {
            color: #52c41a;
            margin: 0;
            line-height: 1.6;
            font-size: 14px;
          }
        }
        
        &.error-message {
          background: #fff2f0;
          border-left-color: #f56c6c;
          
          p {
            color: #f56c6c;
            margin: 0;
            line-height: 1.6;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}
</style>
