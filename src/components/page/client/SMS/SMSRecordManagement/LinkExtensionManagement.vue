<template>
    <div class="simple-signature-page">
        <!-- 页面头部 -->
        <div class="page-content">
            <div class="page-header">
                <div class="header-actions">
                    <el-button size="small" @click="goBack" icon="el-icon-arrow-left">
                        返回
                    </el-button>
                    <el-button v-permission type="primary" @click="showAddDialog" size="small" icon="el-icon-plus" class="add-btn">
                        新增链接
                    </el-button>
                </div>
                <div class="header-content">
                    <h2 class="page-title">
                        <i class="el-icon-document"></i>
                        完整链接报备管理
                    </h2>
                    <div class="link-info">
                        <el-tag type="info" size="small">链接地址：{{ linkAddress }}</el-tag>
                    </div>
                </div>

            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <!-- 表格操作区域 -->
                <div class="table-header-actions">
                    <div class="search-section">
                        <el-form :model="searchForm" :inline="true" ref="searchForm" class="search-form">
                            <el-form-item label="用户名称" prop="clientName">
                                <el-input v-model="searchForm.clientName" placeholder="请输入用户名称" class="search-input"
                                    clearable prefix-icon="el-icon-user" @keyup.enter.native="handleSearch" />
                            </el-form-item>
                            <el-form-item label="短信链接" prop="shortLink">
                                <el-input v-model="searchForm.shortLink" placeholder="请输入短链接地址" class="search-input"
                                    clearable prefix-icon="el-icon-link" @keyup.enter.native="handleSearch" />
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="handleSearch" icon="el-icon-search" size="small">
                                    查询
                                </el-button>
                                <el-button @click="handleReset" icon="el-icon-refresh" size="small">
                                    重置
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="header-actions">
                        <el-tooltip content="刷新数据" placement="top">
                            <!-- <el-button @click="loadData" size="small" icon="el-icon-refresh" class="refresh-btn">
                                刷新
                            </el-button> -->
                        </el-tooltip>

                    </div>
                </div>

                <!-- 搜索结果统计 -->
                <!-- <div class="search-results-info" v-if="hasSearchConditions">
                    <div class="search-info">
                        <i class="el-icon-search"></i>
                        <span>搜索结果：</span>
                        <span class="search-conditions">
                            <template v-if="searchForm.clientName">
                                用户名称：<em>{{ searchForm.clientName }}</em>
                            </template>
                            <template v-if="searchForm.clientName && searchForm.shortLink">，</template>
                            <template v-if="searchForm.shortLink">
                                短链接：<em>{{ searchForm.shortLink }}</em>
                            </template>
                        </span>
                        <span class="search-count">共找到 {{ pagination.total }} 条记录</span>
                    </div>
                    <el-button type="text" @click="handleReset" class="clear-search">
                        <i class="el-icon-close"></i>
                        清空搜索
                    </el-button>
                </div> -->

                <!-- 优化后的表格 -->
                <el-table :data="tableData" v-loading="loading" stripe :border="true" style="width: 100%"
                    :header-cell-style="{
                        background: '#f8fafc',
                        color: '#374151',
                        borderColor: '#e5e7eb',
                        fontWeight: '600'
                    }" :cell-style="{ borderColor: '#e5e7eb' }" empty-text="暂无链接数据" class="enhanced-table">

                    <!-- 用户名称列 -->
                    <el-table-column prop="clientName" label="用户名称" min-width="150" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div class="table-cell user-cell">
                                <div class="user-info">
                                    <i class="el-icon-user-solid user-icon"></i>
                                    <span class="user-name">{{ scope.row.clientName || '-' }}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                                        <!-- 短链接列 -->
                    <el-table-column prop="shortLink" label="短信链接（短信内容中下发链接开头部分保持一致）" min-width="200" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div class="table-cell short-link-cell">
                                <div class="short-link-info">
                                    <i class="el-icon-connection short-link-icon"></i>
                                    <el-link :href="formatLinkUrl(scope.row.shortLink)" target="_blank" type="success" 
                                        v-if="scope.row.shortLink">
                                        {{ scope.row.shortLink }}
                                    </el-link>
                                    <span v-else class="empty-text">-</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                                        <!-- 长链接列 -->
                    <el-table-column prop="longLink" label="完整链接（可点击跳转的落地页地址）" min-width="250" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div class="table-cell link-cell">
                                <div class="link-info">
                                    <i class="el-icon-link link-icon"></i>
                                    <el-link :href="formatLinkUrl(scope.row.longLink)" target="_blank" type="primary" 
                                        v-if="scope.row.longLink">
                                        {{ scope.row.longLink }}
                                    </el-link>
                                    <span v-else class="empty-text">-</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 创建时间列 -->
                    <el-table-column prop="createTime" label="创建时间" width="180" sortable>
                        <template slot-scope="scope">
                            <div class="table-cell time-cell">
                                <div class="time-info">
                                    <i class="el-icon-time time-icon"></i>
                                    <div class="time-details">
                                        <div class="date-text">{{ formatDate(scope.row.createTime) }}</div>
                                        <div class="time-text">{{ formatTime(scope.row.createTime) }}</div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 操作列 -->
                    <el-table-column label="操作" width="180" align="center" fixed="right">
                        <template slot-scope="scope">
                            <div class="table-cell action-cell">
                                <div class="action-buttons">
                                    <el-tooltip content="编辑链接信息" placement="top">
                                        <el-button v-permission v-if="scope.row.allowOperate" type="primary" size="mini"
                                            @click="showEditDialog(scope.row)" class="action-btn edit-btn">
                                            <i class="el-icon-edit"></i>
                                            编辑
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip content="删除链接记录" placement="top">
                                        <el-button v-permission v-if="scope.row.allowOperate" type="danger" size="mini"
                                            @click="handleDelete(scope.row)" class="action-btn delete-btn">
                                            <i class="el-icon-delete"></i>
                                            删除
                                        </el-button>
                                    </el-tooltip>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 空数据状态 -->
                    <template slot="empty">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="el-icon-document-remove"></i>
                            </div>
                            <div class="empty-text">
                                <h3>暂无链接数据</h3>
                                <!-- <p>该链接还没有添加任何链接配置信息</p> -->
                            </div>
                            <div class="empty-actions">
                                <el-button v-permission type="primary" @click="showAddDialog" icon="el-icon-plus">
                                    添加第一个链接
                                </el-button>
                            </div>
                        </div>
                    </template>
                </el-table>

                <!-- 优化后的分页 -->
                <div class="pagination-wrapper">
                    <div class="pagination-info">
                        <span class="total-text">
                            共 <span class="total-count">{{ pagination.total }}</span> 条记录
                        </span>
                        <span class="page-info">
                            第 {{ pagination.currentPage }} 页，共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
                        </span>
                    </div>
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]"
                        :page-size="pagination.pageSize" layout="sizes, prev, pager, next, jumper"
                        :total="pagination.total" :background="true" class="enhanced-pagination" />
                </div>
            </div>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false"
            @close="handleDialogClose" class="extension-dialog" :append-to-body="true">
            <!-- 对话框头部 -->
            <div slot="title" class="dialog-header">
                <div class="dialog-title">
                    <i class="el-icon-document dialog-icon"></i>
                    <span>{{ dialogTitle }}</span>
                </div>
                <div class="dialog-subtitle">
                    为链接 <span class="link-highlight">{{ linkAddress }}</span> 添加长短链接配置
                </div>
            </div>

            <div class="dialog-content">
                <el-form ref="extensionForm" :model="extensionForm" :rules="formRules" label-width="120px"
                    class="extension-form">
                    <!-- 基本信息区域 -->
                    <div class="form-section">
                        <div class="section-header">
                            <i class="el-icon-link"></i>
                            <span>链接配置信息</span>
                        </div>

                        <!-- 重要说明 -->
                        <div class="form-tips">
                            <el-alert title="重要说明" type="warning" :closable="true" show-icon>
                                <div class="tips-content">
                                    <p><strong>短信链接规则：</strong>短信内容中下发链接开头部分保持一致</p>
                                    <div class="example-section">
                                        <div class="example-item">
                                            <span class="example-label">示例1：</span>
                                            <span class="example-text">如果短信内容中的链接是 <code>https://xxx.com</code>，那么报备的短信链接就是 <code>https://xxx.com</code></span>
                                        </div>
                                        <div class="example-item">
                                            <span class="example-label">示例2：</span>
                                            <span class="example-text">如果短信内容中的链接是 <code>xxx.com</code>，那么报备的短信链接就是 <code>xxx.com</code></span>
                                        </div>
                                    </div>
                                    <p class="tips-note">
                                        <i class="el-icon-info"></i>
                                        确保短信链接与实际短信内容中的链接格式完全一致，否则可能影响短信发送
                                    </p>
                                </div>
                            </el-alert>
                        </div>

                        <div class="form-grid">
                            <el-form-item label="短信链接" prop="shortLink" class="form-item-full">
                                <el-input v-model="extensionForm.shortLink"
                                    placeholder="请输入短信内容中的链接格式：例如：https://www.baidu.com 或 www.baidu.com" :maxlength="200"
                                    show-word-limit clearable>
                                    <template slot="prepend">
                                        <i class="el-icon-connection"></i>
                                    </template>
                                </el-input>
                                <div class="form-item-tip">
                                    <i class="el-icon-warning"></i>
                                    <span>请与短信内容中的链接开头部分保持完全一致</span>
                                </div>
                            </el-form-item>
                            
                            <el-form-item label="完整链接" prop="longLink" class="form-item-full">
                                <el-input v-model="extensionForm.longLink"
                                    placeholder="请输入用户点击后跳转的完整链接地址：例如：https://www.baidu.com/xxx/index.html"
                                    :maxlength="500" show-word-limit clearable>
                                    <template slot="prepend">
                                        <i class="el-icon-link"></i>
                                    </template>
                                </el-input>
                                <div class="form-item-tip">
                                    <i class="el-icon-info"></i>
                                    <span>用户点击短信链接后实际跳转到的落地页地址</span>
                                </div>
                            </el-form-item>
                            
                            
                        </div>
                    </div>
                </el-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false" size="medium">
                    <i class="el-icon-close"></i>
                    取消
                </el-button>
                <el-button v-permission type="primary" @click="handleSubmit" :loading="submitLoading" size="medium">
                    <i class="el-icon-check"></i>
                    {{ isEdit ? '更新链接' : '新增链接' }}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    name: 'LinkExtensionManagement',
    data() {
        // 长链接验证规则
        const validateLongUrl = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入长链接'));
                return;
            }
            // 简单的URL格式验证
            // const urlPattern = /^https?:\/\/.+/;
            // if (!urlPattern.test(value)) {
            //     callback(new Error('请输入正确的链接格式（必须包含http://或https://）'));
            //     return;
            // }
            callback();
        };

        // 短链接验证规则
        const validateShortUrl = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入短链接'));
                return;
            }
            // 简单的URL格式验证
            // const urlPattern = /^https?:\/\/.+/;
            // if (!urlPattern.test(value)) {
            //     callback(new Error('请输入正确的链接格式（必须包含http://或https://）'));
            //     return;
            // }
            callback();
        };

        return {
            // 基本数据
            loading: false,
            submitLoading: false,
            dialogVisible: false,
            isEdit: false,
            currentRecord: null,

            // 路由参数
            linkId: '',
            linkAddress: '',

            // 搜索表单
            searchForm: {
                clientName: '',
                shortLink: ''
            },

            // 分页信息
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },

            // 表格数据
            tableData: [],

            // 表单数据
            extensionForm: {
                linkId: '',
                longLink: '',
                shortLink: ''
            },

            // 表单验证规则
            formRules: {
                longLink: [
                    { required: true, validator: validateLongUrl, trigger: 'blur' }
                ],
                shortLink: [
                    { required: true, validator: validateShortUrl, trigger: 'blur' }
                ]
            }
        };
    },

    computed: {
        dialogTitle() {
            return this.isEdit ? '编辑链接配置' : '新增链接配置';
        },

        // 判断是否有搜索条件
        hasSearchConditions() {
            return this.searchForm.clientName || this.searchForm.shortLink;
        }
    },

    mounted() {
        this.initData();
    },

    methods: {
        // 初始化数据
        initData() {
            // 获取路由参数
            this.linkId = this.$route.query.linkId || '';
            this.linkAddress = this.$route.query.linkAddress || '';

            if (!this.linkId) {
                this.$message.error('缺少链接ID参数');
                this.goBack();
                return;
            }

            // 设置表单中的linkId
            this.extensionForm.linkId = this.linkId;

            // 加载数据
            this.loadData();
        },

        // 返回上一页
        goBack() {
            this.$router.go(-1);
        },

        // 加载数据
        loadData() {
            this.loading = true;
            const params = {
                currentPage: this.pagination.currentPage,
                pageSize: this.pagination.pageSize,
                linkId: this.linkId,
                ...this.searchForm
            };

            // 过滤空参数，但保留 linkId
            Object.keys(params).forEach(key => {
                if (key !== 'linkId' && (params[key] === '' || params[key] === null || params[key] === undefined)) {
                    delete params[key];
                }
            });

            this.$api.post(
                this.API.cpus + 'consumersmstemplatereportlinkextension/page',
                params,
                (res) => {
                    this.loading = false;
                    if (res.code === 200) {
                        this.tableData = res.data.records || [];
                        this.pagination.total = res.data.total || 0;
                    } else {
                        this.$message.error(res.msg || '加载数据失败');
                        this.tableData = [];
                        this.pagination.total = 0;
                    }
                },
                (error) => {
                    this.loading = false;
                    console.error('加载数据失败:', error);
                    this.$message.error('加载数据失败，请稍后重试');
                    this.tableData = [];
                    this.pagination.total = 0;
                }
            );
        },

        // 搜索功能
        handleSearch() {
            this.pagination.currentPage = 1; // 重置到第一页
            this.loadData();
        },

        // 重置搜索
        handleReset() {
            this.searchForm = {
                clientName: '',
                shortLink: ''
            };
            this.pagination.currentPage = 1; // 重置到第一页
            this.loadData();
        },

        // 显示新增对话框
        showAddDialog() {
            this.isEdit = false;
            this.currentRecord = null;
            this.extensionForm = {
                linkId: this.linkId,
                longLink: '',
                shortLink: ''
            };
            this.dialogVisible = true;

            // 清除表单验证
            this.$nextTick(() => {
                if (this.$refs.extensionForm) {
                    this.$refs.extensionForm.clearValidate();
                }
            });
        },

        // 显示编辑对话框
        showEditDialog(record) {
            if (!record.allowOperate) {
                this.$message.warning('您没有权限编辑此记录');
                return;
            }

            this.isEdit = true;
            this.currentRecord = record;
            this.extensionForm = {
                id: record.id,
                linkId: this.linkId,
                longLink: record.longLink || '',
                shortLink: record.shortLink || ''
            };
            this.dialogVisible = true;

            // 清除表单验证
            this.$nextTick(() => {
                if (this.$refs.extensionForm) {
                    this.$refs.extensionForm.clearValidate();
                }
            });
        },

        // 关闭对话框
        handleDialogClose() {
            // 清除表单验证
            if (this.$refs.extensionForm) {
                this.$refs.extensionForm.clearValidate();
            }
            this.clearDialogData();
        },

        // 清除对话框数据
        clearDialogData() {
            this.extensionForm = {
                linkId: this.linkId,
                longLink: '',
                shortLink: ''
            };
            this.isEdit = false;
            this.currentRecord = null;
        },

        // 提交表单
        handleSubmit() {
            this.$refs.extensionForm.validate((valid) => {
                if (!valid) {
                    this.$message.warning('请填写完整的表单信息');
                    return;
                }

                this.submitLoading = true;
                if (this.isEdit) {
                    this.updateRecord();
                } else {
                    this.addRecord();
                }
            });
        },

        // 新增记录
        addRecord() {
            this.$api.post(
                this.API.cpus + 'consumersmstemplatereportlinkextension',
                this.extensionForm,
                (res) => {
                    this.submitLoading = false;
                    if (res.code === 200) {
                        this.$message.success('新增链接成功');
                        this.dialogVisible = false;
                        this.clearDialogData();
                        this.loadData();
                    } else {
                        this.$message.error(res.msg || '新增失败');
                    }
                },
                (error) => {
                    this.submitLoading = false;
                    console.error('新增失败:', error);
                    this.$message.error('新增失败，请稍后重试');
                }
            );
        },

        // 更新记录
        updateRecord() {
            this.$api.put(
                this.API.cpus + 'consumersmstemplatereportlinkextension',
                this.extensionForm,
                (res) => {
                    this.submitLoading = false;
                    if (res.code === 200) {
                        this.$message.success('更新链接成功');
                        this.dialogVisible = false;
                        this.clearDialogData();
                        this.loadData();
                    } else {
                        this.$message.error(res.msg || '更新失败');
                    }
                },
                (error) => {
                    this.submitLoading = false;
                    console.error('更新失败:', error);
                    this.$message.error('更新失败，请稍后重试');
                }
            );
        },

        // 删除记录
        handleDelete(record) {
            if (!record.allowOperate) {
                this.$message.warning('您没有权限删除此记录');
                return;
            }

            this.$confirm('确认删除这条链接配置记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$api.delete(
                    this.API.cpus + `consumersmstemplatereportlinkextension/${record.id}`,
                    {},
                    (res) => {
                        if (res.code === 200) {
                            this.$message.success('删除成功');
                            this.loadData();
                        } else {
                            this.$message.error(res.msg || '删除失败');
                        }
                    },
                    (error) => {
                        console.error('删除失败:', error);
                        this.$message.error('删除失败，请稍后重试');
                    }
                );
            }).catch(() => {
                // 用户取消删除
            });
        },

        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.pagination.currentPage = 1;
            this.loadData();
        },

        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.loadData();
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return '-';
            return moment(date).format('YYYY-MM-DD');
        },

        // 格式化时间
        formatTime(date) {
            if (!date) return '';
            return moment(date).format('HH:mm:ss');
        },

        // 格式化链接URL，为无协议的链接添加https://
        formatLinkUrl(url) {
            if (!url) return '';
            
            // 如果已经包含协议，直接返回
            if (url.match(/^https?:\/\//i)) {
                return url;
            }
            
            // 如果没有协议，添加https://
            return 'https://' + url;
        },




    }
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* 页面头部样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .page-title {
            margin: 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;

            i {
                margin-right: 8px;
                color: #409eff;
                font-size: 22px;
            }
        }

        .link-info {
            .el-tag {
                background: #f0f7ff;
                border-color: #b3d8ff;
                color: #409eff;
            }
        }
    }

    .header-actions {
        display: flex;
        gap: 12px;
        .add-btn {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            border: none;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);

            &:hover {
                opacity: 0.9;
                transform: translateY(-1px);
            }
        }
    }
}

/* 表格容器样式 */
.table-container {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 表格头部操作区域 */
.table-header-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;

    .search-section {
        .search-form {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;

            /deep/ .el-form-item {
                margin-bottom: 0;

                .el-form-item__label {
                    font-size: 14px;
                    color: #374151;
                    font-weight: 500;
                    min-width: 70px;
                }

                .search-input {
                    width: 200px;

                    .el-input__inner {
                        border-radius: 6px;
                        border: 1px solid #d1d5db;
                        transition: all 0.3s ease;

                        &:focus {
                            border-color: #6366f1;
                            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
                        }
                    }

                    .el-input__prefix {
                        color: #9ca3af;
                    }
                }

                .el-button {
                    border-radius: 6px;
                    font-weight: 500;
                    margin-left: 8px;

                    &.el-button--primary {
                        // background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                        border: none;

                        &:hover {
                            opacity: 0.9;
                            transform: translateY(-1px);
                        }
                    }

                    &.el-button--default {
                        border-color: #d1d5db;
                        color: #6b7280;

                        &:hover {
                            border-color: #9ca3af;
                            color: #374151;
                            background: #f9fafb;
                        }
                    }
                }
            }
        }
    }

    .header-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .refresh-btn {
            border-color: #d1d5db;
            color: #6b7280;

            &:hover {
                border-color: #6366f1;
                color: #6366f1;
                background: #f0f4ff;
            }
        }

        
    }
}

/* 搜索结果统计样式 */
.search-results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background: #eff6ff;
    border-top: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;

    .search-info {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #374151;

        i {
            color: #6366f1;
            font-size: 16px;
        }

        .search-conditions {
            em {
                color: #6366f1;
                font-weight: 600;
                font-style: normal;
                background: rgba(99, 102, 241, 0.1);
                padding: 2px 6px;
                border-radius: 4px;
                margin: 0 2px;
            }
        }

        .search-count {
            margin-left: 12px;
            color: #6366f1;
            font-weight: 600;
        }
    }

    .clear-search {
        color: #6b7280;
        font-size: 12px;

        &:hover {
            color: #374151;
            background: rgba(107, 114, 128, 0.1);
        }

        i {
            margin-right: 4px;
        }
    }
}

/* 增强的表格样式 */
.enhanced-table {
    /deep/ .el-table__body-wrapper {
        .el-table__row {
            transition: all 0.3s ease;

            &:hover {
                background: #f9fafb !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            &.success-row {
                background: rgba(34, 197, 94, 0.05) !important;
            }

            &.warning-row {
                background: rgba(239, 68, 68, 0.05) !important;
            }
        }
    }

    /deep/ .el-table__header {
        th {
            background: #f8fafc !important;
            border-color: #e5e7eb !important;
            color: #374151 !important;
            font-weight: 600 !important;
        }
    }

    /deep/ .el-table--border {
        border-color: #e5e7eb;

        &::after {
            background: #e5e7eb;
        }
    }

    /deep/ .el-table td,
    /deep/ .el-table th {
        border-color: #e5e7eb !important;
    }
}

/* 表格单元格样式 */
.table-cell {
    padding: 8px 0;
    line-height: 1.5;
}

/* 用户单元格 */
.user-cell {
    .user-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .user-icon {
            color: #6366f1;
            font-size: 16px;
        }

        .user-name {
            font-weight: 500;
            color: #1f2937;
        }
    }
}

/* 链接单元格 */
.link-cell {
    .link-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .link-icon {
            color: #6b7280;
            font-size: 14px;
        }
    }
}

.short-link-cell {
    .short-link-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .short-link-icon {
            color: #059669;
            font-size: 14px;
        }
    }
}

/* 时间单元格 */
.time-cell {
    .time-info {
        display: flex;
        align-items: flex-start;
        gap: 8px;

        .time-icon {
            color: #6b7280;
            font-size: 14px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .time-details {
            .date-text {
                font-weight: 500;
                color: #1f2937;
                font-size: 14px;
                margin-bottom: 2px;
            }

            .time-text {
                font-size: 12px;
                color: #6b7280;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            }
        }
    }
}

/* 操作单元格 */
.action-cell {
    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;

        .action-btn {
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;

            &.edit-btn {
                background: #eff6ff;
                border-color: #3b82f6;
                color: #3b82f6;

                &:hover {
                    background: #3b82f6;
                    color: white;
                    transform: translateY(-1px);
                }
            }

            &.delete-btn {
                background: #fef2f2;
                border-color: #ef4444;
                color: #ef4444;

                &:hover {
                    background: #ef4444;
                    color: white;
                    transform: translateY(-1px);
                }
            }
        }
    }
}

.empty-text {
    color: #9ca3af;
    font-style: italic;
}

/* 空状态样式 */
.empty-state {
    padding: 60px 20px;
    text-align: center;

    .empty-icon {
        margin-bottom: 20px;

        i {
            font-size: 64px;
            color: #d1d5db;
        }
    }

    .empty-text {
        margin-bottom: 24px;

        h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            color: #374151;
            font-weight: 600;
        }

        p {
            margin: 0;
            font-size: 14px;
            color: #6b7280;
        }
    }

    .empty-actions {
        .el-button {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;

            &:hover {
                opacity: 0.9;
                transform: translateY(-1px);
            }
        }
    }
}

/* 分页样式 */
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #fff;

    .pagination-info {
        display: flex;
        align-items: center;
        gap: 16px;
        color: #6b7280;
        font-size: 14px;

        .total-count {
            color: #6366f1;
            font-weight: 600;
        }
    }

    .enhanced-pagination {

        /deep/ .el-pagination {
            .btn-prev,
            .btn-next,
            .el-pager li {
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 4px;
                margin: 0 2px;

                &:hover {
                    color: #6366f1;
                    border-color: #6366f1;
                }

                &.active {
                    background: #6366f1;
                    border-color: #6366f1;
                    color: white;
                }
            }
        }
    }
}

/* 对话框样式 */
.extension-dialog {
    /deep/ .el-dialog {
        border-radius: 8px;
        overflow: hidden;

        .el-dialog__header {
            padding: 0;
            border-bottom: none;
        }

        .el-dialog__body {
            padding: 0;
        }
    }

    .dialog-header {
        background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
        padding: 20px 24px;
        color: white;

        .dialog-title {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;

            .dialog-icon {
                margin-right: 12px;
                font-size: 20px;
            }
        }

        .dialog-subtitle {
            font-size: 14px;
            opacity: 0.9;

            .link-highlight {
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 8px;
                border-radius: 4px;
                font-weight: 500;
            }
        }
    }

    .dialog-content {
        padding: 24px;
    }
}

/* 表单样式 */
.extension-form {
    .form-section {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }

        .section-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f8fafc;
            border-left: 4px solid #6366f1;
            margin-bottom: 20px;
            font-weight: 600;
            color: #2c3e50;
            border-radius: 4px;

            i {
                margin-right: 8px;
                font-size: 16px;
                color: #6366f1;
            }
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;

            .form-item-half {
                grid-column: span 1;
            }

            .form-item-full {
                grid-column: span 1;
            }
        }
    }

    /* 表单提示样式 */
    .form-tips {
        margin-bottom: 20px;

        /deep/ .el-alert {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 8px rgba(234, 179, 8, 0.15);

            .el-alert__icon {
                font-size: 18px;
                top: 12px;
            }

            .el-alert__title {
                font-weight: 600;
                font-size: 16px;
                margin-bottom: 8px;
            }

            .el-alert__description {
                font-size: 14px;
                line-height: 1.6;
            }
        }

        .tips-content {
            p {
                margin: 0 0 12px 0;
                color: #374151;

                strong {
                    color: #1f2937;
                }
            }

            .example-section {
                margin: 16px 0;

                .example-item {
                    margin-bottom: 12px;
                    display: flex;
                    align-items: flex-start;
                    gap: 8px;

                    .example-label {
                        font-weight: 600;
                        color: #f59e0b;
                        flex-shrink: 0;
                        min-width: 60px;
                    }

                    .example-text {
                        color: #4b5563;
                        line-height: 1.5;

                        code {
                            background: #f3f4f6;
                            padding: 2px 6px;
                            border-radius: 4px;
                            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                            font-size: 13px;
                            color: #059669;
                            border: 1px solid #e5e7eb;
                        }
                    }
                }
            }

            .tips-note {
                margin: 16px 0 0 0;
                padding: 12px;
                background: rgba(59, 130, 246, 0.05);
                border-left: 3px solid #3b82f6;
                border-radius: 4px;
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 13px;
                color: #1e40af;

                i {
                    font-size: 16px;
                    color: #3b82f6;
                    flex-shrink: 0;
                }
            }
        }
    }

    /* 表单项提示样式 */
    .form-item-tip {
        margin-top: 8px;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        color: #6b7280;
        background: #f9fafb;
        padding: 8px 12px;
        border-radius: 6px;
        border-left: 3px solid #e5e7eb;

        i {
            font-size: 14px;
            color: #f59e0b;
        }

        span {
            line-height: 1.4;
        }

        &.warning-tip {
            border-left-color: #f59e0b;
            background: rgba(245, 158, 11, 0.05);

            i {
                color: #f59e0b;
            }
        }

        &.info-tip {
            border-left-color: #3b82f6;
            background: rgba(59, 130, 246, 0.05);

            i {
                color: #3b82f6;
            }
        }
    }

    /deep/ .el-form-item {
        margin-bottom: 20px;

        .el-input__inner,
        .el-textarea__inner,
        .el-select .el-input__inner {
            border-radius: 6px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s ease;

            &:focus {
                border-color: #6366f1;
                box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
            }
        }

        .el-input-group__prepend {
            background: #f8f9fa;
            border-color: #e4e7ed;
            color: #606266;
        }

        .el-textarea__inner {
            resize: none;
            font-family: inherit;
        }
    }
}

/* 对话框底部按钮 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .el-button {
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 500;

        i {
            margin-right: 6px;
        }

        &.el-button--primary {
            // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;

            &:hover {
                opacity: 0.9;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            }
        }

        &.el-button--default {
            border-color: #d1d5db;
            color: #6b7280;

            &:hover {
                border-color: #9ca3af;
                color: #374151;
            }
        }
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;

        .header-actions {
            margin-bottom: 16px;
        }

        .header-content {
            margin-left: 0;
        }
    }

    .table-header-actions {
        padding: 16px;
        gap: 12px;

        .search-section {
            .search-form {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;

                /deep/ .el-form-item {
                    .el-form-item__label {
                        min-width: auto;
                        margin-bottom: 4px;
                        display: block;
                    }

                    .search-input {
                        width: 100%;
                    }

                    .el-button {
                        margin-left: 0;
                        margin-right: 8px;
                    }
                }
            }
        }

        .header-actions {
            justify-content: center;
        }
    }

    .search-results-info {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
        padding: 12px 16px;

        .search-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;

            .search-count {
                margin-left: 0;
            }
        }

        .clear-search {
            align-self: flex-end;
        }
    }

    .pagination-wrapper {
        flex-direction: column;
        gap: 16px;
        align-items: center;

        .pagination-info {
            flex-direction: column;
            gap: 8px;
            text-align: center;
        }
    }

    .extension-dialog {
        /deep/ .el-dialog {
            margin-top: 5vh;
            margin-bottom: 5vh;
            width: 95% !important;
        }
    }
}
</style>