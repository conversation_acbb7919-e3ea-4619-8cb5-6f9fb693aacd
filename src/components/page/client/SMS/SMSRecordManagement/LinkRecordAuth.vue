<template>
    <div class="simple-signature-page">
        <!-- 页面头部 -->
        <div class="page-content">
            <div class="page-header">
                <div class="header-actions">
                    <!-- <el-button type="primary" size="small" @click="showAddDialog" icon="el-icon-plus">
                    新增授权
                </el-button> -->
                    <el-button size="small" @click="goBack" icon="el-icon-arrow-left">
                        返回
                    </el-button>
                </div>
                <div class="header-content">
                    <h2 class="page-title">
                        <i class="el-icon-key"></i>
                        域名授权管理
                    </h2>
                    <div class="link-info">
                        <el-tag type="info" size="small">域名地址：{{ linkAddress }}</el-tag>
                    </div>
                </div>

            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <!-- 表格操作区域 -->
                <div class="table-header-actions">
                    <!-- <div class="header-info">
                        <div class="info-item">
                            <i class="el-icon-document"></i>
                            <span>授权管理</span>
                        </div>
                        <div class="info-item">
                            <i class="el-icon-link"></i>
                            <span class="link-text">{{ linkAddress }}</span>
                        </div>
                    </div> -->
                    <div class="header-actions">
                        <el-tooltip content="刷新数据" placement="top">
                            <el-button @click="loadData" size="small" icon="el-icon-refresh" class="refresh-btn">
                                刷新
                            </el-button>
                        </el-tooltip>
                        <el-button v-permission type="primary" @click="showAddDialog" size="small" icon="el-icon-plus"
                            class="add-btn">
                            新增授权
                        </el-button>
                    </div>
                </div>

                <!-- 优化后的表格 -->
                <el-table :data="tableData" v-loading="loading" stripe :border="true" style="width: 100%"
                    :row-class-name="tableRowClassName" :header-cell-style="{
                        background: '#f8fafc',
                        color: '#374151',
                        borderColor: '#e5e7eb',
                        fontWeight: '600'
                    }" :cell-style="{ borderColor: '#e5e7eb' }" empty-text="暂无授权数据" class="enhanced-table">

                    <!-- 用户名称列 -->
                    <el-table-column prop="clientName" label="用户名称" min-width="150" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div class="table-cell user-cell">
                                <div class="user-info">
                                    <i class="el-icon-user-solid user-icon"></i>
                                    <span class="user-name">{{ scope.row.clientName || '-' }}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 公司名称列 -->
                    <el-table-column prop="companyName" label="公司名称" min-width="180" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div class="table-cell company-cell">
                                <div class="company-info">
                                    <i class="el-icon-office-building company-icon"></i>
                                    <div class="company-details">
                                        <div class="company-name">{{ scope.row.companyName || '-' }}</div>
                                        <!-- <div class="company-meta" v-if="scope.row.creditCode">
                                            {{ scope.row.creditCode }}
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 统一社会信用代码列 -->
                    <el-table-column prop="creditCode" label="信用代码" min-width="160" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div class="table-cell credit-cell">
                                <div class="credit-code" v-if="scope.row.creditCode">
                                    <i class="el-icon-postcard credit-icon"></i>
                                    <span class="code-text">{{ scope.row.creditCode }}</span>
                                    <el-button type="text" size="mini" @click="copyToClipboard(scope.row.creditCode)"
                                        class="copy-btn">
                                        <i class="el-icon-copy-document"></i>
                                    </el-button>
                                </div>
                                <span v-else class="empty-text">-</span>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 盖章扫描件列 -->
                    <el-table-column prop="sealImg" label="盖章扫描件" width="140" align="center">
                        <template slot-scope="scope">
                                                            <div class="table-cell image-cell">
                                    <div v-if="scope.row.sealImg" class="image-preview-wrapper">
                                        <el-image 
                                            class="table-image" 
                                            :src="getImageUrl(scope.row.sealImg)" 
                                            fit="cover"
                                            :z-index="9999"
                                            :lazy="true"
                                            :preview-src-list="[getImageUrl(scope.row.sealImg)]"
                                            :initial-index="0"
                                            :close-on-press-escape="true"
                                            :mask-closable="true"
                                            @click="handleImageClick(scope.row.sealImg)">
                                            <div slot="placeholder" class="image-placeholder">
                                                <i class="el-icon-loading"></i>
                                                <span>加载中...</span>
                                            </div>
                                            <div slot="error" class="image-error">
                                                <i class="el-icon-picture-outline"></i>
                                                <span>加载失败</span>
                                                <!-- <span>{{ getImageUrl(scope.row.sealImg) }}</span> -->
                                            </div>
                                        </el-image>
                                        <div class="image-overlay">
                                            <i class="el-icon-zoom-in"></i>
                                            <span>点击预览</span>
                                        </div>
                                    </div>
                                    <div v-else class="no-image">
                                        <i class="el-icon-picture-outline"></i>
                                        <span>暂无文件</span>
                                    </div>
                                </div>
                        </template>
                    </el-table-column>

                    <!-- 审核状态列 -->
                    <el-table-column prop="auditStatus" label="状态" width="120" align="center">
                        <template slot-scope="scope">
                            <div class="table-cell status-cell">
                                <div class="status-wrapper">
                                    <el-tag :type="getAuditStatusTagType(scope.row.auditStatus)" size="small"
                                        class="status-tag">
                                        <i :class="getAuditStatusIcon(scope.row.auditStatus)"></i>
                                        {{ getAuditStatusText(scope.row.auditStatus) }}
                                    </el-tag>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 审核原因列 -->
                    <el-table-column prop="auditReason" label="审核原因" min-width="200">
                        <template slot-scope="scope">
                            <div class="table-cell reason-cell">
                                <div v-if="scope.row.auditReason && scope.row.auditReason !== ''" class="reason-content">
                                    <!-- 审核拒绝状态 -->
                                    <el-tooltip v-if="scope.row.auditStatus == '3'" 
                                               :content="`驳回原因：${scope.row.auditReason}`" 
                                               placement="top" 
                                               effect="dark"
                                               :disabled="scope.row.auditReason.length <= 20">
                                        <div class="reject-reason reason-wrapper">
                                            <i class="el-icon-warning reason-icon reject-icon"></i>
                                            <div class="reason-text">
                                                <span class="reason-label">驳回原因：</span>
                                                <span class="reason-detail">{{ scope.row.auditReason }}</span>
                                            </div>
                                        </div>
                                    </el-tooltip>
                                    
                                    <!-- 审核通过状态 -->
                                    <el-tooltip v-else-if="scope.row.auditStatus == '2'" 
                                               :content="`审核备注：${scope.row.auditReason}`" 
                                               placement="top" 
                                               effect="dark"
                                               :disabled="scope.row.auditReason.length <= 20">
                                        <div class="pass-reason reason-wrapper">
                                            <i class="el-icon-circle-check reason-icon pass-icon"></i>
                                            <div class="reason-text">
                                                <span class="reason-label">审核备注：</span>
                                                <span class="reason-detail">{{ scope.row.auditReason }}</span>
                                            </div>
                                        </div>
                                    </el-tooltip>
                                    
                                    <!-- 其他状态 -->
                                    <el-tooltip v-else 
                                               :content="scope.row.auditReason" 
                                               placement="top" 
                                               effect="dark"
                                               :disabled="scope.row.auditReason.length <= 20">
                                        <div class="default-reason reason-wrapper">
                                            <i class="el-icon-info reason-icon default-icon"></i>
                                            <div class="reason-text">
                                                <span class="reason-detail">{{ scope.row.auditReason }}</span>
                                            </div>
                                        </div>
                                    </el-tooltip>
                                </div>
                                <div v-else class="no-reason">
                                    <span class="empty-text">-</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 创建时间列 -->
                    <el-table-column prop="createTime" label="创建时间" width="180" sortable>
                        <template slot-scope="scope">
                            <div class="table-cell time-cell">
                                <div class="time-info">
                                    <i class="el-icon-time time-icon"></i>
                                    <div class="time-details">
                                        <div class="date-text">{{ formatDate(scope.row.createTime) }}</div>
                                        <div class="time-text">{{ formatTime(scope.row.createTime) }}</div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 操作列 -->
                    <el-table-column label="操作" width="180" align="center" fixed="right">
                        <template slot-scope="scope">
                            <div class="table-cell action-cell">
                                <div class="action-buttons">
                                    <el-tooltip content="编辑授权信息" placement="top">
                                        <el-button v-permission v-if="canEdit(scope.row)" type="primary" size="mini" @click="showEditDialog(scope.row)"
                                            class="action-btn edit-btn">
                                            <i class="el-icon-edit"></i>
                                            编辑
                                        </el-button>
                                    </el-tooltip>
                                    <el-tooltip content="删除授权记录" placement="top">
                                        <el-button v-permission v-if="isCurrentUser(scope.row)" type="danger" size="mini" @click="handleDelete(scope.row)"
                                            class="action-btn delete-btn">
                                            <i class="el-icon-delete"></i>
                                            删除
                                        </el-button>
                                    </el-tooltip>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <!-- 空数据状态 -->
                    <template slot="empty">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="el-icon-document-remove"></i>
                            </div>
                            <div class="empty-text">
                                <h3>暂无授权数据</h3>
                                <p>该链接还没有添加任何授权信息</p>
                            </div>
                            <div class="empty-actions">
                                <el-button v-permission type="primary" @click="showAddDialog" icon="el-icon-plus">
                                    添加第一个授权
                                </el-button>
                            </div>
                        </div>
                    </template>
                </el-table>

                <!-- 优化后的分页 -->
                <div class="pagination-wrapper">
                    <div class="pagination-info">
                        <span class="total-text">
                            共 <span class="total-count">{{ pagination.total }}</span> 条记录
                        </span>
                        <span class="page-info">
                            第 {{ pagination.currentPage }} 页，共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
                        </span>
                    </div>
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]"
                        :page-size="pagination.pageSize" layout="sizes, prev, pager, next, jumper"
                        :total="pagination.total" :background="true" class="enhanced-pagination" />
                </div>
            </div>
        </div>


        <!-- 新增/编辑对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="750px" :close-on-click-modal="false"
            @close="handleDialogClose" class="auth-dialog" :append-to-body="true">
            <!-- 对话框头部 -->
            <div slot="title" class="dialog-header">
                <div class="dialog-title">
                    <i class="el-icon-key dialog-icon"></i>
                    <span>{{ dialogTitle }}</span>
                </div>
                <div class="dialog-subtitle">
                    为链接 <span class="link-highlight">{{ linkAddress }}</span> 添加授权信息
                </div>
            </div>

            <div class="dialog-content">
                <el-form ref="authForm" :model="authForm" :rules="formRules" label-width="140px" class="auth-form">
                    <!-- 基本信息区域 -->
                    <div class="form-section">
                        <div class="section-header">
                            <i class="el-icon-office-building"></i>
                            <span>企业基本信息</span>
                        </div>

                        <div class="form-grid">
                            <el-form-item label="公司名称" prop="companyName" class="form-item-full">
                                <el-input v-model="authForm.companyName" placeholder="请输入完整的公司名称" :maxlength="100"
                                    show-word-limit clearable>
                                    <template slot="prepend">
                                        <i class="el-icon-office-building"></i>
                                    </template>
                                </el-input>
                            </el-form-item>

                            <el-form-item label="统一社会信用代码" prop="creditCode" class="form-item-full">
                                <el-input v-model="authForm.creditCode" placeholder="请输入18位统一社会信用代码" :maxlength="18"
                                    show-word-limit clearable>
                                    <template slot="prepend">
                                        <i class="el-icon-postcard"></i>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </div>
                    </div>

                    <!-- 文件上传区域 -->
                    <div class="form-section">
                        <div class="section-header">
                            <i class="el-icon-document"></i>
                            <span>授权证明文件</span>
                        </div>

                        <el-form-item label="盖章扫描件" prop="sealImg" class="upload-form-item">
                            <div class="upload-section">
                                <!-- 上传区域 -->
                                <div class="upload-area">
                                    <el-upload v-permission class="seal-uploader" :action="uploadUrl" :headers="headers"
                                        :show-file-list="false" :on-success="handleSealImgSuccess"
                                        :on-error="handleUploadError" :before-upload="beforeUpload"
                                        :on-progress="handleUploadProgress" accept="image/*" drag>
                                        <div v-if="authForm.sealImg" class="upload-success">
                                            <div class="uploaded-image-container">
                                                <img :src="getImageUrl(authForm.sealImg)" class="uploaded-image" />
                                                <div class="image-actions">
                                                    <el-button type="text" @click.stop="previewImage(authForm.sealImg)"
                                                        class="action-btn preview-btn">
                                                        <i class="el-icon-zoom-in"></i>
                                                        预览
                                                    </el-button>
                                                    <el-button type="text" @click.stop="removeSealImg"
                                                        class="action-btn remove-btn">
                                                        <i class="el-icon-delete"></i>
                                                        删除
                                                    </el-button>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="upload-dragger">
                                            <div class="upload-content">
                                                <i class="el-icon-upload upload-icon"></i>
                                                <div class="upload-text">
                                                    <p class="primary-text">点击或拖拽文件到此区域上传</p>
                                                    <p class="secondary-text">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
                                                </div>
                                            </div>
                                        </div>
                                    </el-upload>

                                    <!-- 上传进度 -->
                                    <div v-if="uploading" class="upload-progress-wrapper">
                                        <el-progress :percentage="uploadProgress" :stroke-width="8" :text-inside="true"
                                            status="success" class="upload-progress">
                                        </el-progress>
                                        <span class="progress-text">正在上传...</span>
                                    </div>
                                </div>

                                <!-- 示例图片区域 -->
                                <div class="examples-area">
                                    <div class="examples-header">
                                        <i class="el-icon-picture-outline"></i>
                                        <span>参考示例</span>
                                    </div>
                                    <div class="examples-grid">
                                        <div class="example-card" @click="showExampleImages(0)">
                                            <div class="example-image-wrapper">
                                                <img src="../../../.././../assets/images/link1.png" alt="链接授权证明示例"
                                                    class="example-image" />
                                                <div class="example-overlay">
                                                    <i class="el-icon-view"></i>
                                                </div>
                                            </div>
                                            <div class="example-title">链接授权证明</div>
                                        </div>
                                        <div class="example-card" @click="showExampleImages(1)">
                                            <div class="example-image-wrapper">
                                                <img src="../../../.././../assets/images/QICC.png" alt="企业信用证明示例"
                                                    class="example-image" />
                                                <div class="example-overlay">
                                                    <i class="el-icon-view"></i>
                                                </div>
                                            </div>
                                            <div class="example-title">企业信用证明</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 提示信息 -->
                            <div class="upload-tips">
                                <el-alert title="上传说明" type="info" :closable="false" show-icon>
                                    <template slot="description">
                                        <p>• 如果链接注册主体与签名主体控股关系在50%以下，需要提供链接授权使用证明</p>
                                        <p>• 请确保上传的文件清晰可见，包含完整的企业信息和公章</p>
                                        <p>• 支持的格式：JPG、PNG，文件大小不超过2MB</p>
                                    </template>
                                </el-alert>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false" size="medium">
                    <i class="el-icon-close"></i>
                    取消
                </el-button>
                <el-button v-permission type="primary" @click="handleSubmit" :loading="submitLoading" size="medium">
                    <i class="el-icon-check"></i>
                    {{ isEdit ? '更新授权' : '新增授权' }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 图片预览对话框 -->
        <el-dialog :visible.sync="imageDialogVisible" width="800px">
            <el-image style="width: 100%; height: 100%" :src="previewUrl" :preview-src-list="[previewUrl]">
            </el-image>
        </el-dialog>

        <!-- 示例图片管理对话框 -->
        <el-dialog :visible.sync="exampleDialogVisible" width="1000px" class="example-management-dialog"
            :close-on-click-modal="false" :append-to-body="true">
            <!-- 自定义头部 -->
            <div slot="title" class="example-dialog-header">
                <div class="header-left">
                    <i class="el-icon-picture-outline header-icon"></i>
                    <div class="header-info">
                        <h3 class="dialog-title">授权证明示例管理</h3>
                        <p class="dialog-subtitle">查看上传文件的参考示例和格式要求</p>
                    </div>
                </div>
                <div class="header-actions">
                    <el-button type="text" @click="toggleViewMode" class="view-toggle-btn">
                        <i :class="viewMode === 'grid' ? 'el-icon-s-unfold' : 'el-icon-s-grid'"></i>
                        {{ viewMode === 'grid' ? '列表视图' : '网格视图' }}
                    </el-button>
                </div>
            </div>

            <div class="example-management-content">
                <!-- 网格视图 -->
                <div v-if="viewMode === 'grid'" class="grid-view">
                    <div class="examples-grid-large">
                        <div v-for="(item, index) in exampleImagesData" :key="index" class="example-card-large"
                            @click="selectExample(index)">
                            <div class="card-image-wrapper">
                                <img :src="item.url" :alt="item.title" class="card-image" />
                                <div class="card-overlay">
                                    <div class="overlay-content">
                                        <i class="el-icon-view overlay-icon"></i>
                                        <span class="overlay-text">点击查看详情</span>
                                    </div>
                                </div>
                                <div class="card-badge">示例 {{ index + 1 }}</div>
                            </div>
                            <div class="card-content">
                                <h4 class="card-title">{{ item.title }}</h4>
                                <p class="card-description">{{ item.description }}</p>
                                <div class="card-tags">
                                    <el-tag size="mini" type="info">参考示例</el-tag>
                                    <el-tag size="mini" type="success">标准格式</el-tag>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详情视图 -->
                <div v-else class="detail-view">
                    <div class="detail-sidebar">
                        <div class="sidebar-header">
                            <h4>示例列表</h4>
                            <span class="example-count">共 {{ exampleImagesData.length }} 个示例</span>
                        </div>
                        <div class="examples-list">
                            <div v-for="(item, index) in exampleImagesData" :key="index" class="list-item"
                                :class="{ active: index === currentExampleIndex }" @click="currentExampleIndex = index">
                                <div class="list-item-image">
                                    <img :src="item.url" :alt="item.title" />
                                </div>
                                <div class="list-item-content">
                                    <div class="list-item-title">{{ item.title }}</div>
                                    <div class="list-item-index">示例 {{ index + 1 }}</div>
                                </div>
                                <div class="list-item-indicator">
                                    <i class="el-icon-arrow-right"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-main">
                        <div class="detail-header">
                            <div class="detail-title-section">
                                <h3 class="detail-title">{{ currentExample.title }}</h3>
                                <div class="detail-meta">
                                    <span class="meta-item">
                                        <i class="el-icon-picture-outline"></i>
                                        示例 {{ currentExampleIndex + 1 }} / {{ exampleImagesData.length }}
                                    </span>
                                    <span class="meta-item">
                                        <i class="el-icon-document"></i>
                                        授权证明文件
                                    </span>
                                </div>
                            </div>
                            <div class="detail-actions">
                                <el-button size="small" :disabled="currentExampleIndex === 0" @click="previousExample"
                                    icon="el-icon-arrow-left">
                                    上一个
                                </el-button>
                                <el-button size="small" :disabled="currentExampleIndex === exampleImagesData.length - 1"
                                    @click="nextExample" icon="el-icon-arrow-right">
                                    下一个
                                </el-button>
                            </div>
                        </div>

                        <div class="detail-content">
                            <div class="image-section">
                                <div class="image-container">
                                    <el-image :src="currentExample.url" fit="contain" class="detail-image"
                                        :preview-src-list="exampleImagesData.map(item => item.url)"
                                        :initial-index="currentExampleIndex">
                                        <div slot="placeholder" class="image-placeholder">
                                            <i class="el-icon-loading"></i>
                                            <span>加载中...</span>
                                        </div>
                                        <div slot="error" class="image-error">
                                            <i class="el-icon-picture-outline"></i>
                                            <span>加载失败</span>
                                        </div>
                                    </el-image>
                                </div>
                                <div class="image-actions">
                                    <el-button type="text" @click="downloadExample(currentExample)" class="action-btn">
                                        <i class="el-icon-download"></i>
                                        下载示例
                                    </el-button>
                                    <el-button type="text" @click="previewFullscreen" class="action-btn">
                                        <i class="el-icon-full-screen"></i>
                                        全屏查看
                                    </el-button>
                                </div>
                            </div>

                            <div class="description-section">
                                <div class="description-header">
                                    <h4>
                                        <i class="el-icon-info"></i>
                                        使用说明
                                    </h4>
                                </div>
                                <div class="description-content">
                                    <p class="primary-description">{{ currentExample.description }}</p>

                                    <div class="requirements-list">
                                        <h5>文件要求：</h5>
                                        <ul>
                                            <li><i class="el-icon-check"></i>文件格式：JPG、PNG</li>
                                            <li><i class="el-icon-check"></i>文件大小：不超过 2MB</li>
                                            <li><i class="el-icon-check"></i>图片清晰：能够清楚看到文字和印章</li>
                                            <li><i class="el-icon-check"></i>内容完整：包含必要的企业信息</li>
                                        </ul>
                                    </div>

                                    <div class="tips-section">
                                        <el-alert title="温馨提示" type="warning" :closable="false" show-icon>
                                            <template slot="description">
                                                <p>• 请确保上传的文件内容与示例格式一致</p>
                                                <p>• 如有疑问，可参考示例图片的格式和内容要求</p>
                                                <p v-if="currentExampleIndex === 0">• 链接授权证明：适用于控股关系在50%以下的情况</p>
                                                <p v-if="currentExampleIndex === 1">• 企业信用证明：营业执照或统一社会信用代码证书</p>
                                            </template>
                                        </el-alert>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div slot="footer" class="example-dialog-footer">
                <div class="footer-info">
                    <i class="el-icon-info"></i>
                    <span>参考以上示例格式上传您的授权证明文件</span>
                </div>
                <div class="footer-actions">
                    <el-button @click="exampleDialogVisible = false" size="medium">
                        <i class="el-icon-close"></i>
                        关闭
                    </el-button>
                    <el-button type="primary" @click="useExample" size="medium">
                        <i class="el-icon-check"></i>
                        我知道了
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    name: 'LinkRecordAuth',
    data() {
        const validateCreditCode = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入统一社会信用代码'));
                return;
            }
            callback();
        };

        return {
            loading: false,
            submitLoading: false,
            dialogVisible: false,
            imageDialogVisible: false,
            exampleDialogVisible: false,
            isEdit: false,
            currentRecord: null,
            linkId: null,
            linkAddress: '',
            previewUrl: '',

            // 上传相关
            uploading: false,
            uploadProgress: 0,
            headers: {},

            // 示例图片相关
            currentExampleImages: [],
            currentExampleIndex: 0,
            viewMode: 'grid', // 'grid' 或 'detail'

            // 示例图片数据
            exampleImagesData: [
                {
                    url: require('@/assets/images/link1.png'),
                    title: '链接授权证明示例',
                    description: '当链接注册主体与签名主体控股关系在50%以下时，需要提供此类授权证明'
                },
                {
                    url: require('@/assets/images/QICC.png'),
                    title: '企业信用证明示例',
                    description: '企业统一社会信用代码证书或营业执照等相关证明文件'
                }
            ],

            // 分页信息
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },

            // 表格数据
            tableData: [],

            // 表单数据
            authForm: {
                companyName: '',

                creditCode: '',
                sealImg: '',
                linkId: null
            },

            // 表单验证规则
            formRules: {
                companyName: [
                    { required: true, message: '请输入公司名称', trigger: 'blur' },
                    { min: 2, max: 100, message: '公司名称长度在 2 到 100 个字符', trigger: 'blur' }
                ],
                creditCode: [
                    { required: true, validator: validateCreditCode, trigger: 'blur' }
                ],
                sealImg: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!this.authForm.sealImg) {
                                callback(new Error('请上传盖章扫描件'));
                            } else {
                                callback();
                            }
                        },
                        trigger: ['change', 'blur']
                    }
                ]
            }
        };
    },

    computed: {
        dialogTitle() {
            return this.isEdit ? '编辑授权' : '新增授权';
        },

        // 文件上传地址
        uploadUrl() {
            return this.API.cpus + "v3/file/upload";
        },

        // 当前示例
        currentExample() {
            return this.exampleImagesData[this.currentExampleIndex] || {};
        }
    },

    created() {
        // 从路由参数获取linkId
        this.linkId = this.$route.query.linkId;
        this.linkAddress = this.$route.query.linkAddress || '';
        this.authForm.linkId = parseInt(this.linkId);

        this.headers = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN")
        };

        this.loadData();
    },

    methods: {
        // 返回上一页
        goBack() {
            this.$router.go(-1);
        },

        // 加载数据
        loadData() {
            this.loading = true;
            const params = {
                currentPage: this.pagination.currentPage,
                pageSize: this.pagination.pageSize,
                linkId: this.linkId
            };

            this.$api.post(
                this.API.cpus + 'consumersmstemplatereportlinkproof/page',
                params,
                (res) => {
                    this.loading = false;
                    if (res.code === 200) {
                        this.tableData = res.data.records || [];
                        this.pagination.total = res.data.total || 0;
                    } else {
                        this.$message.error(res.msg || '获取数据失败');
                    }
                },
                (error) => {
                    this.loading = false;
                    console.error('加载数据失败:', error);
                    this.$message.error('获取数据失败，请稍后重试');
                }
            );
        },

        // 显示新增对话框
        showAddDialog() {
            this.isEdit = false;
            this.currentRecord = null;
            this.authForm = {
                companyName: '',
                creditCode: '',
                sealImg: '',

                linkId: parseInt(this.linkId)
            };
            this.dialogVisible = true;
        },

        // 显示编辑对话框
        showEditDialog(record) {
            this.isEdit = true;
            this.currentRecord = record;
            this.authForm = {
                id: record.id,

                companyName: record.companyName || '',
                creditCode: record.creditCode || '',
                sealImg: record.sealImg || ''
            };
            this.dialogVisible = true;
        },

        // 关闭对话框
        handleDialogClose() {
            if (this.$refs.authForm) {
                this.$refs.authForm.clearValidate();
            }
            this.clearDialogData();
        },

        // 清除对话框数据
        clearDialogData() {
            this.authForm = {
                companyName: '',
                creditCode: '',
                sealImg: '',
                linkId: parseInt(this.linkId)
            };
            this.isEdit = false;
            this.currentRecord = null;
            this.uploading = false;
            this.uploadProgress = 0;
        },

        // 提交表单
        handleSubmit() {
            this.$refs.authForm.validate((valid) => {
                if (!valid) {
                    this.$message.warning('请填写完整的表单信息');
                    return;
                }

                this.submitLoading = true;
                if (this.isEdit) {
                    this.updateRecord();
                } else {
                    this.addRecord();
                }
            });
        },

        // 新增记录
        addRecord() {
            this.$api.post(
                this.API.cpus + 'consumersmstemplatereportlinkproof',
                this.authForm,
                (res) => {
                    this.submitLoading = false;
                    if (res.code === 200) {
                        this.$message.success('新增成功');
                        this.dialogVisible = false;
                        this.clearDialogData();
                        this.loadData();
                    } else {
                        this.$message.error(res.msg || '新增失败');
                    }
                },
                (error) => {
                    this.submitLoading = false;
                    console.error('新增失败:', error);
                    this.$message.error('新增失败，请稍后重试');
                }
            );
        },

        // 更新记录
        updateRecord() {
            this.$api.put(
                this.API.cpus + 'consumersmstemplatereportlinkproof',
                this.authForm,
                (res) => {
                    this.submitLoading = false;
                    if (res.code === 200) {
                        this.$message.success('更新成功');
                        this.dialogVisible = false;
                        this.clearDialogData();
                        this.loadData();
                    } else {
                        this.$message.error(res.msg || '更新失败');
                    }
                },
                (error) => {
                    this.submitLoading = false;
                    console.error('更新失败:', error);
                    this.$message.error('更新失败，请稍后重试');
                }
            );
        },

        // 删除记录
        handleDelete(record) {
            this.$confirm('确认删除这条授权记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$api.delete(
                    this.API.cpus + 'consumersmstemplatereportlinkproof/' + record.id,
                    {},
                    (res) => {
                        if (res.code === 200) {
                            this.$message.success('删除成功');
                            this.loadData();
                        } else {
                            this.$message.error(res.msg || '删除失败');
                        }
                    },
                    (error) => {
                        console.error('删除失败:', error);
                        this.$message.error('删除失败，请稍后重试');
                    }
                );
            }).catch(() => {
                // 用户取消删除
            });
        },

        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.pagination.currentPage = 1;
            this.loadData();
        },

        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.loadData();
        },

        // 显示新增对话框
        showAddDialog() {
            this.isEdit = false;
            this.authForm = {
                companyName: '',

                creditCode: '',
                sealImg: '',
                linkId: parseInt(this.linkId)
            };
            this.dialogVisible = true;
        },

        // 表格行类名
        tableRowClassName({ row, rowIndex }) {
            if (row.auditStatus === 2) return 'success-row';
            if (row.auditStatus === 3) return 'warning-row';
            return '';
        },

        // 复制到剪贴板
        copyToClipboard(text) {
            try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.$message.success('已复制到剪贴板');
            } catch (error) {
                this.$message.error('复制失败，请手动复制');
            }
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return '-';
            return moment(date).format('YYYY-MM-DD');
        },

        // 格式化时间
        formatTime(date) {
            if (!date) return '-';
            return moment(date).format('HH:mm:ss');
        },

        // 获取审核状态标签类型
        getAuditStatusTagType(status) {
            const statusMap = {
                0: 'info',     // 草稿
                1: 'warning',  // 审核中
                2: 'success',  // 审核通过
                3: 'danger'    // 审核拒绝
            };
            return statusMap[status] || 'info';
        },

        // 获取审核状态图标
        getAuditStatusIcon(status) {
            const statusMap = {
                0: 'el-icon-edit-outline',      // 草稿
                1: 'el-icon-clock',             // 审核中
                2: 'el-icon-circle-check',      // 通过
                3: 'el-icon-circle-close'       // 拒绝
            };
            return statusMap[status] || 'el-icon-question';
        },

        // 获取审核状态文本
        getAuditStatusText(status) {
            const statusMap = {
                0: '草稿',
                1: '审核中',
                2: '审核通过',
                3: '审核拒绝'
            };
            return statusMap[status] || '未知';
        },

        // 文件上传前校验
        beforeUpload(file) {
            const isImage = file.type.startsWith('image/');
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isImage) {
                this.$message.error('上传文件只能是图片格式!');
                return false;
            }
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 2MB!');
                return false;
            }
            return true;
        },

        // 处理上传进度
        handleUploadProgress(event, file, fileList) {
            this.uploading = true;
            this.uploadProgress = Math.round(event.percent);
        },

        // 盖章扫描件上传成功
        handleSealImgSuccess(res, file) {
            this.uploading = false;
            this.uploadProgress = 0;
            if (res.code === 200) {
                this.authForm.sealImg = res.data.fullpath;
                this.$message.success('盖章扫描件上传成功');
            } else {
                this.$message.error(res.msg || '图片上传失败');
            }
        },

        // 文件上传失败
        handleUploadError(err, file, fileList) {
            this.uploading = false;
            this.uploadProgress = 0;
            this.$message.error('图片上传失败，请重试');
        },

        // 预览图片
        previewImage(imagePath, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            this.previewUrl = this.getImageUrl(imagePath);
            this.imageDialogVisible = true;
        },

        // 移除盖章扫描件
        removeSealImg(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            this.$confirm('确认删除已上传的盖章扫描件吗？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.authForm.sealImg = '';
                this.uploading = false;
                this.uploadProgress = 0;
                this.$message.success('盖章扫描件已删除');
            }).catch(() => {
                // 用户取消删除
            });
        },

        // 获取图片URL
        getImageUrl(path) {
            if (!path) return '';
            if (path.startsWith('http') || path.startsWith('https')) {
                return path;
            }
            // 确保返回完整的图片URL
            const imageUrl = this.API.imgU + path;
            console.log('图片URL:', imageUrl); // 调试日志，可以在生产环境中移除
            return imageUrl;
        },

        // 显示示例图片
        showExampleImages(index = 0) {
            this.currentExampleImages = this.exampleImagesData;
            this.currentExampleIndex = index;
            this.viewMode = 'detail'; // 默认打开详情视图
            this.exampleDialogVisible = true;
        },

        // 切换视图模式
        toggleViewMode() {
            this.viewMode = this.viewMode === 'grid' ? 'detail' : 'grid';
        },

        // 选择示例（网格视图）
        selectExample(index) {
            this.currentExampleIndex = index;
            this.viewMode = 'detail'; // 切换到详情视图
        },

        // 上一张示例图片
        previousExample() {
            if (this.currentExampleIndex > 0) {
                this.currentExampleIndex--;
            }
        },

        // 下一张示例图片
        nextExample() {
            if (this.currentExampleIndex < this.exampleImagesData.length - 1) {
                this.currentExampleIndex++;
            }
        },

        // 下载示例图片
        downloadExample(example) {
            try {
                const link = document.createElement('a');
                link.href = example.url;
                link.download = `${example.title}.jpg`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                this.$message.success('示例图片下载中...');
            } catch (error) {
                this.$message.error('下载失败，请稍后重试');
            }
        },

        // 全屏预览
        previewFullscreen() {
            // 触发Element UI的图片预览功能
            const imageList = this.exampleImagesData.map(item => item.url);
            this.$el.querySelector('.detail-image img').click();
        },

        // 使用示例
        useExample() {
            this.exampleDialogVisible = false;
            this.$message.success('请参考示例格式上传您的授权证明文件');
        },

        // 处理图片点击事件（调试用）
        handleImageClick(imagePath) {
            console.log('图片点击事件触发:', imagePath);
            console.log('完整图片URL:', this.getImageUrl(imagePath));
            // 这个方法主要用于调试，确保图片点击事件能正常触发
            // Element UI 的预览功能应该会自动处理
        },
        // 判断是否是当前用户创建的记录
        isCurrentUser(record) {
            // 基于 allowOperate 字段判断
            return record.allowOperate || false;
        },
        canEdit(record) {
            // 基于 allowOperate 字段判断
            if (!record.allowOperate) {
                return false;
            }

            // 审核状态为2（审核通过）时，禁止编辑
            if (record.auditStatus == 2 || record.auditStatus == 1) {
                return false;
            }

            return true;
        },
    }
};
</script>

<style lang="less" scoped>
@import '~@/styles/template-common.less';

/* Element UI 图片预览全局样式覆盖 */
/deep/ .el-image-viewer__wrapper {
    z-index: 9999 !important;
}

/deep/ .el-image__preview {
    cursor: pointer;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .page-title {
            margin: 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;

            i {
                margin-right: 8px;
                color: #409eff;
                font-size: 22px;
            }
        }

        .link-info {
            .el-tag {
                background: #f0f7ff;
                border-color: #b3d8ff;
                color: #409eff;
            }
        }
    }

    .header-actions {
        display: flex;
        gap: 12px;
    }
}

.table-container {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 表格头部操作区域 */
.table-header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;

    .header-info {
        display: flex;
        align-items: center;
        gap: 24px;

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #374151;

            i {
                color: #6366f1;
                font-size: 16px;
            }

            .link-text {
                font-weight: 500;
                color: #1f2937;
                max-width: 300px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .header-actions {
        display: flex;
        gap: 12px;

        .refresh-btn {
            border-color: #d1d5db;
            color: #6b7280;

            &:hover {
                border-color: #6366f1;
                color: #6366f1;
                background: #f0f4ff;
            }
        }

        .add-btn {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            border: none;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);

            &:hover {
                opacity: 0.9;
                transform: translateY(-1px);
            }
        }
    }
}

/* 增强的表格样式 */
.enhanced-table {
    /deep/ .el-table__body-wrapper {
        .el-table__row {
            transition: all 0.3s ease;

            &:hover {
                background: #f9fafb !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            &.success-row {
                background: rgba(34, 197, 94, 0.05) !important;
            }

            &.warning-row {
                background: rgba(239, 68, 68, 0.05) !important;
            }
        }
    }

    /deep/ .el-table__header {
        th {
            background: #f8fafc !important;
            border-color: #e5e7eb !important;
            color: #374151 !important;
            font-weight: 600 !important;
        }
    }

    /deep/ .el-table--border {
        border-color: #e5e7eb;

        &::after {
            background: #e5e7eb;
        }
    }

    /deep/ .el-table td,
    /deep/ .el-table th {
        border-color: #e5e7eb !important;
    }
}

/* 表格单元格样式 */
.table-cell {
    padding: 8px 0;
    line-height: 1.5;
}

/* 用户单元格 */
.user-cell {
    .user-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .user-icon {
            color: #6366f1;
            font-size: 16px;
        }

        .user-name {
            font-weight: 500;
            color: #1f2937;
        }
    }
}

/* 公司单元格 */
.company-cell {
    .company-info {
        display: flex;
        align-items: flex-start;
        gap: 10px;

        .company-icon {
            color: #059669;
            font-size: 16px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .company-details {
            flex: 1;
            min-width: 0;

            .company-name {
                font-weight: 500;
                color: #1f2937;
                margin-bottom: 2px;
                word-break: break-all;
            }

            .company-meta {
                font-size: 12px;
                color: #6b7280;
                word-break: break-all;
            }
        }
    }
}

/* 信用代码单元格 */
.credit-cell {
    .credit-code {
        display: flex;
        align-items: center;
        gap: 8px;

        .credit-icon {
            color: #dc2626;
            font-size: 14px;
            flex-shrink: 0;
        }

        .code-text {
            flex: 1;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            color: #374151;
            word-break: break-all;
        }

        .copy-btn {
            padding: 4px;
            color: #6b7280;
            opacity: 0;
            transition: all 0.3s ease;

            &:hover {
                color: #6366f1;
                background: #f0f4ff;
            }
        }

        &:hover .copy-btn {
            opacity: 1;
        }
    }

    .empty-text {
        color: #9ca3af;
        font-style: italic;
    }
}

/* 审核原因单元格 */
.reason-cell {
    .reason-content {
        width: 100%;

        .reason-wrapper {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: help;
            max-width: 100%;
            overflow: hidden;

            .reason-icon {
                font-size: 16px;
                margin-top: 1px;
                flex-shrink: 0;
            }

            .reason-text {
                flex: 1;
                min-width: 0;
                overflow: hidden;

                .reason-label {
                    font-size: 12px;
                    font-weight: 600;
                    margin-bottom: 2px;
                    display: block;
                }

                .reason-detail {
                    font-size: 13px;
                    line-height: 1.4;
                    display: block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 100%;
                }
            }
        }

        .reject-reason {
            background: rgba(239, 68, 68, 0.05);
            border: 1px solid rgba(239, 68, 68, 0.2);

            &:hover {
                background: rgba(239, 68, 68, 0.08);
                border-color: rgba(239, 68, 68, 0.3);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
            }

            .reject-icon {
                color: #ef4444;
            }

            .reason-label {
                color: #dc2626;
            }

            .reason-detail {
                color: #991b1b;
            }
        }

        .pass-reason {
            background: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.2);

            &:hover {
                background: rgba(16, 185, 129, 0.08);
                border-color: rgba(16, 185, 129, 0.3);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
            }

            .pass-icon {
                color: #10b981;
            }

            .reason-label {
                color: #059669;
            }

            .reason-detail {
                color: #047857;
            }
        }

        .default-reason {
            background: rgba(59, 130, 246, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.2);

            &:hover {
                background: rgba(59, 130, 246, 0.08);
                border-color: rgba(59, 130, 246, 0.3);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
            }

            .default-icon {
                color: #3b82f6;
            }

            .reason-detail {
                color: #1e40af;
            }
        }
    }

    .no-reason {
        .empty-text {
            color: #9ca3af;
            font-style: italic;
            font-size: 13px;
        }
    }
}

/* Tooltip 自定义样式 */
/deep/ .el-tooltip__popper {
    max-width: 400px;
    
    &.is-dark {
        background: rgba(0, 0, 0, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.1);
        
        .popper__arrow {
            border-top-color: rgba(0, 0, 0, 0.9);
        }
    }
}

/* 图片单元格 */
.image-cell {
    .image-preview-wrapper {
        position: relative;
        display: inline-block;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

            .image-overlay {
                opacity: 1;
            }
        }

        .table-image {
            width: 64px;
            height: 64px;
            border-radius: 8px;
            display: block;
            
            /deep/ img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 8px;
                cursor: pointer;
            }
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;

            i {
                font-size: 20px;
                margin-bottom: 4px;
            }

            span {
                font-size: 12px;
                font-weight: 500;
            }
        }

        .image-placeholder,
        .image-error {
            width: 64px;
            height: 64px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f3f4f6;
            color: #9ca3af;
            border-radius: 8px;

            i {
                font-size: 20px;
                margin-bottom: 4px;
            }

            span {
                font-size: 10px;
                text-align: center;
            }
        }
    }

    .no-image {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 64px;
        height: 64px;
        background: #f9fafb;
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        color: #9ca3af;

        i {
            font-size: 20px;
            margin-bottom: 4px;
        }

        span {
            font-size: 10px;
            text-align: center;
        }
    }
}

/* 状态单元格 */
.status-cell {
    .status-wrapper {
        display: flex;
        justify-content: center;

        .status-tag {
            font-weight: 500;
            border-radius: 20px;
            padding: 4px 12px;
            display: flex;
            align-items: center;
            gap: 4px;

            i {
                font-size: 12px;
            }
        }
    }
}

/* 时间单元格 */
.time-cell {
    .time-info {
        display: flex;
        align-items: flex-start;
        gap: 8px;

        .time-icon {
            color: #6b7280;
            font-size: 14px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .time-details {
            .date-text {
                font-weight: 500;
                color: #1f2937;
                font-size: 14px;
                margin-bottom: 2px;
            }

            .time-text {
                font-size: 12px;
                color: #6b7280;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            }
        }
    }
}

/* 操作单元格 */
.action-cell {
    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;

        .action-btn {
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;

            &.edit-btn {
                background: #eff6ff;
                border-color: #3b82f6;
                color: #3b82f6;

                &:hover {
                    background: #3b82f6;
                    color: white;
                    transform: translateY(-1px);
                }
            }

            &.delete-btn {
                background: #fef2f2;
                border-color: #ef4444;
                color: #ef4444;

                &:hover {
                    background: #ef4444;
                    color: white;
                    transform: translateY(-1px);
                }
            }
        }
    }
}

/* 空状态样式 */
.empty-state {
    padding: 60px 20px;
    text-align: center;

    .empty-icon {
        margin-bottom: 20px;

        i {
            font-size: 64px;
            color: #d1d5db;
        }
    }

    .empty-text {
        margin-bottom: 24px;

        h3 {
            margin: 0 0 8px 0;
            font-size: 18px;
            color: #374151;
            font-weight: 600;
        }

        p {
            margin: 0;
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }
    }

    .empty-actions {
        .el-button {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);

            &:hover {
                opacity: 0.9;
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
            }
        }
    }
}

/* 分页样式 */
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;

    .pagination-info {
        display: flex;
        align-items: center;
        gap: 16px;
        font-size: 14px;
        color: #6b7280;

        .total-text {
            .total-count {
                font-weight: 600;
                color: #1f2937;
            }
        }

        .page-info {
            color: #9ca3af;
        }
    }

    .enhanced-pagination {
        /deep/ .el-pagination {
            .el-pager li {
                background: white;
                border: 1px solid #e5e7eb;
                margin: 0 2px;
                border-radius: 6px;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #6366f1;
                    color: #6366f1;
                }

                &.active {
                    background: #6366f1;
                    border-color: #6366f1;
                    color: white;
                }
            }

            .btn-prev,
            .btn-next {
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #6366f1;
                    color: #6366f1;
                }
            }

            .el-select .el-input {
                .el-input__inner {
                    border-radius: 6px;
                }
            }
        }
    }
}

.content-cell {
    word-break: break-all;
    line-height: 1.4;
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;
}

/* 授权对话框样式 */
.auth-dialog {
    /deep/ .el-dialog {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

        .el-dialog__header {
            padding: 0;
            border: none;
        }

        .el-dialog__body {
            padding: 0;
        }

        .el-dialog__footer {
            padding: 20px 30px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
        }
    }
}

/* 对话框头部 */
.dialog-header {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    padding: 24px 30px;
    color: white;

    .dialog-title {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;

        .dialog-icon {
            font-size: 20px;
            margin-right: 10px;
            opacity: 0.9;
        }
    }

    .dialog-subtitle {
        font-size: 14px;
        opacity: 0.9;
        line-height: 1.5;

        .link-highlight {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 500;
        }
    }
}

/* 对话框内容 */
.dialog-content {
    padding: 30px;
    background: #fff;
}

.auth-form {
    .form-section {
        margin-bottom: 32px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f1f5f9;

        i {
            font-size: 18px;
            color: #667eea;
            margin-right: 10px;
        }

        span {
            font-size: 16px;
            font-weight: 600;
            color: #334155;
        }
    }

    .form-grid {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .form-item-full {
        /deep/ .el-form-item__label {
            font-weight: 500;
            color: #475569;
        }

        /deep/ .el-input {
            .el-input__inner {
                border-radius: 8px;
                border: 1px solid #d1d5db;
                transition: all 0.3s ease;
                font-size: 14px;
                padding: 0 15px;

                &:focus {
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
            }

            .el-input-group__prepend {
                background: #f8fafc;
                border-color: #d1d5db;
                color: #6b7280;
            }
        }
    }
}

/* 上传区域样式 */
.upload-form-item {
    /deep/ .el-form-item__label {
        font-weight: 500;
        color: #475569;
    }
}

.upload-section {
    display: flex;
    gap: 24px;
    align-items: flex-start;

    .upload-area {
        flex: 1;
    }

    .examples-area {
        width: 240px;
        flex-shrink: 0;
    }
}

.seal-uploader {
    /deep/ .el-upload {
        width: 100%;
    }

    /deep/ .el-upload-dragger {
        width: 100%;
        height: 200px;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        background: #f8fafc;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        &.is-dragover {
            border-color: #667eea;
            background: #eff6ff;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
        }
    }
}

.upload-dragger {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .upload-content {
        text-align: center;

        .upload-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 16px;
            opacity: 0.8;
        }

        .upload-text {
            .primary-text {
                font-size: 16px;
                color: #374151;
                margin: 0 0 8px 0;
                font-weight: 500;
            }

            .secondary-text {
                font-size: 13px;
                color: #6b7280;
                margin: 0;
                line-height: 1.4;
            }
        }
    }
}

.upload-success {
    padding: 20px;

    .uploaded-image-container {
        text-align: center;

        .uploaded-image {
            max-width: 100%;
            max-height: 120px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 16px;
        }

        .image-actions {
            display: flex;
            justify-content: center;
            gap: 12px;

            .action-btn {
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 500;

                &.preview-btn {
                    color: #667eea;

                    &:hover {
                        background: #eff6ff;
                        color: #4f46e5;
                    }
                }

                &.remove-btn {
                    color: #ef4444;

                    &:hover {
                        background: #fef2f2;
                        color: #dc2626;
                    }
                }

                i {
                    margin-right: 4px;
                }
            }
        }
    }
}

.upload-progress-wrapper {
    margin-top: 16px;
    text-align: center;

    .upload-progress {
        margin-bottom: 8px;
    }

    .progress-text {
        font-size: 13px;
        color: #6b7280;
    }
}

/* 示例图片区域 */
.examples-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    color: #475569;

    i {
        margin-right: 8px;
        color: #667eea;
    }
}

.examples-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.example-card {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;

    &:hover {
        border-color: #667eea;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        transform: translateY(-2px);
    }

    .example-image-wrapper {
        position: relative;
        overflow: hidden;

        .example-image {
            width: 100%;
            height: 80px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .example-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(102, 126, 234, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            i {
                color: white;
                font-size: 20px;
            }
        }

        &:hover {
            .example-image {
                transform: scale(1.05);
            }

            .example-overlay {
                opacity: 1;
            }
        }
    }

    .example-title {
        padding: 8px 12px;
        font-size: 12px;
        color: #374151;
        font-weight: 500;
        text-align: center;
        background: #f9fafb;
    }
}

/* 上传提示 */
.upload-tips {
    margin-top: 20px;

    /deep/ .el-alert {
        border-radius: 8px;
        border: 1px solid #bfdbfe;
        background: #f0f9ff;

        .el-alert__content {
            .el-alert__title {
                font-size: 14px;
                font-weight: 500;
                color: #1e40af;
            }

            .el-alert__description {
                margin-top: 8px;
                font-size: 13px;
                line-height: 1.6;
                color: #3730a3;

                p {
                    margin: 4px 0;
                }
            }
        }

        .el-alert__icon {
            color: #3b82f6;
        }
    }
}

/* 对话框底部 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .el-button {
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 500;

        i {
            margin-right: 6px;
        }

        &.el-button--primary {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            border: none;

            &:hover {
                opacity: 0.9;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            }
        }

        &.el-button--default {
            border-color: #d1d5db;
            color: #6b7280;

            &:hover {
                border-color: #9ca3af;
                color: #374151;
            }
        }
    }
}

/* 示例图片管理对话框样式 */
.example-management-dialog {
    /deep/ .el-dialog {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);

        .el-dialog__header {
            padding: 0;
            border: none;
        }

        .el-dialog__body {
            padding: 0;
            height: 600px;
            overflow: hidden;
        }

        .el-dialog__footer {
            padding: 20px 30px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
        }
    }
}

/* 示例对话框头部 */
.example-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    color: white;

    .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .header-icon {
            font-size: 24px;
            opacity: 0.9;
        }

        .header-info {
            .dialog-title {
                margin: 0 0 4px 0;
                font-size: 18px;
                font-weight: 600;
            }

            .dialog-subtitle {
                margin: 0;
                font-size: 14px;
                opacity: 0.9;
            }
        }
    }

    .header-actions {
        .view-toggle-btn {
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
            }

            i {
                margin-right: 6px;
            }
        }
    }
}

/* 示例管理内容 */
.example-management-content {
    height: 100%;
    overflow: hidden;
}

/* 网格视图 */
.grid-view {
    padding: 30px;
    height: 100%;
    overflow-y: auto;

    .examples-grid-large {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 24px;
    }
}

.example-card-large {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    &:hover {
        border-color: #4f46e5;
        box-shadow: 0 8px 24px rgba(79, 70, 229, 0.15);
        transform: translateY(-4px);
    }

    .card-image-wrapper {
        position: relative;
        height: 200px;
        overflow: hidden;

        .card-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(79, 70, 229, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            .overlay-content {
                text-align: center;
                color: white;

                .overlay-icon {
                    font-size: 32px;
                    margin-bottom: 8px;
                    display: block;
                }

                .overlay-text {
                    font-size: 14px;
                    font-weight: 500;
                }
            }
        }

        .card-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            background: rgba(79, 70, 229, 0.9);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        &:hover {
            .card-image {
                transform: scale(1.05);
            }

            .card-overlay {
                opacity: 1;
            }
        }
    }

    .card-content {
        padding: 20px;

        .card-title {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .card-description {
            margin: 0 0 16px 0;
            font-size: 14px;
            color: #6b7280;
            line-height: 1.5;
        }

        .card-tags {
            display: flex;
            gap: 8px;
        }
    }
}

/* 详情视图 */
.detail-view {
    display: flex;
    height: 100%;
}

.detail-sidebar {
    width: 280px;
    background: #f9fafb;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;

    .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid #e5e7eb;

        h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .example-count {
            font-size: 13px;
            color: #6b7280;
        }
    }

    .examples-list {
        flex: 1;
        overflow-y: auto;
        padding: 8px;
    }
}

.list-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        background: #f3f4f6;
    }

    &.active {
        background: #eff6ff;
        border: 1px solid #3b82f6;
    }

    .list-item-image {
        width: 48px;
        height: 48px;
        border-radius: 6px;
        overflow: hidden;
        margin-right: 12px;
        flex-shrink: 0;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }

    .list-item-content {
        flex: 1;

        .list-item-title {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 2px;
        }

        .list-item-index {
            font-size: 12px;
            color: #6b7280;
        }
    }

    .list-item-indicator {
        color: #9ca3af;
        font-size: 12px;
    }
}

.detail-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #e5e7eb;
    background: white;

    .detail-title-section {
        .detail-title {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }

        .detail-meta {
            display: flex;
            gap: 20px;

            .meta-item {
                display: flex;
                align-items: center;
                font-size: 13px;
                color: #6b7280;

                i {
                    margin-right: 6px;
                    color: #4f46e5;
                }
            }
        }
    }

    .detail-actions {
        display: flex;
        gap: 8px;
    }
}

.detail-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.image-section {
    width: 60%;
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f9fafb;

    .image-container {
        max-width: 100%;
        max-height: 80%;
        display: flex;
        align-items: center;
        justify-content: center;

        .detail-image {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .image-placeholder,
        .image-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #6b7280;

            i {
                font-size: 48px;
                margin-bottom: 12px;
            }
        }
    }

    .image-actions {
        margin-top: 20px;
        display: flex;
        gap: 12px;

        .action-btn {
            padding: 8px 16px;
            color: #4f46e5;
            font-size: 13px;

            &:hover {
                background: #eff6ff;
            }

            i {
                margin-right: 4px;
            }
        }
    }
}

.description-section {
    width: 40%;
    padding: 30px;
    background: white;
    overflow-y: auto;

    .description-header {
        margin-bottom: 20px;

        h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;

            i {
                margin-right: 8px;
                color: #4f46e5;
            }
        }
    }

    .description-content {
        .primary-description {
            font-size: 14px;
            color: #374151;
            line-height: 1.6;
            margin-bottom: 24px;
        }

        .requirements-list {
            margin-bottom: 24px;

            h5 {
                margin: 0 0 12px 0;
                font-size: 14px;
                font-weight: 600;
                color: #1f2937;
            }

            ul {
                list-style: none;
                padding: 0;
                margin: 0;

                li {
                    display: flex;
                    align-items: center;
                    margin-bottom: 8px;
                    font-size: 13px;
                    color: #374151;

                    i {
                        margin-right: 8px;
                        color: #10b981;
                        font-size: 14px;
                    }
                }
            }
        }

        .tips-section {
            /deep/ .el-alert {
                border-radius: 8px;

                .el-alert__description p {
                    margin: 4px 0;
                    font-size: 13px;
                }
            }
        }
    }
}

/* 示例对话框底部 */
.example-dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .footer-info {
        display: flex;
        align-items: center;
        color: #6b7280;
        font-size: 14px;

        i {
            margin-right: 8px;
            color: #4f46e5;
        }
    }

    .footer-actions {
        display: flex;
        gap: 12px;

        .el-button {
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;

            i {
                margin-right: 6px;
            }

            &.el-button--primary {
                background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
                border: none;

                &:hover {
                    opacity: 0.9;
                    transform: translateY(-1px);
                }
            }
        }
    }
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .table-header-actions {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .header-info {
            justify-content: center;
        }

        .header-actions {
            justify-content: center;
        }
    }

    .pagination-wrapper {
        flex-direction: column;
        gap: 16px;
        align-items: center;

        .pagination-info {
            order: 2;
        }

        .enhanced-pagination {
            order: 1;
        }
    }

    .company-cell .company-info {
        .company-details .company-meta {
            display: none;
        }
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
        text-align: center;
    }

    .table-header-actions {
        padding: 16px;

        .header-info {
            flex-direction: column;
            gap: 12px;

            .info-item .link-text {
                max-width: none;
                word-break: break-all;
            }
        }

        .header-actions {
            .refresh-btn {
                display: none;
            }
        }
    }

    .enhanced-table {
        /deep/ .el-table__body-wrapper {
            .el-table__row:hover {
                transform: none;
                box-shadow: none;
            }
        }

        /* 隐藏部分列在移动端 */
        /deep/ .el-table__header {
            th:nth-child(3),
            /* 信用代码列 */
            th:nth-child(6),
            /* 审核原因列 */
            th:nth-child(7) {
                /* 创建时间列 */
                display: none;
            }
        }

        /deep/ .el-table__body {
            td:nth-child(3),
            /* 信用代码列 */
            td:nth-child(6),
            /* 审核原因列 */
            td:nth-child(7) {
                /* 创建时间列 */
                display: none;
            }
        }
    }

    .pagination-wrapper {
        padding: 16px;

        .pagination-info {
            flex-direction: column;
            gap: 8px;
            text-align: center;

            .page-info {
                display: none;
            }
        }

        .enhanced-pagination {
            /deep/ .el-pagination {
                .el-pagination__sizes {
                    display: none;
                }

                .el-pagination__jump {
                    display: none;
                }
            }
        }
    }

    .user-cell .user-info {
        .user-icon {
            display: none;
        }
    }

    .company-cell .company-info {
        .company-icon {
            display: none;
        }
    }

    .time-cell .time-info {
        .time-icon {
            display: none;
        }

        .time-details .time-text {
            display: none;
        }
    }

    .action-cell .action-buttons {
        flex-direction: column;
        gap: 4px;

        .action-btn {
            font-size: 11px;
            padding: 4px 8px;
        }
    }

    .auth-dialog {
        /deep/ .el-dialog {
            width: 95% !important;
            margin: 20px !important;
        }
    }

    .dialog-content {
        padding: 20px;
    }

    .upload-section {
        flex-direction: column;
        gap: 16px;

        .examples-area {
            width: 100%;
        }

        .examples-grid {
            flex-direction: row;
            gap: 8px;

            .example-card {
                flex: 1;

                .example-image-wrapper .example-image {
                    height: 60px;
                }
            }
        }
    }

    .example-management-dialog {
        /deep/ .el-dialog {
            width: 95% !important;
            margin: 10px !important;

            .el-dialog__body {
                height: 500px;
            }
        }
    }

    .detail-view {
        .detail-sidebar {
            width: 100px;

            .sidebar-header {
                padding: 12px;

                h4 {
                    font-size: 14px;
                }

                .example-count {
                    display: none;
                }
            }

            .list-item {
                padding: 8px;

                .list-item-content {
                    display: none;
                }
            }
        }
    }

    .detail-content {
        flex-direction: column;

        .image-section {
            width: 100%;
            padding: 20px;
        }

        .description-section {
            width: 100%;
            padding: 20px;
        }
    }

    .grid-view {
        padding: 20px;

        .examples-grid-large {
            grid-template-columns: 1fr;
            gap: 16px;
        }
    }
}

@media (max-width: 480px) {
    .table-container {
        border-radius: 8px;
        margin: 0 -8px;
    }

    .table-header-actions {
        padding: 12px;
    }

    .image-cell {
        .image-preview-wrapper {
            .table-image {
                width: 48px;
                height: 48px;

                /deep/ img {
                    width: 48px;
                    height: 48px;
                }
            }

            .image-overlay {
                i {
                    font-size: 16px;
                    margin-bottom: 2px;
                }

                span {
                    font-size: 9px;
                }
            }

            .image-placeholder,
            .image-error {
                width: 48px;
                height: 48px;

                i {
                    font-size: 16px;
                    margin-bottom: 2px;
                }

                span {
                    font-size: 8px;
                }
            }
        }

        .no-image {
            width: 48px;
            height: 48px;

            i {
                font-size: 16px;
            }

            span {
                font-size: 9px;
            }
        }
    }

    .empty-state {
        padding: 40px 16px;

        .empty-icon i {
            font-size: 48px;
        }

        .empty-text {
            h3 {
                font-size: 16px;
            }

            p {
                font-size: 13px;
            }
        }
    }
}
</style>
