<template>
    <div class="simple-signature-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <el-button v-permission @click="showAddDialog" class="action-btn primary" icon="el-icon-plus">
                                    新增域名备案
                                </el-button>
                            </div>

                            <!-- 统计信息 -->
                            <div class="stats-info">
                                <span class="stats-text">共 {{ pagination.total }} 条记录</span>
                                <el-divider direction="vertical"></el-divider>
                                <span class="stats-text">当前第 {{ pagination.currentPage }} 页</span>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <el-form :model="searchForm" :inline="true" ref="searchForm" class="advanced-search-form">
                                <div class="search-row">
                                    <el-form-item label="用户名称" prop="clientName" class="search-item">
                                        <el-input v-model="searchForm.clientName" placeholder="请输入用户名称" class="search-input"
                                            clearable prefix-icon="el-icon-search" />
                                    </el-form-item>
                                    <el-form-item label="域名地址" prop="link" class="search-item">
                                        <el-input v-model="searchForm.link" placeholder="请输入链接地址" class="search-input"
                                            clearable prefix-icon="el-icon-search" />
                                    </el-form-item>

                                    <el-form-item label="审核状态" prop="auditStatus" class="search-item">
                                        <el-select v-model="searchForm.auditStatus" placeholder="请选择审核状态"
                                            class="search-input" clearable>
                                            <el-option label="草稿" value="0"></el-option>
                                            <el-option label="审核中" value="1"></el-option>
                                            <el-option label="审核通过" value="2"></el-option>
                                            <el-option label="审核拒绝" value="3"></el-option>
                                        </el-select>
                                    </el-form-item>

                                    <el-form-item class="search-buttons">
                                        <el-button type="primary" @click="handleSearch" class="search-btn primary"
                                            icon="el-icon-search">
                                            查询
                                        </el-button>
                                        <el-button @click="handleReset" class="search-btn" icon="el-icon-refresh">
                                            重置
                                        </el-button>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>

                <!-- 链接备案列表 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3 class="table-title">域名备案列表</h3>
                    </div>

                    <div class="table-container">
                        <el-table v-loading="loading" element-loading-text="正在加载..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(255, 255, 255, 0.8)" ref="multipleTable" border
                            :data="tableData" class="enhanced-table" stripe
                            :header-cell-style="{ background: '#fafafa', color: '#333' }"
                            :row-style="{ height: '60px' }" empty-text="暂无链接备案数据">
                            <el-table-column prop="clientName" label="用户名称" min-width="200">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <span v-if="scope.row.clientName">
                                            {{ scope.row.clientName }}
                                        </span>
                                        <span v-else>-</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="link" label="域名地址" min-width="200">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <el-link v-if="scope.row.link" :href="scope.row.link" target="_blank"
                                            type="primary">
                                            {{ scope.row.link }}
                                        </el-link>
                                        <span v-else>-</span>
                                    </div>
                                </template>
                            </el-table-column>

                            <el-table-column prop="companyName" label="域名注册公司" min-width="150">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        {{ scope.row.companyName || '-' }}
                                    </div>
                                </template>
                            </el-table-column>

                            <el-table-column prop="creditCode" label="统一社会信用代码" min-width="180">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        {{ scope.row.creditCode || '-' }}
                                    </div>
                                </template>
                            </el-table-column>

                            <el-table-column prop="recordNumber" label="备案号" min-width="150">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        {{ scope.row.recordNumber || '-' }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="reportImg" label="备案图片" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        <el-image v-if="scope.row.reportImg" style="width: 50px; height: 50px"
                                            :z-index="9999" :src="getImageUrl(scope.row.reportImg)" fit="cover"
                                            :preview-src-list="[getImageUrl(scope.row.reportImg)]">
                                            <template #error>
                                                <div class="image-slot">
                                                    <el-icon>
                                                        <Picture />
                                                    </el-icon>
                                                </div>
                                            </template>
                                        </el-image>
                                        <span v-else>-</span>
                                    </div>
                                    <!-- <span v-else>-</span> -->
                                </template>
                            </el-table-column>

                            <!-- <el-table-column prop="sealImg" label="印章图片" width="100">
                                <template slot-scope="scope">
                                    <div>
                                        <el-image v-if="scope.row.sealImg" style="width: 50px; height: 50px"
                                            :z-index="9999" :src="getImageUrl(scope.row.sealImg)" fit="cover"
                                            :preview-src-list="[getImageUrl(scope.row.sealImg)]">
                                            <template #error>
                                                <div class="image-slot">
                                                    <el-icon>
                                                        <Picture />
                                                    </el-icon>
                                                </div>
                                            </template>
                                        </el-image>
                                        <span v-else>-</span>
                                    </div>
                                </template>
                            </el-table-column> -->
                            <el-table-column prop="auditStatus" label="审核状态" width="100" align="center">
                                <template slot-scope="scope">
                                    <el-tag :type="getAuditStatusTagType(scope.row.auditStatus)" size="small">
                                        {{ getAuditStatusText(scope.row.auditStatus) }}
                                    </el-tag>
                                </template>
                            </el-table-column>

                            <!-- 审核原因列 -->
                            <el-table-column prop="auditReason" label="审核原因" min-width="200">
                                <template slot-scope="scope">
                                    <div class="table-cell reason-cell">
                                        <div v-if="scope.row.auditReason && scope.row.auditReason !== ''" class="reason-content">
                                            <!-- 审核拒绝状态 -->
                                            <el-tooltip v-if="scope.row.auditStatus == '3'" 
                                                       :content="`驳回原因：${scope.row.auditReason}`" 
                                                       placement="top" 
                                                       effect="dark"
                                                       :disabled="scope.row.auditReason.length <= 20">
                                                <div class="reject-reason reason-wrapper">
                                                    <i class="el-icon-warning reason-icon reject-icon"></i>
                                                    <div class="reason-text">
                                                        <span class="reason-label">驳回原因：</span>
                                                        <span class="reason-detail">{{ scope.row.auditReason }}</span>
                                                    </div>
                                                </div>
                                            </el-tooltip>
                                            
                                            <!-- 审核通过状态 -->
                                            <el-tooltip v-else-if="scope.row.auditStatus == '2'" 
                                                       :content="`审核备注：${scope.row.auditReason}`" 
                                                       placement="top" 
                                                       effect="dark"
                                                       :disabled="scope.row.auditReason.length <= 20">
                                                <div class="pass-reason reason-wrapper">
                                                    <i class="el-icon-circle-check reason-icon pass-icon"></i>
                                                    <div class="reason-text">
                                                        <span class="reason-label">审核备注：</span>
                                                        <span class="reason-detail">{{ scope.row.auditReason }}</span>
                                                    </div>
                                                </div>
                                            </el-tooltip>
                                            
                                            <!-- 其他状态 -->
                                            <el-tooltip v-else 
                                                       :content="scope.row.auditReason" 
                                                       placement="top" 
                                                       effect="dark"
                                                       :disabled="scope.row.auditReason.length <= 20">
                                                <div class="default-reason reason-wrapper">
                                                    <i class="el-icon-info reason-icon default-icon"></i>
                                                    <div class="reason-text">
                                                        <span class="reason-detail">{{ scope.row.auditReason }}</span>
                                                    </div>
                                                </div>
                                            </el-tooltip>
                                        </div>
                                        <div v-else class="no-reason">
                                            <span class="empty-text">-</span>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>

                            <el-table-column prop="createTime" label="创建时间" width="180">
                                <template slot-scope="scope">
                                    {{ formatDate(scope.row.createTime) }}
                                </template>
                            </el-table-column>

                            <el-table-column prop="updateTime" label="更新时间" width="180">
                                <template slot-scope="scope">
                                    {{ formatDate(scope.row.updateTime) }}
                                </template>
                            </el-table-column>

                            <el-table-column prop="createName" label="创建人" width="120">
                                <template slot-scope="scope">
                                    <span :class="{ 'current-user': isCurrentUser(scope.row) }">
                                        {{ scope.row.createName || '-' }}
                                    </span>
                                </template>
                            </el-table-column>

                            <el-table-column label="操作" width="320" align="center" fixed="right">
                                <template slot-scope="scope">
                                    <el-button v-permission type="text" size="small" v-if="canEdit(scope.row)"
                                        @click="showEditDialog(scope.row)">
                                        <i class="el-icon-edit"></i>
                                        编辑
                                    </el-button>
                                    <el-button type="text" size="small" @click="showAuthDialog(scope.row)"
                                        style="color: #409EFF;">
                                        <i class="el-icon-key"></i>
                                        授权
                                    </el-button>
                                    <el-button type="text" size="small" @click="showExtensionDialog(scope.row)"
                                        style="color: #67C23A;">
                                        <i class="el-icon-document"></i>
                                        新增链接
                                    </el-button>
                                    <el-button v-permission type="text" size="small" v-if="canDelete(scope.row)"
                                        style="color: #f56c6c;" @click="handleDelete(scope.row)">
                                        <i class="el-icon-delete"></i>
                                        删除
                                    </el-button>
                                    <!-- <span v-if="scope.row.auditStatus === 2 && isCurrentUser(scope.row)" class="audit-passed-tip">
                                        已审核通过，无法编辑
                                    </span> -->
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页 -->
                        <div class="pagination-container">
                            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                                :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]"
                                :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper"
                                :total="pagination.total" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false"
            @close="handleDialogClose" class="enhanced-dialog">
            <el-form ref="recordForm" :model="recordForm" :rules="formRules" label-width="140px" class="dialog-form">
                <el-form-item label="域名地址" prop="link">
                    <div class="link-input-container">
                        <el-input v-model="recordForm.link" :disabled="isEdit"
                            placeholder="请输入域名地址，支持域名、IP地址，如example.com 或 ***********" class="link-input" />
                        <div v-if="!isEdit" class="domain-extract-section">
                            <div class="extract-input-wrapper">
                                <el-input v-model="domainExtractForm.sourceLink" placeholder="输入完整链接地址会帮您进行提取域名"
                                    class="extract-input" @keyup.enter.native="handleExtractDomain" clearable />
                                <el-button type="primary" size="small" @click="handleExtractDomain"
                                    :loading="extractLoading" class="extract-btn">
                                    提取域名
                                </el-button>
                            </div>
                            <div class="extract-tip">
                                <i class="el-icon-info"></i>
                                <span>输入完整链接地址会帮您进行提取域名，自动填充到域名地址中</span>
                            </div>
                        </div>
                    </div>
                </el-form-item>

                <el-form-item label="公司名称" prop="companyName">
                    <el-input v-model="recordForm.companyName" placeholder="请输入公司名称" />
                </el-form-item>

                <el-form-item label="统一社会信用代码" prop="creditCode">
                    <el-input v-model="recordForm.creditCode" placeholder="请输入统一社会信用代码" />
                </el-form-item>

                <el-form-item label="备案号" prop="recordNumber">
                    <el-input v-model="recordForm.recordNumber" placeholder="请输入备案号" />
                </el-form-item>

                <el-form-item label="备案图片" prop="reportImg">
                    <div class="upload-with-example">
                        <div class="upload-container">
                            <el-upload class="image-uploader" :action="uploadUrl" :headers="headers"
                                :show-file-list="false" :on-success="handleReportImgSuccess"
                                :on-error="handleUploadError" :before-upload="beforeUpload"
                                :on-progress="handleUploadProgress" :disabled="!!recordForm.reportImg" accept="image/*">
                                <div v-if="recordForm.reportImg" class="image-preview" @click.stop>
                                    <img :src="getImageUrl(recordForm.reportImg)" class="uploaded-image" />
                                    <div class="image-overlay">
                                        <div class="image-actions">
                                            <i class="el-icon-zoom-in"
                                                @click.stop="previewImage(recordForm.reportImg, $event)"></i>
                                            <i class="el-icon-delete" @click.stop="removeReportImg($event)"></i>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="upload-placeholder">
                                    <i class="el-icon-plus upload-icon"></i>
                                    <div class="upload-text">点击上传备案图片</div>
                                    <div class="upload-hint">支持 JPG、PNG 格式，文件大小不超过 2MB</div>
                                </div>
                            </el-upload>
                            <el-progress v-if="reportImgUploading" :percentage="reportImgProgress"
                                class="upload-progress">
                            </el-progress>
                        </div>
                        <div class="example-image-container">
                            <div class="example-label">示例图片</div>
                            <div class="example-images-grid">
                                <div class="example-item" @click="showExampleImages('report')">
                                    <img src="../../../.././../assets/images/ICP.png" alt="ICP备案示例"
                                        class="example-image" />
                                    <div class="example-item-label">ICP备案示例</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-form-item>


            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button v-permission @click="handleSaveDraft">
                    保存草稿
                </el-button>
                <el-button v-permission type="primary" @click="handleSubmitAudit">
                    提交审核
                </el-button>
            </div>
        </el-dialog>
        <el-dialog :visible.sync="imageDialogVisible" width="800px">
            <el-image style="width: 100%; height: 100%" :src="url" :preview-src-list="srcList">
            </el-image>
        </el-dialog>

        <!-- 示例图片查看对话框 -->
        <el-dialog :visible.sync="exampleDialogVisible" width="900px" title="示例图片管理" class="example-dialog">
            <div class="example-viewer" v-if="currentExampleImages.length > 0">
                <div class="example-header">
                    <h3 class="example-title">{{ currentExampleImages[currentExampleIndex].title }}</h3>
                    <div class="example-counter">{{ currentExampleIndex + 1 }} / {{ currentExampleImages.length }}</div>
                </div>

                <div class="example-content">
                    <div class="example-image-wrapper">
                        <el-image :src="currentExampleImages[currentExampleIndex].url" fit="contain"
                            style="width: 100%; height: 400px;"
                            :preview-src-list="currentExampleImages.map(item => item.url)"
                            :initial-index="currentExampleIndex" />
                    </div>

                    <div class="example-description">
                        <p>{{ currentExampleImages[currentExampleIndex].description }}</p>
                    </div>
                </div>

                <div class="example-navigation" v-if="currentExampleImages.length > 1">
                    <el-button size="small" :disabled="currentExampleIndex === 0" @click="previousExample"
                        icon="el-icon-arrow-left">
                        上一张
                    </el-button>
                    <el-button size="small" :disabled="currentExampleIndex === currentExampleImages.length - 1"
                        @click="nextExample" icon="el-icon-arrow-right">
                        下一张
                    </el-button>
                </div>

                <div class="example-thumbnails" v-if="currentExampleImages.length > 1">
                    <div v-for="(item, index) in currentExampleImages" :key="index" class="thumbnail-item"
                        :class="{ active: index === currentExampleIndex }" @click="currentExampleIndex = index">
                        <img :src="item.url" :alt="item.title" />
                        <span class="thumbnail-title">{{ item.title }}</span>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    name: 'LinkRecordManagement',
    data() {
        // // URL验证规则（支持域名和IP地址）
        // const validateUrl = (rule, value, callback) => {
        //     if (!value) {
        //         callback(new Error('请输入链接地址'));
        //         return;
        //     }
        //     console.log(11);

        //     // 移除协议部分用于验证
        //     let urlToCheck = value.replace(/^https?:\/\//, '');

        //     // 提取主机部分（去除路径、查询参数等）
        //     // let hostPart = urlToCheck.split('/')[0].split('?')[0].split('#')[0];

        //     // 分离主机和端口
        //     // let [host, port] = hostPart.split(':');

        //     // 验证端口号（如果存在）
        //     // if (port && (!/^\d+$/.test(port) || parseInt(port) < 1 || parseInt(port) > 65535)) {
        //     //     callback(new Error('端口号格式不正确（1-65535）'));
        //     //     return;
        //     // }

        //     // IP地址验证正则（IPv4格式：0.0.0.0 - ***************）
        //     const ipPattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

        //     // 域名验证正则（支持多级域名、国际化域名）
        //     const domainPattern = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;

        //     // localhost验证
        //     // const isLocalhost = host === 'localhost';

        //     // 验证主机部分
        //     if (!ipPattern.test(urlToCheck) && !domainPattern.test(urlToCheck) && !isLocalhost) {
        //         console.log(22);

        //         callback(new Error('请输入正确的链接地址格式（支持域名、IP地址或localhost）'));
        //         return;
        //     }

        //     callback();
        // };

        // 统一社会信用代码验证规则
        const validateCreditCode = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入统一社会信用代码'));
                return;
            }
            // const creditCodePattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
            // if (!creditCodePattern.test(value)) {
            //     callback(new Error('请输入正确的18位统一社会信用代码'));
            //     return;
            // }
            callback();
        };

        return {
            loading: false,
            submitLoading: false,
            dialogVisible: false,
            imageDialogVisible: false,
            isEdit: false,
            currentRecord: null,
            headers: {
            },
            url: '',
            srcList: [],
            // 上传进度相关
            reportImgUploading: false,
            reportImgProgress: 0,

            // 域名提取相关
            extractLoading: false,
            domainExtractForm: {
                sourceLink: ''
            },

            // 示例图片相关
            exampleDialogVisible: false,
            exampleImageUrl: '',
            currentExampleImages: [],
            currentExampleIndex: 0,

            // 示例图片数据
            exampleImagesData: {
                report: [
                    {
                        url: require('@/assets/images/ICP.png'),
                        title: 'ICP备案示例',
                        description: '展示正确的ICP备案证书格式'
                    }
                ]
            },
            // 搜索表单
            searchForm: {
                clientName: '',
                link: '',
                auditStatus: ''
            },

            // 分页信息
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },

            // 表格数据
            tableData: [],

            // 表单数据
            recordForm: {
                link: '',
                companyName: '',
                creditCode: '',
                recordNumber: '',
                reportImg: '',
                auditStatus: 0
            },

            // 表单验证规则
            formRules: {
                link: [
                    // { required: true, validator: validateUrl, trigger: 'blur' }
                    {
                        required: true,
                        message: '请输入地址',
                        trigger: 'blur'
                    },
                    {
                        validator: (rule, value, callback) => {
                            const urlPattern = /^(https?:\/\/)?([\w.-]+)\.[a-z]{2,6}(:\d+)?(\/\S*)?$/i
                            const ipPattern = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/

                            if (!value) {
                                callback(new Error('请输入地址'))
                            } else if (urlPattern.test(value) || ipPattern.test(value)) {
                                callback() // 校验通过
                            } else {
                                callback(new Error('请输入有效的链接或IP地址'))
                            }
                        },
                        trigger: 'blur'
                    }

                ],
                companyName: [
                    { required: true, message: '请输入公司名称', trigger: 'blur' },
                    { min: 2, max: 100, message: '公司名称长度在 2 到 100 个字符', trigger: 'blur' }
                ],
                creditCode: [
                    { required: true, validator: validateCreditCode, trigger: 'blur' }
                ],
                recordNumber: [
                    { required: true, message: '请输入备案号', trigger: 'blur' },
                    { min: 1, max: 50, message: '备案号长度在 1 到 50 个字符', trigger: 'blur' }
                ],
                reportImg: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            // 使用自定义验证器，直接检查表单数据
                            if (!this.recordForm.reportImg) {
                                callback(new Error('请上传备案图片'));
                            } else {
                                callback();
                            }
                        },
                        trigger: ['change', 'blur']
                    }
                ],
                // sealImg: [
                //     {
                //         required: true,
                //         validator: (rule, value, callback) => {
                //             // 使用自定义验证器，直接检查表单数据
                //             if (!this.recordForm.sealImg) {
                //                 callback(new Error('请上传盖章扫描件'));
                //             } else {
                //                 callback();
                //             }
                //         },
                //         trigger: ['change', 'blur']
                //     }
                // ]

            }
        };
    },

    computed: {
        dialogTitle() {
            return this.isEdit ? '编辑域名备案' : '新增域名备案';
        },

        // 获取当前用户信息
        currentUserInfo() {
            return JSON.parse(localStorage.getItem('userInfo') || '{}');
        },

        // 获取当前用户ID
        currentUserId() {
            return this.currentUserInfo.userId;
        },

        // 文件上传地址
        uploadUrl() {
            return this.API.cpus + "v3/file/upload";
        },

        // 是否为只读模式（审核通过时禁止编辑）
        isReadOnly() {
            return this.isEdit && this.currentRecord && this.currentRecord.auditStatus === 2;
        }
    },

    created() {
        // 初始化用户名称查询，默认获取本账号数据
        this.initializeUserSearch();
        this.loadData();
        this.headers = { Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"), };
    },

    methods: {
        // 加载数据
        loadData() {
            this.loading = true;
            const params = {
                currentPage: this.pagination.currentPage,
                pageSize: this.pagination.pageSize,
                ...this.searchForm
            };

            this.$api.post(
                this.API.cpus + 'consumersmstemplatereportlink/page',
                params,
                (res) => {
                    this.loading = false;
                    if (res.code === 200) {
                        this.tableData = res.data.records || [];
                        this.pagination.total = res.data.total || 0;
                    } else {
                        this.$message.error(res.msg || '获取数据失败');
                    }
                },
                (error) => {
                    this.loading = false;
                    console.error('加载数据失败:', error);
                    this.$message.error('获取数据失败，请稍后重试');
                }
            );
        },

        // 搜索
        handleSearch() {
            this.pagination.currentPage = 1;
            this.loadData();
        },

        // 重置搜索
        handleReset() {
            const userInfo = this.currentUserInfo;
            this.searchForm = {
                clientName: userInfo.username || '',
                link: '',
                auditStatus: ''
            };
            this.pagination.currentPage = 1;
            this.loadData();
        },

        // 初始化用户搜索
        initializeUserSearch() {
            // 默认填入当前用户名称，查询本账号的数据
            const userInfo = this.currentUserInfo;
            // this.searchForm.clientName = userInfo.clientName || userInfo.username || userInfo.name || userInfo.nickName || '';
            this.searchForm.clientName = userInfo.username || '';
        },

        // 显示新增对话框
        showAddDialog() {
            this.isEdit = false;
            this.currentRecord = null;
            this.recordForm = {
                link: '',
                companyName: '',
                creditCode: '',
                recordNumber: '',
                reportImg: '',
                sealImg: '',
                auditStatus: 0
            };

            // 初始化域名提取表单
            this.domainExtractForm = {
                sourceLink: ''
            };

            this.dialogVisible = true;

            // 确保对话框完全打开后清除之前的校验状态
            this.$nextTick(() => {
                if (this.$refs.recordForm) {
                    this.$refs.recordForm.clearValidate();
                }
            });
        },

        // 显示授权页面
        showAuthDialog(record) {
            const linkId = record.id;
            const linkAddress = record.link;
            this.$router.push({
                path: '/linkRecordAuth',
                query: {
                    linkId: linkId,
                    linkAddress: linkAddress
                }
            });
        },

        // 显示完整链接报备页面
        showExtensionDialog(record) {
            const linkId = record.id;
            const linkAddress = record.link;
            this.$router.push({
                path: '/linkExtensionManagement',
                query: {
                    linkId: linkId,
                    linkAddress: linkAddress
                }
            });
        },

        // 显示编辑对话框
        showEditDialog(record) {
            // 检查权限
            if (!this.canEdit(record)) {
                if (!record.allowOperate) {
                    this.$message.warning('您没有权限编辑此记录');
                } else if (record.auditStatus === 2) {
                    this.$message.warning('记录已审核通过，无法编辑');
                } else {
                    this.$message.warning('您没有权限编辑此记录');
                }
                return;
            }

            this.isEdit = true;
            this.currentRecord = record;
            this.recordForm = {
                link: record.link || '',
                companyName: record.companyName || '',
                creditCode: record.creditCode || '',
                recordNumber: record.recordNumber || '',
                reportImg: record.reportImg || '',
                auditStatus: record.auditStatus || 0,
                id: record.id
            };
            this.dialogVisible = true;

            // 确保对话框完全打开后清除之前的校验状态
            this.$nextTick(() => {
                if (this.$refs.recordForm) {
                    this.$refs.recordForm.clearValidate();
                }
            });
        },

        // 关闭对话框
        handleDialogClose() {
            // 清除表单验证
            this.$refs.recordForm.clearValidate();

            // 清除对话框数据
            this.clearDialogData();
        },

        // 清除对话框数据
        clearDialogData() {
            // 清除表单数据
            this.recordForm = {
                link: '',
                companyName: '',
                creditCode: '',
                recordNumber: '',
                reportImg: '',
                auditStatus: 0
            };

            // 清除域名提取表单数据
            this.domainExtractForm = {
                sourceLink: ''
            };

            // 清除图片预览缓存
            this.url = '';
            this.srcList = [];
            this.imageDialogVisible = false;

            // 重置编辑状态
            this.isEdit = false;
            this.currentRecord = null;

            // 清除上传组件的文件列表
            this.$nextTick(() => {
                try {
                    // 清除页面上所有的上传文件列表显示
                    const uploadLists = this.$el.querySelectorAll('.el-upload-list');
                    uploadLists.forEach(uploadList => {
                        if (uploadList) {
                            uploadList.innerHTML = '';
                        }
                    });

                    // 查找并清除上传组件的内部状态
                    const findAndClearUploads = (component) => {
                        if (component && component.$children) {
                            component.$children.forEach(child => {
                                if (child.$options.name === 'ElUpload') {
                                    // 清除上传组件的文件列表
                                    if (child.uploadFiles) {
                                        child.uploadFiles = [];
                                    }
                                    if (child.clearFiles) {
                                        child.clearFiles();
                                    }
                                } else {
                                    // 递归查找子组件
                                    findAndClearUploads(child);
                                }
                            });
                        }
                    };

                    // 从根组件开始递归清除
                    findAndClearUploads(this);

                } catch (error) {
                    console.warn('清除上传组件缓存时出现错误:', error);
                }
            });
        },

        // 保存草稿
        handleSaveDraft() {
            // 草稿状态不需要验证图片
            const rules = { ...this.formRules };
            // delete rules.reportImg;
            // delete rules.sealImg;

            this.validateForm(() => {
                this.submitLoading = true;
                this.recordForm.auditStatus = 0; // 草稿状态
                this.submitRecord();
            });
        },

        // 提交审核
        handleSubmitAudit() {
            this.validateForm(() => {
                this.submitLoading = true;
                this.recordForm.auditStatus = 1; // 提交审核状态
                this.submitRecord();
            });
        },

        // 域名提取功能
        handleExtractDomain() {
            if (!this.domainExtractForm.sourceLink) {
                this.$message.warning('请输入需要提取域名的完整链接');
                return;
            }

            this.extractLoading = true;
            const params = {
                link: this.domainExtractForm.sourceLink
            };

            this.$api.get(
                this.API.cpus + 'consumersmstemplatereportlink/extractDomain',
                params,
                (res) => {
                    this.extractLoading = false;
                    if (res.code == 200) {
                        if (res.data) {
                            this.recordForm.link = res.data;
                            this.$message.success('域名提取成功');
                            // 清空提取输入框
                            this.domainExtractForm.sourceLink = '';
                        } else {
                            this.$message.warning('未能提取到有效域名');
                        }
                    } else {
                        this.$message.error(res.msg || '域名提取失败');
                    }
                },
                (error) => {
                    this.extractLoading = false;
                    console.error('域名提取失败:', error);
                    this.$message.error('域名提取失败，请稍后重试');
                }
            );
        },

        // 统一的表单校验方法，解决打包后$refs失效问题
        validateForm(callback) {
            this.$nextTick(() => {
                // 检查表单引用是否存在
                if (!this.$refs.recordForm) {
                    console.error('表单引用不存在，请检查ref设置');
                    this.$message.error('表单初始化失败，请重新打开对话框');
                    return;
                }

                try {
                    this.$refs.recordForm.validate((valid) => {
                        if (!valid) {
                            this.$message.warning('请填写完整的表单信息');
                            return;
                        }
                        callback && callback();
                    });
                } catch (error) {
                    console.error('表单校验异常:', error);
                    this.$message.error('表单校验失败，请重试');
                }
            });
        },

        // 提交记录
        submitRecord() {
            if (this.isEdit) {
                this.updateRecord();
            } else {
                this.addRecord();
            }
        },

        // 新增记录
        addRecord() {
            this.$api.post(
                this.API.cpus + 'consumersmstemplatereportlink',
                this.recordForm,
                (res) => {
                    this.submitLoading = false;
                    if (res.code === 200) {
                        const statusText = this.recordForm.auditStatus === 0 ? '保存草稿成功' : '提交审核成功';
                        const actionText = this.recordForm.auditStatus == 0 ? '保存草稿' : '提交审核';
                        this.$message.success(statusText);
                        this.dialogVisible = false;
                        this.clearDialogData(); // 清除对话框数据
                        this.loadData();
                        
                        // 引导用户跳转到新增链接页面
                        this.showLinkExtensionGuide(res.data,actionText);
                    } else {
                        this.$message.error(res.msg || '操作失败');
                    }
                },
                (error) => {
                    this.submitLoading = false;
                    console.error('操作失败:', error);
                    this.$message.error('操作失败，请稍后重试');
                }
            );
        },

        // 更新记录
        updateRecord() {
            this.$api.put(
                this.API.cpus + 'consumersmstemplatereportlink',
                this.recordForm,
                (res) => {
                    this.submitLoading = false;
                    if (res.code === 200) {
                        const statusText = this.recordForm.auditStatus === 0 ? '保存草稿成功' : '提交审核成功';
                        this.$message.success(statusText);
                        this.dialogVisible = false;
                        this.clearDialogData(); // 清除对话框数据
                        this.loadData();
                        
                        // 对于更新操作，使用现有记录的ID进行引导
                        // 只有在状态变更为提交审核或保存草稿时才进行引导
                        // if (this.recordForm.id) {
                        //     this.showLinkExtensionGuide(this.recordForm.id);
                        // }
                    } else {
                        this.$message.error(res.msg || '操作失败');
                    }
                },
                (error) => {
                    this.submitLoading = false;
                    console.error('操作失败:', error);
                    this.$message.error('操作失败，请稍后重试');
                }
            );
        },

        // 删除记录
        handleDelete(record) {
            // 检查删除权限
            if (!this.canDelete(record)) {
                this.$message.warning('您没有权限删除此记录');
                return;
            }

            this.$confirm('确认删除这条链接备案记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$api.delete(
                    this.API.cpus + `consumersmstemplatereportlink/${record.id}`,
                    {},
                    (res) => {
                        if (res.code === 200) {
                            this.$message.success('删除成功');
                            this.loadData();
                        } else {
                            this.$message.error(res.msg || '删除失败');
                        }
                    },
                    (error) => {
                        console.error('删除失败:', error);
                        this.$message.error('删除失败，请稍后重试');
                    }
                );
            }).catch(() => {
                // 用户取消删除
            });
        },

        // 分页大小改变
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.pagination.currentPage = 1;
            this.loadData();
        },

        // 当前页改变
        handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.loadData();
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return '-';
            return moment(date).format('YYYY-MM-DD HH:mm:ss');
        },

        // 获取审核状态标签类型
        getAuditStatusTagType(status) {
            const statusMap = {
                0: 'info',     // 草稿
                1: 'warning',  // 审核中
                2: 'success',  // 审核通过
                3: 'danger'    // 审核拒绝
            };
            return statusMap[status] || 'info';
        },

        // 获取审核状态文本
        getAuditStatusText(status) {
            const statusMap = {
                0: '草稿',
                1: '审核中',
                2: '审核通过',
                3: '审核拒绝'
            };
            return statusMap[status] || '未知';
        },

        // 文件上传前校验
        beforeUpload(file) {
            const isImage = file.type.startsWith('image/');
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isImage) {
                this.$message.error('上传文件只能是图片格式!');
                return false;
            }
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 2MB!');
                return false;
            }
            return true;
        },

        // 处理上传进度 - 备案图片
        handleUploadProgress(event, file, fileList) {
            this.reportImgUploading = true;
            this.reportImgProgress = Math.round(event.percent);
        },



        // 预览图片
        previewImage(imagePath, event) {
            // 阻止事件冒泡，防止触发上传
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            this.url = this.getImageUrl(imagePath);
            this.srcList = [this.url];
            this.imageDialogVisible = true;
        },

        // 移除备案图片
        removeReportImg(event) {
            // 阻止事件冒泡，防止触发上传
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            this.recordForm.reportImg = '';
            this.reportImgUploading = false;
            this.reportImgProgress = 0;
            this.$message.success('备案图片已移除');
        },



        // 备案图片上传成功
        handleReportImgSuccess(res, file) {
            this.reportImgUploading = false;
            this.reportImgProgress = 0;
            if (res.code === 200) {
                this.recordForm.reportImg = res.data.fullpath;
                this.$message.success('备案图片上传成功');
            } else {
                this.$message.error(res.msg || '图片上传失败');
            }
        },



        // 文件上传失败
        handleUploadError(err, file, fileList) {
            this.reportImgUploading = false;
            this.reportImgProgress = 0;
            this.$message.error('图片上传失败，请重试');
        },

        // 获取图片URL
        getImageUrl(path) {
            if (!path) return '';
            if (path.startsWith('http')) {
                return path;
            }
            return this.API.imgU + path;
        },

        // 判断是否是当前用户创建的记录
        isCurrentUser(record) {
            // 基于 allowOperate 字段判断
            return record.allowOperate || false;
        },

        // 判断是否可以编辑（审核通过后不可编辑，但只能编辑自己的数据）
        canEdit(record) {
            // 基于 allowOperate 字段判断
            if (!record.allowOperate) {
                return false;
            }

            // 审核状态为2（审核通过）时，禁止编辑
            if (record.auditStatus == 2 || record.auditStatus == 1) {
                return false;
            }

            return true;
        },

        // 判断是否可以删除（只能删除自己的数据，审核通过后仍可删除）
        canDelete(record) {
            // 基于 allowOperate 字段判断
            return record.allowOperate || false;
        },

        // 判断是否可以操作该记录（兼容旧接口，主要用于编辑）
        canOperate(record) {
            return this.canEdit(record);
        },

        // 显示示例图片管理
        showExampleImages(type, index = 0) {
            this.currentExampleImages = this.exampleImagesData[type] || [];
            this.currentExampleIndex = index;
            this.exampleDialogVisible = true;
        },

        // 上一张示例图片
        previousExample() {
            if (this.currentExampleIndex > 0) {
                this.currentExampleIndex--;
            }
        },

        // 下一张示例图片
        nextExample() {
            if (this.currentExampleIndex < this.currentExampleImages.length - 1) {
                this.currentExampleIndex++;
            }
        },

        // 显示链接扩展引导对话框
        showLinkExtensionGuide(recordId,actionText) {
            // 如果没有返回ID，则不进行引导
            if (!recordId) {
                console.warn('未获取到记录ID，无法进行跳转引导');
                return;
            }

            // 延迟显示，确保之前的成功消息已经显示
            setTimeout(() => {
                // const actionText = this.recordForm.auditStatus == 0 ? '保存草稿' : '提交审核';
                this.$confirm('', '完善链接配置', {
                    confirmButtonText: '立即配置',
                    cancelButtonText: '稍后配置',
                    type: 'info',
                    customClass: 'link-extension-guide-dialog',
                    dangerouslyUseHTMLString: true,
                    message: `
                        <div class="guide-content">
                            <div class="guide-icon">
                                <i class="el-icon-link" style="font-size: 24px; color: #409eff;"></i>
                            </div>
                            <div class="guide-text">
                                <p><strong>域名备案${actionText}成功！</strong></p>
                                <p>为了更好地使用您的短信链接功能，建议您立即前往配置完整链接信息：</p>
                                <ul style="text-align: left; margin: 12px 0; padding-left: 20px;">
                                    <li><strong>短信链接：</strong>短信内容中下发链接开头部分保持一致</li>
                                    <li><strong>完整链接：</strong>配置用户点击后实际跳转的目标地址</li>
                                </ul>
                                <p style="color: #67c23a; font-size: 14px;">
                                    <i class="el-icon-success"></i>
                                    完善配置后，确保短信链接与实际短信内容中的链接格式完全一致，否则可能影响短信发送！
                                </p>
                            </div>
                        </div>
                    `
                }).then(() => {
                    // 用户选择立即前往，跳转到链接扩展管理页面
                    this.navigateToLinkExtension(recordId);
                }).catch(() => {
                    // 用户选择稍后操作，显示提示信息
                    this.$message({
                        type: 'info',
                        message: '您可以随时通过操作列的"新增链接"按钮来配置完整链接信息',
                        duration: 4000
                    });
                });
            }, 1000);
        },

        // 跳转到链接扩展管理页面
        navigateToLinkExtension(recordId) {
            // 根据当前记录获取相关信息
            const linkAddress = this.recordForm.link;
            
            // 跳转到链接扩展管理页面，传递必要的参数
            this.$router.push({
                path: '/linkExtensionManagement',
                query: {
                    linkId: recordId,
                    linkAddress: linkAddress,
                    fromGuide: true // 标识来源于引导跳转
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* LinkRecordManagement 特有样式 */
.search-input {
    width: 240px; // 适合链接地址输入的宽度
}

/* 权限控制相关样式 */
.current-user {
    color: #67c23a;
    font-weight: 500;
}

.no-permission-tip {
    color: #f56c6c;
    font-size: 12px;
    font-style: italic;
}

.audit-passed-tip {
    color: #67c23a;
    font-size: 12px;
    font-style: italic;
    font-weight: 500;
    background: #f0f9ff;
    padding: 2px 8px;
    border-radius: 4px;
    border: 1px solid #e1f5fe;
}

/* 内容单元格样式 */
.content-cell {
    word-break: break-all;
    line-height: 1.4;
}

/* 审核原因单元格样式 */
.reason-cell {
    .reason-content {
        width: 100%;

        .reason-wrapper {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: help;
            max-width: 100%;
            overflow: hidden;

            .reason-icon {
                font-size: 16px;
                margin-top: 1px;
                flex-shrink: 0;
            }

            .reason-text {
                flex: 1;
                min-width: 0;
                overflow: hidden;

                .reason-label {
                    font-size: 12px;
                    font-weight: 600;
                    margin-bottom: 2px;
                    display: block;
                }

                .reason-detail {
                    font-size: 13px;
                    line-height: 1.4;
                    display: block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 100%;
                }
            }
        }

        .reject-reason {
            background: rgba(239, 68, 68, 0.05);
            border: 1px solid rgba(239, 68, 68, 0.2);

            &:hover {
                background: rgba(239, 68, 68, 0.08);
                border-color: rgba(239, 68, 68, 0.3);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
            }

            .reject-icon {
                color: #ef4444;
            }

            .reason-label {
                color: #dc2626;
            }

            .reason-detail {
                color: #991b1b;
            }
        }

        .pass-reason {
            background: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.2);

            &:hover {
                background: rgba(16, 185, 129, 0.08);
                border-color: rgba(16, 185, 129, 0.3);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
            }

            .pass-icon {
                color: #10b981;
            }

            .reason-label {
                color: #059669;
            }

            .reason-detail {
                color: #047857;
            }
        }

        .default-reason {
            background: rgba(59, 130, 246, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.2);

            &:hover {
                background: rgba(59, 130, 246, 0.08);
                border-color: rgba(59, 130, 246, 0.3);
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
            }

            .default-icon {
                color: #3b82f6;
            }

            .reason-detail {
                color: #1e40af;
            }
        }
    }

    .no-reason {
        .empty-text {
            color: #9ca3af;
            font-style: italic;
            font-size: 13px;
        }
    }
}

/* Tooltip 自定义样式 */
/deep/ .el-tooltip__popper {
    max-width: 400px;
    
    &.is-dark {
        background: rgba(0, 0, 0, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.1);
        
        .popper__arrow {
            border-top-color: rgba(0, 0, 0, 0.9);
        }
    }
}

/* 链接扩展引导对话框样式 */
/deep/ .link-extension-guide-dialog {
    .el-message-box {
        width: 500px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        
        .el-message-box__header {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            color: white;
            border-radius: 12px 12px 0 0;
            padding: 20px 24px;
            
            .el-message-box__title {
                color: white;
                font-weight: 600;
                font-size: 18px;
            }
            
            .el-message-box__close {
                color: white;
                opacity: 0.8;
                
                &:hover {
                    opacity: 1;
                }
            }
        }
        
        .el-message-box__content {
            padding: 24px;
            
            .el-message-box__message {
                margin: 0;
                
                .guide-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                    
                    .guide-icon {
                        margin-bottom: 16px;
                        padding: 12px;
                        background: #f0f7ff;
                        border-radius: 50%;
                        border: 2px solid #e1f5fe;
                    }
                    
                    .guide-text {
                        p {
                            margin: 8px 0;
                            color: #2c3e50;
                            line-height: 1.6;
                            
                            &:first-child {
                                font-size: 16px;
                                color: #1a73e8;
                                margin-bottom: 12px;
                            }
                        }
                        
                        ul {
                            background: #f8f9fa;
                            border-radius: 6px;
                            padding: 16px 20px;
                            border: 1px solid #e9ecef;
                            
                            li {
                                margin: 6px 0;
                                color: #495057;
                                font-size: 14px;
                                line-height: 1.5;
                            }
                        }
                    }
                }
            }
        }
        
        .el-message-box__btns {
            padding: 16px 24px 24px;
            
            .el-button {
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: 500;
                
                &.el-button--primary {
                    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
                    border: none;
                    
                    &:hover {
                        opacity: 0.9;
                        transform: translateY(-1px);
                    }
                }
            }
        }
    }
}

/* 图片上传样式 */
.upload-container {
    width: 100%;
}

.image-uploader {
    /deep/ .el-upload {
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        width: 200px;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:hover {
            border-color: #409eff;
            background-color: #fafafa;
        }
    }
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;

    .upload-icon {
        font-size: 32px;
        color: #c0c4cc;
        margin-bottom: 12px;
    }

    .upload-text {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .upload-hint {
        font-size: 12px;
        color: #999;
        line-height: 1.4;
    }
}

.image-preview {
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    pointer-events: none;

    .uploaded-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: auto;

        &:hover {
            opacity: 1;
        }

        .image-actions {
            display: flex;
            gap: 12px;

            i {
                font-size: 20px;
                color: #fff;
                cursor: pointer;
                padding: 8px;
                border-radius: 4px;
                background: rgba(0, 0, 0, 0.3);
                transition: all 0.3s ease;
                pointer-events: auto;

                &:hover {
                    background: rgba(0, 0, 0, 0.6);
                    transform: scale(1.1);
                }

                &.el-icon-zoom-in:hover {
                    color: #409eff;
                }

                &.el-icon-delete:hover {
                    color: #f56c6c;
                }
            }
        }
    }
}

.upload-progress {
    margin-top: 12px;
    width: 200px;
}

/* 对话框增强样式 */
.enhanced-dialog {
    /deep/ .el-dialog {
        border-radius: 8px;

        .el-dialog__header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px 24px;
            border-bottom: 1px solid #ebeef5;

            .el-dialog__title {
                font-size: 18px;
                font-weight: 600;
                color: #2c3e50;
            }
        }
    }
}

.dialog-form {
    /deep/ .el-form-item {
        margin-bottom: 24px;

        .el-input__inner,
        .el-textarea__inner {
            border-radius: 6px;
            transition: all 0.3s ease;

            &:focus {
                border-color: #409eff;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
        }
    }
}

/* 分页容器样式 */
.pagination-container {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
}

/* 对话框底部按钮样式 */
.dialog-footer {
    text-align: right;

    .el-button {
        margin-left: 12px;
    }
}

/* 域名提取工具样式 */
.link-input-container {
    .link-input {
        margin-bottom: 12px;
    }

    .domain-extract-section {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 16px;

        .extract-input-wrapper {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;

            .extract-input {
                flex: 1;
            }

            .extract-btn {
                min-width: 80px;
                border-radius: 4px;
                font-size: 13px;
            }
        }

        .extract-tip {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 12px;

            .el-icon-info {
                margin-right: 6px;
                color: #17a2b8;
            }
        }
    }
}

/* 上传区域带示例图样式 */
.upload-with-example {
    // display: flex;
    // align-items: flex-start;
    // gap: 20px;

    .upload-container {
        flex-shrink: 0;
    }

    .example-image-container {
        display: flex;
        flex-direction: column;

        .example-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .example-images-grid {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;

            .example-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-2px);
                }

                .example-image {
                    width: 80px;
                    height: 80px;
                    border: 1px solid #e4e7ed;
                    border-radius: 4px;
                    object-fit: contain;
                    transition: all 0.3s ease;

                    &:hover {
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                        border-color: #409eff;
                    }
                }

                .example-item-label {
                    font-size: 11px;
                    color: #666;
                    text-align: center;
                    margin-top: 4px;
                    line-height: 1.2;
                    max-width: 80px;
                    word-break: break-all;
                }
            }
        }
    }
}

/* 表单项提示样式 */
.form-item-tip {
    margin-top: 8px;
    padding: 10px 12px;
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 4px;
    color: #ad6800;
    font-size: 12px;
    line-height: 1.5;
    display: flex;
    align-items: flex-start;

    .el-icon-warning {
        font-size: 14px;
        margin-right: 8px;
        margin-top: 2px;
        color: #fa8c16;
        flex-shrink: 0;
    }

    span {
        flex: 1;
    }
}

/* 示例图片查看对话框样式 */
.example-dialog {
    /deep/ .el-dialog {
        .el-dialog__header {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            color: white;

            .el-dialog__title {
                color: white;
                font-weight: 600;
            }

            .el-dialog__close {
                color: white;

                &:hover {
                    background: rgba(255, 255, 255, 0.1);
                }
            }
        }
    }
}

.example-viewer {
    .example-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid #ebeef5;

        .example-title {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
        }

        .example-counter {
            background: #409eff;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
    }

    .example-content {
        margin-bottom: 20px;

        .example-image-wrapper {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 16px;
        }

        .example-description {
            padding: 12px 16px;
            background: #f0f7ff;
            border-left: 4px solid #409eff;
            border-radius: 4px;

            p {
                margin: 0;
                color: #5e6d82;
                font-size: 14px;
                line-height: 1.6;
            }
        }
    }

    .example-navigation {
        display: flex;
        justify-content: center;
        gap: 12px;
        margin-bottom: 20px;

        .el-button {
            padding: 8px 16px;
        }
    }

    .example-thumbnails {
        display: flex;
        gap: 12px;
        justify-content: center;
        flex-wrap: wrap;

        .thumbnail-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 2px solid transparent;

            &:hover {
                background: #f8f9fa;
                transform: translateY(-2px);
            }

            &.active {
                border-color: #409eff;
                background: #ecf5ff;
            }

            img {
                width: 60px;
                height: 60px;
                object-fit: contain;
                border-radius: 4px;
                border: 1px solid #e4e7ed;
            }

            .thumbnail-title {
                font-size: 10px;
                color: #666;
                text-align: center;
                margin-top: 4px;
                max-width: 60px;
                line-height: 1.2;
                word-break: break-all;
            }
        }
    }
}
</style>

<style>
.el-table--small th {
    background: #f5f5f5;
}
</style>
