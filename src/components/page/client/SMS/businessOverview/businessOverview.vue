<template>
    <div>
        <div class="Top_title">
            <span>产品总览</span>
        </div>
        <!-- 顶部各种数据 -->
        <div class="businessOverview-header">
            <el-row class="fillet statistics-amount">
                
                <template v-if="this.$store.state.isDateState == 1">
                    <div style="text-align: left;margin: 17px 15px;">
                        <el-radio-group v-model="MsgNotice" size="medium"  class="mixchange">
                            <el-radio-button label="验证码"  style="margin-right:10px;"></el-radio-button>
                            <el-radio-button label="行业通知"  style="margin-right:10px;border-left: 1px solid #dcdfee;"></el-radio-button>
                            <el-radio-button label="会员营销"  style="margin-right:10px;border-left: 1px solid #dcdfee;"></el-radio-button>
                        </el-radio-group>
                    </div>
                    <el-col :span="6" class="total-box">
                        <div class="total-data"><span>{{billingNumber}}</span>条</div>
                        <div class="this-month">本月发送量</div>
                    </el-col>
                    <el-col :span="6" class="border-l-r total-box">
                        <div class="total-data "><span>{{sendAmount}}</span>个</div>
                        <div class="this-month">本月提交号码数</div>
                    </el-col>
                    <el-col :span="6" class="border-l-r total-box">
                        <div class="total-data "><span>{{sendAmount}}</span>个</div>
                        <div class="this-month">本月提交号码数</div>
                    </el-col>
                    <el-col :span="6" class="total-box">
                        <div class="total-data"><span>{{successRate}}</span>%</div>
                        <div class="this-month">本月发送成功率</div>
                    </el-col>
                </template>
                <template v-if="this.$store.state.isDateState == 2">
                    <el-col :span="12" class="total-box">
                        <div class="total-data"><span>{{billingNumber}}</span>条</div>
                        <div class="this-month">本月发送量</div>
                    </el-col>
                    <el-col :span="12" class="border-l-r-1 total-box">
                        <div class="total-data "><span>{{sendAmount}}</span>个</div>
                        <div class="this-month">本月提交号码数</div>
                    </el-col>
                </template>
            </el-row>
            <template v-if="this.$store.state.isDateState == 1">
                <el-row class="fillet product-amount" >
                    <el-col :span="12" style="padding-left:50px;">
                    <div class="amount-title amount-titles"><span class="vertical-line">| </span> 产品名称</div>
                    <div class="amount-content amount-contents">短信</div>
                    </el-col>
                    <el-col :span="12"  style="padding-left:30px;">
                    <div class="amount-title"><span class="vertical-line">| </span> 余额（条）</div>
                    <div class="amount-content">{{restNumSum}}</div>
                    </el-col>
                </el-row>
            </template>
            <template v-if="this.$store.state.isDateState == 2">
                <el-row class="fillet product-amount-1" >
                    <el-col :span="12" style="padding-left:50px;">
                    <div class="amount-title amount-titles"><span class="vertical-line">| </span> 产品名称</div>
                    <div class="amount-content amount-contents">短信</div>
                    </el-col>
                    <el-col :span="12"  style="padding-left:30px;">
                    <div class="amount-title"><span class="vertical-line">| </span> 余额（条）</div>
                    <div class="amount-content">{{restNumSum}}</div>
                    </el-col>
                </el-row>
            </template>
        </div>
        <!-- 中间统计图表 -->
        <statistical-chart></statistical-chart>
        <!-- 下面图表     -->
        <!-- <recharge-record></recharge-record> -->
    </div>    
</template>

<script>
// import StatisticalChart from './components/StatisticalChart'
import RechargeRecord from './components/RechargeRecord'
export default {
    name: "BusinessOverview",
    components: {
        // StatisticalChart,
        RechargeRecord
    },
    data(){
        return{
            MsgNotice:'验证码',
            billingNumber:'',
            restNumSum:'',
            sendAmount:'',
            successRate:''
        }
    },
    methods:{
        getDatas(){
            this.$api.get(this.API.cpus+'consumerdataoverviewday/businessOverview',{},res=>{
                this.billingNumber = res.data.billingNumber;
                this.restNumSum = res.data.restNumSum;
                this.sendAmount = res.data.sendAmount;
                this.successRate = res.data.successRate;
            })
        }
    },
    mounted(){
        this.getDatas();
    }
}
</script>

<style scoped>
    .businessOverview-header{
        display: flex;
    }
    .statistics-amount{
        width: calc(100% - 410px);
        height:200px;
        text-align: center;
    }
    .total-data{
        font-size:14px;
        margin-top:23px;
    }
    .total-box{
        margin:20px 0;
        height:110px;
    }
    .border-l-r{
        border-left:1px solid #eaeaea;
        border-right:1px solid #eaeaea;
    }
    .border-l-r-1{
        border-left:1px solid #eaeaea;
    }
    .total-data span{
         font-size: 30px;
         color:#333;
     }
    .this-month{
        font-size:14px;
    }
    .product-amount{
        width:400px;
        margin-left:10px;
        height:200px;
    }
    .product-amount-1{
        width:500px;
        margin-left:10px;
        height:150px;
    }
    .vertical-line{
        font-weight: bold;
    }
    .amount-title{
        font-size: 14px;
        margin-top:68px;
    }
    .amount-titles{
        padding-left:16px;
    }
    .amount-content{
        font-size:20px;
        padding:10px 0 0 5px;
        color:#000;
    }
    .amount-contents{
         padding:10px 0 0 18px;
    }
</style>