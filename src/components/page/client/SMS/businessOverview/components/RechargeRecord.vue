<template>
  <div class="rechargerecord fillet">
    <div class="statistical-title">充值记录</div>
    <div style="text-align:right;margin-bottom:10px;">
      充值日期 &nbsp;
      <date-plugin class="search-date"  :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal" ></date-plugin>
    </div>
    <div style="padding-bottom:40px;">
      <table-tem :tableDataObj="tableDataObj" ></table-tem>
      <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page pageStyle" slot="pagination" >
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="chaxunObj.currentPage"  :page-size='chaxunObj.pageSize' :page-sizes="[10, 20, 50, 100,300]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
          </el-pagination>
      </el-col>
    </div>
  </div>
</template>

<script>
import TableTem from '../../../../publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import { mapState, mapMutations,mapActions } from "vuex";
export default {
  name: "RechargeRecord",
  components: {TableTem,DatePlugin},
  computed:{
      ...mapState({  //比如'movies/hotMovies
          roleId:state=>state.userId,
        })
  },
  data() {
    return {
      datePluginValueList: { //日期选择器
          type:"daterange",
          start:"",
          end:'',
          range:'-',
          clearable:false,
          pickerOptions:{
                disabledDate: (time) => {
                    return time.getTime() > Date.now() ;
                }
              },
          datePluginValue: ''
      },
      chaxunObj:{
        currentPage:1,
        pageSize:10,
        beginTime:'',
        endTime:''
      },
      tableDataObj:{
        total:0,
        tableData: [],
        tableLabel:[{prop:"rechargeTime",showName:'日期',width:'160',fixed:false},
        {prop:"rechargeNum",showName:'充值条数',fixed:false},
        {prop:"rechargeAmount",showName:'充值金额',fixed:false},
        // {prop:"rechargePrice",showName:'单价（元/条）',fixed:false},
        {prop:"rechargeCategory",showName:'产品',formatData: function(val) { 
            if(val == 1){
              return '短信'
            }else if(val == 2){
              return '彩信'
            }},fixed:false
        }],
        tableStyle:{
          isSelection:false,//是否复选框
          isExpand:false,//是否是折叠的
          style: {//表格样式,表格宽度
            width:"100%"
            },
          optionWidth:'180',//操作栏宽度
          border:true,//是否边框
          stripe:false,//是否有条纹
        },
      },
    };
  },
  methods: {
    getDate(){
      this.tableDataObj.loading2 = true;
        this.$api.get(this.API.recharge+'client/recharge/page?'+'currentPage='+this.chaxunObj.currentPage+'&pageSize='+this.chaxunObj.pageSize+'&beginTime='+this.chaxunObj.beginTime+'&endTime='+this.chaxunObj.endTime+'&rechargeCategory='+this.chaxunObj.rechargeCategory,{},res=>{
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
        })
    },
    handleSizeChange(size) { //分页一页的size
      this.chaxunObj.pageSize = size
    },
    handleCurrentChange: function(currentPage){//分页第几页
      this.chaxunObj.currentPage = currentPage;
    },
    handledatepluginVal: function(val1,val2){
      if(val1){
          this.chaxunObj.beginTime = val1;
          this.chaxunObj.endTime = val2;
      }else{
        this.chaxunObj.beginTime = '';
          this.chaxunObj.endTime = '';
      }
    },
  },
  watch:{
    chaxunObj:{//监听弹框是否关闭
      handler(){
          this.getDate();
      },
      deep:true,
      immediate:true
    }
  }
}
</script>

<style scoped>
.rechargerecord{
  padding:20px;
}
.statistical-title{
  font-weight: bold;
  padding-bottom: 10px;
}
.detailsList{
  height:42px;
  line-height: 26px;
  padding-left:30px;
  font-size:12px;
}
.detailsList-title{
  display: inline-block;
  width:80px;
}
.detailsList-content{
  display: inline-block;
  width:340px;
  color:#848484;
  padding-left:10px;
  border-bottom: 1px solid #eee;
}
 .look-at-more{
    color:#16a589;
    padding-top: 12px;
}
</style>
<style>
.rechargerecord .el-dialog__footer{
  text-align:center;
}
.el-dialog__title{
  font-size:16px;
}
</style>


