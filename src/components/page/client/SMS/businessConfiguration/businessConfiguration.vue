<template>
    <div>
         <div class="busi-cog-title-box">
            <span v-bind:class="BasicConfiguration"  @click="handleclick('BasicConfiguration')">基础配置</span> |
            <span v-bind:class="BlackList"  @click="handleclick('BlackList')">黑名单管理</span> 
        </div>
        <div>
            <component v-bind:is="currentTabComponent"></component>
        </div>
    </div>    
</template>

<script>
import BasicConfiguration from './components/BasicConfiguration.vue'
import BlackList from './components/BlackList.vue'
export default {
    name: "BusinessConfiguration",
    components:{
        BasicConfiguration,
        BlackList
    },
    data(){
        return{
            BasicConfiguration:{
                'busi-cog-title':true,
                "busiColor":true
            },
            BlackList:{
                'busi-cog-title':true,
                "busiColor":false
            },
            currentTabComponent:'BasicConfiguration'
        }
    },
    methods:{
        handleclick(ele){
            this.currentTabComponent = ele;
            this.BasicConfiguration.busiColor = false;
            this.BlackList.busiColor = false;
            if(ele == 'BasicConfiguration'){
                this.BasicConfiguration.busiColor = true;
            }else if(ele == 'BlackList'){
                this.BlackList.busiColor = true;   
            }
        }
    }
}
</script>

<style scoped>
.busi-cog-title{
    cursor: pointer;
    display: inline-block;
    margin:0 5px;
}
.busi-cog-title-box{
    margin: 10px 0 10px 0;
}
.router-link-active{
    color:#16a589;
}
.busiColor{
    color:#16a589;
}
</style>

