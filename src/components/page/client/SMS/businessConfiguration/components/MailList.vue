<template>
    <div class="fillet mail-list-box">
        <el-tabs v-model="activeName2" type="card" @tab-click="handleClick">
            <!--       联系组      -->
            <el-tab-pane label="联系组" name="first">
                <el-button type="success" style="margin-top:10px;">创建联系组</el-button>
                <div class="Mail-search-fun">
                    <span class="Mail-list-header">联系组列表</span>
                    <!-- 搜索框和日期选择器开始 -->
                    <el-input
                        placeholder="姓名、手机号"
                        v-model="MaillinkGSearchVal" style="width:200px;" class="Mail-search-box">
                        <i slot="suffix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                    <date-plugin class="Mail-search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal"></date-plugin>
                    <!-- 搜索框和日期选择器结束 -->
                </div>
                <div class="Mail-table">
                    <!-- 表格和分页开始 -->
                    <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem>
                        <!--分页-->
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="1" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tableData.length">
                        </el-pagination>
                    </el-col>
                    <!-- 表格和分页结束 -->
                </div>
                <!-- 编辑弹出框 -->
                <el-dialog
                    title="编辑联系人分组"
                    :visible.sync="MailDialogVisible"
                    width="540px"
                    >
                    <el-form :model="form" label-width="80px">
                        <el-form-item label="分组名称">
                            <el-input v-model="form.name"></el-input>
                        </el-form-item>
                        <el-form-item label="备注内容">
                            <el-input type="textarea" v-model="form.desc"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="MailDialogVisible = false">确 定</el-button>
                        <el-button @click="MailDialogVisible = false">取 消</el-button>
                    </span>
                </el-dialog>
                <!-- 编辑弹出框 -->
            </el-tab-pane>
            <!--       联系组       -->

            <!--       联系人       -->
            <el-tab-pane label="联系人" name="second">
                <el-button type="success" style="margin-top:10px;">创建联系人</el-button>
                <el-button type="success" style="margin-top:10px;">Excel批量导入黑名单</el-button>
                <div class="Mail-search-fun">
                    <span class="Mail-list-header">联系人列表</span>
                    <!-- 搜索框和日期选择器开始 -->
                    <el-input
                        placeholder="姓名、手机号"
                        v-model="MailLinkUSearchVal" style="width:200px;" class="Mail-search-box">
                        <i slot="suffix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                    <date-plugin class="Mail-search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal"></date-plugin>
                    <!-- 搜索框和日期选择器结束 -->
                </div>
                <div class="Mail-table">
                    <!-- 表格和分页开始 -->
                    <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem>
                        <!--分页-->
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="1" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tableData.length">
                        </el-pagination>
                    </el-col>
                    <!-- 表格和分页结束 -->
                </div>
                <!-- 编辑弹出框 -->
                <el-dialog
                    title="编辑联系人分组"
                    :visible.sync="MailDialogVisible"
                    width="540px"
                    >
                    <el-form :model="form" label-width="80px">
                        <el-form-item label="分组名称">
                            <el-input v-model="form.name"></el-input>
                        </el-form-item>
                        <el-form-item label="备注内容">
                            <el-input type="textarea" v-model="form.desc"></el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="MailDialogVisible = false">确 定</el-button>
                        <el-button @click="MailDialogVisible = false">取 消</el-button>
                    </span>
                </el-dialog>
            </el-tab-pane>
            <!--       联系人       -->
        </el-tabs>  
    </div>
      
</template>

<script>
import TableTem from '../../../../publicComponents/TableTem'
import FileUpload from '../../../../publicComponents/FileUpload'
import DatePlugin from '../../../../publicComponents/DatePlugin'
export default {
    name: "Home",
    components: {
        TableTem,
        FileUpload,
        DatePlugin
    },
    data () {
        return {
            activeName2: 'first',
            MailLinkUSearchVal: '', //联系人搜索框
            MaillinkGSearchVal:'',//联系组搜索框
            datePluginValueList: { //日期参数配置
                type:"datetimerange",
                start:"",
                end:'',
                range:'-',
                defaultTime:['00:00:00', '23:59:59'], //默认起始时刻
                datePluginValue: [new Date(2017, 10, 10, 10, 10), new Date(2017, 10, 11, 10, 10)]
            },
            MailDialogVisible: false,
            form: {
                name:'',
                desc: ''
            },
            tableDataObj: { //列表数据
                tableData: [{
                    date: '颜值组',
                    chongzhi: '2018-09-12',
                    chongzhij: '20',
                    danjian: '长得美',
                    }],
                tableLabel:[{
                    prop:"date",
                    showName:'联系组',
                    fixed:false
                    },{
                    prop:"chongzhi",
                    showName:'添加时间',
                    width:'150',
                    fixed:false
                    },{
                    prop:"chongzhij",
                    showName:'号码数',
                    fixed:false
                    },{
                    prop:"danjian",
                    showName:'备注',
                    fixed:false
                    }
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'180',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
                tableOptions:[
                {
                    optionName:'编辑',
                    type:'',
                    size:'mini',
                    optionMethod:'details',
                    icon:'el-icon-success'
                },
                {
                    optionName:'删除',
                    type:'',
                    size:'mini',
                    optionMethod:'dele',
                    icon:'el-icon-error'
                }
            ]
        }
        };
    },
    methods: {
        detailsRow(){
             this.MailDialogVisible = true
        },
        handleClick(tab, event) {
            console.log(tab, event);
        },
        handledatepluginVal: function(val1,val2){ //日期
            this.datePluginValue = [val1,val2];
        },
        handelOptionButton: function(val){
            if(val.methods=='details'){
                this.detailsRow()
            }
        },
         handleSizeChange(size) {
         this.pagesize = size;
        },
        handleCurrentChange: function(currentPage){
            this.currentPage = currentPage;
        }
    }
}
</script>

<style scoped>
.mail-list-box{
    padding:20px;
}
.Mail-list-header{
    position: absolute;
    font-weight: bold;
    left:0;
    top:18px;
}
.Mail-search-fun{
    position: relative;
    height:40px;
    margin-top:10px;
    margin-bottom: 10px;
}
.Mail-search-box{
    position: absolute;
    top:5px;
    right:410px;
}
.Mail-search-date{
    position: absolute;
    top:5px;
    right:0px;
}
</style>