<template>
    <div class="fillet notice-box" >
        <el-button type="success" style="margin-right:10px;">添加警告联系人</el-button>
        <span>最多可添加 5 个指定联系人</span>
        <div class="notice-list-header">通知联系人列表</div>
        <div class="notice-table">
          <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem>
        </div>
         <!-- 订单详情的弹出框 -->
        <el-dialog
        title="编辑警告联系人"
        :visible.sync="dialogVisible"
        width="540px"
        >
            <el-form :model="form">
                <el-form-item label="姓名" label-width="110px">
                   <el-input v-model="form.name" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="手机号码" label-width="110px">
                   <el-input v-model="form.region" autocomplete="off"></el-input>
                </el-form-item>
                 <el-form-item label="邮箱" label-width="110px">
                   <el-input v-model="form.date1" autocomplete="off"></el-input>
                </el-form-item>
               <el-form-item label="余额不足提醒" prop="resource" label-width="110px">
                    <el-radio-group v-model="form.date2">
                        <el-radio label="是"></el-radio>
                        <el-radio label="否"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="发送超量提醒" prop="resource" label-width="110px">
                    <el-radio-group v-model="form.resource">
                        <el-radio label="是"></el-radio>
                        <el-radio label="否"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="人工审核通知" prop="resource" label-width="110px">
                    <el-radio-group v-model="form.desc">
                        <el-radio label="是"></el-radio>
                        <el-radio label="否"></el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
                 <el-button @click="dialogVisible = false">取 消</el-button>
            </span>
        </el-dialog>
    </div>    
</template>

<script>
import TableTem from '../../../../publicComponents/TableTem'
export default {
    name: "Home",
    components: {TableTem},
    data() {
      return {
        dialogVisible: false,
        tableDataObj:{
            tableData: [{
            date: '王二',
            chongzhi: '18701456123',
            chongzhij: '<EMAIL>',
            danjian: 'Y',
            leix: 'Y',
            sh:'N'
            }],
            tableLabel:[{
            prop:"date",
            showName:'姓名',
            width:'120',
            fixed:false
            },{
            prop:"chongzhi",
            showName:'手机',
            fixed:false
            },{
            prop:"chongzhij",
            showName:'邮箱',
            fixed:false
            },{
            prop:"danjian",
            showName:'余额不足提醒',
            fixed:false
            },{
            prop:"leix",
            showName:'发送超量提醒',
            fixed:false
            },{
            prop:"sh",
            showName:'审核通知',
            fixed:false
            }],
            tableStyle:{
            isSelection:false,//是否复选框
            // height:250,//是否固定表头
            isExpand:false,//是否是折叠的
            style: {//表格样式,表格宽度
                width:"100%"
                },
            optionWidth:'180',//操作栏宽度
            border:true,//是否边框
            stripe:false,//是否有条纹
            },
            tableOptions:[
                {
                    optionName:'编辑',
                    type:'',
                    size:'mini',
                    optionMethod:'details',
                    icon:'el-icon-success'
                },
                {
                    optionName:'删除',
                    type:'',
                    size:'mini',
                    optionMethod:'dele',
                    icon:'el-icon-error'
                }
            ]
        },
        form: {
            name: '',
            region: '',
            date1: '',
            date2: '',
            resource: '',
            desc: ''
        }
      };
    },
    methods: {
      detailsRow: function (index, rows) {
        this.dialogVisible = true
      },
      deleRow: function(index,rows){
         this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.tableData.splice(index, 1);
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
      },
      handelOptionButton: function(val){
        if(val.methods=='details'){
            this.detailsRow()
        }
        if(val.methods=='dele'){
            this.deleRow()
        }
      }
    }
}
</script>

<style scoped>
.notice-box{
    padding:20px;
}
.notice-table{
    margin-top:14px;
}
.notice-list-header{
    padding-top:30px;
    font-weight: bold;
}
</style>
<style>
.notice-box .el-dialog__body{
    padding:30px 50px;
}
.notice-box .el-form-item__label{
    text-align:left;
}
</style>
