<template>
    <div>
        <!-- 接口信息 -->
        <div class="fillet bas-block">
            <div class="basic-cfg-title">接口信息</div>
            <div class="basic-cfg-cot">
                <span class="basic-cfg-tit">用户名</span>
                <span>{{userName}}</span>
                <div class="basic-cfg-tips">用户名是短信应用的唯一标识，调用短信API接口时需要提供该参数</div>
            </div>
            <div class="basic-cfg-cot">
                <span class="basic-cfg-tit">接口密码 <i class="el-icon-warning"></i></span>
                <span class="create-time" v-if="ispwd == false" style="display:inline-block;">******</span>
                <span class="create-time" v-if="ispwd == true" style="display:inline-block;">{{password}}</span>
                <!-- <span class="appkeyShow" @click="ispeds()">显示</span> -->
                <span class="appkeyShow" @click="modifyPsd()">修改</span>
                <div class="basic-cfg-tips">接口密码是用来校验短信发送请求合法性的密码，与用户名对应，需要业务方高度保密，切勿把密码存储在客户端。</div>
            </div>
            <div class="basic-cfg-cot" style="margin-bottom:0px;">
                <span class="basic-cfg-tit">创建时间</span>
                <span>{{createLocalDateTime}}</span>
            </div>
        </div>
        <div>
            <div class="fillet bas-block" style="height:230px;">
                <div class="basic-cfg-title">余额提醒</div>
                <el-button type="primary"  class="set-balance-tips" @click="handleRequencyEnable" v-if="requencyEnable == false">启用</el-button>
                <div v-if="requencyEnable == true">
                    <transition name="fade">  
                        <div class="tips-box" v-show="sendfrequency">
                            <div style="padding-bottom:15px;">当前账号余额条数 在不足 <span class="bac-color-red"> {{reminderBalances}} </span> 条时提醒</div>
                            <span  style="color:red;font-size:12px;"> 注：请去添加告警联系人，以便正常接收余额不足通知！ </span>
                            <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                            <el-row>
                                <el-button type="primary"  class="set-balance-tips" @click="SetBalance">设置</el-button>
                                <el-button type="primary"  class="set-balance-tips" @click="shutDownSetBalance">关闭设置</el-button>
                            </el-row>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div class="tips-box" v-show="!sendfrequency" >
                            <div style="padding-bottom:15px;">当前账号余额条数 在不足 <el-input style="width:180px;" v-model="NumberBalances"></el-input> 条时提醒</div>
                            <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                            <el-row>
                                <el-button type="primary" class="sure-balance-tips" @click="determine">确定</el-button>
                                <el-button type="primary" plain  class="cancel-balance-tips" @click="sendfrequency=true">取消</el-button>
                            </el-row>
                        </div>
                    </transition>
                </div>
            </div>
        </div>
        <el-dialog title="接口密码修改" :visible.sync="dialogFormVisible" width="520px" :close-on-click-modal="false" :before-close="beforec" class="dialogBasics">
            <el-steps :active="active" simple>
                <el-step title="获取手机验证码" icon="el-icon-edit"></el-step>
                <el-step title="接口密码修改" icon="el-icon-upload"></el-step>
            </el-steps>
            <!-- 表格 -->
            <div v-show="active == 1">
                <el-table
                    :data="dialogTable"
                    border
                    style="width: 100%;margin-top:10px;" class="passWord_table" >
                    <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
                    <el-table-column align="center" prop="phone" label="手机号"></el-table-column>
                    <el-table-column label="选择" align="center"  width="60">
                        <template slot-scope="scope">
                            <!-- 必须要有label才能单选，就是不同的值 -->
                            <el-radio :label="scope.$index" v-model="templateRadio" @change.native="getTemplateRow(scope.$index,scope.row)">&nbsp;</el-radio>
                        </template>
                    </el-table-column>
                </el-table>
                <el-form :model="updateFormDialog.formData"  ref="updateFormDialog" label-width="146px" style="margin-top:16px;" >
                    <el-form-item label="手机验证码" prop="code" :rules="filter_rules({required:true,type:'code',message:'验证码必填！'})">
                        <el-input v-model="updateFormDialog.formData.code" style="width:180px;" ></el-input>
                        <el-button  type="primary" plain style="width:124px;padding:9px 0px;" @click.prevent="send" v-if="nmb==120">获取验证码</el-button>
                        <el-button  type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{nmb}})</el-button>
                    </el-form-item>
                    <el-form-item style="margin-top: 26px;">
                        <el-button class="footer-center-button" @click="dialogFormVisible=false">取 消</el-button> 
                        <el-button class="footer-center-button" type="primary" @click="updateFormDialog_ok('updateFormDialog')">确 认</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div  v-show="active == 2">
                    <div class="passWord-main" >
                        <el-form label-position="right" ref="passWordForm" :rules="passWordForm.formRule" :model="passWordForm.formData"  label-width="100px" >
                            <el-form-item label="接口密码" prop="password" :rules="filter_rules({required:true,message:'接口密码必填！',type:'passwords',min:8,max:16})">
                                <el-input v-model="passWordForm.formData.password"></el-input>
                            </el-form-item>
                            <!-- <el-form-item label="适用产品" prop="product">
                                <el-checkbox v-model="passWordForm.formData.product">短信</el-checkbox>
                            </el-form-item> -->
                        </el-form>
                    </div>
                    <div class="passWord-footer">
                        <el-button @click="dialogFormVisible=false">取消</el-button>
                        <el-button @click="updateFormOk('passWordForm')"  type="primary">确定</el-button>
                    </div>
            </div>
        </el-dialog>
    </div>    
</template>

<script>
import { mapState, mapMutations,mapActions } from "vuex";
export default {
    name: "BasicConfiguration",
    data () {
        return {
            sendfrequency:true,
            requencyEnable:false,
            ispwd:false,
            compName:'',
            active:1,
            passWordForm:{//修改密码的表格--下面表格
                formData:{
                    password:'',
                    // product:[]
                },
                // formRule:{
                //     product: [
                //         { type: 'array', required: true, message: '请至少选择一个适用产品', trigger: 'change' }
                //     ]
                // }
            },
            // 余额条数
            NumberBalances:'',
            //余额提醒条数
            reminderBalances:'',
            updateFormDialog:{//修改接口密码--弹窗
                formData:{
                    code:""//验证码
                },
            }, 
            dialogFormVisible:false,//弹窗显示状态
            //弹窗里的表格
            dialogTable: [],
            templateRadio:0,
            templateSelection:'',
            nmb:120,
        }
    },
    computed:{
        ...mapState({  //比如'movies/hotMovies
            userName:state=>state.userName,
            createLocalDateTime:state=>state.createLocalDateTime,
            password:state=>state.password,
        }),
    },
    methods:{
        //点击修改密码按钮
        modifyPsd(){
            this.getPhoneArr();
        },
        //获取弹出框手机号
        getPhoneArr(){
            this.dialogTable=[];
            this.$api.post(this.API.cpus + 'consumerclientinfo/loginTelephoneManager/list',{},res=>{
                let phonelength=res.data.data;
                this.templateSelection = phonelength[0].phone; //把第一个手机号设置为默认选中
                
                for(var i=0;i<phonelength.length;i++){
                    this.dialogTable.push({phone:phonelength[i].phone})
                }
                this.dialogFormVisible = true;//显示弹窗
            })
        },
        //获取验证码
        send(){
            if(this.templateSelection){
                --this.nmb;
                const timer= setInterval(res=>{
                    --this.nmb;
                    if(this.nmb<1){
                        this.nmb=120;
                        clearInterval(timer);
                    };
                },1000);
                this.$api.get(this.API.cpus+"code/sendVerificationCode?phone="+this.templateSelection+'&flag=1',{},res=>{
                    if(res.data.code == 200){
                        this.$message({
                            type: 'success',
                            duration:'2000',
                            message:'验证码已发送至手机!'
                        });
                    }else{
                        this.$message({
                            type: 'warning',
                            message:'验证码未失效，请失效后再次申请!'
                        });
                    }
                    
                })
            }else{
                this.$message({
                    message: '选择发送验证码的手机号码！',
                    type: 'warning'
                });
            }
        },
        ispeds(){
            this.ispwd=!this.ispwd;
        },
        //获取选中手机号码
        getTemplateRow(index,row){
            this.templateSelection = row.phone;
        },
        beforec(){
            this.dialogFormVisible=false;//隐藏弹窗
        },
        updateFormDialog_ok(formName){ //弹框第一步确认
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    //提交验证码
                    this.$api.get(this.API.cpus + "code/checkVerificationCode?code=" + this.updateFormDialog.formData.code +'&flag=1',{},res=>{
                            if(res.code == 200){
                                this.active = 2;
                            }else{
                                this.$message.error('验证码无效!');
                            }
                        })
                } else {
                    return false;
                }
            });
        },
        updateFormOk(formName){//弹框第二步确认
            this.$refs[formName].validate((valid) => { 
                if (valid) {
                    this.$confirms.confirmation('put','此操作将修改接口密码数据, 是否继续？',this.API.cpus + 'consumerclientinfo/updPassword',{password:this.passWordForm.formData.password},res =>{
                        this.dialogFormVisible=false;//隐藏弹窗
                    });
                } else {
                    return false;
                }
            });
        },
        // 点击启用
        handleRequencyEnable(){
            this.$api.post(this.API.cpus+'/consumerClientBalanceNotice/save',{smsNum:this.reminderBalances},res=>{

            })
            this.requencyEnable = true;
            this.sendfrequency = true;
        },
        //设置
        SetBalance(){
            this.sendfrequency = !this.sendfrequency;
        },
        //点击确定
        determine(){
            const testNum= /^([1-9][0-9]{0,6}|10000000)$/;
            if(testNum.test(this.NumberBalances)){
                this.$confirms.confirmation('post','确定余额条数不足'+this.NumberBalances+'条时提醒',this.API.cpus + '/consumerClientBalanceNotice/save',{smsNum:this.NumberBalances},res =>{
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    //     this.reminderBalances=res.smsNum
                    // })
                    this.sendfrequency = !this.sendfrequency;
                    this.dialogFormVisible=false;//隐藏弹窗
                });
            }else{
                this.$message({
                    type: 'error',
                    duration:'2000',
                    message:"请填写1-10000000的余额条数"
                });
            }
        },
        shutDownSetBalance(){
            this.$confirms.confirmation('get','确认关闭余额提醒设置',this.API.cpus+'consumerClientBalanceNotice/close',{},res =>{
                this.requencyEnable = !this.requencyEnable;
            });
        }
    },
    created(){
        // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
        //     if(res){
        //         if(res.smsIsOpen==2){
        //             this.requencyEnable=false
        //         }else{
        //             this.requencyEnable=true         
        //         }
        //             this.reminderBalances=res.smsNum
        //     }else{
        //         this.requencyEnable=false
        //     }
        // })
    },
    
    watch:{//监听弹框是否关闭
        dialogFormVisible(val){
            if(val == false){
                this.$refs.updateFormDialog.resetFields(); //清空表单
                this.$refs.passWordForm.resetFields(); //清空表单
                this.active = 1;
            }
        }
    },

}
</script>

<style scoped>
.appkeyShow{
    display: inline-block;
    padding-left:10px;
    color:#16a589;
    cursor: pointer;
}
.bas-block{
    margin-bottom: 10px;
    padding:30px;
    position: relative;
    height:220px;
}
.basic-cfg-title{
    font-weight: bold;
    padding-bottom:32px;
    color:#333;
}
.basic-cfg-tit{
    width:92px;
    display: inline-block;
}
.basic-cfg-cot{
    margin-bottom: 26px;
}
.basic-cfg-tips{
    padding-left:96px;
    padding-top:4px;
    font-size: 12px;
    color:#999;
}
.appkeyShow{
    color:#16a589;
    cursor: pointer;
}
.dialogTable {
    margin: 0 0 30px 0;
}
.passWord-footer {
    padding-top: 20px;
    text-align:right;
}
.footer-center-button {
    width: 100px;
    padding: 9px 0px;
}
.passWord-main {
    padding: 35px 24px 71px 0px;
}
.tips-box{
    position: absolute;
    background: #fff;
    padding: 20px;
    border: 1px solid #e6e6e6;
}
.fade-enter-active{
    animation: show-in 1s;
    transition: all 1s;
}
.fade-leave-active{
    animation: show-out 1s;
    transition: all 1s;
}
.fade-enter,.fade-leave-to{
    opacity: 0;
}
.basic-help{
    margin:30px 0 20px 0;
    padding-bottom:10px;
    width:500px;
    border-bottom: 1px solid #e6e6e6;
}
@keyframes show-in {
    0%{
        transform: rotateX(0deg);
    }
    100%{
        transform: rotateX(360deg);
    }
}
@keyframes show-out {
    0%{
        transform:rotateX(360deg);
    }
    100%{
        transform: rotateX(0deg);
    }
}
.bac-color-red{
    color:red;
}
</style>
<style>
.dialogTable .passWord_table .el-radio__label {
    padding-left: 0px;
}
.el-table .el-table__header tr, .el-table .el-table__header tr th, .el-table__fixed-right-patch {
    background-color: #f5f5f5;
}
.el-steps--simple{
    background:#fff;
}
.el-dialog__body{
    padding-top:10px;
}
.dialogBasics .el-radio__label{
    padding-left:0px !important;
}
</style>