<template>
  <div class="simple-statistical-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 页面标题 -->
        <div class="page-header">
          <h2 class="page-title">统计分析</h2>
          <div class="page-subtitle">查看短信发送统计数据和分析报告</div>
        </div>

        <!-- 组件内容区域 -->
        <div class="component-content">
          <component v-bind:is="currentTabComponent"></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DataScreening from './components/DataScreening.vue'
import ShortMessageRecording from './components/ShortMessageRecording.vue'
import TemplateQuery from './components/TemplateQuery.vue'
import reportForm from './components/reportForm.vue'
export default {
    name: "StatisticalAnalysis",
    components:{
        DataScreening,
        ShortMessageRecording,
        TemplateQuery,
        reportForm
    },
    data(){
        return{
            DataScreening:{
                'busi-cog-title':true,
                "busiColor":true
            },
            ShortMessageRecording:{
                'busi-cog-title':true,
                "busiColor":false
            },
            TemplateQuery:{
                'busi-cog-title':true,
                "busiColor":false
            },
            reportForm:{
                'busi-cog-title':true,
                "busiColor":false
            },
            currentTabComponent:'DataScreening'
        }
    },
    created(){
        if(this.$route.query.a == 4){
            this.handleclick('reportForm');
        }
    },
    methods:{

    }
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* statisticalAnalysis 特有样式 */
.simple-statistical-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

.page-header {
  background: #fff;
  padding: 24px 32px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.component-content {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 保持原有的一些必要样式 */
.busi-cog-title{
    cursor: pointer;
    display: inline-block;
    margin:0 5px;
}
.busi-cog-title-box{
    margin: 10px 0 10px 0;
}
.router-link-active{
    color:#16a589;
}
.busiColor{
    color:#16a589;
}
</style>