<template>
    <div id="ReturnAnalysis">
        <div class="fillet  return-analysis-chart-box">
            <div class=" return-analysis-chart-title">
                <slot name="sele"></slot>
                <el-radio-group v-model="specificTime" size="small" text-color="#fff" fill="#16a589" @change="handleChangeTimeOptions">
                    <el-radio-button label="今天" ></el-radio-button>
                    <el-radio-button label="昨天"></el-radio-button>
                    <el-radio-button label="近7天"></el-radio-button>
                    <el-radio-button label="近30天" ></el-radio-button>
                     <el-radio-button label="近一年" class="threeDay"></el-radio-button>
                </el-radio-group>
                <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal" @IsThereValue="IsThereValue"></date-plugin>
            </div>
            <div class="return-analysis-tab-type">
                <el-tabs v-model="activeName2" type="card" @tab-click="handleClick">
                    <el-tab-pane label="短信回执分析" name="first">
                        <div class="return-analysis-tips">
                            <div>提示：回执分析统计存在两小时左右延迟，请知晓。</div>
                         </div>
                        <table-tem :tableDataObj="tableDataObj" ></table-tem>
                    </el-tab-pane>
                    <el-tab-pane label="回执失败分析" name="second">
                        <div class="return-analysis-tips">
                            <div>提示：回执分析统计存在两小时左右延迟，请知晓。</div>
                        </div>
                        <table-tem :tableDataObj="tableDataObj" ></table-tem>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        
    </div>
</template>

<script>

import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
export default {
    name: "ReturnAnalysis",
    components: {
        TableTem,
        DatePlugin
    },
    data () {
        return {
            value: '',
            va1: '',
            activeName2: 'first',
            specificTime: '今天', //选择那一天
            datePluginValueList: { //日期选择器
                type:"datetimerange",
                start:"",
                end:'',
                range:'-',
                clearable:false,
                pickerOptions:{
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    }
                },
                defaultTime:['00:00:00', '23:59:59'], //默认起始时刻
                datePluginValue: ''
            },
            tableDataObj:{ //表格数据
                tableData: [{
                    date: '王二',
                    chongzhi: '18701456123',
                    chongzhij: '<EMAIL>',
                    danjian: 'Y',
                    leix: 'Y',
                    sh:'N'
                    }],
                    tableLabel:[{
                    prop:"date",
                    showName:'姓名',
                    width:'120',
                    fixed:false
                    },{
                    prop:"chongzhi",
                    showName:'手机',
                    fixed:false
                    },{
                    prop:"chongzhij",
                    showName:'邮箱',
                    fixed:false
                    },{
                    prop:"danjian",
                    showName:'余额不足提醒',
                    fixed:false
                    },{
                    prop:"leix",
                    showName:'发送超量提醒',
                    fixed:false
                    },{
                    prop:"sh",
                    showName:'审核通知',
                    fixed:false
                }],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'180',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            }
        }
    },
    methods: {
        handledatepluginVal: function(val1,val2){
            // this.datePluginValue = [val1,val2];
            if(val1){
                this.specificTime=''
            }
        },
        IsThereValue: function (val){
            if(!val){
                if(this.specificTime == ''){
                    this.specificTime = '今天'
                }
            }
        },
        handleChangeTimeOptions: function(){
            this.datePluginValueList.datePluginValue = ''
        },
        handleClick(tab, event) {
            console.log(tab, event);
        }
    }
}
</script>

<style scoped>
    .return-analysis-chart-box{
        margin:10px 0;
        padding:20px 20px 20px 20px;
    }
    .return-analysis-chart-title{
            display: flex;
    }
    .return-analysis-chart{
        height:360px;
    }
    .return-analysis-title{
        padding-top:40px;
        font-weight: bold;
    }
    .look-at-more{
        color:#16a589;
    }
    .return-analysis-select{
        position: relative;
        margin-top:10px;
    }
    .return-analysis-tab-type{
        margin-top:40px;
    }
    .query-frame{
       margin-top:20px; 
    }
    .query-frame .el-form-item{
        display: inline-block;
        margin-bottom:12px; 
    }
    .return-analysis-tips{
        border:1px solid #66CCFF;
        background: #E5F0FF;
        padding:10px;
        font-size: 12px;
        margin:20px 0px 12px 0px; 
    }
    .return-analysis-tips > div{
        height:26px;
        line-height: 26px;
    }
</style>
<style>
#ReturnAnalysis .threeDay .el-radio-button__inner{
    border-right: 0 ;
}
.return-analysis-chart-box .el-radio-button:last-child .el-radio-button__inner{
    border-radius: 0;
}
.return-analysis-chart-box .el-range-editor.el-input__inner{
    border-radius: 0px 4px 4px 0;
}
</style>
