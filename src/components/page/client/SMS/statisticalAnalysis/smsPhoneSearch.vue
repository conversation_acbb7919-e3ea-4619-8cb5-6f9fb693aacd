<template>
  <div class="simple-phonesearch-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">号码状态查询</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-count">共 {{ tableDataObj.total }} 条记录</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <div class="search-controls">
                <!-- 筛选条件 -->
                <div class="filter-controls">
                  <div class="filter-item">
                    <span class="control-label">手机号码：</span>
                    <el-input
                      v-model="form.mobile"
                      placeholder="请输入手机号码"
                      class="filter-input"
                      clearable
                      @keyup.enter.native="search"
                    />
                  </div>

                  <div class="filter-actions">
                    <el-button type="primary" @click="search" class="reset-btn">
                      <i class="el-icon-search"></i> 查询
                    </el-button>
                    <el-button @click="resetting" class="reset-btn">
                      <i class="el-icon-refresh-left"></i> 重置
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 号码状态查询列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">查询结果列表</h3>
            <div class="table-subtitle">
              <el-tag
                :type="tableDataObj.total > 0 ? 'success' : 'info'"
                size="small"
              >
                {{ tableDataObj.total > 0 ? '查询成功' : '暂无数据' }}
              </el-tag>
            </div>
          </div>

          <div class="table-container">
            <div class="table-wrapper">
              <el-table
                v-loading="tableDataObj.loading2"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                ref="multipleTable"
                border
                :stripe="true"
                :data="tableDataObj.tableData"
                style="width: 100%"
                class="enhanced-table"
              >
                <el-table-column label="手机号" width="140">
                  <template slot-scope="scope">
                    <span class="phone-number">{{ scope.row.mobile }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="短信内容" width="500">
                  <template slot-scope="scope">
                    <span class="content-text">{{ scope.row.content }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="计费条数" width="100">
                  <template slot-scope="scope">
                    <span class="charge-num">{{ scope.row.chargeNum }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="消息ID" width="200">
                  <template slot-scope="scope">
                    <div class="msgid-cell">
                      <span class="msgid-text">{{ scope.row.msgid }}</span>
                      <i
                        class="el-icon-document-copy copy-icon"
                        @click="handleCopy(scope.row.msgid, $event)"
                        title="复制消息ID"
                      />
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="发送时间" width="160">
                  <template slot-scope="scope">
                    <span class="time-text">{{ scope.row.sendTime }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="状态上报时间" width="160">
                  <template slot-scope="scope">
                    <span class="time-text">{{ scope.row.reportTime }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="发送状态" width="120">
                  <template slot-scope="scope">
                    <el-tag
                      :type="scope.row.smsStatus == 1 ? 'success' : scope.row.smsStatus == 2 ? 'danger' : 'warning'"
                      size="small"
                    >
                      <span v-if="scope.row.smsStatus == 1">成功</span>
                      <span v-else-if="scope.row.smsStatus == 2">失败</span>
                      <span v-else>待返回</span>
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column label="备注">
                  <template slot-scope="scope">
                    <span class="remark-text">{{ scope.row.originalCode }}</span>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 简单的统计信息 -->
              <div class="table-footer">
                <div class="total-info">
                  <el-tag type="info" size="small">
                    <i class="el-icon-info"></i>
                    共查询到 {{ tableDataObj.total }} 条记录
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import clip from '../../../utils/clipboard'
export default {
    name: "smsPhoneSearch",
    components: {
        TableTem,
        DatePlugin
    },
    data () {
        return {
            name: "smsPhoneSearch",
            //发送查询的值
            form:{
                mobile:'',
            },
            tableDataObj: {
                //列表数据
                //总条数
                total: 0,
                loading2: false,
                tableData: [],
            },
            //复制发送查询的值
            formData:{
                mobile:'',
            },
        }
    },
     created(){
        this.getdate()
    },
    // activated(){
    //     this.getdate()
    // },
    methods: {
        getdate(){
            this.$api.get(this.API.cpus+'statistics/queryMobile',{mobile:this.form.mobile},res=>{
                this.tableDataObj.tableData = res.data
                this.tableDataObj.total = res.data.length
                // console.log(res.data);
        })
    },
    handleCopy(name,event){
        clip(name, event)
      },
    search(){
        this.getdate()
    },
    resetting(){
        this.form.mobile = ""
        this.getdate()
    },

    // 刷新列表
    refreshList() {
        this.getdate();
    }
    }
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* smsPhoneSearch 特有样式 */
.simple-phonesearch-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

/* 搜索控件样式 */
.search-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.filter-input {
  min-width: 300px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;

  .search-btn {
    background: #409eff;
    border-color: #409eff;

    &:hover {
      background: #66b1ff;
      border-color: #66b1ff;
    }
  }

  .reset-btn {
    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }
}

.table-subtitle {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格特殊样式 */
.phone-number {
  font-weight: 500;
  color: #333;
}

.content-text {
  line-height: 1.4;
  word-break: break-word;
}

.charge-num {
  font-weight: 600;
  color: #409eff;
}

.msgid-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .msgid-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 160px;
  }

  .copy-icon {
    color: #409eff;
    cursor: pointer;
    font-size: 14px;
    flex-shrink: 0;

    &:hover {
      color: #66b1ff;
    }
  }
}

.time-text {
  font-size: 13px;
  color: #666;
}

.remark-text {
  color: #999;
  font-size: 13px;
}

.table-footer {
  padding: 16px 0;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;

  .total-info {
    display: inline-flex;
    align-items: center;
    gap: 8px;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-item {
    width: 100%;
  }

  .filter-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .filter-input {
    min-width: 250px;
  }

  .msgid-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    .msgid-text {
      max-width: 100%;
    }
  }
}
</style>
<style>
/* 全局样式覆盖 */
.simple-phonesearch-page .el-table--small td,
.simple-phonesearch-page .el-table--small th {
  padding-top: 8px;
  padding-bottom: 8px;
}

.simple-phonesearch-page .el-table {
  border-radius: 8px;
  overflow: hidden;
}

.simple-phonesearch-page .el-table th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
}

.simple-phonesearch-page .el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.simple-phonesearch-page .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}
</style>
