<template>
    <div>
        <div class="busi-cog-title-box">
            <span v-bind:class="sendIndex"  @click="handleclick('sendIndex')">短信发送</span> |
            <span v-bind:class="timing"  @click="handleclick('timing')">定时管理</span> |
            <span v-bind:class="filewebNumber"  @click="handleclick('filewebNumber')">web端提交无效数据</span> 
        </div>
        <div>
            <component v-bind:is="currentTabComponent"></component>
        </div>
    </div>    
</template>

<script>
import sendIndex from './components/sendIndex.vue'
import timing from './components/timing.vue'
import filewebNumber from './components/filewebNumber.vue'
export default {
    name: "sendMessages",
    components:{
        sendIndex,
        timing,
        filewebNumber
    },
    data(){
        return{
            sendIndex:{
                'busi-cog-title':true,
                "busiColor":true
            },
            timing:{
                'busi-cog-title':true,
                "busiColor":false
            },
            filewebNumber:{
                'busi-cog-title':true,
                "busiColor":false
            },
            currentTabComponent:'sendIndex'
        }
    },
    methods:{
        handleclick(ele){
            this.currentTabComponent = ele;
            this.sendIndex.busiColor = false;
            this.timing.busiColor = false;
            this.filewebNumber.busiColor = false;
            if(ele == 'sendIndex'){
                this.sendIndex.busiColor = true;
            }else if(ele == 'timing'){
                this.timing.busiColor = true;   
            }else if(ele == 'filewebNumber'){
                this.filewebNumber.busiColor = true;   
            }
        }
    }
}
</script>

<style scoped>
.busi-cog-title{
    cursor: pointer;
    display: inline-block;
    margin:0 5px;
}
.busi-cog-title-box{
    margin: 10px 0 10px 0;
}
.router-link-active{
    color:#16a589;
}
.busiColor{
    color:#16a589;
}
</style>