<template>
    <div>
        <div class="OuterFrame fillet">
            <div class="Timing-matter">
                <p style="font-weight:bolder">温馨提醒：</p>
                <p>1. 定时短信在未发送前取消需至少提前十分钟；</p>
                <p>2.取消定时任务操作不可逆转，请确认好之后再进行处理。</p>
            </div>
            <div>
               <el-form :inline="true" :model="sensitiveCondition" class="demo-form-inline" label-width="82px" ref="sensitiveCondition">
                    <el-form-item label="短信内容" label-width="82px"  prop="smsContent">
                        <el-input v-model="sensitiveCondition.smsContent" placeholder="" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="提交时间" label-width="82px"  prop="subtime">
                        <el-date-picker class="input-w"
                        v-model="sensitiveCondition.subtime"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        align="right"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="handeleDate1"
                        :clearable=false
                        >
                        </el-date-picker>
                    </el-form-item>
                     <el-form-item label="定时时间" label-width="82px"  prop="dsTime">
                        <el-date-picker class="input-w"
                        v-model="sensitiveCondition.dsTime"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        align="right"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="handeleDate2"
                        :clearable=false
                        >
                        </el-date-picker>
                    </el-form-item> 
                    <el-form-item >
                        <el-button type="primary" plain  @click="sensitiveQuery()">查询</el-button>
                        <el-button type="primary" plain  @click="sensitiveReload('sensitiveCondition')">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="sensitive-fun">
                <span class="sensitive-list-header" >定时短信列表</span>
                <el-button type="primary" v-if='selectId.length != 0 '  @click="batchCancellation()">批量取消</el-button>
            </div>
            <div class="sensitive-table">
                <!-- 表格和分页开始 -->
                <el-table
                    v-loading="tableDataObj.loading2 "
                    element-loading-text="拼命加载中"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="rgba(0, 0, 0, 0.6)"
                    ref="multipleTable"
                    border
                    :data="tableDataObj.tableData"
                    style="width: 100%"
                    @selection-change="handelSelection">
                    <el-table-column type="selection" width="46"></el-table-column>
                    <el-table-column label="提交时间" width="142">
                         <template slot-scope="scope">{{ scope.row.createTime}}</template>
                    </el-table-column>
                    <el-table-column label="短信内容" >
                        <template slot-scope="scope" >
                              <span>{{scope.row.signature}}</span>
                            <span>{{scope.row.content}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="number" label="提交数量" width="90"></el-table-column>
                    <el-table-column prop="sendTime" label="定时时间" width="142"></el-table-column>
                    <el-table-column label="提交状态" width="80">
                        <template slot-scope="scope" >
                            <span v-if="scope.row.timingStatus=='1'">未执行</span>
                            <span v-else-if="scope.row.timingStatus=='3'" >取消</span>
                            <span v-else-if="scope.row.timingStatus=='5'" >执行完成</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width='130'>
                        <template slot-scope="scope">
                            <el-button type="text"  @click="handleReject(scope.$index, scope.row)"><i class="el-icon-success"></i>&nbsp;报告</el-button>
                            <el-button type="text" style="color:orange"  v-if="scope.row.timingStatus=='1'"  @click="handleAdopt(scope.$index, scope.row)"><i class="el-icon-error"></i>&nbsp;取消</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页-->
                <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                    <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="sensitiveConditions.pagesize" :page-size="sensitiveConditions.pagesize" :page-sizes="[20, 50, 100, 200]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.totalRow">
                    </el-pagination>
                </el-col>
            </div>
        </div>
         <!-- 报告的弹出框 -->
        <el-dialog title="任务报告查看" :visible.sync="sendMsgDialogVisible" width="860px" >
            <template v-if="this.$store.state.isDateState == 1">
                <div style="display:inline-block;width:49%;text-align:center; border-right:1px solid #f7f7f7;margin-top:-20px;">
                    <PieChart id="pie2" width="390px" height="340px" :basicOption="basicOption1" ></PieChart>
                </div>
                <div style="display:inline-block;width:49%;text-align:center;margin-top:-20px;">
                    <PieChart id="pie1" width="390px" height="340px" :basicOption="basicOption2" ></PieChart>
                </div>
                <span style="display:block;padding:0 0 10px 0;color:#333;"> 发送明细列表</span>
                <table-tem :tableDataObj="tableDataObj1" ></table-tem> 
            </template>
            <template v-if="this.$store.state.isDateState == 2">
                <div style="text-align:center;margin-top:-20px;">
                    <PieChart id="pie1"  height="300px" :basicOption="basicOption2" ></PieChart>
                </div>
            </template>
            <span style="display:block;padding:20px 0 10px 0;color:#333;"> 回执失败代码分析列表</span>
            <table-tem :tableDataObj="tableDataObj2" ></table-tem>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="sendMsgDialogVisible = false">知道了</el-button>
            </div>
        </el-dialog>
    </div>        
</template>
<script>
import TableTem from '@/components/publicComponents/TableTem'
import PieChart from '@/components/publicComponents/PieChart' //饼图
// 引入时间戳转换
import {formatDate} from '@/assets/js/date.js' 

export default {
    name:'timing',
    components:{
        TableTem,
        PieChart
    },
     data(){
        return {
            name:'timing',
            sensitiveCondition: { //查询条件的值
                smsContent: '',
                dsTime:[],
                startTime:'', //提交开始时间
                stopTime:'',//提交结束时间
                beginTime:'',//定时开始时间
                endTime:'',//定时结束时间
                currentPage:1,
                pageSize:10,
            },
            sensitiveConditions: { //赋值查询条件的值
                smsContent: '',
                dsTime:[],
                startTime:'', //提交开始时间
                stopTime:'',//提交结束时间
                beginTime:'',//定时开始时间
                endTime:'',//定时结束时间
                currentPage:1,
                pageSize:10,
            },
            tableDataObj: { //列表数据
                loading2:false,
                tablecurrent:{ //分页参数
                    totalRow:0,
                },
                tableData: []
            },
            selectId:'',//列表选中项的id
            timingStatus:[], //列表选中项的的状态
            sendTimess:[], //列表选中项的定时时间
            sendMsgDialogVisible: false, //弹出框
            tableDataObj1: { 
                tableData: [],
                tableLabel:[{
                        prop:"mobileNumber",
                        showName:'提交号码数',
                        fixed:false
                    },{
                        prop:"mobileChanrgeNum",
                        showName:'提交号码计费数',
                        fixed:false
                    },{
                        prop:"successAmount",
                        showName:'成功号计费数',
                        fixed:false
                    },{
                        prop:"failureAmount",
                        showName:'失败号计费数',
                        width:100,
                        fixed:false
                    },{
                        prop:"waitNumber",
                        showName:'待返回号码计费数',
                        width:120,
                        fixed:false
                    }
                ],
                tableStyle:{ //列表配置项
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    isDefaultExpand:false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'120',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            },
            tableDataObj2: { 
                tableData: [],
                tableLabel:[{ //列表表头
                        prop:"failureCodeNoteName",
                        showName:'失败原因',
                        fixed:false
                    },{
                        prop:"codeNoteNum",
                        showName:'数量',
                        fixed:false
                    },{
                        prop:"codeNoteProportion",
                        showName:'占比',
                        fixed:false
                    }
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    height:240,//是否固定表头
                    isExpand:false,//是否是折叠的
                    isDefaultExpand:false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'120',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            },
            basicOption1:{//发送明细图表
                data:[{
                        value: 0,
                        name: '成功'
                    }, {
                        value: 0,
                        name: '失败',
                    }, {
                        value: 0,
                        name: '待返回'
                }],
                ledate:['成功','失败','待返回'],
                bgColor: ['#8996E6', '#98D87D','#FFD86E'],
                radius:'62%',
                title:{
                    textStyle:{
                        color:"#999",
                        fontSize:14
                    },
                    text:'发送明细图表',
                    x:'right'
                },

            },
            basicOption2:{//回执失败代码分析图表
                data:[],
                ledate:[],
                bgColor: ['#8996E6', '#49A9EE', '#98D87D','#FFD86E','#F3857C','#8996E6', '#49A9EE', '#98D87D','#FFD86E','#F3857C','#8996E6', '#49A9EE', '#98D87D','#FFD86E','#F3857C'],
                radius:'62%',
                title: {
                    text:'回执失败代码分析图表',
                    textStyle:{
                        color:"#999",
                        fontSize:14
                    },
                    x:'right'
                },

            },
            msgid:'', //任务报告行的消息ID
        }
    },
     methods: {
        goBack(){//返回
            this.$router.go(-1);
        },
        GettableDtate(){ //获取列表数据
            this.tableDataObj.loading2 = true
            this.$api.post(this.API.cpus + 'consumertimingsms/page',this.sensitiveConditions,res=>{
                this.tableDataObj.tableData = res.records;
                this.tableDataObj.tablecurrent.totalRow = res.total; 
                this.tableDataObj.loading2 = false;
            })
        },
        sensitiveQuery(){ //查询
            Object.assign(this.sensitiveConditions,this.sensitiveCondition);
        },
        sensitiveReload(formName){ //重置
            this.$refs[formName].resetFields();
            this.sensitiveCondition.startTime = '';//提交开始时间
            this.sensitiveCondition.stopTime = '';//提交结束时间
            this.sensitiveCondition.beginTime = '';//定时开始时间
            this.sensitiveCondition.endTime = '';//定时结束时间
            Object.assign(this.sensitiveConditions,this.sensitiveCondition);
        },
        handleSizeChange(size) {
            this.sensitiveConditions.pageSize = size;
        },
        handleCurrentChange: function(currentPage){
            this.sensitiveConditions.currentPage = currentPage;
        },
        handeleDate1(val){
            if(val){
                this.sensitiveCondition.startTime = val[0];//提交开始时间
                this.sensitiveCondition.stopTime = val[1];//提交结束时间
            }else{
                this.sensitiveCondition.startTime = '';//提交开始时间
                this.sensitiveCondition.stopTime = '';//提交结束时间
            }
        },
        handeleDate2(val){
            if(val){
                this.sensitiveCondition.beginTime = val[0];//定时开始时间
                this.sensitiveCondition.endTime = val[1];//定时结束时间
            }else{
                this.sensitiveCondition.beginTime = '';//定时开始时间
                this.sensitiveCondition.endTime = '';//定时结束时间
            }
        },
        // //--------------操作框各个按钮功能开始 ----------
        // handelOptionButton: function(val){
        //     this.msgid = val.row.msgid;
        //     if(val.methods=='setLabel'){ //报告
        //         this.getconsunerTaskData();
        //         this.sendMsgDialogVisible = true;


        //     //    this.setLabelDialog=true
        //     }else if (val.methods=='delLabel'){ //取消
        //        let aa =  new Date(val.row.sendTime).getTime(); //定时时间
        //        let bb =  new Date().getTime(); //当前时间
        //         if((aa-bb)>600000) {
        //             this.$confirms.confirmation('delete','此操作将取消该条发送数据, 是否继续?',this.API.cpus + 'consumertimingsms/'+val.row.timingSmsId,{},res =>{
        //                 this.GettableDtate();
        //             })
        //         }else{
        //             this.$message({
        //                 message: '该项的定时时间距离发送时间不到10分钟，不可取消！',
        //                 type: 'warning'
        //             });
        //         }
                
        //     }
        // },
        //报告
        handleReject(index,row){
            this.msgid = row.msgid;
            this.getconsunerTaskData();
            this.sendMsgDialogVisible = true;
        },
        //取消
        handleAdopt(index,row){
            let aa =  new Date(row.sendTime).getTime(); //定时时间
            let bb =  new Date().getTime(); //当前时间
            if((aa-bb)>600000) {
                this.$confirms.confirmation('get','此操作将取消该条发送数据, 是否继续?',this.API.cpus + 'consumertimingsms/batchCancel/'+row.msgid,{},res =>{
                    this.GettableDtate();
                })
            }else{
                this.$message({
                    message: '该项的定时时间距离发送时间不到10分钟，不可取消！',
                    type: 'warning'
                });
            }
        },
        //批量取消
        batchCancellation(){
            let aas = true;
            let bbs = true;
            for(let i=0;i<this.timingStatus.length;i++){
                if(this.timingStatus[i] != 1){
                    aas = false;
                    break;
                }
                let cc = new Date().getTime();
                let dd = new Date(this.sendTimess[i]).getTime();
                if((dd - cc) < 600000){
                    bbs= false;
                    break;
                }
            }
            if(aas == true){
                if(bbs == true){
                    this.$confirms.confirmation('get','此操作将取消该条发送数据, 是否继续?',this.API.cpus + 'consumertimingsms/batchCancel/'+ this.selectId,{},res =>{
                        this.GettableDtate();
                    })
                }else{
                    this.$message({
                        message: '选中项中的定时时间距离发送时间不到10分钟，不可取消！',
                        type: 'warning'
                    });
                }
            }else{
                this.$message({
                    message: '选中项中包含不可取消项，需重新选取！',
                    type: 'warning'
                });
            }
           
        },
        //列表复选框的值
        handelSelection(val){ 
            let selectId = [];
            let timingStatus = [];
            let sendTimess = [];
            for(let i=0;i<val.length;i++){
                // selectId.push(val[i].timingSmsId);
                selectId.push(val[i].msgid);
                timingStatus.push(val[i].timingStatus);
                sendTimess.push(val[i].sendTime);
            }
            this.selectId = selectId.join(','); //批量操作选中消息id
            this.timingStatus = timingStatus; //批量操作选中项的 状态
            this.sendTimess = sendTimess; //批量操作选中的发送时间
        },
        getconsunerTaskData(){
            //获得短信失败状态报告的图表数据
            this.$api.get(this.API.cpus + 'consumertasksms/selectConsumerFailureCodeNoteStatisticsByTemplateId?msgid='+this.msgid,{},res=>{
                this.tableDataObj2.tableData = res;
                this.basicOption2.data = [];
                this.basicOption2.ledate = [];
                for(let i=0;i<res.length;i++){
                    this.basicOption2.data.push({name:res[i].failureCodeNoteName,value:res[i].codeNoteNum});
                    this.basicOption2.ledate.push(res[i].failureCodeNoteName);
                }
            })
            //获得任务报告图表数据
            this.$api.get(this.API.cpus + 'consumersmsinfo/selTemplateDetail?msgid='+this.msgid,{},res=>{
                this.tableDataObj1.tableData = [res];
                this.basicOption1.data[0].value = res.successAmount;
                this.basicOption1.data[1].value = res.failureAmount;
                this.basicOption1.data[2].value = res.waitNumber;
            })
        }
    },
    watch:{
        sensitiveConditions:{ 
            handler(){
                this.GettableDtate();
            },
            deep:true,
            immediate:true
        },
    }

}
</script>
<style scoped>
.Timing-matter{
    border:1px solid #66CCFF;
    background: #E5F0FF;
    padding:10px 14px;
    border-radius: 5px;
    font-size: 12px;
    margin-bottom: 20px;
}
.Timing-matter>p{
   padding:5px 0px;
}
.OuterFrame {
    padding: 20px;  
}
.demo-form-inline .el-form-item{
    margin-right: 50px;
}
.sensitive-table{
    padding-bottom: 40px;
}
.sig-type .el-radio+.el-radio{
    margin-left:0px;
}
.sig-type .el-radio{
    display: block;
    white-space:normal;
    padding-bottom: 16px;
}
.sig-type .el-radio-group{
    padding-top:8px;
}
.tips {
    margin-top: 30px;
}
.tips p {
    margin-left: 29px;
    color: #c5c5c5;
}
</style>
<style>
.el-table--small th{
    background:#f5f5f5;
}
</style>


