<template>
    <div>
        <div class="OuterFrame fillet">
            <div class="sensitive-fun">
                <span class="sensitive-list-header" >提交无效数据列表</span>
            </div>
            <div class="sensitive-table">
                <!-- 表格和分页开始 -->
                <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" ></table-tem>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                    <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="sensitiveConditions.pagesize" :page-size='sensitiveConditions.pagesize' :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.totalRow">
                    </el-pagination>
                </el-col>
                <!-- 表格和分页结束 -->
            </div>
        </div>
    </div>        
</template>
<script>
import TableTem from '@/components/publicComponents/TableTem'
// 引入时间戳转换
import {formatDate} from '@/assets/js/date.js' 

export default {
    name:'filewebNumber',
    components:{
        TableTem,
    },
     data(){
        return {
            name:'filewebNumber',
            sensitiveConditions:{
                pageSize:10,
                currentPage:1
            },
            tableDataObj: { //列表数据
                loading2:false,
                tablecurrent:{ //分页参数
                    totalRow:0,
                },
                tableData: [],
                tableLabel:[//列表标题
                    {prop:"createTime",showName:'提交时间',fixed:false,width:"150px"},
                    {prop:"content",showName:'提交内容',fixed:false},
                    {prop:"effectiveNum",showName:'有效提交号码数',width:"110px",fixed:false},
                    {prop:"invalidNum",showName:'无效数据',width:"110px",fixed:false}
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    isDefaultExpand:false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'160',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
            },
        }
    },
     methods: {
        goBack(){//返回
            this.$router.go(-1);
        },
        GettableDtate(){ //获取列表数据
            this.tableDataObj.loading2 = true
            this.$api.post(this.API.cpus + 'consumerWebSubmitSmsData/page',this.sensitiveConditions,res=>{
                this.tableDataObj.tableData = res.records;
                this.tableDataObj.tablecurrent.totalRow = res.total; 
                this.tableDataObj.loading2 = false;
            })
        },
        handleSizeChange(size) {
            this.sensitiveConditions.pageSize = size;
        },
        handleCurrentChange: function(currentPage){
            this.sensitiveConditions.currentPage = currentPage;
        },
       
        //--------------操作框各个按钮功能开始 ----------
        handelOptionButton: function(val){
          
        },
    },
    mounted(){
        this.GettableDtate();
    },
    watch:{
        sensitiveConditions:{
            handler(){
                this.GettableDtate();
            },
            deep:true
        }
    }

}
</script>
<style scoped>
.OuterFrame {
    padding: 20px;  
   
}
.demo-form-inline .el-form-item{
    margin-right: 50px;
}
.sensitive-table{
    padding-bottom: 40px;
}
.sig-type .el-radio+.el-radio{
    margin-left:0px;
}
.sig-type .el-radio{
    display: block;
    white-space:normal;
    padding-bottom: 16px;
}
.sig-type .el-radio-group{
    padding-top:8px;
}
.tips {
    margin-top: 30px;
}
.tips p {
    margin-left: 29px;
    color: #c5c5c5;
}
</style>

