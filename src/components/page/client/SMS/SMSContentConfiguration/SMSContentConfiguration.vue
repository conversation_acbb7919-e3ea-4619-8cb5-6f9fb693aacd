<template>
    <div>
        <div class="busi-cog-title-box">
            <span v-bind:class="TemplateManagement"  @click="handleclick('TemplateManagement')">模板管理</span> |
            <span v-bind:class="SignatureManagement"  @click="handleclick('SignatureManagement')">签名管理</span> 
        </div>
        <div>
            <component v-bind:is="currentTabComponent"></component>
        </div>
    </div>
</template>

<script>
import SignatureManagement from './components/SignatureManagement.vue'
import TemplateManagement from './components/TemplateManagement.vue'
export default {
    name: "SMSContentConfiguration",
    components:{
        SignatureManagement,
        TemplateManagement
    },
    created(){
        if(this.$route.query.a == 2){
            this.handleclick('TemplateManagement');
        }else if(this.$route.query.a == 1){
            this.handleclick('SignatureManagement');
        }else{
            this.handleclick('TemplateManagement');
        }
    },
    data(){
        return{
            SignatureManagement:{
                'busi-cog-title':true,
                "busiColor":true
            },
            TemplateManagement:{
                'busi-cog-title':true,
                "busiColor":false
            },
            currentTabComponent:'TemplateManagement'
        }
    },
    methods:{
        handleclick(ele){
            this.currentTabComponent = ele;
            this.SignatureManagement.busiColor = false;
            this.TemplateManagement.busiColor = false;
            if(ele == 'SignatureManagement'){
                this.SignatureManagement.busiColor = true;
            }else if(ele == 'TemplateManagement'){
                this.TemplateManagement.busiColor = true;   
            }
        }
    }
}
</script>
<style scoped>
.busi-cog-title{
    cursor: pointer;
    display: inline-block;
    margin:0 5px;
}
.busi-cog-title-box{
    margin: 10px 0 10px 0;
}
.router-link-active{
    color:#16a589;
}
.busiColor{
    color:#16a589;
}
</style>