<template>
    <div class="user-modifi-cation bag">
        <div class="Top_title" style="padding:10px">
            <span style="display:inline-block;padding-right:10px; cursor:pointer;color:#16a589;" @click="goBack()"><i
                    class="el-icon-arrow-left"></i>返回</span>|
            <span> {{ statusOf }}</span>
            <!-- <el-button style="margin-left: 10px;" size="mini" type="primary">旧版添加模板</el-button> -->
            <el-button class="el-icon-sort" style="margin-left: 10px;" icon size="mini" type="primary"
                @click="handelNewTemp">切换新版添加模板</el-button>
        </div>
        <div class="fillet">
            <div class="sign TemDialog">

                <!-- 模板规则弹窗(内层弹窗) -->
                <el-dialog :visible.sync="dialogTemRule" append-to-body>
                    <!-- 模板规则的模板内容 -->
                    <templateRule></templateRule>
                </el-dialog>
                <!-- 我的模板库（内层弹窗） -->
                <el-dialog title="我的模板库" width="580px" class="myTems" style="padding-bottom: 40px;"
                    :visible.sync="dialogMyTems" append-to-body>
                    <el-input placeholder="模板名称、内容、ID" v-model="myTemSearch" style="width:200px;margin-bottom:10px;">
                        <i slot="suffix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                    <!-- 表格和分页开始 -->
                    <table-tem :tableDataObj="myTemTableDataObj" @handelOptionButton="handelOptionButton">
                        <!--分页-->
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page pageStyle" slot="pagination">
                            <el-pagination style="margin-bottom:-2px;" class="page_bottom"
                                @size-change="handleSizeChangeMyTem" @current-change="handleCurrentChangeMyTem"
                                :current-page="myTemlist.currentPage" :page-size="myTemlist.pageSize"
                                :page-sizes="[10, 20, 50, 100, 300]" layout="total, sizes, prev, pager, next, jumper"
                                :total="myTemPageTotal">
                            </el-pagination>
                        </el-col>
                    </table-tem>
                    <!-- 表格和分页结束 -->

                </el-dialog>
                <!-- 插入参数（内层弹窗） -->
                <el-dialog :title="dialogInsertValTitle" width="40%" :visible.sync="dialogInsertVal" append-to-body>
                    <div class="Templat-matter">
                        <p style="font-weight: bolder">tips：变量类型</p>
                        <p>1、验证码：{valid_code}， <span style="color:red">4-6位数字英文混合，支持英文大小写。</span> </p>
                        <p>2、电话号码：{ mobile_number}， <span style="color:red">1-15位纯数字。</span> </p>
                        <p>3、其他号码：{other_number}， <span style="color:red">1-32位字母+数字组合,支持中划线-。</span> </p>
                        <p>
                            4、金额：{amount}， <span style="color:red">支持数字（含英文小数点.）或数字的中文
                                （壹贰叁肆伍陆柒捌玖拾佰仟万亿 圆元整角分厘毫）。</span>
                        </p>
                        <p>
                            5、时间：{date}， <span style="color:red">符合时间的表达方式
                                也支持中文：2019年9月3日16时24分35秒。</span>
                        </p>
                        <p>6、中文汉字：{chinese}， <span style="color:red">1-32中文，支持中文圆括号（）。</span> </p>
                        <p>7、其他：{others}， <span style="color:red">1-35个中文数字字母组合,支持中文符号和空格。</span> </p>
                    </div>
                    <el-form :model="insertVal.formData" :rules="insertVal.formRule" ref="insertVal" label-width="100px"
                        style="margin-top:10px">
                        <el-form-item label="参数内容" prop="val">
                            <el-select v-model="insertVal.formData.val" clearable style="width:280px" placeholder="请选择参数内容">

                                <el-option label="验证码" v-if="form.resource == 1" value="valid_code"></el-option>
                                <el-option label="中文汉字" v-if="form.resource != 1" value="chinese"></el-option>
                                <el-option label="电话号码" v-if="form.resource != 1" value="mobile_number"></el-option>
                                <el-option label="金额" v-if="form.resource != 1" value="amount"></el-option>
                                <el-option label="时间" v-if="form.resource != 1" value="date"></el-option>
                                <el-option label="其他号码" v-if="form.resource != 1" value="other_number"></el-option>
                                <el-option label="其他" v-if="form.resource != 1" value="others"></el-option>

                                <!-- <el-option label="纯数字（d）" value="d"></el-option>
                            <el-option label="字母数字（w）" value="w"></el-option>
                            <el-option label="金额（$）" value="$"></el-option>
                            <el-option label="中文汉字（c）" value="c"></el-option>     
                            <el-option label="时间（hh:mm:ss）" value='hh:mm:ss'></el-option>
                            <el-option label="日期（MM-DD）" value='MM-DD'></el-option>  
                            <el-option label="日期（YYYY-MM-DD）" value='YYYY-MM-DD'></el-option>
                            <el-option label="日期时间(YYYY-MM-DD hh:mm:ss)" value='YYYY-MM-DD hh:mm:ss'></el-option>
                            <el-option label="日期时间（MM-DD hh:mm:ss）" value='MM-DD hh:mm:ss'></el-option> -->
                            </el-select>
                            <template>
                                <p v-if="insertVal.formData.val == 'valid_code'" style="font-size: 10px;">*支持4-6位纯数字；实际内容仅支持
                                    <span style="color:red;">数字或英文字母，</span> 在模板中仅支持一个变量；
                                </p>
                                <p v-if="insertVal.formData.val == 'chinese'" style="font-size: 10px;">
                                    *仅支持中文汉字以及中文括号（）输入，不能包含任何的特殊符号和数字，支持1-32位</p>
                                <p v-if="insertVal.formData.val == 'mobile_number'" style="font-size: 10px;">
                                    *支持1-15位纯数字；<span style="color:red;">可以传入手机号、座机号、95、400或800等手机号码。</span></p>
                                <p v-if="insertVal.formData.val == 'amount'" style="font-size: 10px;">
                                    *变量中仅支持输入数字或中文，￥$等货币符号要放在模板中。</p>
                                <p v-if="insertVal.formData.val == 'date'" style="font-size: 10px;">
                                    *需要符合时间的表达方式，例如yyyymmdd、yyyy-mm-dd、yyyy/mm/dd等。</p>
                                <p v-if="insertVal.formData.val == 'other_number'" style="font-size: 10px;">
                                    *支持1-32位字符；仅包含<span style="color:red;">大小写字母、数字</span>组合。<br />
                                    订单号等数字或编码，主要用于订单号、密码、随机秘钥等数字组成的编号。<br />
                                    不支持<span style="color:red;">手机号、QQ号、微信号、URL</span>等联系方式。
                                </p>
                                <p v-if="insertVal.formData.val == 'others'" style="font-size: 10px;">
                                    *可以设置为<span style="color:red;">公司 / 产品 / 地址 / 姓名 / 内容 / 账号 / 会员名</span> 等。<br />
                                    不允许设置为<span style="color:red;">QQ号 / 微信号（公众号）/ 手机号 / 网址 / 座机号 </span>等联系方式。<br />
                                    如果有特殊需要，请联系方式放入模板中。
                                </p>
                            </template>
                        </el-form-item>
                        <!-- <div v-if="insertVal.formData.val=='d' || insertVal.formData.val=='w' || insertVal.formData.val=='$' || insertVal.formData.val=='c' ">
                        <el-form-item label="长度限制" prop="len">
                            <el-radio-group v-model="insertVal.formData.len" >
                                <el-radio label="1">可变长度</el-radio>
                                <el-radio label="2">固定长度</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <template v-if="insertVal.formData.len=='1'">  
                            <el-form-item label="最小长度" prop="min">
                                <el-input-number v-model="insertVal.formData.min" :min="0" :max="insertVal.formData.val=='c'?4:31"></el-input-number>
                                <span>{{insertVal.formData.val!='c'?'(大小范围在0-31)':'(大小范围在0-4)'}}</span>
                            </el-form-item>
                            <el-form-item label="最大长度" prop="max">
                                <el-input-number v-model="insertVal.formData.max"  :min="1" :max="insertVal.formData.val=='c'?5:32"></el-input-number>
                                <span>{{insertVal.formData.val!='c'?'(大小范围在1-32)':'(大小范围在1-5)'}}</span>
                            </el-form-item>
                        </template>  

                        <template v-else>
                            <el-form-item label="固定长度" prop="fixed">
                                <el-input-number v-model="insertVal.formData.fixed"  :min="1" :max="insertVal.formData.val=='c'?5:32"></el-input-number>
                                <span>{{insertVal.formData.val=='c'?'大小范围在1-5':'大小范围在1-32'}}</span>
                            </el-form-item>
                        </template>
                    </div> -->
                        <el-form-item style="margin-top: 30px;">
                            <el-button type="primary" @click="insertValOk(dialogInsertValStatus)">提交</el-button>
                            <el-button @click="dialogInsertVal = false">取消</el-button>
                        </el-form-item>
                    </el-form>
                </el-dialog>
                <div>
                    <el-form :model="form" :rules="temFormRules" label-width="80px" style="padding-right:115px;"
                        ref="temForm">
                        <el-form-item label="模板类型" prop="resource" style="margin-bottom:0px;">
                            <el-radio-group v-model="form.resource" :disabled="editTemDialog_status == 0">
                                <el-radio label="1">验证码</el-radio>
                                <el-radio label="2">行业通知</el-radio>
                                <el-radio label="3">会员营销</el-radio>
                            </el-radio-group>
                            <template>
                                <p v-if="form.resource == '1'" style="font-size: 10px;">适用于注册、登录、找回密码、身份验证、号码绑定身份验证的短信模板</p>
                                <p v-if="form.resource == '2'" style="font-size: 10px;">
                                    可发告知类短信，例如：快递通知、消费通知、账单、服务通知；含有优惠折扣、促销、推广、超链接、抢购、购物劵等营销内容，会导致审核失败，请联系客服</p>
                                <p v-if="form.resource == '3'" style="font-size: 10px;">可用于会员服务，内容可带链接，建议转换成短链接发送。</p>
                            </template>
                        </el-form-item>
                        <el-form-item label="模板名称" v-if="this.statusOf == '添加模板'" prop="name">
                            <el-input v-model="form.name" :disabled="editTemDialog_status == 0"
                                placeholder="请填写模板名称，名称只做标识作用"></el-input>
                        </el-form-item>
                        <el-form-item label="选择签名" label-width="80px" prop="signId">
                            <!-- <el-select v-model="form.signId" placeholder="请选择签名" class="input-w" clearable filterable remote
                                :remote-method="remoteMethod" :loading="loadingcomp" @change="changeLocationValue">
                                <el-option v-for="(item, index) in labelName" :label="item.signature"
                                    :value="item.signature" :key="index"></el-option>
                            </el-select> -->
                            <el-select v-model="form.signId" placeholder="请选择签名" class="input-w" clearable filterable @change="changeLocationValue">
                                <el-option v-for="(item, index) in labelName" :label="item.signature"
                                    :value="item.signature" :key="index"></el-option>
                            </el-select>
                            <el-button type="primary" style="margin-left:10px;" @click="establishSig">添加签名</el-button>
                        </el-form-item>
                        <span class="tem-title">短信内容</span>
                        <!-- <span class="tem-subTitle" @click="openTemRule">模板规则</span> -->
                        <el-tabs v-model="form.temFormat" type="card" style="padding-left:80px;">

                            <!-- <el-tab-pane label="普通模板" name="2" prop='temContent' :disabled="editTemDialog_status==0">
                        输入框组件
                        
                        <edit-div  ref="divInput1" v-model="form.input1.commonInputVal" id="input1"></edit-div>
                        <div class="common-template-val" ref="input1" contenteditable="true" id="input1" v-html="input1.commonInputVal" @keyup.ctrl.86="paste" @input="changeInput('input1')" @keyup="keyInput('input1',$event)"></div>
                        <el-button type="primary" @click="shortReset()" class="template-btn-1" style="padding:9px 21px;" >短链转换</el-button>
                        <div class="tem-be-careful" style="">
                            <div class="tem-font" style="display:inline-block;"> 已输入<span> {{wordCount1}} </span>个字，最多可输入800个字</div>,
                            <div class="tem-font" style="display:inline-block;">当前模板 预计发送条数约为<span> {{numTextMsg1}} </span>条短信</div>
                            <p style="font-weight:bold;color:#666;padding-top:7px;">注意点：</p>
                            <p> 1、模板内容中<span class="f-basic">不包含</span>签名，首尾不可有【】。</p>
                            <p>2、模板内容合法，不能发送房产、发票、移民等国家法律法规严格禁止的内容。</p>
                            <p>3、如有链接，请将链接地址写于短信模板中，便于核实网站真实合法性</p>
                        </div>
                    </el-tab-pane> -->
                            <!-- <el-tab-pane label="变量模板" name="1"  prop='temContent' :disabled="editTemDialog_status==0">  -->

                            <!-- 输入框组件 -->
                            <!-- <edit-div ref="divInput2" @click.native="editVal" v-model="form.input2.commonInputVal" id="input2"></edit-div> -->
                            <edit-div ref="divInput2" v-model="form.input2.commonInputVal" id="input2"> </edit-div>
                            <!-- <el-input
                            type="textarea"
                            :rows="2"
                            placeholder="请输入内容"
                            v-model="form.input2.commonInputVal">
                        </el-input> -->
                            <!-- <div class="variable-template-val" ref="input2" contenteditable="true"  v-html="input2.commonInputVal" @input="changeInput('input2')" v-model="input2.commonInputVal"></div> -->
                            <!-- <el-button type="primary" @click="dialogMyTems=true" class="template-btn-1" >我的模板库</el-button> -->
                            <el-button type="primary" @click="shortReset()" class="template-btn-1" style="padding:9px 21px;"
                                v-if="form.resource == '3'">短链转换</el-button>
                            <el-button type="primary" style="padding:9px 21px;" v-if="form.resource != '3'"
                                @click="openValDialog" class="template-btn-2">插入参数</el-button>

                            <el-button type="primary" style="padding:9px 21px;" @click="changeRow"
                                class="template-btn-2">换行</el-button>
                            <i style="font-size:12px" class="el-icon-back"></i><span
                                style="color:#999;font-size:12px">(换行请点击此按钮)</span>
                            <el-checkbox v-if="form.resource != '1'" style="margin-left:10px" @change="handAutoSwitch"
                                v-model="form.autoSwitch">是否自动转短链</el-checkbox>
                            <el-checkbox v-if="form.resource == '3'" style="margin-left:10px" @change="handleQuite"
                                v-model="form.checked">拒收请回复R</el-checkbox>
                            <div v-if="form.autoSwitch" style="display:flex;margin:10px 0">
                                <span style="display:inline_block;width:45px;margin-top:7px">长链接</span>
                                <el-tooltip class="woring" effect="light" content="请检查长链接是否含有多余字符，确保链接能正常打开！"
                                    placement="top">
                                    <i style="color:red" class="el-icon-warning"></i>
                                </el-tooltip>
                                <el-input v-model="form.longUrl" placeholder="请输入内容"></el-input>
                            </div>

                            <!-- <el-button type="primary" style="padding:9px 21px;" v-if="form.resource=='1'" @click="insertHtmlAtCaret(1)" class="template-btn-2" >插入验证码</el-button> -->
                            <div class="tem-be-careful">
                                <div class="tem-font" style="display:inline-block;"> 已输入<span> {{ wordCount2 }}
                                    </span>个字，最多可输入800个字</div>,
                                <div class="tem-font" style="display:inline-block;">当前模板 预计发送条数约为<span> {{ numTextMsg2 }}
                                    </span>条短信</div>
                                <div class="tem-font" style="display:inline-block;color:red">（如果此内容包含变量，以实际下发条数为准）</div>
                                <!-- <p style="font-weight:bold;color:#666;padding-top:7px;">注意点：</p>
                            <p>1、模板内容<span class="f-basic">不包含</span>签名，首尾不可有【】</p>
                            <p>2、模板内容可手动填写或点击“我的模板库”在您已有的模板基础进行修改（重新创建，之前模板<span class="f-basic">不会被替换</span>）。</p>
                            <p>3、在模板中插入参数，请将鼠标光标移至填写参数处，点击“<span class="f-basic">插入参数</span>”，系统将根据您选择的参数内容进行<span class="f-basic">自动填写</span>。</p>
                            <p>4、变量模板<span class="f-basic">最少</span>需添加<span class="f-basic">1 个</span>参数，<span class="f-basic">最多</span>可添加<span class="f-basic">16 个</span>参数，两个参数不可挨在一起，中间至少有一个字符隔开 。</p>
                            <p>5、模板内容合法，不能发送房产、发票、移民等国家法律法规严格禁止的内容。</p>
                            <p>6、如有链接，请将链接地址写于短信模板中，便于核实网站真实合法性</p> -->
                            </div>
                            <!-- </el-tab-pane> -->
                        </el-tabs>
                        <template v-for="item in getcode">
                            <el-form-item :index="item.index" :key="item.id" label="已选变量" class="slectcode" v-if="item.show"
                                :prop="item.code">
                                <el-input :value='item.value' :data="item.key" :name='item.codes'
                                    @input="testchange(item.value, item.key, $event)"></el-input>
                                <el-select v-model="item.code" style="width:280px" placeholder="请选择参数内容">
                                    <el-option label="验证码" v-if="form.resource == 1" value="valid_code"></el-option>
                                    <el-option label="中文汉字" v-if="form.resource != 1" value="chinese"></el-option>
                                    <el-option label="电话号码" v-if="form.resource != 1" value="mobile_number"></el-option>
                                    <el-option label="金额" v-if="form.resource != 1" value="amount"></el-option>
                                    <el-option label="时间" v-if="form.resource != 1" value="date"></el-option>
                                    <el-option label="其他号码" v-if="form.resource != 1" value="other_number"></el-option>
                                    <el-option label="其他" v-if="form.resource != 1" value="others"></el-option>
                                </el-select>

                            </el-form-item>
                        </template>

                        <el-form-item label="申请说明" prop='desc'>
                            <template v-if="form.resource == '1'">
                                <el-input rows='4' resize='none' placeholder="用于我公司app用户注册时发送验证码" type="textarea"
                                    v-model="form.desc"></el-input>
                            </template>
                            <template v-if="form.resource == '2'">
                                <el-input rows='4' resize='none' placeholder="用于行业通知类短信发送" type="textarea"
                                    v-model="form.desc"></el-input>
                            </template>
                            <template v-if="form.resource == '3'">
                                <el-input rows='4' resize='none' placeholder="用户会员营销类短信发送" type="textarea"
                                    v-model="form.desc"></el-input>
                            </template>
                            <el-tooltip class="item" effect="light" content="为加快审核速度与模板通过率，请填写申请该模板的原因或说明" placement="top">
                                <i style="position:relative;top:-95px;left:100.5%;" class="el-icon-warning"></i>
                            </el-tooltip>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer" style="margin-left: 80px;">
                        <el-button type="primary" @click="addTemOk(statusOf, '1')">提交审核</el-button>
                        <el-button type="primary" @click="addTemOk(statusOf, '0')">保存但不提交</el-button>
                        <el-button @click="calcelTem()">取 消</el-button>
                    </span>
                </div>
                <!-- 编辑弹框 -->
                <!-- 短链转换 -->
                <el-dialog title="短链转换" :visible.sync="temshortVisible" width="520px">
                    <div class="short-box">
                        <p class="short-title" style="padding-top:10px">长网址链接</p>
                        <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
                            <el-button slot="append" type="primary" icon="el-icon-refresh"
                                @click="transformation()">转换</el-button>
                        </el-input>
                        <div class="font-sizes font-sizes1"><span style="color:red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。</div>
                        <div class="font-sizes"><span style="color:red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
                        </div>
                    </div>
                    <div class="short-box">
                        <p class="short-title" style="padding-top:20px">短网址链接</p>
                        <el-input v-model="shortConUrl" class="width-l" :disabled="true">
                            <el-button slot="append" type="primary" @click="handlePreview()"
                                icon="el-icon-share">预览</el-button>
                        </el-input>
                    </div>
                    <div style="text-align:right;margin-top:16px">
                        <el-button @click="handleCancles()">取 消</el-button>
                        <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
                    </div>

                </el-dialog>

                <div class="Signature-matter">
                    <p style="font-weight:bolder;font-size: 14px;">内容规范：</p>
                    <p>• 内容中必须带有<span style="color:#ff0000">【签名】</span>，签名内容可以是公司名称或产品名称，字数要求<span
                            style="color:#ff0000">2-20</span>个字；</p>
                    <p>• 签名只能选择审核通过的签名；如没有签名，请重新添加签名提交审核；内容首位不能添加<span style="color:#ff0000">【】</span>；</p>
                    <p>• 内容合法，不能发送<span style="color:#ff0000">房产、发票、移民、黄、赌、毒</span>犯法等国家法律法规严格禁止的内容；</p>
                    <p>• 超链接地址请写在短信内容中，便于进行核实，部分安卓系统存在超链接识别问题，需在超链接前后添加空格；</p>
                    <!-- <p>• 变量用<span style="color:#ff0000">{z数字}</span>代替，数字代表变量的最大长度，根据变量要求视情况填写数值，例：尊敬的会员{z5}先生/女士，则变量位置最多输入5位昵称，超过5位则无法发送，短信发送时根据顺序依次替换变量；</p> -->
                    <p style="font-weight:bolder;font-size: 14px;">计费规则：</p>
                    <p>• 短信字数<span style="color:#ff0000"> &lt;=70个字</span>，按照<span
                            style="color:#ff0000">70个字一条</span>短信计算，中文、英文、符号统一计算一个字符；</p>
                    <p>• 短信字数<span style="color:#ff0000">>70个字</span>，按照<span
                            style="color:#ff0000">67个字一条</span>短信计算，其中3个字占用为分条字符；</p>
                    <p style="font-weight:bolder;font-size: 14px;">审核规则：</p>
                    <p>• 工作日的9点至21点，每30分钟审核一次，休息日（含假期）的每天9-18点，每1小时审核一次；</p>
                    <p>• 如果出现批量短信发送被驳回的情况，可能为<span style="color:#ff0000">“敏感词拦截”</span>进入人工审核，请联系客服处理</p>
                </div>
            </div>

        </div>
    </div>
</template>
<script>
import TableTem from '../../../../../publicComponents/TableTem'
import templateRule from './TemplateRule'
import editDiv from './editDiv'
let _ = require('lodash')
export default {
    components: { TableTem, templateRule, editDiv },
    name: 'CreateTemplate',
    data() {
        var checkName = (rule, value, callback) => {
            if (this.statusOf != '添加模板') callback()
            var regs = /^[\S]{1,20}$/
            if (!regs.test(value)) {
                return callback(new Error("长度在 1 到 20 个字符"))
            } else {
                var reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/g
                if (!reg.test(value)) {
                    return callback(new Error("不允许输入空格等特殊符号"))
                } else {
                    this.$api.post(this.API.cpus + 'v3/consumersmstemplate/validateUserTempExist/', { temName: value }, res => {
                        if (res.data == "0") {
                            callback()
                        } else {
                            return callback(new Error("此模板已存在"))
                        }
                    })
                }
            }
        }
        return {
            rowDatas: '',//存储编辑数据
            statusOf: '',//是新增还是编辑
            labelName: [],//签名存储
            getcode: [],//存储模板变量
            temshortVisible: false, //短链弹框
            originalUrl: '',//长链接的值
            shortConUrl: '',//短连接的值
            shortCode: "", //短链的code码
            isShort: false,//是否短链转换
            name: "TemplateManagement",
            loadingcomp: false,//发送短信loading
            // mostImportant:'',
            insertStatus: false,
            divInputVal: '',//div输入框的内容
            tabelAlllist: {//------发送表格请求的对象
                param: '',
                currentPage: 1,//当前页
                pageSize: 10,//每一页条数
            },
            param: '',//搜索条件--改变监听
            searchPage: {//搜索的页数--改变监听
                currentPage: 1,
                pageSize: 10
            },
            pageTotal: 0,//总共条数
            TemDialogVisible: false,//---添加模板的弹窗
            formAdd: {
                temFormat: '',//模板格式
                temContent: '',//内容
                temType: '',//模板类型
                remark: '',//备注
                temName: ''//模板名

            },
            form: {
                params: {},
                autoSwitch: false,
                longUrl: "",
                temStatus: '',
                temId: '',
                temFormat: '1',
                signId: '',
                name: '',
                desc: '',
                checked: false,
                resource: '1',
                input1: {//-----普通模板的输入框内容
                    commonInputVal: '',//输入框内容
                    numTextMsg: '0',//短信条数
                    wordCount: '0',//字数
                },
                input2: {//-----变量模板的输入框内容
                    commonInputVal: '',//输入框内容
                    numTextMsg: '0',//短信条数
                    wordCount: '0',//字数
                },
            },
            formEmpty: {//清空对象
                temId: '',
                temFormat: '2',
                name: '',
                desc: '',
                resource: '1',
                input1: {//-----普通模板的输入框内容
                    commonInputVal: '',//输入框内容
                    numTextMsg: '0',//短信条数
                    wordCount: '0',//字数
                },
                input2: {//-----变量模板的输入框内容
                    commonInputVal: '',//输入框内容
                    numTextMsg: '0',//短信条数
                    wordCount: '0',//字数
                },
            },
            temFormRules: {//添加模板内容的验证
                signId: [
                    { required: true, message: '请选择签名', trigger: 'change' }
                ],
                name: [
                    { required: true, validator: checkName, trigger: ['change', 'blur'] },
                    // { pattern:/^[A-Za-z0-9\u4e00-\u9fa5]+$/ ,message: '不允许输入空格等特殊符号' },
                    // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
                ],
                resource: [
                    { required: true, message: '请选择模板类型', trigger: 'change' }
                ],
                temContent: [
                    { required: true, message: '请输入模板内容', trigger: 'blur' }
                ],
                desc: [{ min: 1, max: 200, message: '长度在 1 到 200个字符', trigger: 'blur' },]
            },
            //插入参数弹窗
            insertVal: {
                formData: {
                    val: '',
                    len: '1',
                    min: '',
                    max: '',
                    fixed: ''
                },
                formRule: {
                    val: [
                        { required: true, message: '请选择参数', trigger: 'change' }
                    ],
                    min: [
                        { required: true, message: '请输入最小长度', trigger: 'blur' },
                    ],
                    max: [
                        { required: true, message: '请输入最大长度', trigger: 'blur' },
                    ],
                    fixed: [
                        { required: true, message: '请输入固定长度', trigger: 'blur' },
                    ]
                }
            },
            tableDataObj: { //列表数据
                tableData: []
            },
            myTemlist: {
                //   templateFormat:1,
                tempName: "",
                temFormat: 1,
                isDisable: 1,
                status: 2,
                param: '',
                currentPage: 1,
                pageSize: 10
            },
            editVarCount: '',
            myTemSearch: '',//搜索我的模板
            myTemPageTotal: '',//总条数
            myTemTableDataObj: { //我的模板列表数据
                loading2: true,
                tableData: [],
                tableLabel: [{
                    prop: "temId",
                    showName: 'ID',
                    fixed: false
                }, {
                    prop: "temType",
                    showName: '模板类型',
                    fixed: false,
                    width: '150',
                    formatData: function (val) {
                        let type = ''
                        if (val == 1) {
                            type = '验证码'
                        } else if (val == 2) {
                            type = '通知'
                        } else if (val == 3) {
                            type = '营销推广'
                        }
                        return type
                    }
                }, {
                    prop: "temName",
                    showName: '模板名称',
                    fixed: false
                }
                ],
                // 表头--折叠的
                tableLabelExpand: [{
                    prop: "temContent",
                    showName: '模板内容'
                }
                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand: true,//是否是折叠的
                    isDefaultExpand: true,//默认打开折叠
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                },
                tableOptions: [
                    {
                        optionName: '选取',
                        type: '',
                        size: 'mini',
                        optionMethod: 'getIt',
                        icon: 'el-icon-success'
                    },
                ]
            },
            dialogTemRule: false,
            dialogMyTems: false,//我的模板库弹窗
            dialogInsertVal: false,//插入参数弹窗
            dialogInsertValStatus: '1',//参数弹窗的状态---1：新增   0：编辑
            insertDom: '',//添加的dom
            getCurIndex: '',//光标位置
            editTemDialog_status: 1,//模板是否是新增---1为新增，0为编辑

            varCount: 0,
        }

    },

    created() {
        if (this.$route.query.i == '1') {
            this.statusOf = '添加模板';
        } else {
            this.statusOf = '编辑模板';
            this.getTem();
        }
    },
    methods: {
        //修改模板类型时，修改的属性
        // changeItem(value,name){
        //     let divarr = document.getElementsByClassName("edit-div");
        //     let dataspan = divarr[0].getElementsByTagName('span');
        //     for (let i = 0; i < dataspan.length; i++) {
        //         if (dataspan[i].getAttribute('constvar') == name) {
        //             dataspan[i].setAttribute('data',value);
        //         }                                
        //     }
        // },
        //变量里边的值
        testchange(value, dataIndex, Event) {
            let regex = /\{(.+?)\}/g;
            let options = this.form.input2.commonInputVal.match(regex);
            let b = this.getcode;
            let valid_code = '', chinese = '', mobile_number = '', amount = '', date = '', other_number = '', others = '';

            valid_code = document.getElementsByName('valid_code');
            chinese = document.getElementsByName('chinese');
            mobile_number = document.getElementsByName("mobile_number");
            amount = document.getElementsByName("amount");
            date = document.getElementsByName("date");
            other_number = document.getElementsByName("other_number");
            others = document.getElementsByName("others");

            let divarr = document.getElementsByClassName("edit-div");
            let dataspan = divarr[0].getElementsByTagName('span');

            for (let k = 0; k < dataspan.length; k++) {
                // if(value.length>0){
                if (this.form.resource == 1) {
                    if (valid_code[k].name == dataspan[k].getAttribute('data')) {
                        let a = "{" + valid_code[k].value + "}";
                        b[k].value = valid_code[k].value;
                        // if (this.statusOf == '编辑模板') {
                        //     this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[k],a);
                        // } else {
                        if (options) {
                            if (options.length != this.getcode.length) {
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                            } else {
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">' + options[k], 'constvar="' + dataIndex + '" class="text-adaNum">' + a);
                            }
                        } else {
                            this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                        }
                        // }                         
                    }
                } else if (this.form.resource == 2) {
                    for (let i = 0; i < chinese.length; i++) {
                        if (chinese[i] != null && chinese[i].name == dataspan[k].getAttribute('data') &&
                            chinese[i].getAttribute('data') == dataspan[k].getAttribute('constvar')) {
                            let a = "{" + chinese[i].value + "}";
                            b[k].value = chinese[i].value;
                            // if (this.statusOf == '编辑模板') {
                            //     this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[k],a);
                            // } else {
                            if (options) {
                                if (options.length != this.getcode.length) {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                                } else {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">' + options[k], 'constvar="' + dataIndex + '" class="text-adaNum">' + a);
                                }
                            } else {
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                            }
                            // } 
                        }
                    }
                    for (let i = 0; i < mobile_number.length; i++) {
                        if (mobile_number[i] != null && mobile_number[i].name == dataspan[k].getAttribute('data') &&
                            mobile_number[i].getAttribute('data') == dataspan[k].getAttribute('constvar')) {
                            let a = "{" + mobile_number[i].value + "}";
                            b[k].value = mobile_number[i].value;
                            // if (this.statusOf == '编辑模板') {
                            //     this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[k],a);
                            // } else {
                            if (options) {
                                if (options.length != this.getcode.length) {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                                } else {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">' + options[k], 'constvar="' + dataIndex + '" class="text-adaNum">' + a);
                                }
                            } else {
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                            }
                            // }     
                        }
                    }
                    for (let i = 0; i < amount.length; i++) {
                        if (amount[i] != null && amount[i].name == dataspan[k].getAttribute('data') &&
                            amount[i].getAttribute('data') == dataspan[k].getAttribute('constvar')) {
                            let a = "{" + amount[i].value + "}";
                            b[k].value = amount[i].value;
                            // if (this.statusOf == '编辑模板') {
                            //     this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[k],a);
                            // } else {
                            if (options) {
                                if (options.length != this.getcode.length) {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                                } else {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">' + options[k], 'constvar="' + dataIndex + '" class="text-adaNum">' + a);
                                }
                            } else {
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                            }
                            // }
                        }
                    }


                    for (let i = 0; i < date.length; i++) {
                        if (date[i] != null && date[i].name == dataspan[k].getAttribute('data') &&
                            date[i].getAttribute('data') == dataspan[k].getAttribute('constvar')) {
                            let a = "{" + date[i].value + "}";
                            b[k].value = date[i].value;
                            //    if (this.statusOf == '编辑模板') {
                            //         this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[k],a);
                            //     } else {
                            if (options) {
                                if (options.length != this.getcode.length) {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                                } else {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">' + options[k], 'constvar="' + dataIndex + '" class="text-adaNum">' + a);
                                }
                            } else {
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                            }
                            // } 
                        }
                    }

                    for (let i = 0; i < other_number.length; i++) {
                        if (other_number[i] != null && other_number[i].name == dataspan[k].getAttribute('data') &&
                            other_number[i].getAttribute('data') == dataspan[k].getAttribute('constvar')) {
                            let a = "{" + other_number[i].value + "}";
                            b[k].value = other_number[i].value;
                            // if (this.statusOf == '编辑模板') {
                            //     this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[k],a);
                            // } else {
                            if (options) {
                                if (options.length != this.getcode.length) {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                                } else {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">' + options[k], 'constvar="' + dataIndex + '" class="text-adaNum">' + a);
                                }
                            } else {
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                            }
                            // } 
                        }
                    }

                    for (let i = 0; i < others.length; i++) {
                        if (others[i] != null && others[i].name == dataspan[k].getAttribute('data') &&
                            others[i].getAttribute('data') == dataspan[k].getAttribute('constvar')) {
                            let a = "{" + others[i].value + "}";
                            b[k].value = others[i].value;
                            // if (this.statusOf == '编辑模板') {
                            //     this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[k],a);
                            // } else {
                            if (options) {
                                if (options.length != this.getcode.length) {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                                } else {
                                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">' + options[k], 'constvar="' + dataIndex + '" class="text-adaNum">' + a);
                                }
                            } else {
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace('constvar="' + dataIndex + '" class="text-adaNum">{}', 'constvar="' + dataIndex + '" class="text-adaNum">{' + Event + "}");
                            }
                            // } 
                        }
                    }


                    // }

                }
            }

            // for (let i = 0; i < b.length; i++) {  
            //     if(value.length>0){               
            //         if (value != options[i].slice(1,options[i].length-1)) {
            //             let a = "{"+value+"}";
            //             b[i].value = value;
            //             this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[i],a);
            //         }
            //     }
            // }
            this.getcode = b;
        },
        // remoteMethod(query) {
        //     if (query !== '') {
        //         this.loadingcomp = true
        //         this.getpassLabels(query)
        //         this.loadingcomp = false
        //     } else {
        //         this.compNamelist = []
        //         this.getpassLabels()
        //     }
        // },
        //获取全部用户签名
        getpassLabels(query) {
            this.$api.post(this.API.cpus + 'signature/signatureList', {
                // signature: query || '',
                auditStatus: '2',
                currentPage: 1,
                pageSize: 200,
            }, res => {
                this.labelName = res.records;
            })
        },
        changeLocationValue(val) {
            // if (!val) {
            //     this.getpassLabels()
            // }
        },
        getTem() {
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/page', {
                param: this.$route.query.param,
                currentPage: 1,
                pageSize: 200
            }, res => {
                //编辑框里赋值
                let rowData = res.records[0];
                this.rowDatas = res.records[0];
                this.form.name = rowData.temName;
                this.form.temFormat = rowData.temFormat;
                this.form.temStatus = rowData.temStatus;
                this.form.temId = rowData.temId
                this.form.params = rowData.text;
                if (rowData.autoSwitch) {
                    this.form.autoSwitch = rowData.autoSwitch
                }
                if (rowData.longUrl) {
                    this.form.longUrl = rowData.longUrl
                }
                this.form.params = JSON.parse(this.form.params)
                if (rowData.temFormat == 2) {//全文模板
                    this.form.input2.commonInputVal = rowData.temContent.replace(/ /g, "&nbsp;").replace(/\n/g, '<br>')
                    let regexCenter = /\【(.+?)\】/g;
                    let sings = this.form.input2.commonInputVal.match(regexCenter);
                    this.form.signId = sings.join(',');
                    setTimeout(() => {
                        this.$refs.divInput2.$refs.input2.innerHTML = rowData.temContent.replace(/ /g, "&nbsp;").replace(/\n/g, '<br>')
                    }, 1);
                } else if (rowData.temFormat == 1) {//变量模板
                    if (rowData.temContent) {
                        let regexCenter = /\【(.+?)\】/g;
                        this.form.input2.commonInputVal = rowData.temContent;//值更改 --方便计数
                        let regex = /\{(.+?)\}/g;
                        let sings = this.form.input2.commonInputVal.match(regexCenter);
                        let options = this.form.input2.commonInputVal.match(regex);
                        let insertVal = '';
                        for (let i = 0; i < options.length; i++) {
                            const e = options[i].substring(1, options[i].length - 1);
                            const ee = this.form.params[e];
                            let s = new Date().getTime()
                            insertVal += '<span style="display:-moz-inline-box;display:inline-block;"  contenteditable="false" data="' + ee + '" constvar="' + (s + i + 1) + '" class="text-adaNum">{' + e + '}</span>|'
                            this.getcode.push({ 'codes': ee, 'code': ee, 'show': true, 'value': e, 'key': s + i + 1 });
                        }
                        if (options.length == 1) {
                            let str = insertVal.split("|")[0];
                            this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options, str).replace(/\n/g, '<br>');
                        } else {
                            for (let j = 0; j < options.length; j++) {
                                let str = insertVal.split("|")[j];
                                this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(options[j], str).replace(/\n/g, '<br>');
                            }
                        }
                        this.form.signId = sings.join(',');

                        // setTimeout(() => {
                        //     this.$refs.divInput2.$refs.input2.innerHTML = this.form.input2.commonInputVal.replace(/ /g,"&nbsp;").replace(/&nbsp;contenteditable/g," contenteditable").replace(/&nbsp;constvar/g," constvar").replace(/&nbsp;data/g," data").replace(/&nbsp;class/g," class")
                        // }, 1);
                    } else {
                        this.form.input2.commonInputVal = rowData.temContent.replace(/\n/g, '<br>')
                    }
                }
                this.form.resource = rowData.temType + '';
                this.form.desc = rowData.remark;
                this.form.temId = rowData.temId;
            })
        },
        handelNewTemp() {
            const { i, param } = this.$route.query
            if (i) {
                this.$router.push({ path: '/NewCreateTemplate', query: { i: "1" } });
            } else {
                this.$router.push({ path: '/NewCreateTemplate', query: { param } });
            }

        },
        establishSig() {//创建签名功能
            this.$router.push({ path: '/CreateSign', query: { i: "1" } });
        },
        //返回
        goBack() {
            this.$router.push({ path: 'TemplateManagement' }, () => { }, (e) => {
                console.log(e, 'e');
            })
        },
        calcelTem() {
            this.$router.push({ path: 'TemplateManagement' })
            // this.$refs.divInput1.$refs.input1.innerHTML=""
            // this.$refs.divInput2.$refs.input2.innerHTML=""
            // this.TemDialogVisible = false
        },


        /**-----------------列表展示我的模板------------- */
        getMyTemTableData() {//获取列表数据
            this.myTemTableDataObj.loading2 = true
            let formDatas = this.myTemlist;
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate', formDatas, res => {
                this.myTemTableDataObj.tableData = res.records;
                this.myTemTableDataObj.loading2 = false;
                this.myTemPageTotal = res.total;
            })
        },
        handleSizeChangeMyTem(size) { //分页每一页的有几条
            this.myTemlist.pageSize = size;
            this.getMyTemTableData();
        },
        handleCurrentChangeMyTem: function (currentPage) {//分页的第几页
            this.myTemlist.currentPage = currentPage;
            this.getMyTemTableData();
        },
        openValDialog() {//点击“插入参数”按钮，打开弹窗--添加参数的弹窗
            if (this.insertStatus) {
                this.$message({
                    message: '"两个参数不可挨在一起，中间至少有一个字符隔开。"',
                    type: 'warning'
                })
                return false
            }
            else {
                this.dialogInsertVal = true;
                this.dialogInsertValStatus = 1;
                this.$refs.divInput2.$refs.input2.focus();//自动聚焦
                let selection = getSelection();
                // 设置最后光标对象
                this.getCurIndex = selection.getRangeAt(0);
            }
        },
        insertValOk(val) {//提交参数--  val=0为编辑   val=1为新增
            this.$refs.insertVal.validate((valid) => {
                if (valid) {
                    let showVal = '';//显示在输入框中的参数
                    if (this.insertVal.formData.val == '$' || this.insertVal.formData.val == 'c' || this.insertVal.formData.val == 'd' || this.insertVal.formData.val == 'w') {
                        if (this.insertVal.formData.len == 1) {
                            if (this.insertVal.formData.min > this.insertVal.formData.max) {
                                this.$message({
                                    message: '"最小长度"不能大于"最大长度"',
                                    type: 'warning'
                                });
                                return false;
                            } else if (this.insertVal.formData.min == this.insertVal.formData.max) {
                                this.$message({
                                    message: '"最小长度"不能等于"最大长度"',
                                    type: 'warning'
                                });
                                return false;
                            } else {
                                showVal = this.insertVal.formData.val + this.insertVal.formData.min + '-' + this.insertVal.formData.max
                            }
                            // if(this.insertVal.formData.val=="$" && this.insertVal.formData.min <=4){
                            //     this.$message({
                            //         message: '"金额最小长度"不能小于4',
                            //         type: 'warning' 
                            //     })
                            //     return false
                            // }
                        } else {
                            showVal = this.insertVal.formData.val + this.insertVal.formData.fixed
                        }
                    } else {
                        showVal = this.insertVal.formData.val
                    }
                    let countNum = [];
                    let addVarCount = 1;
                    if (val == 1) {//新增--参数
                        let regx = /<[^>]+>([^<]+)<\/[^>]+>/g;
                        let replace = this.form.input2.commonInputVal.replace(/<div>/g, '').replace(/<\/div>/g, '')
                        console.log(this.form.input2.commonInputVal, 'this.form.input2.commonInputVal');
                        console.log(replace, 'replace');
                        let result1 = replace.match(regx)//取出含标签的内容
                        console.log(result1, 'result1');
                        if (this.form.input2.commonInputVal.indexOf(showVal) != -1 && this.form.resource == '1') {
                            this.$message({
                                message: '"添加的参数只能添加一个，不能重复添加！"',
                                type: 'warning'
                            });
                            return false
                        }
                        // if (this.form.input2.commonInputVal.indexOf(showVal) != -1 && this.form.resource == '2') {
                        //     this.$message({
                        //         message: '"相同的参数只能添加一个，不能重复添加！"',
                        //         type: 'warning'
                        //     });
                        //     return false
                        // }
                        //判断参数个数--不能超过32



                        if (result1) {
                            if (result1.length >= 16) {
                                this.$message({
                                    message: '"添加的参数个数不能超过16个"',
                                    type: 'warning'
                                });
                                return false;
                            }
                            // result1.forEach((item, index)=>
                            //     countNum.push(item.substr(item.indexOf('|')-1,1))//获取所有的var变量后的数字
                            // )
                            for (let i = 1; i < result1.length + 1; i++) {
                                countNum.push(i)
                            }
                        }
                        // if(countNum.length>0){
                        // let maxNum=Math.max.apply(Math,countNum);
                        // addVarCount=maxNum+1;
                        // }else{
                        //     addVarCount=1
                        // }
                        // let varCount=addVarCount;

                        // let divarr =  document.getElementsByClassName("edit-div");
                        // let dataspan = divarr[0].getElementsByTagName('span');

                        this.varCount = new Date().getTime()
                        let insertVal = '<span style="display:-moz-inline-box;display:inline-block;"  contenteditable="false" data="' + showVal + '" constvar="' + this.varCount + '"  class="text-adaNum">{' + showVal + '}</span>';

                        let selection = window.getSelection ? window.getSelection() : document.selection;

                        if (this.getCurIndex) {
                            // 存在最后光标对象，选定对象清除所有光标并添加最后光标还原之前的状态
                            selection.removeAllRanges();
                            selection.addRange(this.getCurIndex);
                        }
                        this.insertHtmlAtCaret(insertVal)

                    } else {//编辑--参数
                        if (this.insertDom) {
                            this.insertDom.setAttribute("data", showVal);
                            this.insertDom.innerHTML = '{var' + this.editVarCount + '|' + showVal + '}'
                        }
                    }

                    var a = this.$refs.divInput2.$refs.input2.innerHTML.split('|')
                    var str = ''
                    for (var i = 1; i < a.length + 1; i++) {
                        str += a[i - 1].replace(/var\d[0-9]{0,1}/g, 'var' + i + '') + "|"
                    }
                    this.form.input2.commonInputVal = str.slice(0, str.length - 1);//值更改 --方便计数            
                    this.dialogInsertVal = false;//关闭弹窗 
                    let reg = /<\/span><span/g;
                    if (!reg.test(this.form.input2.commonInputVal)) {
                        // console.log(this.$refs.divInput2.$refs.input2.getElementsByTagName("span"))                        
                        // this.getcode.push({'codes':showVal,'code':showVal,'show':true,'value':showVal,"key":this.varCount});
                        let divtext = this.$refs.divInput2.$refs.input2
                        let GetNum = this.getCursortPosition1(divtext)
                        if (GetNum == divtext.innerText.length) {
                            this.getcode.push({ 'codes': showVal, 'code': showVal, 'show': true, 'value': showVal, "key": this.varCount });
                        } else {
                            this.getcode.splice(divtext.innerText.slice(0, GetNum).split("{").length - 2, 0, { 'codes': showVal, 'code': showVal, 'show': true, 'value': showVal, "key": this.varCount })
                        }
                    }
                    // console.log("-------------")
                    // console.log(this.getCursortPosition1(this.$refs.divInput2.$refs.input2))
                    // console.log(this.$refs.divInput2.$refs.input2.innerText.length)
                    // let divtext=this.$refs.divInput2.$refs.input2
                    // let GetNum=this.getCursortPosition1(divtext)
                    // if(GetNum==divtext.innerText.length){
                    //     this.getcode.push({'codes':showVal,'code':showVal,'show':true,'value':showVal,"key":this.varCount});
                    // }else{
                    //     console.log(divtext.innerText)
                    //     console.log(divtext.innerText.slice(0,GetNum))
                    //     console.log(divtext.innerText.slice(0,GetNum).split("{").length-2)
                    //     this.getcode.splice(divtext.innerText.slice(0,GetNum).split("{").length-2,0,{'codes':showVal,'code':showVal,'show':true,'value':showVal,"key":this.varCount})
                    // }
                    // console.log(this.$refs.divInput2.$refs.input2.getElementsByTagName("span"))
                    // console.log(this.getcode)
                } else {
                    return false;
                }
            })
        },
        //输入框换行
        changeRow() {
            let insertVal = '<br>'
            this.insertHtmlAtCaret(insertVal)
            // console.log(this.$refs.divInput2.$refs.input2.innerHTML,'this.$refs.divInput2');
            this.form.input2.commonInputVal = this.$refs.divInput2.$refs.input2.innerHTML
            this.countNum1(this.form.input2.commonInputVal)
        },
        //判断模板内容里面前后是否有签名
        checkSignature(value) {
            let ret = false;
            let beg = value.indexOf('【');
            let end = value.indexOf('】');
            let lastbeg = value.lastIndexOf('【');
            let lastend = value.lastIndexOf('】');
            let valLength = value.length;
            //前有签名
            if (beg > - 1 && end > -1 && end > beg && beg == 0) {
                ret = false;
            } else if (lastbeg > -1 && lastend > -1 && lastend > lastbeg && lastend == valLength - 1) {
                //后有签名
                ret = false;
            } else {
                ret = true;
            }
            return ret;
        },
        handAutoSwitch(val) {
            var re = new RegExp('<[^<>]+>', 'g');
            let content = this.form.input2.commonInputVal.replace(/<div>/g, '').replace(/<\/div>/g, '').replace(/&amp;/g, "&").replace(/&gt;/g, "").replace(/&lt;/g, '').replace(/undefined/g, "").replace(/<br>/g, '\n').replace(re, "").replace(/&nbsp;/g, ' ');
            if (val) {
                this.$api.post(this.API.cpus + "v3/consumersmstemplate/longUrl", {
                    temContent: content
                }, (res) => {
                    this.form.longUrl = res.data
                    // this.dynamicTags = res.data;
                });
            } else {
                this.form.longUrl = ''
            }
        },
        handleQuite(val) {
            if (val) {
                this.form.input2.commonInputVal = this.form.input2.commonInputVal + "，拒收请回复R";
            } else {
                var reg = new RegExp("，拒收请回复R");
                var str = this.form.input2.commonInputVal;
                str = str.replace(reg, "");
                this.form.input2.commonInputVal = str;
            }
        },
        //添加编辑模板--确定
        addTemOk(val, val2) {
            // console.log(val,'val');
            // console.log(val2,'val');
            this.$refs.temForm.validate((valid) => {
                if (valid) {
                    let aa = false;
                    var reg = new RegExp("拒收请回复R");
                    const matches = this.form.input2.commonInputVal.match(/【.*?】/g);;
                    if(this.form.signId !== matches[0]){
                        this.$message({
                            message: '选择的签名与模板内容签名不匹配！',
                            type: 'warning'
                        });
                        return
                    }
                    if (this.form.resource == '3' && !reg.test(this.form.input2.commonInputVal)) {
                        this.$message({
                            message: '请勾选拒收请回复为R!',
                            type: 'warning'
                        });
                        return
                    }
                    let addForm = {};//接收传参的对象
                    if (this.form.temFormat == '2') {//全文模板
                        //内容不可为空
                        if (this.form.input2.commonInputVal.trim().length != 0) {
                            // if(this.checkSignature(this.form.input2.commonInputVal)){
                            aa = true;
                            addForm.initialContent = this.form.input2.commonInputVal.replace(/<div>/g, '').replace(/<\/div>/g, '').replace(/&amp;/g, "&").replace(/&gt;/g, "").replace(/&lt;/g, '').replace(/undefined/g, "").replace(/<br>/g, '\n');

                            // addForm.initialContent = addForm.initialContent.replace(/<br>/g,'\n')
                            // console.log(addForm.initialContent,'addForm.initialContent');
                            //    }else{
                            //         this.$message({
                            //             message: '模板内容必须有签名！',
                            //             type: 'warning'
                            //         });
                            //     }
                        } else {
                            this.$message({
                                message: '全文模板内容不可为空！',
                                type: 'warning'
                            });
                        }
                    } else if (this.form.temFormat == '1') {//变量模板
                        //判断是否有签名
                        this.form.input2.commonInputVal = this.$refs.divInput2.$refs.input2.innerHTML;//值更改 --方便计数
                        // if(this.checkSignature(this.form.input2.commonInputVal)){
                        aa = true
                        addForm.initialContent = this.$refs.divInput2.$refs.input2.innerHTML.replace(/<div>/g, '').replace(/<\/div>/g, '').replace(/&amp;/g, '&').replace(/&gt;/g, "").replace(/&lt;/g, '').replace(/undefined/g, "").replace(/<br>/g, '\n');
                        // console.log(addForm.initialContent,'addForm.initialContent');
                        let regx = /<[^>]+>([^<]+)<\/[^>]+>/g;
                        let result1 = [];
                        result1 = addForm.initialContent.match(regx)//取出含标签的内容
                        //限制参数个数
                        if (!result1) {
                            if (this.form.resource == 1) {
                                this.$message({
                                    message: '"验证码至少添加一个参数!"',
                                    type: 'warning'
                                });
                                return false;
                            }
                            // if (this.form.resource == 2) {
                            //     this.$message({
                            //         message: '"变量模板至少添加一个参数!"',
                            //         type: 'warning'
                            //     });
                            //     return false;
                            // }
                        }

                        if (result1 != null) {

                            if (result1.length > 16) {
                                this.$message({
                                    message: '"变量模板参数不能超过16个"',
                                    type: 'warning'
                                });
                                return false;
                            }

                        }
                        if (result1 != null) {
                            let result = [];
                            result1.forEach((item, index) => result.push(item.match(/>([^<]+)</)[1].replace(/var[1-9]\d?[|]/ig, '')));//数组形式取出标签里面的内容
                            addForm.text = result.join(',')//数组转换成字符串
                            result = result.sort();
                            for (let i = 0; i < result.length; i++) {
                                if (result[i] == result[i - 1]) {
                                    this.$message({
                                        message: '"已选变量名称不能重复"',
                                        type: 'warning'
                                    });
                                    return false;
                                }
                            }
                        }
                        // }else{
                        //     this.$message({
                        //         message: '模板内容必须有签名！',
                        //         type: 'warning'
                        //     });
                        // }

                    }
                    var re = new RegExp('<[^<>]+>', 'g');
                    // console.log(this.form.input2.commonInputVal,'lll');
                    var tes = addForm.initialContent.replace(re, "").replace(/&nbsp;/g, ' ').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&').replace(/<br>/g, '\n');
                    // console.log(tes,'tes');
                    addForm.temContent = tes;
                    addForm.temFormat = this.form.temFormat;
                    addForm.temName = this.form.name;
                    addForm.temType = this.form.resource;
                    addForm.remark = this.form.desc;
                    addForm.longUrl = this.form.longUrl
                    addForm.autoSwitch = this.form.autoSwitch
                    //判断是否短链转换
                    if (this.isShort) {
                        addForm.shortCode = this.shortCode;
                    } else {
                        addForm.shortCode = '';
                    }
                    if (val == '添加模板' && aa == true) {//新增模板 
                        //发送请求           
                        var a = this.$refs.divInput2.$refs.input2.innerHTML.split('|')
                        var str = ''
                        for (var i = 1; i < a.length + 1; i++) {
                            str += a[i - 1].replace(/var\d[0-9]{0,1}/g, 'var' + i + '') + "|"
                        }
                        if (addForm.temFormat == '1') {
                            addForm.initialContent = str.slice(0, str.length - 1)
                        }
                        let regex = /\【(.+?)\】/g;
                        let regexs = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/;
                        let options = addForm.temContent.match(regex)

                        if (addForm.temContent.substring(0, 1) != '【' && addForm.temContent.substring(addForm.temContent.length - 1, addForm.temContent.length) != '】') {
                            this.$message({
                                type: "warning",
                                message: '模板内容前后必须有签名！'
                            })
                            return false
                        }
                        if (options != null && addForm.temContent.indexOf('【') != -1 && addForm.temContent.indexOf('】') != -1) {
                            for (let i = 0; i < options.length; i++) {
                                const e = options[i].substring(1, options[i].length - 1);
                                if (e.length > 20 || e.length < 2 || regexs.test(e) == false) {
                                    this.$message({
                                        type: "warning",
                                        message: '签名中间字数必须2-20位,并且不能使用空格和特殊符号“ - + = * & % # @ ~”等'
                                    })
                                    return false
                                }
                            }
                        }

                        if (addForm.initialContent.indexOf("</span><span") == -1) {
                            // var ch = "&nbsp;";
                            // var regch = "/"+ch+"/g";
                            let aaa = this.getcode;
                            let bbb = {}
                            for (let i = 0; i < aaa.length; i++) {
                                bbb[aaa[i].value] = aaa[i].code;
                            }
                            addForm.params = bbb;
                            addForm.submit = val2;
                            addForm.initialContent = addForm.initialContent.replace(/<div>/g, '').replace(/<\/div>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, "&").replace(/&gt;/g, "").replace(/&lt;/g, '').replace(/undefined/g, "").replace(/<br>/g, '\n')
                            this.$confirms.confirmation('post', '确认新增模板？', this.API.cpus + 'v3/consumersmstemplate/template/add', addForm, res => {
                                if (res.code == 200) {
                                    this.goBack();
                                } else {
                                    this.$message({
                                        message: '"操作失败"',
                                        type: 'error'
                                    });
                                    return false;
                                }
                                this.$refs.divInput2.$refs.input2.innerHTML = '';
                            });



                        } else {
                            this.$message({
                                message: '"两个参数不可挨在一起，中间至少有一个字符隔开。"',
                                type: 'warning'
                            })
                        }
                    } else if (val == '编辑模板') {//编辑模板
                        //发送请求
                        addForm.temId = this.form.temId;
                        //判断是否短链转换
                        if (this.isShort) {
                            addForm.shortCode = this.shortCode;
                        } else {
                            addForm.shortCode = '';
                        }

                        let regex = /\【(.+?)\】/g;
                        let regexs = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/;
                        let options = addForm.temContent.match(regex)
                        if (addForm.temContent.substring(0, 1) != '【' && addForm.temContent.substring(addForm.temContent.length - 1, addForm.temContent.length) != '】') {
                            this.$message({
                                type: "warning",
                                message: '模板内容前后必须有签名！'
                            })
                            return false
                        }
                        if (options != null && addForm.temContent.indexOf('【') != -1 && addForm.temContent.indexOf('】') != -1) {
                            for (let i = 0; i < options.length; i++) {
                                const e = options[i].substring(1, options[i].length - 1);
                                if (e.length > 20 || e.length < 2 || regexs.test(e) == false) {
                                    this.$message({
                                        type: "warning",
                                        message: '签名中间字数必须2-20位,并且不能使用空格和特殊符号“ - + = * & % # @ ~”等'
                                    })
                                    return false
                                }
                            }

                        } else {
                            this.$message({
                                type: "warning",
                                message: '模板内容必须有签名！'
                            })
                            return false
                        }

                        if (addForm.initialContent.indexOf("</span><span") == -1) {
                            // if(this.form.temStatus=="3")addForm.temStatus=1;
                            addForm.temStatus = this.form.temStatus;
                            if (addForm.temFormat == "1") {
                                var a = this.$refs.divInput2.$refs.input2.innerHTML.split('|')
                                var str = ''
                                for (var i = 1; i < a.length + 1; i++) {
                                    str += a[i - 1].replace(/var\d[0-9]{0,1}/g, 'var' + i + '') + "|"
                                }
                                addForm.initialContent = str.slice(0, str.length - 1)
                            }
                            let aaa = this.getcode;
                            let bbb = {}
                            for (let i = 0; i < aaa.length; i++) {
                                bbb[aaa[i].value] = aaa[i].code;
                            }
                            addForm.params = bbb;
                            addForm.submit = val2;
                            addForm.initialContent = addForm.initialContent.replace(/<div>/g, '').replace(/<\/div>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, "&").replace(/&gt;/g, "").replace(/&lt;/g, '').replace(/undefined/g, "").replace(/<br>/g, '\n')
                            this.$confirms.confirmation('post', '确认编辑模板？', this.API.cpus + 'v3/consumersmstemplate/template/update', addForm, res => {
                                if (res.code == 200) {
                                    this.goBack();
                                } else {
                                    this.$message({
                                        message: '"操作失败"',
                                        type: 'error'
                                    });
                                    return false;
                                }
                                this.$refs.divInput2.$refs.input2.innerHTML = ''
                            });
                        }
                        else {
                            this.$message({
                                message: '"两个参数不可挨在一起，中间至少有一个字符隔开。"',
                                type: 'warning'
                            })
                        }
                    }
                } else {
                    return false;
                }
            })
        },
        //编辑参数弹窗--打开
        editVal(e) {
            this.dialogInsertValStatus = 0//弹窗是编辑
            let dom = e.target;
            let val = dom.getAttribute('data');
            this.editVarCount = dom.getAttribute('constvar');
            if (val) {
                this.insertDom = dom;
                let valSame = val
                let regVar1 = /\-/g;//可变长度--
                let regVar2 = /\-/g;//固定长度--
                let regVar3 = /(\w)\1{1,}/g;
                let regFirstVar = /[d|w|$|c]/g;//参数首字母匹配
                let regIsNum = /[^0-9]/g;//验证数字
                let firstVar;//变量的首字母
                let fixLen;//固定长度的数字
                let variableLen1;//可变长度1
                let variableLen2;//可变长度2
                let formatDate;//日期格式
                if (!regVar3.test(val)) {
                    if (!regVar2.test(valSame)) {//固定长度
                        firstVar = valSame.match(regFirstVar)[0];//首字母
                        fixLen = valSame.replace(/[^0-9]/ig, "");//固定位数的数字
                        this.insertVal.formData.val = firstVar;
                        this.insertVal.formData.len = '2';//固定长度-单选
                        this.insertVal.formData.fixed = fixLen;//固定的长度
                        this.dialogInsertVal = true;//插入参数弹窗--打开
                        this.dialogInsertValStatus = 0//弹窗是编辑
                    } else if (regVar1.test(val)) {//可变长度
                        firstVar = val.match(regFirstVar)[0];//首字母
                        variableLen1 = val.split('-')[0].replace(/[^0-9]/ig, ""); //可变长度1
                        variableLen2 = val.split('-')[1].replace(/[^0-9]/ig, ""); //可变长度2
                        this.insertVal.formData.val = firstVar;
                        this.insertVal.formData.len = '1';//可变长度-单选
                        this.insertVal.formData.min = variableLen1;//可变长度的min
                        this.insertVal.formData.max = variableLen2;//可变长度的max
                        this.dialogInsertVal = true;//插入参数弹窗--打开
                        this.dialogInsertValStatus = 0//弹窗是编辑
                    }
                }
                else {
                    this.insertVal.formData.val = val;
                }

                this.dialogInsertVal = true;//插入参数弹窗--打开
                this.dialogInsertValStatus = 0//弹窗是编辑
            }
        },
        /**---------插入数据----------- */
        insertHtmlAtCaret(html) {
            let sel, range;
            if (window.getSelection) {
                // IE9 and non-IE
                sel = window.getSelection();
                if (sel.getRangeAt && sel.rangeCount) {
                    range = sel.getRangeAt(0);
                    range.deleteContents();
                    // Range.createContextualFragment() would be useful here but is
                    // non-standard and not supported in all browsers (IE9, for one)
                    let el = document.createElement("div");
                    el.innerHTML = html;
                    let frag = document.createDocumentFragment(), node, lastNode;
                    while ((node = el.firstChild)) {
                        lastNode = frag.appendChild(node);
                    }
                    range.insertNode(frag);//设置选择范围的内容为插入的内容
                    // Preserve the selection
                    if (lastNode) {
                        range = range.cloneRange();
                        range.setEndAfter(lastNode); //把编辑光标放到我们插入内容之后
                        range.setStartAfter(lastNode);//设置光标位置为插入内容的末尾
                        range.collapse(true);//移动光标位置到末尾
                        sel.removeAllRanges();//移出所有选区
                        sel.addRange(range);//添加修改后的选区
                    }
                }
            } else if (document.selection && document.selection.type != "Control") {
                // IE < 9
                document.selection.createRange().pasteHTML(html);
            }
        },
        getCaretPosition(oField) {//获取光标位置
            var iCaretPos = 0;
            var doc = oField.ownerDocument || oField.document;
            var win = doc.defaultView || doc.parentWindow;
            var sel;
            if (typeof win.getSelection != "undefined") {
                sel = win.getSelection();
                if (sel.rangeCount > 0) {
                    var range = win.getSelection().getRangeAt(0);
                    var preCaretRange = range.cloneRange();
                    preCaretRange.selectNodeContents(oField);
                    preCaretRange.setEnd(range.endContainer, range.endOffset);
                    iCaretPos = preCaretRange.toString().length;
                }
            } else if ((sel = doc.selection) && sel.type !== 'Control') {
                var textRange = sel.createRange();
                var preCaretTextRange = doc.body.createTextRange();
                preCaretTextRange.moveToElementText(oField);
                preCaretTextRange.setEndPoint('EndToEnd', textRange);
                iCaretPos = preCaretTextRange.text.length;
            }
            return (iCaretPos);
        },
        getCursortPosition1(element) {
            var caretOffset = 0;
            var doc = element.ownerDocument || element.document;
            var win = doc.defaultView || doc.parentWindow;
            var sel;
            if (typeof win.getSelection != "undefined") {//谷歌、火狐
                sel = win.getSelection();
                if (sel.rangeCount > 0) {//选中的区域
                    var range = win.getSelection().getRangeAt(0);
                    var preCaretRange = range.cloneRange();//克隆一个选中区域
                    preCaretRange.selectNodeContents(element);//设置选中区域的节点内容为当前节点
                    preCaretRange.setEnd(range.endContainer, range.endOffset);  //重置选中区域的结束位置
                    caretOffset = preCaretRange.toString().length;
                }
            } else if ((sel = doc.selection) && sel.type != "Control") {//IE
                var textRange = sel.createRange();
                var preCaretTextRange = doc.body.createTextRange();
                preCaretTextRange.moveToElementText(element);
                preCaretTextRange.setEndPoint("EndToEnd", textRange);
                caretOffset = preCaretTextRange.text.length;
            }
            return caretOffset;
        },
        //监听字数
        countNum1(val) {
            let len = 0;//字数
            if (val != null) {
                let regVar1 = /[{](var[1-9]\d?[|])[d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g;//可变长度
                let regVar2 = /[{](var[1-9]\d?[|])[d|w|$|c]([1-9]\d?)[}]/g;//固定长度
                if (regVar1.test(val) || regVar2.test(val)) {
                    let regVar1Arr = val.match(regVar1);
                    let regVar2Arr = val.match(regVar2);
                    if (regVar1Arr) {  //如果是可变长度类型的参数，要取出长度
                        for (let i = 0; i < regVar1Arr.length; i++) {
                            let variableLen = Number(regVar1Arr[i].split('-')[1].replace(/[^0-9]/ig, ""));
                            len += variableLen;
                        }
                    }
                    if (regVar2Arr) {
                        for (let i = 0; i < regVar2Arr.length; i++) {
                            let fixedLen = Number(regVar2Arr[i].match(/\d+/g)[1])//字符串中找出数字，组中的第二个数字为固定长度
                            len += fixedLen;
                        }
                    }
                    val = val.replace(regVar1, '').replace(regVar2, '')
                }
                // .replace(/\{/gi,'').replace(/\}/gi,'')
                val = val.replace(/<br>/g, ' ').replace(/\n/g, ' ').replace(/\r/g, ' ').replace(/<\/?[^>]*>/g, '').replace(/&nbsp;/g, ' ').replace(/var[1-9]\d?[|]/ig, '').replace(/&amp;/g, '&');
                len += val.length;
                return len;

            } else {
                return len = 0;
            }
        },
        //分割模板内容的参数 验证参数间是否有间隔
        arrSplit(contentString) {
            var arrs = []
            var a = contentString.split(/<[^>]+>([^<]+)<\/[^>]+>/g);
            if (a) {
                for (var i = 0; i < a.length - 1; i++) {
                    if (i != 0) {
                        arrs[arrs.length] = a[i].split(/<[^>]+>([^<]+)<\/[^>]+>/g)[0]
                    }
                }
            }
            return arrs
        },
        //监听字数
        countNum(dom) {
            let recentVal = '';//目前的内容
            let len = 0;//字数
            let smsNum = 0;//短信条数
            recentVal = this.$refs[dom].innerText || this.$refs[dom].textContent;
            let regVar1 = /[{][d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g;//可变长度
            let regVar2 = /[{][d|w|$|c]([1-9]\d?)[}]/g;//固定长度
            if (regVar1.test(recentVal) || regVar2.test(recentVal)) {
                let regVar1Arr = recentVal.match(regVar1);
                let regVar2Arr = recentVal.match(regVar2);
                if (regVar1Arr) {  //如果是长度类型的参数，要取出长度
                    for (let i = 0; i < regVar1Arr.length; i++) {
                        let variableLen = Number(regVar1Arr[i].split('-')[1].replace(/[^0-9]/ig, ""));
                        len += variableLen;
                    }
                }
                if (regVar2Arr) {
                    for (let i = 0; i < regVar2Arr.length; i++) {
                        let fixedLen = Number(regVar2Arr[i].replace(/[^0-9]/ig, ""))
                        len += fixedLen;
                    }
                }
                recentVal = recentVal.replace(regVar1, '').replace(regVar2, '')
            }
            recentVal = recentVal.replace(/<br>/g, '').replace(/\n/g, '').replace(/\r/g, '').replace(/<\/?[^>]*>/g, '');
            if (recentVal.length >= 800) {//超过800个字字数截取
                this.$refs[dom].innerHTML = recentVal.substr(0, 800);//截取后文字内容放在里面--由于有插入的标签内容，所以使用innerHTML
                this.keepLastIndex(this.$refs[dom]);//光标放在最后
                len = 800;
            } else {
                len += recentVal.length;
            }
            //字数和短信条数的显示
            if (len == 0) {
                smsNum = 0;
            }
            else if (parseInt(len) <= 70 && parseInt(len) > 0) {
                smsNum = 1;
            } else {
                smsNum = Math.floor((parseInt(len) - 70) / 67) + 1
            }
            if (dom == 'input1') {
                this.form.input1.wordCount = len
                this.form.input1.numTextMsg = smsNum
            } else if (dom == 'input2') {
                this.form.input2.wordCount = len
                this.form.input2.numTextMsg = smsNum
            }
            let num = {};
            num.wordCount = len;
            num.numTextMsg = smsNum;
            return num;
        },
        // changeInput(input){//监听输入框---手动输入内容变化
        //     // this.$refs[input].innerHTML
        //     console.log(event.target)
        //     event.target.innerText.replace(/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/gi,'');
        //     this.countNum(input)//计数
        // },


        //--------------操作栏的按钮--------------------
        handelOptionButton: function (val) {
            if (val.methods == 'getIt') {//----------------我的模板库里的“选取模板”
                let temContent = val.row.initialContent;//获取模板内容
                this.$refs.divInput2.$refs.input2.innerHTML = temContent
                this.form.input2.commonInputVal = temContent;
                this.dialogMyTems = false;//关闭弹窗
            }
        },

        openTemRule() {//点击模板规则
            this.dialogTemRule = true;
        },
        //提交
        submit(val) {
            this.$refs[val].validate((valid) => {
                if (val == 'insertVal') {
                    if (valid) {
                        this.dialogInsertVal = false
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                }
            })
        },
        //点击短链转换
        shortReset() {
            this.temshortVisible = true;
        },
        //短连接转换
        transformation() {
            if (this.originalUrl != '') {
                this.$api.post(this.API.slms + 'v3/shortLink/add', { originalUrl: this.originalUrl }, res => {
                    if (res.code == 200) {
                        this.shortConUrl = res.data.shortLinkUrl;
                        this.shortCode = res.data.shortCode;
                        this.$message({
                            message: '短链接转换成功！',
                            type: 'success'
                        });
                    } else {
                        this.originalUrl = '';
                        this.$message({
                            message: res.msg,
                            type: 'warning'
                        });
                    }
                })
            } else {
                this.$message({
                    message: '长链接不可为空',
                    type: 'warning'
                });
            }
        },
        //打开新窗口预览
        handlePreview() {
            if (this.shortConUrl != '') {
                window.open(this.shortConUrl, '_blank');
            } else {
                this.$message({
                    message: '短连接为空，无法预览',
                    type: 'warning'
                });
            }
        },
        //短连接弹框的确定
        shortConDetermine() {
            if (this.shortConUrl != '') {
                this.form.input2.commonInputVal += "&nbsp;" + this.shortConUrl + "&nbsp;";
                this.isShort = true;
                this.temshortVisible = false;
            } else {
                this.$message({
                    message: '短链接不可为空',
                    type: 'warning'
                });
            }
        },
        //短链取消
        handleCancles() {
            this.temshortVisible = false;
            this.isShort = false;
        },
        //取出两个数组的不同元素
        getArrDifference(arr1, arr2) {
            return arr1.concat(arr2).filter(function (v, i, arr) {
                return arr.indexOf(v) === arr.lastIndexOf(v);
            });

        },
        //删除数组中对象对应的属性值
        // removeByValue: function(arr, attr, value) {  //数组，属性，属性值
        //         var index=0;
        //         for(var i in arr){
        //             if(arr[i][attr]==value){
        //                 index=i;
        //                 break;
        //             }
        //         }
        //         arr.splice(index,1);
        //      } ,
        //删除数组的对象
        // removeArray(_arr, _obj) {
        //     let length = _arr.length;
        //     for (let i = 0; i < length; i++) {
        //         if (_arr[i] === _obj) {
        //             if (i === 0) {
        //                 _arr.shift(); //删除并返回数组的第一个元素
        //                 return _arr;
        //             }
        //             else if (i === length - 1) {
        //                 _arr.pop();  //删除并返回数组的最后一个元素
        //                 return _arr;
        //             }
        //             else {
        //                 _arr.splice(i, 1); //删除下标为i的元素
        //                 return _arr;
        //             }
        //         }
        //     }
        // }
    },
    computed: {//普通模板计数
        numTextMsg1: function () {//1短信条数
            let numMsg = 0;
            //字数和短信条数的显示
            if (this.wordCount1 == 0) {
                numMsg = 0;
            } else if (parseInt(this.wordCount1) <= 70 && parseInt(this.wordCount1) > 0) {
                numMsg = 1;
            } else {
                numMsg = Math.ceil((parseInt(this.wordCount1)) / 67)
            }
            return numMsg;
        },
        wordCount1: function () {//1字数
            if (this.form.input1.commonInputVal == " ") {
                return 0
                // console.log("computed111",this.form.input1.commonInputVal)
            }
            else {
                if (this.form.input1.commonInputVal.indexOf(" ") != -1) {
                    if (this.form.input1.commonInputVal, length == 2) {
                        // console.log("computed222",this.form.input1.commonInputVal)
                        return 1
                    } else {
                        // console.log("computed333",this.form.input1.commonInputVal)
                        if (this.form.input1.commonInputVal.indexOf("&nbsp;" != -1)) return this.countNum1(this.form.input1.commonInputVal.replace(/&nbsp;/g, "a"))
                        return (this.form.input1.commonInputVal.length - 1)
                    }
                }
                // console.log("computed444",this.form.input1.commonInputVal)
                return this.countNum1(this.form.input1.commonInputVal)
            }
        },
        numTextMsg2: function () {//2短信条数
            let numMsg = 0;
            //字数和短信条数的显示
            if (this.wordCount2 == 0) {
                numMsg = 0;
            } else if (parseInt(this.wordCount2) <= 70 && parseInt(this.wordCount2) > 0) {
                numMsg = 1;
            } else {
                numMsg = Math.ceil((parseInt(this.wordCount2)) / 67)
            }
            return numMsg;
        },
        wordCount2: function () {//2字数
            if (this.form.input2.commonInputVal == " ") {
                return 0
            }
            else {
                let reg = /^<span/g
                let reg1 = /<\/span> $/g
                if (this.form.input2.commonInputVal.length == 2 && this.form.input2.commonInputVal.indexOf(" ") != -1) {
                    return 1
                }
                if (reg.test(this.form.input2.commonInputVal) && reg1.test(this.form.input2.commonInputVal)) {
                    return 1
                }
                return this.countNum1(this.form.input2.commonInputVal)
            }
        },
        dialogInsertValTitle: function () {//插入参数弹窗的标题
            return this.dialogInsertValStatus == '1' ? '插入参数' : '编辑参数';
        },
        editTemDialog_title: function () {//模板内容弹窗的标题
            return this.editTemDialog_status == '1' ? '新增短信模板' : '编辑短信模板';
        },

    },
    watch: {
        'insertVal.formData.val': {
            handler: function () {
                if (this.insertVal.formData.val == 'c' && (this.insertVal.formData.min > 5 || this.insertVal.formData.max > 5)) {
                    this.insertVal.formData.min = 0
                    this.insertVal.formData.max = 1
                }
            }
        },
        //监听变量模板的内容变化(主要控制两个参数不能挨着)
        'form.input2.commonInputVal': {
            handler: function (newVal, oldVal) {
                var reg = /<\/span><span/g;
                if (reg.test(newVal)) {
                    this.$message({
                        type: "warning",
                        message: '两个参数不可挨在一起，中间至少有一个字符隔开!'
                    })
                    this.$refs.divInput2.$refs.input2.innerHTML = oldVal
                    this.form.input2.commonInputVal = oldVal
                    //    this.mostImportant=oldVal
                    //    this.$refs.divInput2.$refs.input2.focus();//自动聚焦
                }

                var regcheck = new RegExp("拒收请回复R")
                if (this.form.resource == '3' && regcheck.test(newVal)) {
                    this.form.checked = true
                } else {
                    this.form.checked = false
                }
                let regex = /\【(.+?)\】/g;
                let regexs = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/;
                let options = newVal.match(regex)
                if (options != null && newVal.indexOf('【') != -1 && newVal.indexOf('】') != -1) { } else {
                    this.form.signId = ''
                }
                if (newVal == '') {
                    this.getcode = [];
                }

                if (newVal) {
                    let are = /<[^>]+>([^<]+)<\/[^>]+>/g;
                    let aress = newVal.match(are);
                    let divs = document.createElement('div');
                    divs.id = 'codecopy';
                    divs.innerHTML = aress;
                    let arr = divs.getElementsByTagName('span');
                    if (aress == [] || aress == null || aress == '') {
                        this.getcode = [];
                    } else {
                        if (newVal.length < oldVal.length) {
                            let abb = [];
                            let abbs = [];
                            for (var i = 0; i < this.getcode.length; i++) {
                                abb.push(this.getcode[i].code);
                            }
                            for (var j = 0; j < arr.length; j++) {
                                abbs.push(arr[j].getAttribute('data'));
                            }
                            if (arr.length < this.getcode.length) {
                                let arrCode = this.getArrDifference(abb, abbs);
                                // if (arrCode.length!=0) {
                                // this.removeByValue(this.getcode ,'code',arrCode)
                                // }else{
                                let divarr = document.getElementsByClassName("edit-div");
                                let dataspan = divarr[0].getElementsByTagName('span');
                                let codeNum = [];

                                for (var k = 0; k < dataspan.length; k++) {
                                    codeNum.push(dataspan[k].getAttribute('constvar'))//12
                                }
                                codeNum = codeNum.join('')
                                for (var d = 0; d < this.getcode.length; d++) {
                                    if (codeNum.indexOf(this.getcode[d].key) == -1) {
                                        this.getcode.splice(d, 1);
                                        break;
                                    }
                                }

                                // }
                            }
                        }

                    }
                }

            },
            immediate: true
        },
        //监听签名
        'form.signId': {
            handler: function (val) {
                if (val != '') {
                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(/\【.*?\】/g, '')
                    if (this.form.input2.commonInputVal.indexOf('【') == -1) {
                        this.form.input2.commonInputVal = val + this.form.input2.commonInputVal;
                    }
                } else {
                    this.form.input2.commonInputVal = this.form.input2.commonInputVal.replace(/\【.*?\】/g, '')
                }
            },
            immediate: true
        },
        /**--------------监听参数变化时清空模板内容--------------------*/
        'form.resource': {
            handler: function (newVal, oldVal) {
                this.form.longUrl = ''
                this.form.autoSwitch = false
                this.form.checked = false
                console.log(newVal, 'newVal');
                let a = true;
                if (this.statusOf == "编辑模板") {
                    a = false;
                    if (newVal == this.rowDatas.temType) {
                        a = false;
                    } else {
                        a = true;
                    }
                }
                if (newVal && this.form.input2.commonInputVal != '') {
                    if (a) {
                        this.$alert('切换会清除刚刚添加的参数!', '提示', {
                            confirmButtonText: '确定',
                            callback: action => {
                                this.form.input2.commonInputVal = '';
                                this.getcode = [];
                                this.form.signId = '';
                            }
                        });
                    }
                }
                if (newVal == 3) {
                    this.form.temFormat = '2'
                } else {
                    this.form.temFormat = '1'
                }
                if (newVal != 1) {
                    this.form.resource = newVal;
                }
            },
            immediate: true
        },
        /**---------------监听参数弹窗是否开启关闭------------- */
        dialogInsertVal(newVal, oldVal) {
            if (newVal == false) {
                this.$refs.insertVal.resetFields();//置空参数弹窗   
                this.insertVal.formData.val = '';//初始参数弹窗
                this.insertVal.formData.len = '1';
                this.insertVal.formData.min = '';
                this.insertVal.formData.max = '';
                this.insertVal.formData.fixed = '';
            }
        },

        /**----------监听添加模板弹窗是否关闭------------- */
        TemDialogVisible(newVal, oldVal) {
            if (newVal == false) {
                this.$refs.temForm.resetFields();
                Object.assign(this.form, this.formEmpty);
                // this.addVarCount=0;
                this.editVarCount = '';
                this.isShort = false;
                this.$refs.divInput2.$refs.input2.innerHTML = '';
                this.$refs.divInput1.$refs.input1.innerHTML = '';
            }

        },
        /**---------监听模板的查询条件是否改变--------------- */
        param() {
            this.tabelAlllist.param = this.param;
            this.tabelAlllist.currentPage = 1;
            this.tabelAlllist.pageSize = 10;
            this.getCompany();
        },
        /**-------监听我的模板查询条件是否改变------------- */
        myTemSearch() {
            this.myTemlist.param = this.myTemSearch;
            this.myTemlist.currentPage = 1;
            this.myTemlist.pageSize = 10;
            this.getMyTem_delay();
        },
        /**-----------监听我的模板库 弹窗是否关闭(将内容清空和选择page置为初始值) */
        dialogMyTems() {
            this.myTemSearch = ''
            this.myTemlist.param = '';
            this.myTemlist.currentPage = 1;
            this.myTemlist.pageSize = 10;
            this.getMyTemTableData()
        },
        // /**-----------监听插入参数弹框，为新增还是编辑 */
        // dialogInsertValStatus(newVal,oldVal){s
        //     if(newVal=="1"){
        //         this.insertVal.formData.min='4'
        //     }
        // }
        //短连接弹框是否关闭
        temshortVisible(val) {
            if (val == false) {
                this.originalUrl = '';//长链接的值
                this.shortConUrl = '';//短连接的值
            }
        }
    },
    mounted() {
        this.getMyTemTableData();
        this.getpassLabels();//获取签名        
        this.getMyTem_delay = _.debounce(this.getMyTemTableData, 500)
    }
}
</script>
<style scoped>
.slectcode .el-input {
    width: 160px;
}

.sign {
    padding: 20px;
    position: relative;
}

.sig-type .el-radio+.el-radio {
    margin-left: 0px;
}

.sig-type .el-radio {
    display: block;
    white-space: normal;
    padding-bottom: 16px;
}

.sig-type .el-radio-group {
    padding-top: 8px;
}

.sig-type-title-tips {
    font-weight: bold;
}

.audit-criteria {
    display: inline-block;
    padding-left: 80px;
    margin-bottom: 10px;
    font-size: 12px;
    color: #049679;
    cursor: pointer;
}

.Signature-matter {
    margin-top: 50px;
    border: 1px solid #66CCFF;
    margin-left: 80px;
    margin-right: 115px;
    /* background: #E5F0FF; */
    padding: 10px 14px;
    border-radius: 5px;
    font-size: 12px;
}

.Signature-matter>p {
    padding: 5px 0px;
}

.Templat-box {
    padding: 20px;
}

.Templat-matter {
    border: 1px solid #66CCFF;
    background: #E5F0FF;
    padding: 10px 14px;
    border-radius: 5px;
    font-size: 12px;
}

.Templat-matter>p {
    padding: 5px 0;
}

.Templat-set {
    color: #0066CC;
}

.Templat-contact {
    color: #0066FF;
    font-size: 12px;
    cursor: pointer;
}

.Templat-list-header {
    position: absolute;
    font-weight: bold;
    left: 0px;
    top: 12px;
}

.Templat-table {
    padding-bottom: 40px;
}

.Templat-search-fun {
    position: relative;
    height: 40px;
    margin-top: 20px;
    padding-bottom: 6px;
}

.Templat-search-box {
    position: absolute;
    right: 0px;
}

.tem-font {
    font-size: 12px;
}

.tem-font span {
    color: red;
}

.tem-be-careful {
    font-size: 12px;
    color: #999;
    line-height: 20px;
    margin: 10px 0;
}

.tem-title {
    position: absolute;
    left: 32px;
}

.tem-title:before {
    position: absolute;
    content: '*';
    color: #f56c6c;
    left: -10px;
}

.tem-subTitle {
    position: absolute;
    left: 32px;
    top: 170px;
    color: #16A589;
    cursor: pointer;
}

/* .template-btn-1{
    position: absolute;
    right:-100px;
    top:5px;
}
.template-btn-2{
    position: absolute;
    right:-100px;
    top:50px;
} */
/* div输入框 */
.common-template-val,
.variable-template-val {
    min-height: 100px;
    _height: 100px;
    /* border: 1px solid #ddd; */
    border-right: 1px solid #e4e7ed;
    border-left: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
    outline: none;
    word-wrap: break-word;
    padding: 4px 10px;
    color: #000;
}

.f-basic {
    color: #16A589;
    font-weight: bold;
}

.short-title {
    font-weight: bolder;
    padding-bottom: 5px;
}

.font-sizes {
    padding-top: 2px;
    font-size: 12px;
    color: rgb(163, 163, 163);
}

.font-sizes1 {
    margin-top: 10px;
}

.woring {
    margin: 8px;
}
</style>
<style>
.el-tabs__header .is-top {
    margin-bottom: 0px !important;
}

.el-table .cell.el-tooltip {
    white-space: pre-wrap !important;
}

.myTems .el-dialog__body {
    padding-bottom: 50px !important;
}

.text-adaNum {
    color: #16A589;
    cursor: pointer;
}

.TemDialog .el-tabs__item {
    height: 32px;
    line-height: 32px;
}

.TemDialog .el-tabs__header {
    margin: 0px;
}

.TemDialog .el-tabs__content {
    overflow: inherit;
}

.el-table--small th {
    background: #f5f5f5;
}
</style>




