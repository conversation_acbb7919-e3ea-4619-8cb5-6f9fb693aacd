<template>
    <div class="Signature-box" >
        <div style="margin-bottom: 20px;">
            <el-page-header @back="goBack" :content="`${$route.query.signature}`">
            </el-page-header>
        </div>
        <div class="fillet">
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                <el-tab-pane label="行业短信示例" name="2">
                </el-tab-pane>
                <el-tab-pane label="营销短信示例" name="3">
                </el-tab-pane>
            </el-tabs>
            <div class="table-operations">
                <el-button type="primary" @click="handleAdd">新增示例</el-button>
            </div>
            <el-table v-loading="loading" :data="tableData" border style="width: 100%">
                <el-table-column prop="signature" label="签名" width="180">
                    <template slot-scope="scope">
                        {{$route.query.signature}}
                    </template>
                </el-table-column>
                <el-table-column prop="content" label="示例内容" min-width="300">
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="180">
                    <template slot-scope="scope">
                        {{moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')}}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100" fixed="right">
                    <template slot-scope="scope">
                        <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination-container">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="[10, 20, 30, 50]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
            <!-- 新增/编辑对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false" class="example-dialog">
                <el-form :model="form" :rules="rules" ref="form" label-width="100px">
                    <el-form-item label="签名" prop="signature">
                        <el-input v-model="form.signature" disabled>
                        </el-input>
                    </el-form-item>
                    <el-form-item :label="activeTab === '2' ? '行业示例内容' : '营销示例内容'" prop="content">
                        <div class="example-content-container">
                            <div class="example-header">
                                <el-button type="primary" size="small" icon="el-icon-plus" @click="addExample" 
                                    :disabled="form.contentList.length >= 10" v-if="!form.id">添加示例</el-button>
                            </div>
                            <div class="example-list">
                                <div v-for="(item, index) in form.contentList" :key="index" class="example-item">
                                    <div class="example-item-header">
                                        <span class="index" v-if="!form.id">示例 {{index + 1}}</span>
                                        <el-button type="danger" size="mini" icon="el-icon-delete" circle 
                                          @click="removeExample(index)" v-if="form.contentList.length > 1"></el-button>
                                    </div>
                                    <el-input type="textarea" :rows="2" v-model="form.contentList[index]" 
                                        :placeholder="'请输入第' + (index + 1) + '个示例内容'"
                                        @blur="validateContent(index)"
                                        :class="{'is-error': form.errors && form.errors[index]}">
                                    </el-input>
                                    <div class="error-message" v-if="form.errors && form.errors[index]">
                                        <span style="color: #F56C6C;">{{form.errors[index]}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="example-tip" v-if="form.contentList.length >= 10">
                                <i style="color: #F56C6C;" class="el-icon-warning"></i>
                                <span style="color: #F56C6C;">最多可添加10个示例</span>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import moment from 'moment'

export default {
    name: 'ContentExample',
    data() {
        return {
            moment,
            activeTab: '2',
            userId: '',
            loading: false,
            searchForm: {
                signature: '',
                smsType: '2'
            },
            form: {
                signature: '',
                contentList: [''],
                errors: {}
            },
            rules: {
                contentList: [
                    { 
                        validator: (rule, value, callback) => {
                            const hasEmpty = value.some(item => !item.trim())
                            const hasError = Object.keys(this.form.errors).length > 0
                            if (hasEmpty || hasError) {
                                callback(new Error('请填写所有示例内容'))
                            } else {
                                callback()
                            }
                        },
                        trigger: 'blur'
                    }
                ]
            },
            tableData: [],
            currentPage: 1,
            pageSize: 10,
            total: 0,
            dialogVisible: false,
            dialogTitle: '新增示例'
        }
    },
    created() {
        this.searchForm.signature = this.$route.query.signature || ''
        this.userId = this.$route.query.userId || ''
        this.getList()
    },
    methods: {
        goBack() {
            if (this.userId) {
                this.$router.push({
                    path: '/subSignatureManagement',
                })
            } else {
                this.$router.push({ path: "/SignatureManagement" });
            }
        },
        handleTabClick(tab) {
            this.resetForm()
        },
        handleSearch() {
            this.currentPage = 1
            this.getList()
        },
        resetForm() {
            this.currentPage = 1
            this.getList()
        },
        handleSizeChange(val) {
            this.pageSize = val
            this.getList()
        },
        handleCurrentChange(val) {
            this.currentPage = val
            this.getList()
        },
        handleAdd() {
            this.dialogTitle = '新增示例'
            this.form = {
                signature: this.searchForm.signature,
                contentList: [''],
                errors: {}
            }
            this.dialogVisible = true
        },
        handleEdit(row) {
            this.dialogTitle = '编辑示例'
            this.form = {
                id: row.id,
                signature: this.searchForm.signature,
                contentList: [row.content],
                errors: {}
            }
            this.dialogVisible = true
        },
        addExample() {
            if (this.form.contentList.length >= 10) {
                this.$message.warning('最多可添加10个示例')
                return
            }
            this.form.contentList.push('')
            this.$nextTick(() => {
                this.validateContent(this.form.contentList.length - 1)
            })
        },
        removeExample(index) {
            this.form.contentList.splice(index, 1)
            this.$delete(this.form.errors, index)
            // 重新验证所有内容
            this.form.contentList.forEach((_, index) => {
                this.validateContent(index)
            })
        },
        validateContent(index) {
            const content = this.form.contentList[index]
            if (!content || !content.trim()) {
                this.$set(this.form.errors, index, '示例内容不能为空')
            } else {
                this.$delete(this.form.errors, index)
            }
        },
        submitForm() {
            // 先验证所有内容
            this.form.contentList.forEach((_, index) => {
                this.validateContent(index)
            })
            
            // 检查是否有空内容
            const hasEmpty = this.form.contentList.some(item => !item.trim())
            if (hasEmpty) {
                this.$message.warning('请填写所有示例内容')
                return
            }
            
            this.$refs.form.validate(valid => {
                if (valid) {
                    // 过滤掉空内容
                    const contentArray = this.form.contentList.filter(item => item.trim() !== '')
                    
                    // 根据activeTab构建不同的请求数据
                    const requestData = {
                        signature: this.form.signature,
                        id: this.form.id,
                       
                    }
                    if (this.userId) {
                        requestData.userId = this.userId
                    }
                    if (this.activeTab === '2') {
                        requestData.hyContentExamples = contentArray
                    } else {
                        requestData.yxContentExamples = contentArray
                    }
                    if(this.form.id){
                        delete requestData.hyContentExamples
                        delete requestData.yxContentExamples
                        delete requestData.signature
                        requestData.content = contentArray[0] 
                    }
                    
                    const method = this.form.id ? 'put' : 'post'
                    
                    this.$api[method](this.API.cpus + 'consumersignatureexample', requestData, res => {
                        if (res.code === 200) {
                            this.$message.success(this.form.id ? '更新成功' : '添加成功')
                            this.dialogVisible = false
                            this.getList()
                        } else {
                            this.$message.error(res.msg || (this.form.id ? '更新失败' : '添加失败'))
                        }
                    })
                }
            })
        },
        getList() {
            this.loading = true
            const params = {
                currentPage: this.currentPage,
                curPageSize: this.pageSize,
                signature: this.searchForm.signature,
                smsType: this.activeTab
            }
            if (this.userId) {
                params.userId = this.userId
            }
            
            this.$api.post(this.API.cpus + 'consumersignatureexample/page', params, res => {
                this.loading = false
                if (res.code === 200) {
                    this.tableData = res.data.records
                    this.total = res.data.total
                } else {
                    this.$message.error(res.msg || '获取列表失败')
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.Signature-box {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);

  .table-operations {
    margin-bottom: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}

.example-dialog {
  .example-content-container {
    background: #f5f7fa;
    border-radius: 4px;
    padding: 15px;

    .example-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      .title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }

    .example-list {
      .example-item {
        background: #fff;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &:last-child {
          margin-bottom: 0;
        }

        .example-item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .index {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }
        }

        .el-input {
          &.is-error {
            .el-textarea__inner {
              border-color: #f56c6c;
              &:focus {
                border-color: #f56c6c;
              }
            }
          }
        }
        .error-message {
          color: #f56c6c;
          font-size: 12px;
          line-height: 1;
          padding-top: 4px;
        }
      }
    }

    .example-tip {
      margin-top: 10px;
      padding: 8px 12px;
      background-color: #fdf6ec;
      border-radius: 4px;
      display: flex;
      align-items: center;
      
      i {
        color: #e6a23c;
        margin-right: 5px;
        font-size: 16px;
      }
      
      span {
        color: #e6a23c;
        font-size: 13px;
      }
    }
  }
}

.crumbs {
  margin-bottom: 20px;
  
  .el-breadcrumb {
    font-size: 14px;
    line-height: 1;
    
    .el-breadcrumb__item {
      .el-icon-lx-emoji {
        margin-right: 5px;
        color: #409EFF;
      }
    }
  }
}

// 自定义输入框样式
:deep(.el-input-group__prepend),
:deep(.el-input-group__append) {
  background-color: #f5f7fa;
  color: #909399;
  font-weight: 500;
}
</style>
