<template>
  <div class="modern-template-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button type="text" @click="goBack()" class="back-btn">
            <i class="el-icon-arrow-left"></i>
            返回
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <h1 class="page-title">{{ statusOf }}</h1>
        </div>
        <div class="header-right">
          <el-tag :type="statusOf === '添加模板' ? 'success' : 'warning'" size="medium">
            {{ statusOf === '添加模板' ? '新建模板' : '编辑模板' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 模板配置表单 -->
        <el-form :model="form" :rules="temFormRules" label-width="140px" ref="temForm" class="modern-form">
          <!-- 简化模板类型选择 -->
          <div class="template-type-section">
            <el-form-item label="模板类型" prop="temType" class="compact-form-item">
              <el-radio-group v-model="form.temType" :disabled="editTemDialog_status == 0" @change="changeTemType"
                class="compact-radio-group">
                <el-radio :label="1" class="compact-radio">
                  <i class="el-icon-key"></i>
                  验证码
                  <!-- <el-tag type="success" size="mini" style="margin-left: 8px;">推荐</el-tag> -->
                </el-radio>
                <el-radio :label="2" class="compact-radio">
                  <i class="el-icon-bell"></i>
                  行业通知
                </el-radio>
                <el-radio :label="3" class="compact-radio">
                  <i class="el-icon-star-on"></i>
                  会员营销
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 模板规范说明卡片 - 置于顶部让用户熟知 -->
          <el-card shadow="hover" class="form-card template-rules-card" style="margin-bottom: 20px;">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-info"></i>
                模板规范说明
              </span>
              <div class="header-actions">
                <el-tag type="warning" size="small" style="margin-right: 8px;">请仔细阅读</el-tag>
                <el-button 
                  type="text" 
                  @click="toggleRulesVisible" 
                  class="toggle-btn"
                  size="small"
                >
                  <i :class="rulesVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ rulesVisible ? '收起' : '展开' }}
                </el-button>
              </div>
            </div>
            <div class="rules-content" v-show="rulesVisible">
              <!-- 内容规范 -->
              <el-alert title="内容规范" type="warning" :closable="false" show-icon>
                <template slot="default">
                  <div class="rules-list">
                    <div class="rule-item">
                      <strong>签名要求：</strong>内容中必须带有<span class="highlight">【签名】</span>，签名内容可以是公司名称或产品名称，字数要求<span
                        class="highlight">2-16</span>个字
                    </div>
                    <div class="rule-item">
                      <strong>签名选择：</strong>签名只能选择审核通过的签名；如没有签名，请重新添加签名提交审核；内容首位不能添加<span class="highlight">【】</span>
                    </div>
                    <div class="rule-item">
                      <strong>内容合规：</strong>不能发送<span class="highlight">房产、发票、移民、黄、赌、毒</span>犯法等国家法律法规严格禁止的内容
                    </div>
                    <div class="rule-item">
                      <strong>链接规范：</strong>超链接地址请写在短信内容中，便于审核，例如：https://www.baidu.com/，请在链接后加/，以防运营商不识别！
                    </div>
                    <div class="rule-item">
                      <strong>号码规范：</strong>内容中有固定号码，例如：010-12345678、（01012345678）等，请更换成01012345678。
                    </div>
                    <div class="rule-item">
                      <strong>变量长度规范：</strong>您填写的变量参数（如姓名、金额等）合计长度上限不能超过短信全文的70%，具体是：变量长度上限相加后≤ 短信模板总长度 × 70%；例如：模板原文："尊敬的{name}，您的订单{other_number}已发货，运费{amount}元"{other_number}1-20位字母+数字组合,支持中划线-，取20字模板总长50字 → 变量{name}+{other_number}+{amount}总字数需≤35字提交前请自行核对，超长将自动驳回。
                    </div>
                  </div>
                </template>
              </el-alert>

              <!-- 变量类型说明 -->
              <el-alert title="变量类型说明" type="info" :closable="false" show-icon style="margin-top: 16px;">
                <template slot="default">
                  <div class="variable-types">
                    <div class="variable-type-item">
                      <span class="variable-name">验证码：{valid_code}</span>
                      <span class="variable-desc">4-6位数字英文混合，支持英文大小写</span>
                    </div>
                    <div class="variable-type-item">
                      <span class="variable-name">数字：{int_number}</span>
                      <span class="variable-desc">支持1-10位数字</span>
                    </div>
                    <div class="variable-type-item">
                      <span class="variable-name">电话号码：{mobile_number}</span>
                      <span class="variable-desc">1-15位纯数字</span>
                    </div>
                    <div class="variable-type-item">
                      <span class="variable-name">其他号码：{other_number}</span>
                      <span class="variable-desc">1-20位字母+数字组合,支持中划线-</span>
                    </div>
                    <div class="variable-type-item">
                      <span class="variable-name">金额：{amount}</span>
                      <span class="variable-desc">支持数字（含英文小数点.）或数字的中文（壹贰叁肆伍陆柒捌玖拾佰仟万亿 圆元整角分厘毫），最多15位</span>
                    </div>
                    <div class="variable-type-item">
                      <span class="variable-name">时间：{date}</span>
                      <span class="variable-desc">符合时间的表达方式，也支持中文：2019年9月3日16时24分35秒，最多20位</span>
                    </div>
                    <div class="variable-type-item">
                      <span class="variable-name">中文汉字：{chinese}</span>
                      <span class="variable-desc">1-15中文，支持中文圆括号（）</span>
                    </div>
                    <div class="variable-type-item">
                      <span class="variable-name">其他：{others}</span>
                      <span class="variable-desc">1-30个中文数字英文组合,支持中文符号和空格</span>
                    </div>
                  </div>
                </template>
              </el-alert>

              <!-- 计费规则 -->
              <el-alert title="计费规则" type="success" :closable="false" show-icon style="margin-top: 16px;">
                <template slot="default">
                  <div class="billing-rules">
                    <div class="billing-item">
                      <span class="billing-condition">短信字数 ≤ 70个字</span>
                      <span class="billing-desc">按照<span class="highlight">70个字一条</span>短信计算，中文、英文、符号统一计算一个字符</span>
                    </div>
                    <div class="billing-item">
                      <span class="billing-condition">短信字数 > 70个字</span>
                      <span class="billing-desc">按照<span class="highlight">67个字一条</span>短信计算，其中3个字占用为分条字符</span>
                    </div>
                  </div>
                </template>
              </el-alert>

              <!-- 审核规则 -->
              <el-alert title="审核规则" type="warning" :closable="false" show-icon style="margin-top: 16px;">
                <template slot="default">
                  <div class="audit-rules">
                    <div class="audit-item">
                      <span class="audit-time">工作日审核：</span>
                      <span class="audit-desc">9点至21点，每30分钟审核一次</span>
                    </div>
                    <div class="audit-item">
                      <span class="audit-time">休息日审核：</span>
                      <span class="audit-desc">每天9-18点，每1小时审核一次</span>
                    </div>
                    <div class="audit-item">
                      <span class="audit-note">特殊情况：</span>
                      <span class="audit-desc">如果出现批量短信发送被驳回的情况，可能为<span
                          class="highlight">"敏感词拦截"</span>进入人工审核，请联系客服处理</span>
                    </div>
                  </div>
                </template>
              </el-alert>
            </div>
          </el-card>

          <!-- 模板配置卡片（合并基本信息和短信内容） -->
          <el-card shadow="hover" class="form-card template-config-card">
            <div slot="header" class="card-header">
              <div style="display: flex;">
                <span class="card-title">
                  <i class="el-icon-edit-outline"></i>
                  模板配置
                </span>
                <AiTemplateAssistant :template-type="form.temType" :api-instance="$api" :message-instance="$message"
                  :api-base-url="API.cpus" :enable-esc-close="true" @use-template="handleUseTemplate"
                  @generate-success="handleGenerateSuccess" @rewrite-success="handleRewriteSuccess"
                  @generate-error="handleGenerateError" @rewrite-error="handleRewriteError" ref="aiAssistant" />
              </div>
              <div class="card-header-actions">

                <el-tooltip content="变量格式：{name}；变量限制：数字+字母组合或仅字母2种类型" placement="top">
                  <i class="el-icon-question help-icon"></i>
                </el-tooltip>
              </div>
            </div>

            <!-- 基本信息区域 -->
            <div class="basic-info-section">
              <div class="form-row">
                <el-form-item label="模板名称" prop="temName" class="form-item-compact">
                  <el-input v-model="form.temName" :disabled="editTemDialog_status == 0" placeholder="请填写模板名称，名称只做标识作用"
                    class="compact-input" maxlength="20" show-word-limit>
                    <i slot="prefix" class="el-icon-edit"></i>
                  </el-input>
                </el-form-item>

                <el-form-item label="选择签名" prop="signId" class="form-item-compact">
                  <el-select v-model="form.signId" placeholder="请选择签名" class="compact-select" clearable filterable
                    remote :remote-method="remoteSearchSignature" :loading="signatureLoading"
                    @change="changeLocationValue">
                    <i slot="prefix" class="el-icon-price-tag"></i>
                    <el-option v-for="(item, index) in labelName" :label="item.signature" :value="item.signature"
                      :key="index">
                      <template #default>
                        <span>{{ item.signature }}</span>
                        <span style="color: #999;font-size: 12px;margin-left: 5px;">{{ item.consumerName }}</span>
                      </template>
                      <!-- <span style="float: left">{{ item.signature }}</span> -->
                    </el-option>
                  </el-select>
                  <div class="input-tip" v-if="!labelName || labelName.length === 0">
                    <el-alert title="暂无可用签名，请先添加并审核通过签名" type="warning" :closable="false" show-icon size="small" />
                  </div>
                </el-form-item>
              </div>
            </div>

            <!-- 短信内容区域 -->
            <div class="content-section">
              <el-divider content-position="left">短信内容</el-divider>

              <el-form-item label="短信内容" prop="temContent" class="content-form-item">
                <div class="content-input-wrapper">
                  <el-input type="textarea" :placeholder="placeholderText" v-model="form.temContent"
                    @input="handelInput" maxlength="800" show-word-limit :rows="5" class="compact-textarea" />
                </div>

                <!-- 统计信息 -->
                <div>
                  <div class="content-stats">
                    <div class="stats-item">
                      <span class="stats-label">预计条数：</span>
                      <span class="stats-value">{{ numTextMsg2 }}</span>
                      <span class="stats-unit">条短信</span>
                    </div>
                    <div class="stats-note">
                      <span class="note-text">如果此内容包含变量，以实际下发条数为准</span>
                    </div>
                  </div>
                  <div class="content-stats">
                    <div class="stats-note">
                      <span class="note-text">温馨提示：{{ placeholderText }}</span>
                    </div>
                  </div>
                </div>

                <!-- 功能选项 -->
                <div class="content-options">
                  <div class="option-group">
                    <el-button type="primary" size="small" @click="shortReset()" v-if="form.temType == 3">
                      <i class="el-icon-link"></i>
                      短链转换
                    </el-button>

                    <!-- <el-checkbox v-if="form.temType != 1" @change="handAutoSwitch" v-model="form.autoSwitch">
                      是否自动转短链
                    </el-checkbox> -->

                    <el-checkbox v-if="form.temType == 3" @change="handleQuite" v-model="form.checked">
                      拒收请回复R
                    </el-checkbox>
                  </div>
                </div>

                <!-- 长链接输入 -->
                <div v-if="form.autoSwitch" class="long-url-section">
                  <el-form-item label="长链接" class="long-url-form-item">
                    <el-input v-model="form.longUrl" placeholder="请输入长链接地址" class="compact-input">
                      <i slot="prefix" class="el-icon-link"></i>
                    </el-input>
                    <div class="input-tip">
                      <span class="tip-text">请检查长链接是否含有多余字符，确保链接能正常打开！</span>
                    </div>
                  </el-form-item>
                </div>
              </el-form-item>
            </div>
          </el-card>
          <!-- 变量配置卡片 -->
          <el-card shadow="hover" class="form-card variables-card" v-if="getcode && getcode.length > 0">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-set-up"></i>
                变量配置
              </span>
              <el-tooltip content="变量名称允许数字+字母组合或仅字母2种类型" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>

            <div class="variables-list">
              <div class="variable-item" v-for="(item, index) in getcode" :key="index">
                <div class="variable-row">
                  <div class="variable-name">
                    <el-form-item :label="'变量' + (index + 1)" class="form-item-modern">
                      <el-input v-model="item.value" disabled class="modern-input variable-name-input">
                        <i slot="prefix" class="el-icon-price-tag"></i>
                      </el-input>
                    </el-form-item>
                  </div>

                  <div class="variable-type">
                    <el-form-item label="变量类型" class="form-item-modern">
                      <el-select v-model="item.codeName" @change="typeChange($event, item)" placeholder="请选择参数内容"
                        class="modern-select">
                        <el-option label="验证码" v-if="form.temType == 1" value="valid_code">
                          <span style="float: left">验证码</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">4-6位数字英文混合</span>
                        </el-option>
                        <el-option label="数字" value="int_number">
                          <span style="float: left">数字</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">支持1-10位数字</span>
                        </el-option>
                        <el-option label="中文汉字" v-if="form.temType != 1" value="chinese">
                          <span style="float: left">中文汉字</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">1-15中文</span>
                        </el-option>
                        <el-option label="电话号码" v-if="form.temType != 1" value="mobile_number">
                          <span style="float: left">电话号码</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">1-15位纯数字</span>
                        </el-option>
                        <el-option label="金额" v-if="form.temType != 1" value="amount">
                          <span style="float: left">金额</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">支持数字或数字的中文，最多15位</span>
                        </el-option>
                        <el-option label="时间" v-if="form.temType != 1" value="date">
                          <span style="float: left">时间</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">符合时间的表达方式，例如yyyymmdd、yyyy-mm-dd、yyyy/mm/dd等,最多20位</span>
                        </el-option>
                        <el-option label="其他号码" v-if="form.temType != 1" value="other_number">
                          <span style="float: left">其他号码</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">1-20位字母+数字</span>
                        </el-option>
                        <el-option label="其他" v-if="form.temType != 1" value="others">
                          <span style="float: left">其他</span>
                          <span style="float: right; color: #8492a6; font-size: 13px">1-30个混合字符</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
          <!-- 申请说明卡片 -->
          <el-card shadow="hover" class="form-card remark-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-document"></i>
                申请说明
              </span>
              <el-tooltip content="为加快审核速度与模板通过率，请填写申请该模板的原因或说明" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>

            <el-form-item label="申请说明" prop="remark" class="remark-form-item">
              <el-input type="textarea" v-model="form.remark" :placeholder="getRemarkPlaceholder()" maxlength="200"
                show-word-limit :rows="4" class="modern-textarea" />
              <div class="remark-tips">
                <el-alert title="请详细说明模板的使用场景和目的，有助于提高审核通过率" type="info" :closable="false" show-icon />
              </div>
            </el-form-item>
          </el-card>
        </el-form>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <div class="button-group">
            <el-button v-permission type="primary" size="large" @click="addTemOk(statusOf, '1')" class="submit-btn">
              <i class="el-icon-check"></i>
              提交审核
            </el-button>
            <el-button v-permission type="success" size="large" @click="addTemOk(statusOf, '0')" class="save-btn">
              <i class="el-icon-document"></i>
              保存但不提交
            </el-button>
            <el-button size="large" @click="calcelTem()" class="cancel-btn">
              <i class="el-icon-close"></i>
              取消
            </el-button>
          </div>
        </div>


      </div>
    </div>


    <!-- 短链转换对话框 -->
    <el-dialog title="短链转换" :visible.sync="temshortVisible" width="520px">
      <div class="short-box">
        <p class="short-title" style="padding-top: 10px">长网址链接</p>
        <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
          <el-button v-permission slot="append" type="primary" icon="el-icon-refresh" @click="transformation()">转换</el-button>
        </el-input>
        <div class="font-sizes font-sizes1">
          <span style="color: red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。
        </div>
        <div class="font-sizes">
          <span style="color: red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
        </div>
      </div>
      <div class="short-box">
        <p class="short-title" style="padding-top: 20px">短网址链接</p>
        <el-input v-model="shortConUrl" class="width-l" :disabled="true">
          <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share">预览</el-button>
        </el-input>
      </div>
      <div style="text-align: right; margin-top: 16px">
        <el-button @click="handleCancles()">取 消</el-button>
        <el-button v-permission type="primary" @click="shortConDetermine()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AiTemplateAssistant from '@/components/common/AiTemplateAssistant';

export default {
  name: "NewCreateTemplate",
  components: {
    AiTemplateAssistant
  },
  data() {
    // 模板名称校验规则
    const checkName = (_, value, callback) => {
      if (this.statusOf != "添加模板") {
        callback();
        return;
      }

      if (!value || (value && (value.length > 20 || value.length < 1))) {
        return callback(new Error("长度在 1 到 20 个字符"));
      }

      const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/g;
      if (!reg.test(value)) {
        return callback(new Error("不允许输入空格等特殊符号"));
      }

      this.$api.post(
        this.API.cpus + "v3/consumersmstemplate/validateUserTempExist/",
        { temName: value },
        (res) => {
          if (res.data == "0") {
            callback();
          } else {
            return callback(new Error("此模板已存在"));
          }
        }
      );
    };

    // 校验变量格式
    const paramValid = (_, value, callback) => {
      if (!value) {
        callback();
        return;
      }

      const list = value.match(/{(.+?)}/g);
      if (!list || list.length === 0) {
        callback();
        return;
      }

      for (let i = 0; i < list.length; i++) {
        const checkItem = list[i].replace("{", "").replace("}", "");
        if (checkItem.indexOf("{") !== -1) {
          callback("变量中不能含有{字符");
          return;
        }
      }
      callback();
    };

    return {
      statusOf: "", // 是新增还是编辑
      rowDatas: "",
      editTemDialog_status: 1, // 模板是否是新增---1为新增，0为编辑
      labelName: [], // 签名列表
      signatureLoading: false, // 签名搜索加载状态
      oldTemType: 0, // 保存原模板类型，用于切换回退
      getcode: [], // 变量列表
      getcodes: [],
      temshortVisible: false, // 短链弹框
      originalUrl: "", // 长链接的值
      shortConUrl: "", // 短连接的值
      shortCode: "", // 短链的code码
      isShort: false, // 是否短链转换
      valid_code: "",
      signatureTit: "",
      paramsList: [],
      number: null,
      oldNumber: null,
      loadingcomp: false,
      textArr: [],
      placeholderText: "请输入短信内容，如需要变量例如：{name}，如内容中有链接，例如：https://www.baidu.com/，请在链接后加/，以防运营商不识别；内容中有固定号码，例如：010-12345678、（01012345678）等，请更换成01012345678。",
      rulesVisible: true, // 控制模板规范说明的显示/隐藏，默认展开
      // 表单数据
      form: {
        params: {}, // 模板变量
        autoSwitch: false, // 是否自动切换短链 false：否, true：是
        longUrl: "", // 长链接
        submit: "", // 是否提交审核 0：否, 1：是
        temFormat: "1", // 1.变量模板；2是全文模板
        signId: "", // 签名
        temName: "", // 模板名称
        temContent: "", // 模板内容
        temType: 1, // 模板类型
        shortCode: "", // 短链Code
        remark: "", // 申请说明
        checked: false // 是否包含拒收回复R
      },
      // 表单验证规则
      temFormRules: {
        signId: [
          { required: true, message: "请选择签名", trigger: "change" }
        ],
        temName: [
          { required: true, validator: checkName, trigger: ["change", "blur"] }
        ],
        temType: [
          { required: true, message: "请选择模板类型", trigger: "change" }
        ],
        temContent: [
          { required: true, message: "请输入模板内容", trigger: "change" },
          { validator: paramValid, trigger: "change" }
        ],
        remark: [
          {
            min: 1,
            max: 200,
            message: "长度在 1 到 200个字符",
            trigger: "blur"
          }
        ]
      },
      timmer: null, // 防抖定时器
      changeTypeTimer: null, // 模板类型切换防抖定时器
      searchTimer: null, // 搜索防抖定时器
      arrStorage: [] // 存储变量信息
    };
  },
  computed: {
    // 计算短信条数
    numTextMsg2() {
      const content = this.form.temContent || '';
      const length = content.length;
      if (length <= 70) {
        return 1;
      } else {
        return Math.ceil(length / 67);
      }
    }
  },
  created() {
    if (this.$route.query.i == "1") {
      this.statusOf = "添加模板";
      // 初始化oldTemType为默认值
      this.oldTemType = this.form.temType;
    } else {
      this.statusOf = "编辑模板";
      this.getTem();
    }
  },
  mounted() {
    // 获取签名列表
    this.getpassLabels();
  },
  beforeDestroy() {
    // 清理定时器
    if (this.timmer) {
      clearTimeout(this.timmer);
      this.timmer = null;
    }
    if (this.changeTypeTimer) {
      clearTimeout(this.changeTypeTimer);
      this.changeTypeTimer = null;
    }
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },
  methods: {
    // 切换模板规范说明的显示/隐藏
    toggleRulesVisible() {
      this.rulesVisible = !this.rulesVisible;
    },

    // 获取模板信息
    getTem() {
      this.$api.post(
        this.API.cpus + "v3/consumersmstemplate/page",
        {
          param: this.$route.query.param,
          currentPage: 1,
          pageSize: 200,
        },
        (res) => {
          if (!res.records || res.records.length === 0) {
            this.$message.error("获取模板信息失败");
            return;
          }

          const row = res.records[0];
          this.rowDatas = row;

          // 提取签名
          const regexCenter = /\【(.+?)\】/g;
          const sings = row.temContent.match(regexCenter);

          if (!sings || sings.length === 0) {
            this.$message.warning("模板内容中未找到签名");
          }

          // 解析模板参数
          try {
            this.form.params = JSON.parse(row.text);
          } catch (e) {
            console.error("解析模板参数失败", e);
            this.form.params = {};
          }

          // 设置表单数据
          this.form.temName = row.temName;
          this.form.temType = row.temType;
          this.form.signId = sings ? sings[0] : "";
          this.form.temContent = row.temContent;
          this.form.temId = row.temId;
          this.form.remark = row.remark;
          this.form.temFormat = row.temFormat;

          // 处理变量列表
          // if (row.temType != 3) {
          //   this.getcode = this.getNewCodeList();
          // } else {
          //   this.getcode = [];
          // }
          this.getcode = this.getNewCodeList();
          // 设置其他可选字段
          if (row.autoSwitch) {
            this.form.autoSwitch = row.autoSwitch;
          }
          if (row.longUrl) {
            this.form.longUrl = row.longUrl;
          }
          if (row.shortCode) {
            this.form.shortCode = row.shortCode;
          }
        }
      );
    },

    // 选择模板类型（点击卡片）
    selectTemplateType(type) {
      if (this.editTemDialog_status == 0) return; // 编辑状态下禁用

      // 如果选择的是当前类型，不需要切换
      if (this.form.temType === type) return;

      // 直接设置类型，不调用changeTemType，避免双重触发
      this.form.temType = type;
    },

    // 切换模板类型
    changeTemType(val) {
      if (!val) return;

      // 防抖处理，避免重复触发
      if (this.changeTypeTimer) {
        clearTimeout(this.changeTypeTimer);
      }

      this.changeTypeTimer = setTimeout(() => {
        this.handleTemplateTypeChange(val);
        this.changeTypeTimer = null;
      }, 100);
    },

    // 处理模板类型切换
    handleTemplateTypeChange(val) {
      // 如果是初始化或者没有变化，不显示确认对话框
      if (!this.oldTemType || this.oldTemType === val) {
        this.oldTemType = val;
        return;
      }

      this.$alert('切换模板会清除刚刚添加的参数以及内容!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showCancelButton: true,
        callback: action => {
          if (action === 'confirm') {
            // 确认切换，更新oldTemType
            this.oldTemType = val;

            // 更新模板格式
            this.placeholderText = '请输入短信内容，如需要变量例如：{name}';

            // 重置表单
            this.form = {
              params: {}, // 模板变量
              autoSwitch: false, // 是否自动切换短链 false：否, true：是
              longUrl: "", // 长链接
              submit: "", // 是否提交审核 0：否, 1：是
              temFormat: 1, // 1.变量模板；2是全文模板
              signId: "", // 签名
              temName: "", // 模板名称
              temContent: "", // 模板内容
              temType: val, // 模板类型
              shortCode: "", // 短链Code
              remark: "",
              checked: false
            };
            this.getcode = [];
          } else {
            // 取消切换，恢复原来的模板类型
            this.$nextTick(() => {
              this.form.temType = this.oldTemType;
            });
          }
        }
      });
    },

    // 获取新的代码列表
    getNewCodeList() {
      // 构建变量对象数组
      let objNew = [];
      for (let i in this.form.params) {
        objNew.push({ value: i, codeName: this.form.params[i] });
      }

      // 匹配内容中的变量
      const compareArr = this.form.temContent.match(/{(.+?)}/g) || [];
      const result = [];

      // 提取变量名
      const extractedVariables = compareArr.map(item =>
        item.replace("{", "").replace("}", "")
      );

      // 已存在的变量名
      const existingValues = objNew.map(item => item.value);

      // 处理每个变量
      extractedVariables.forEach(varName => {
        const existingVarIndex = existingValues.indexOf(varName);

        if (existingVarIndex === -1) {
          // 新变量
          result.push({
            value: varName,
            codeName: ""
          });
        } else {
          // 已有变量
          result.push({
            value: varName,
            codeName: objNew[existingVarIndex].codeName
          });
        }
      });

      return result;
    },

    // 切换签名
    // changeLocationValue(val) {
    //   console.log(val,'2');

    //   this.signatureTit = val;
    // },

    // 获取全部用户签名
    getpassLabels(signature = '') {
      this.$api.post(
        this.API.cpus + "signature/signatureList",
        {
          auditStatus: "2",
          currentPage: 1,
          pageSize: 20,
          needParentSignature: true,
          signature: signature // 添加signature字段用于搜索
        },
        (res) => {
          if (res && res.records) {
            this.labelName = res.records;
          }
        }
      );
    },

    // 远程搜索签名
    remoteSearchSignature(query) {
      if (query !== '') {
        this.signatureLoading = true;
        // 防抖处理
        clearTimeout(this.searchTimer);
        this.searchTimer = setTimeout(() => {
          this.getpassLabels(query);
          this.signatureLoading = false;
        }, 300);
      } else {
        this.getpassLabels();
      }
    },
    changeLocationValue(val) {
      if (val) {
        this.signatureTit = val;
      } else {
        this.getpassLabels();
      }
    },
    // 获取申请说明占位符文本
    getRemarkPlaceholder() {
      switch (this.form.temType) {
        case 1:
          return "用于我公司app用户注册时发送验证码";
        case 2:
          return "用于行业通知类短信发送";
        case 3:
          return "用于会员营销类短信发送";
        default:
          return "请详细说明模板的使用场景和目的";
      }
    },

    // 返回
    goBack() {
      this.$router.push("/TemplateManagement");
    },

    // 切换到旧版模板
    handelNewTemp() {
      const { i, param } = this.$route.query;
      if (i) {
        this.$router.push({ path: '/CreateTemplate', query: { i: "1" } });
      } else {
        this.$router.push({ path: '/CreateTemplate', query: { param } });
      }
    },

    // 处理输入内容
    handelInput(newVal) {
      // 检查"拒收请回复R"
      const reg = new RegExp("拒收请回复R");
      if (this.form.temType == 3 && reg.test(newVal)) {
        this.form.checked = true;
      } else {
        this.form.checked = false;
      }

      // 匹配变量
      const regex3 = /{(.+?)}/g; // 匹配花括号内的内容
      const regex4 = /{(.+?)}{(.+?)}/g; // 匹配连续的变量
      const arr = newVal.match(regex3);

      // 检查变量是否有重复
      const isRepeat = (arr) => {
        const hash = {};
        for (let i in arr) {
          if (hash[arr[i]]) {
            return true;
          }
          hash[arr[i]] = true;
        }
        return false;
      };

      // 验证码模板处理
      // 验证码模板处理
      if (this.form.temType == 1) {
        // this.form.temFormat = "1";

        if (!arr) {
          this.getcode = [];
          return;
        }

        if (arr.length > 2) {
          // 验证码类型最多支持两个变量
          if (!this.timmer) {
            this.$message({
              type: "warning",
              message: "验证码类型的参数最多添加两个，请删除多余的变量！"
            });
            this.timmer = setTimeout(() => {
              this.timmer = null;
            }, 2000);
          }
        } else if (isRepeat(arr)) {
          // 变量名不能重复
          if (!this.timmer) {
            this.$message({
              type: "warning",
              message: "已选变量名称不能重复"
            });
            this.timmer = setTimeout(() => {
              this.timmer = null;
            }, 2000);
          }
          this.getcode = [];
        } else if (regex4.test(newVal)) {
          // 变量不能连续
          if (!this.timmer) {
            this.$message({
              type: "warning",
              message: "两个参数不可挨在一起，中间至少有一个字符隔开。"
            });
            this.timmer = setTimeout(() => {
              this.timmer = null;
            }, 2000);
          }
          this.getcode = [];
        } else {
          // 保存当前代码设置
          const saveArr = JSON.parse(JSON.stringify(this.getcode));

          // 更新变量列表
          this.getcode = arr.map(item => ({
            value: item.replace("{", "").replace("}", ""),
            codeName: ""
          }));

          // 恢复之前的变量类型设置
          this.getcode.forEach(gcItem => {
            saveArr.forEach(taItem => {
              if (gcItem.value === taItem.value) {
                gcItem.codeName = taItem.codeName;
              }
            });
          });
        }
      }
      // 行业通知模板处理
      else {
        // this.form.temFormat = "1";

        if (!arr) {
          this.getcode = [];
          return;
        }

        if (arr.length > 16) {
          // 变量数量限制
          this.$message({
            type: "warning",
            message: "变量模板参数不能超过16个"
          });
        } else if (isRepeat(arr)) {
          // 变量名不能重复
          this.$message({
            message: "已选变量名称不能重复",
            type: "warning"
          });
        } else if (regex4.test(newVal)) {
          // 变量不能连续
          if (!this.timmer) {
            this.$message({
              type: "warning",
              message: "两个参数不可挨在一起，中间至少有一个字符隔开。"
            });
            this.timmer = setTimeout(() => {
              this.timmer = null;
            }, 2000);
          }
          this.getcode = [];
        } else {
          // 正常处理变量
          if (this.statusOf == "添加模板") {
            // 保存当前代码设置
            const saveArr = JSON.parse(JSON.stringify(this.getcode));

            // 更新变量列表
            this.getcode = arr.map(item => ({
              value: item.replace("{", "").replace("}", ""),
              codeName: ""
            }));

            // 恢复之前的变量类型设置
            this.getcode.forEach(gcItem => {
              saveArr.forEach(taItem => {
                if (gcItem.value === taItem.value) {
                  gcItem.codeName = taItem.codeName;
                }
              });
            });
          } else {
            // 编辑模板场景
            this.paramsList = arr.map(item => ({
              value: item.replace("{", "").replace("}", ""),
              codeName: ""
            }));

            this.number = this.paramsList.length;

            // 比较变量列表
            const compareParamsArr = this.paramsList.map(item => item.value);
            const compareCodeArr = [];

            this.textArr = [];
            this.getcode.forEach(item => {
              this.textArr.push(item.value);
              compareCodeArr.push(item.value.replace("{", "").replace("}", ""));
            });

            // 移除不再存在的变量
            for (let i = compareCodeArr.length - 1; i >= 0; i--) {
              if (compareParamsArr.indexOf(compareCodeArr[i]) === -1) {
                this.getcode.splice(i, 1);
              }
            }

            // 更新变量列表
            this.getcode = this.getNewCodeList();
          }
        }
      }
      // 会员营销模板处理
      // else {
      //   this.form.temFormat = "2";
      // }
    },

    // 变量类型变更
    typeChange(value, item) {
      if (value && item.value) {
        this.arrStorage.forEach(asItem => {
          if (asItem.value === item.value) {
            asItem.codeName = value;
          }
        });
      }
    },

    // 测试变量变更
    testchange() {
      // 占位方法，目前未使用
    },

    // 打开短链转换对话框
    shortReset() {
      this.temshortVisible = true;
    },

    // 长链转换为短链
    transformation() {
      if (!this.originalUrl) {
        this.$message({
          message: "长链接不可为空",
          type: "warning"
        });
        return;
      }

      this.$api.post(
        this.API.slms + "v3/shortLink/add",
        { originalUrl: this.originalUrl },
        (res) => {
          if (res.code == 200) {
            this.shortConUrl = res.data.shortLinkUrl;
            this.shortCode = res.data.shortCode;
            this.$message({
              message: "短链接转换成功！",
              type: "success"
            });
          } else {
            this.originalUrl = "";
            this.$message({
              message: res.msg,
              type: "warning"
            });
          }
        }
      );
    },

    // 预览短链
    handlePreview() {
      if (this.shortConUrl) {
        window.open(this.shortConUrl, "_blank");
      } else {
        this.$message({
          message: "短连接为空，无法预览",
          type: "warning"
        });
      }
    },

    // 确认使用短链
    shortConDetermine() {
      if (this.shortConUrl) {
        this.form.temContent += this.shortConUrl;
        this.isShort = true;
        this.temshortVisible = false;
      } else {
        this.$message({
          message: "短链接不可为空",
          type: "warning"
        });
      }
    },

    // 取消短链转换
    handleCancles() {
      this.temshortVisible = false;
      this.isShort = false;
    },

    // 自动识别链接
    handAutoSwitch(val) {
      if (val) {
        this.$api.post(
          this.API.cpus + "v3/consumersmstemplate/longUrl",
          {
            temContent: this.form.temContent
          },
          (res) => {
            if (res.data) {
              this.form.longUrl = res.data;
            }
          }
        );
      } else {
        this.form.longUrl = "";
      }
    },

    // 处理拒收回复
    handleQuite(val) {
      if (val) {
        if (!this.form.temContent.includes("，拒收请回复R")) {
          this.form.temContent = this.form.temContent + "，拒收请回复R";
        }
      } else {
        const reg = new RegExp("，拒收请回复R");
        this.form.temContent = this.form.temContent.replace(reg, "");
      }
    },

    // 提交模板
    addTemOk(_, val2) {
      this.$refs.temForm.validate((valid) => {
        if (!valid) return;

        // 验证签名匹配
        const matches = this.form.temContent.match(/【.*?】/g);
        if (!matches || matches.length === 0) {
          this.$message({
            message: '模板内容中未找到签名！',
            type: 'warning'
          });
          return;
        }

        if (this.form.signId !== matches[0]) {
          this.$message({
            message: '选择的签名与模板内容签名不匹配！',
            type: 'warning'
          });
          return;
        }

        // 验证营销类短信是否包含拒收回复R
        const reg = new RegExp("拒收请回复R");
        if (this.form.temType == '3' && !reg.test(this.form.temContent)) {
          this.$message({
            message: '请勾选拒收请回复为R!',
            type: 'warning'
          });
          return;
        }

        // 准备变量参数
        this.form.params = {};
        for (let i = 0; i < this.getcode.length; i++) {
          const varName = this.getcode[i].value.replace("{", "").replace("}", "");
          this.form.params[varName] = this.getcode[i].codeName;
        }

        // 设置提交状态
        this.form.submit = val2;

        // 验证变量参数是否都有类型
        let showToast = false;
        this.getcode.forEach(item => {
          if (!item.codeName) {
            showToast = true;
          }
        });

        if (showToast) {
          this.$message({
            message: '参数内容不能为空!',
            type: 'warning'
          });
          return;
        }

        // 验证码模板必须包含验证码参数类型
        if (this.form.temType == 1 && this.getcode.length > 0) {
          const hasValidCode = this.getcode.some(item => item.codeName === 'valid_code');
          if (!hasValidCode) {
            this.$message({
              message: '验证码模板必须包含至少一个验证码类型的参数！',
              type: 'warning'
            });
            return;
          }
        }

        if (this.getcode.length == 0) {
          this.form.temFormat = 2;
        } else {
          this.form.temFormat = 1;
        }

        // 选择请求路径
        let requestUrl = this.$route.query.param
          ? "v3/consumersmstemplate/template/update"
          : "v3/consumersmstemplate/template/add";

        if (this.$route.query.param) {
          this.form.temId = this.$route.query.param;
        }

        // 提交请求
        this.$confirms.confirmation(
          "post",
          "确认新增模板？",
          this.API.cpus + requestUrl,
          this.form,
          (res) => {
            if (res.code == 200) {
              this.goBack();
            }
          }
        );
      });
    },

    // 取消
    calcelTem() {
      this.$router.push("/TemplateManagement");
    },

    // 计算字符数
    countNum1(val) {
      if (!val) return 0;
      return val.length;
    },

    // ========== AI助手事件处理方法 ==========

    // 显示AI助手
    showAiAssistant() {
      console.log('点击AI助手按钮');

      if (this.$refs.aiAssistant) {
        // 使用nextTick确保DOM更新后再定位
        this.$nextTick(() => {
          console.log('准备显示AI助手面板');
          this.$refs.aiAssistant.show();

          // 额外的重新定位尝试
          setTimeout(() => {
            if (this.$refs.aiAssistant.forceRepositionPanel) {
              console.log('尝试强制重新定位');
              this.$refs.aiAssistant.forceRepositionPanel();
            }
          }, 500);
        });
      } else {
        console.error('未找到AI助手组件引用');
      }
    },

    // 处理AI模板使用
    handleUseTemplate(data) {
      // 保存当前的签名信息
      const currentSignature = this.form.signId;
      const currentContent = this.form.temContent;

      // 提取当前内容中的签名
      let existingSignature = '';
      const signatureMatch = currentContent.match(/【.*?】/);
      if (signatureMatch) {
        existingSignature = signatureMatch[0];
      }

      // 处理AI生成的内容
      let newContent = data.content;

      // 如果原内容有签名，需要保留
      if (existingSignature) {
        // 移除AI生成内容中可能存在的签名
        newContent = newContent.replace(/【.*?】/g, '');

        // 在内容开头添加原有签名
        newContent = existingSignature + newContent;

        console.log('保留原有签名:', existingSignature);
      } else if (currentSignature) {
        // 如果没有签名但选择了签名，添加选择的签名
        newContent = currentSignature + newContent;
        console.log('添加选择的签名:', currentSignature);
      }

      // 将处理后的内容填入表单
      this.form.temContent = newContent;

      // 处理变量
      if (data.variables && data.variables.length > 0) {
        // 先触发内容变化处理，生成变量列表
        this.handelInput(newContent);

        // 然后应用AI返回的变量类型映射
        this.applyAiVariableTypes(data.variables);
      } else {
        // 没有变量信息时，正常触发内容变化处理
        this.handelInput(newContent);
      }

      // 关闭AI助手面板
      if (this.$refs.aiAssistant) {
        this.$refs.aiAssistant.hide();
      }

      this.$message.success('模板已应用到内容中，签名已保留');
    },

    // 处理AI生成成功
    handleGenerateSuccess(data) {
      console.log('AI模板生成成功:', data);
      this.$message.success(`模板生成成功！为您生成了${data.results.length}个专业模板`);
    },

    // 处理AI改写成功
    handleRewriteSuccess(data) {
      console.log('AI模板改写成功:', data);
      this.$message.success(`模板改写成功！为您优化了${data.results.length}个版本`);
    },

    // 处理AI生成错误
    handleGenerateError(error) {
      console.error('AI模板生成失败:', error);
      this.$message.error('模板生成失败，请稍后重试');
    },

    // 处理AI改写错误
    handleRewriteError(error) {
      console.error('AI模板改写失败:', error);
      this.$message.error('模板改写失败，请稍后重试');
    },

    // 应用AI返回的变量类型
    applyAiVariableTypes(aiVariables) {
      if (!aiVariables || !Array.isArray(aiVariables)) return;

      // 创建AI变量映射表
      const aiVariableMap = {};
      aiVariables.forEach(aiVar => {
        if (aiVar.name && aiVar.type) {
          aiVariableMap[aiVar.name] = aiVar.type;
        }
      });

      // 应用到当前的getcode数组
      this.getcode.forEach(codeItem => {
        if (aiVariableMap[codeItem.value]) {
          // 如果AI返回了该变量的类型，则应用
          codeItem.codeName = aiVariableMap[codeItem.value];
        }
      });

      // 验证码模板特殊处理：支持最多两个变量，类型为valid_code或int_number
      if (this.form.temType == 1) {
        if (this.getcode.length <= 2) {
          this.getcode.forEach(variable => {
            const aiType = aiVariableMap[variable.value];
            if (aiType === 'valid_code' || aiType === 'int_number') {
              variable.codeName = aiType;
            } else if (aiType) {
              // 如果AI返回的不是验证码支持的类型，默认设置为验证码类型
              variable.codeName = 'valid_code';
              console.warn(`AI返回的变量类型"${aiType}"不适用于验证码模板，已自动设置为验证码类型`);
            } else {
              // 如果AI没有返回变量类型，默认设置为验证码类型
              variable.codeName = 'valid_code';
            }
          });
        }
      }

      // 输出调试信息
      console.log('AI变量类型应用完成:', {
        aiVariables,
        appliedVariables: this.getcode,
        templateType: this.form.temType
      });
    },


  },
  computed: {
    // 弹窗标题
    editTemDialog_title() {
      return this.editTemDialog_status == "1" ? "新增短信模板" : "编辑短信模板";
    },

    // 短信条数
    numTextMsg2() {
      if (this.wordCount2 === 0) return 0;

      if (this.wordCount2 <= 70) {
        return 1;
      } else {
        return Math.ceil(this.wordCount2 / 67);
      }
    },

    // 字数统计
    wordCount2() {
      if (!this.form.temContent) return 0;

      if (this.form.temContent === " ") return 0;

      if (this.form.temContent.length === 2 && this.form.temContent.indexOf(" ") !== -1) {
        return 1;
      }

      return this.countNum1(this.form.temContent);
    }
  },
  watch: {
    // 监听签名变化
    "form.signId": {
      handler(_, oldVal) {
        // if (!newVal || !oldVal) return;
        console.log(this.signatureTit, '1');
        this.form.temContent = this.signatureTit + this.form.temContent;
        this.form.temContent = this.form.temContent.replace(oldVal, "");
      }
    },

    // 保存原模板类型
    'form.temType': {
      handler(_, oldVal) {
        this.oldTemType = oldVal;
      },
      immediate: true
    },

    // 监听短链弹框关闭
    temshortVisible(val) {
      if (val === false) {
        this.originalUrl = ""; // 清空长链接
        this.shortConUrl = ""; // 清空短链接
      }
    },

    // 监听变量列表变化
    getcode: {
      handler(newVal) {
        if (!newVal || newVal.length === 0) return;

        newVal.forEach(item => {
          let pushFlag = true;

          this.arrStorage.forEach(asItem => {
            if (item.value === asItem.value) {
              item.codeName = asItem.codeName;
              pushFlag = false;
            }
          });

          if (pushFlag) {
            this.arrStorage.push(item);
          }
        });
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style lang="less" scoped>
// 引入创建模板通用样式
@import '~@/styles/create-template-common.less';

// 页面特有样式覆盖和扩展
.modern-template-page {
  min-height: 100vh;
  background: #f5f7fa;

  // 页面头部样式
  .page-header {
    background: #fff;
    border-bottom: 1px solid #e8eaec;
    padding: 16px 0;
    margin-bottom: 24px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        display: flex;
        align-items: center;

        .back-btn {
          color: #8c8c8c;
          font-size: 14px;
          padding: 0;
          margin-right: 16px;

          &:hover {
            color: #1890ff;
          }

          i {
            margin-right: 4px;
          }
        }

        .page-title {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          margin: 0;
        }
      }

      .header-right {
        .el-tag {
          font-weight: 500;
        }
      }
    }
  }
}

// 卡片头部操作区域样式
.card-header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}



// 页面特有的组件样式
.user-modifi-cation {
  padding: 10px;
}

.fillet {
  border-radius: 5px;
  background-color: #fff;
  padding: 15px;
}

.Top_title {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.template-btn-1 {
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.tem-be-careful {
  margin: 10px 0;
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 4px;

  .tem-font {
    font-size: 12px;
    line-height: 1.5;
    margin-right: 5px;

    span {
      font-weight: bold;
      color: #409EFF;
    }
  }
}

.Signature-matter {
  margin-top: 50px;
  border: 1px solid #66ccff;
  margin-left: 80px;
  margin-right: 115px;
  padding: 14px;
  border-radius: 5px;
  font-size: 12px;
  line-height: 1.6;
  background-color: #f9fdff;

  >p {
    padding: 5px 0;
    margin: 0;
  }
}

.woring {
  margin: 8px;
}

.short-box {
  margin-bottom: 15px;

  .short-title {
    font-weight: bold;
    margin-bottom: 8px;
  }

  .font-sizes {
    font-size: 12px;
    color: #606266;
    margin-top: 8px;
    line-height: 1.4;
  }
}

// 模板规范说明卡片样式
.template-rules-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #409eff;
    }

    .header-actions {
      display: flex;
      align-items: center;

      .toggle-btn {
        color: #409eff;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(64, 158, 255, 0.1);
        }

        i {
          margin-right: 4px;
          transition: transform 0.3s ease;
        }
      }
    }
  }

  .rules-content {
    animation: fadeIn 0.3s ease;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000px;
  }
}

// 表单元素样式覆盖
/deep/ .el-textarea__inner {
  height: 100px;
  resize: vertical;
}

/deep/ .el-radio-group {
  margin-bottom: 8px;
}

/deep/ .el-select {
  width: 100%;
}

/deep/ .width-l {
  width: 100%;
}

/deep/ .el-tooltip {
  cursor: pointer;
}

// 短信内容区域样式
.textarea-with-ai {
  position: relative;

  .ai-assistant-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    cursor: pointer;
    transition: all 0.3s ease;

    .ai-btn-content {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      border: 1px solid rgba(102, 126, 234, 0.3);
      border-radius: 16px;
      color: white;
      font-size: 12px;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
      backdrop-filter: blur(10px);

      .ai-icon {
        font-size: 14px;
        animation: sparkle 2s ease-in-out infinite;
      }

      .ai-text {
        white-space: nowrap;
      }
    }

    &:hover {
      transform: translateY(-1px);

      .ai-btn-content {
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        border-color: rgba(102, 126, 234, 0.5);
      }
    }

    &:active {
      transform: translateY(0);
    }
  }
}

@keyframes sparkle {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

// 短信内容统计样式
.sms-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #666;

  .stats-item {
    display: flex;
    align-items: center;

    .stats-label {
      margin-right: 4px;
    }

    .stats-value {
      font-weight: 600;
      color: #1890ff;
    }
  }
}

// 变量配置样式
.variable-config {
  .variable-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 6px;

    .variable-name {
      font-weight: 500;
      color: #262626;
      min-width: 80px;
    }

    .variable-select {
      flex: 1;
    }
  }
}

// 按钮组样式
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e8eaec;

  .el-button {
    min-width: 120px;
    border-radius: 6px;
    font-weight: 500;

    &.el-button--primary {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
      }
    }
  }
}
</style>





<style>
/* 全局样式 */
.el-dialog__body {
  padding: 20px;
}

.el-form-item__label {
  font-weight: 500;
}
</style>

<style lang="less" scoped>
/* 简化样式 */
// 简化模板类型选择
.template-type-section {
  background: #f8f9fa;
  padding: 16px 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.compact-form-item {
  margin-bottom: 0;

  /deep/ .el-form-item__label {
    font-weight: 500;
    color: #333;
  }
}

.compact-radio-group {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;

  /deep/ .compact-radio {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: white;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    &.is-checked {
      border-color: #409eff;
      background: #e6f7ff;
      color: #409eff;
    }

    i {
      margin-right: 6px;
      font-size: 16px;
    }

    .el-radio__input {
      display: none;
    }

    .el-radio__label {
      padding-left: 0;
      font-weight: 500;
    }
  }
}

// 模板配置卡片
.template-config-card {
  .basic-info-section {
    margin-bottom: 20px;

    .form-row {
      display: flex;
      gap: 20px;

      .form-item-compact {
        flex: 1;
        margin-bottom: 16px;

        .compact-input,
        .compact-select {
          width: 100%;
        }

        .input-tip {
          margin-top: 8px;

          /deep/ .el-alert {
            padding: 8px 12px;

            .el-alert__content {
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .content-section {
    /deep/ .el-divider {
      margin: 16px 0;

      .el-divider__text {
        font-weight: 500;
        color: #333;
      }
    }

    .content-form-item {
      .content-input-wrapper {
        margin-bottom: 12px;

        .compact-textarea {
          /deep/ .el-textarea__inner {
            border-radius: 6px;
            border: 1px solid #d9d9d9;

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
          }
        }
      }

      .content-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;

        .stats-item {
          display: flex;
          align-items: center;
          gap: 4px;

          .stats-label {
            color: #666;
            font-size: 14px;
          }

          .stats-value {
            color: #409eff;
            font-weight: 500;
            font-size: 16px;
          }

          .stats-unit {
            color: #666;
            font-size: 14px;
          }
        }

        .stats-note {
          .note-text {
            color: #999;
            font-size: 12px;
          }
        }
      }

      .content-options {
        margin-bottom: 12px;

        .option-group {
          display: flex;
          gap: 16px;
          align-items: center;
          flex-wrap: wrap;
        }
      }

      .long-url-section {
        .long-url-form-item {
          margin-bottom: 0;

          .input-tip {
            margin-top: 8px;

            .tip-text {
              color: #f56c6c;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
</style>