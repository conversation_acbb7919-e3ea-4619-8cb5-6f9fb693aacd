<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 模板管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="fillet Templat-box">
      <div class="Templat-matter">
        <p style="font-weight: bolder">温馨提醒：</p>
        <p>
          1、一个完整的短信由短信签名和短信正文内容两部分组成，您可以根据业务需求分别设置不同的短信正文内容模板，然后进行组合形成最终展示。短信签名+短信正文内容=最终显示内容。
        </p>
        <p>
          2、模板提交审核，工作日预计2小时内完成，非工作日预计4小时内完成。审核时间：周一至周日9:30-22:00（法定节假日顺延）。
        </p>
        <p>3、可设置常用手机和邮箱，用于即时接收该应用短信内容审核通知。</p>
        <p>4、支持变量模版批量导入。</p>
      </div>

      <div class="Templat-search-fun">
        <!-- <span class="Templat-list-header">模板列表</span> -->
        <div style="display: flex">
          <el-button type="primary" style="" @click="clickAddTem">创建正文模板</el-button>

          <el-button type="primary" style="" @click="exportNums1">导出</el-button>
          <el-button type="primary" style="" @click="batchQu">批量导入</el-button>
        </div>

        <!-- 搜索框开始 -->
        <div>
          <el-input placeholder="模板名称、内容、ID" v-model="param" style="width: 250px" class="Templat-search-box">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </el-input>
          <el-select v-model="tabelAlllist.status" placeholder="请选择状态" style="width: 150px;margin-left: 10px;"
            @change="getTableData" clearable>
            <el-option label="待审核" value="1"></el-option>
            <el-option label="已通过" value="2"></el-option>
            <el-option label="驳回" value="3"></el-option>
            <el-option label="待复审" value="4"></el-option>
            <el-option label="编辑中" value="0"></el-option>
          </el-select>
        </div>

      </div>
      <div class="Templat-table">
        <!-- 表格和分页开始 -->
        <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
          border :data="tableDataObj.tableData" style="width: 100%">
          <el-table-column prop="temId" label="模板ID" width="60"></el-table-column>
          <el-table-column label="模板类型" width="90">
            <template slot-scope="scope">
              <span v-if="scope.row.temType == '1'">验证码</span>
              <span v-else-if="scope.row.temType == '2'">通知</span>
              <span v-else-if="scope.row.temType == '3'">营销推广</span>
            </template>
          </el-table-column>
          <el-table-column label="申请时间" width="170">
            <template slot-scope="scope">{{ scope.row.createTime }}</template>
          </el-table-column>
          <el-table-column label="模板名称" width="170">
            <template slot-scope="scope">{{ scope.row.temName }}</template>
          </el-table-column>
          <el-table-column label="内容" min-width="500">
            <template slot-scope="scope">
              <!-- <span>{{scope.row.temContent.replace(/ /g,"&nbsp;")}}</span> -->
              <span>{{ scope.row.temContent }}</span>
              <span v-if="scope.row.checkReason == null"></span>
              <span v-else-if="scope.row.checkReason == ''"></span>
              <span v-else-if="scope.row.temStatus == '3'" style="color: #f56c6c">( 驳回原因：{{ scope.row.checkReason }}
                )</span>
            </template>
          </el-table-column>
          <el-table-column label="模板变量" width="220">
            <template slot-scope="scope">
              <div v-if="scope.row.text == '{}' || !scope.row.text"></div>
              <div v-else v-for="(item, index) in scope.row.text
                .replace(/\{|}/g, '')
                .split(',')" :key="index">
                <span style="color: red">{{ item.split(":")[0] }}</span> :
                <span style="color: green">{{ item.split(":")[1] }}</span>
              </div>
            </template>
            <!-- <template slot-scope="scope">{{ scope.row.text==null?'':(scope.row.text==''?'':scope.row.text.replace(/{/g,"").replace(/}/g,"")) }}</template> -->
          </el-table-column>
          <el-table-column label="字数" width="80">
            <template slot-scope="scope">{{
              scope.row.temContent.length
              }}</template>
          </el-table-column>
          <el-table-column label="状态" width="90">
            <template slot-scope="scope">
              <span v-if="scope.row.temStatus == '1'">待审核</span>
              <span v-else-if="scope.row.temStatus == '2'">已通过</span>
              <span v-else-if="scope.row.temStatus == '3'">驳回</span>
              <span v-else-if="scope.row.temStatus == '4'">待复审</span>
              <span v-else-if="scope.row.temStatus == '0'">编辑中</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="实名状态" width="120px">
            <template slot-scope="scope">
              <div class="carrier-status-container">
                移动状态
                <div class="carrier-status-item"
                     :data-status="getStatusDataAttr(scope.row.ydStatus)"
                     :title="`移动: ${getRealNameStatusLabel(scope.row.ydStatus)}`">
                  <i class="iconfont icon-yidong carrier-icon"
                     :style="{ color: getCarrierStatusColor(scope.row.ydStatus) }"></i>
                </div>
                联通状态
                <div class="carrier-status-item"
                     :data-status="getStatusDataAttr(scope.row.ltStatus)"
                     :title="`联通: ${getRealNameStatusLabel(scope.row.ltStatus)}`">
                  <i class="iconfont icon-liantong carrier-icon"
                     :style="{ color: getCarrierStatusColor(scope.row.ltStatus) }"></i>
                </div>
                电信状态
                <div class="carrier-status-item"
                     :data-status="getStatusDataAttr(scope.row.dxStatus)"
                     :title="`电信: ${getRealNameStatusLabel(scope.row.dxStatus)}`">
                  <i class="iconfont icon-dianxin carrier-icon"
                     :style="{ color: getCarrierStatusColor(scope.row.dxStatus) }"></i>
                </div>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button type="text" style="color: #f56c6c" @click="delTem(scope.$index, scope.row)"><i
                  class="el-icon-error"></i>&nbsp;删除</el-button>
              <el-button type="text" style="color: #E6A23C;"
                v-if="scope.row.temStatus == '0' || scope.row.temStatus == '3'"
                @click="editTem(scope.$index, scope.row)"><i class="el-icon-edit"></i>&nbsp;编辑</el-button>
              <el-button v-if="scope.row.temStatus == '2' && scope.row.version == '2'" type="text"
                style="margin-left: 10px; color: #16a589" @click="copySmsTemp(scope.row)"><i
                  class="el-icon-document-copy"></i>&nbsp;复制</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
          style="background: #fff; padding: 10px 0">
          <el-pagination style="float: right" class="page_bottom" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
          </el-pagination>
        </el-col>
        <!-- 表格和分页结束 -->
      </div>
      <!-- 编辑弹框 -->
      <el-dialog :title="editTemDialog_title" :visible.sync="TemDialogVisible" width="620px" class="TemDialog"
        :close-on-click-modal="false">
        <!-- 模板规则弹窗(内层弹窗) -->
        <el-dialog :visible.sync="dialogTemRule" append-to-body>
          <!-- 模板规则的模板内容 -->
          <templateRule></templateRule>
        </el-dialog>
        <!-- 我的模板库（内层弹窗） -->
        <el-dialog title="我的模板库" width="580px" class="myTems" style="padding-bottom: 40px" :visible.sync="dialogMyTems"
          append-to-body>
          <el-input placeholder="模板名称、内容、ID" v-model="myTemSearch" style="width: 200px; margin-bottom: 10px">
            <i slot="suffix" class="el-input__icon el-icon-search"></i>
          </el-input>
          <!-- 表格和分页开始 -->
          <table-tem :tableDataObj="myTemTableDataObj" @handelOptionButton="handelOptionButton">
            <!--分页-->
            <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page pageStyle" slot="pagination">
              <el-pagination style="margin-bottom: -2px" class="page_bottom" @size-change="handleSizeChangeMyTem"
                @current-change="handleCurrentChangeMyTem" :current-page="myTemlist.currentPage"
                :page-size="myTemlist.pageSize" :page-sizes="[10, 20, 50, 100, 300]"
                layout="total, sizes, prev, pager, next, jumper" :total="myTemPageTotal">
              </el-pagination>
            </el-col>
          </table-tem>
          <!-- 表格和分页结束 -->
        </el-dialog>
        <!-- 插入参数（内层弹窗） -->
        <el-dialog :title="dialogInsertValTitle" width="500px" :visible.sync="dialogInsertVal" append-to-body>
          <el-form :model="insertVal.formData" :rules="insertVal.formRule" ref="insertVal" label-width="100px">
            <el-form-item label="参数内容" prop="val">
              <el-select v-model="insertVal.formData.val" clearable style="width: 280px" placeholder="请选择参数内容">
                <el-option label="纯数字（d）" value="d"></el-option>
                <el-option label="字母数字（w）" value="w"></el-option>
                <el-option label="金额（$）" value="$"></el-option>
                <el-option label="中文汉字（c）" value="c"></el-option>
                <el-option label="时间（hh:mm:ss）" value="hh:mm:ss"></el-option>
                <el-option label="日期（MM-DD）" value="MM-DD"></el-option>
                <el-option label="日期（YYYY-MM-DD）" value="YYYY-MM-DD"></el-option>
                <el-option label="日期时间(YYYY-MM-DD hh:mm:ss)" value="YYYY-MM-DD hh:mm:ss"></el-option>
                <el-option label="日期时间（MM-DD hh:mm:ss）" value="MM-DD hh:mm:ss"></el-option>
              </el-select>
            </el-form-item>

            <div v-if="
              insertVal.formData.val == 'd' ||
              insertVal.formData.val == 'w' ||
              insertVal.formData.val == '$' ||
              insertVal.formData.val == 'c'
            ">
              <el-form-item label="长度限制" prop="len">
                <el-radio-group v-model="insertVal.formData.len">
                  <el-radio label="1">可变长度</el-radio>
                  <el-radio label="2">固定长度</el-radio>
                </el-radio-group>
              </el-form-item>
              <template v-if="insertVal.formData.len == '1'">
                <el-form-item label="最小长度" prop="min">
                  <el-input-number v-model="insertVal.formData.min" :min="0"
                    :max="insertVal.formData.val == 'c' ? 4 : 31"></el-input-number>
                  <span>{{
                    insertVal.formData.val != "c"
                      ? "(大小范围在0-31)"
                      : "(大小范围在0-4)"
                  }}</span>
                </el-form-item>
                <el-form-item label="最大长度" prop="max">
                  <el-input-number v-model="insertVal.formData.max" :min="1"
                    :max="insertVal.formData.val == 'c' ? 5 : 32"></el-input-number>
                  <span>{{
                    insertVal.formData.val != "c"
                      ? "(大小范围在1-32)"
                      : "(大小范围在1-5)"
                  }}</span>
                </el-form-item>
              </template>

              <template v-else>
                <el-form-item label="固定长度" prop="fixed">
                  <el-input-number v-model="insertVal.formData.fixed" :min="1"
                    :max="insertVal.formData.val == 'c' ? 5 : 32"></el-input-number>
                  <span>{{
                    insertVal.formData.val == "c"
                      ? "大小范围在1-5"
                      : "大小范围在1-32"
                  }}</span>
                </el-form-item>
              </template>
            </div>
            <el-form-item style="margin-top: 50px">
              <el-button type="primary" @click="insertValOk(dialogInsertValStatus)">提交</el-button>
              <el-button @click="dialogInsertVal = false">取消</el-button>
            </el-form-item>
          </el-form>
        </el-dialog>
        <el-form :model="form" :rules="temFormRules" label-width="80px" style="padding-right: 115px" ref="temForm">
          <el-form-item label="模板名称" prop="name">
            <el-input v-model="form.name" :disabled="editTemDialog_status == 0" style="width: 126%"></el-input>
          </el-form-item>
          <el-form-item label="模板类型" prop="resource">
            <el-radio-group v-model="form.resource" :disabled="editTemDialog_status == 0">
              <el-radio label="1">验证码</el-radio>
              <el-radio label="2">行业通知</el-radio>
              <el-radio label="3">会员营销</el-radio>
            </el-radio-group>
          </el-form-item>
          <span class="tem-title">模板内容</span>
          <span class="tem-subTitle" @click="openTemRule">模板规则</span>
          <el-tabs v-model="form.temFormat" type="card" style="padding-left: 80px">
            <el-tab-pane label="普通模板" name="2" prop="temContent" :disabled="editTemDialog_status == 0">
              <!-- 输入框组件 -->
              <el-button type="primary" @click="shortReset()" class="template-btn-1" style="padding: 9px 21px"
                plain>短链转换</el-button>
              <edit-div ref="divInput1" v-model="form.input1.commonInputVal" id="input1"></edit-div>
              <!-- <div class="common-template-val" ref="input1" contenteditable="true" id="input1" v-html="input1.commonInputVal" @keyup.ctrl.86="paste" @input="changeInput('input1')" @keyup="keyInput('input1',$event)"></div> -->
              <div class="tem-be-careful" style="">
                <div class="tem-font" style="display: inline-block">
                  已输入<span> {{ wordCount1 }} </span>个字，最多可输入450个字
                </div>
                ,
                <div class="tem-font" style="display: inline-block">
                  当前模板 预计发送条数约为<span> {{ numTextMsg1 }} </span>条短信
                </div>
                <p style="font-weight: bold; color: #666; padding-top: 7px">
                  注意点：
                </p>
                <p>
                  1、模板内容中<span class="f-basic">不包含</span>签名，首尾不可有【】。
                </p>
                <p>
                  2、模板内容合法，不能发送房产、发票、移民等国家法律法规严格禁止的内容。
                </p>
                <p>
                  3、如有链接，请将链接地址写于短信模板中，便于核实网站真实合法性
                </p>
              </div>
            </el-tab-pane>
            <el-tab-pane label="变量模板" name="1" prop="temContent" :disabled="editTemDialog_status == 0">
              <el-button type="primary" @click="dialogMyTems = true" class="template-btn-1" plain>我的模板库</el-button>
              <el-button type="primary" style="padding: 9px 21px" v-if="insertTem" @click="openValDialog"
                class="template-btn-2" plain>插入参数</el-button>
              <!-- 输入框组件 -->
              <edit-div ref="divInput2" @click.native="editVal" v-model="form.input2.commonInputVal"
                id="input2"></edit-div>
              <!-- <div class="variable-template-val" ref="input2" contenteditable="true"  v-html="input2.commonInputVal" @input="changeInput('input2')" v-model="input2.commonInputVal"></div> -->
              <div class="tem-be-careful">
                <div class="tem-font" style="display: inline-block">
                  已输入<span> {{ wordCount2 }} </span>个字，最多可输入450个字
                </div>
                ,
                <div class="tem-font" style="display: inline-block">
                  当前模板 预计发送条数约为<span> {{ numTextMsg2 }} </span>条短信
                </div>
                <p style="font-weight: bold; color: #666; padding-top: 7px">
                  注意点：
                </p>
                <p>
                  1、模板内容<span class="f-basic">不包含</span>签名，首尾不可有【】
                </p>
                <p>
                  2、模板内容可手动填写或点击“我的模板库”在您已有的模板基础进行修改（重新创建，之前模板<span class="f-basic">不会被替换</span>）。
                </p>
                <p>
                  3、在模板中插入参数，请将鼠标光标移至填写参数处，点击“<span class="f-basic">插入参数</span>”，系统将根据您选择的参数内容进行<span
                    class="f-basic">自动填写</span>。
                </p>
                <p>
                  4、变量模板<span class="f-basic">最少</span>需添加<span class="f-basic">1 个</span>参数，<span
                    class="f-basic">最多</span>可添加<span class="f-basic">16 个</span>参数，两个参数不可挨在一起，中间至少有一个字符隔开 。
                </p>
                <p>
                  5、模板内容合法，不能发送房产、发票、移民等国家法律法规严格禁止的内容。
                </p>
                <p>
                  6、如有链接，请将链接地址写于短信模板中，便于核实网站真实合法性
                </p>
              </div>
            </el-tab-pane>
          </el-tabs>
          <el-form-item label="申请说明" prop="desc" style="width: 122%">
            <template v-if="form.resource == '1'">
              <el-input rows="4" resize="none" placeholder="用于我公司app用户注册时发送验证码" type="textarea"
                v-model="form.desc"></el-input>
            </template>
            <template v-if="form.resource == '2'">
              <el-input rows="4" resize="none" placeholder="用于行业通知类短信发送" type="textarea" v-model="form.desc"></el-input>
            </template>
            <template v-if="form.resource == '3'">
              <el-input rows="4" resize="none" placeholder="用户会员营销类短信发送" type="textarea" v-model="form.desc"></el-input>
            </template>
            <el-tooltip class="item" effect="light" content="为加快审核速度与模板通过率，请填写申请该模板的原因或说明" placement="top">
              <i style="position: relative; top: -88px; left: 493px" class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="addTemOk(editTemDialog_status)">确 定</el-button>
          <el-button @click="calcelTem()">取 消</el-button>
        </span>
      </el-dialog>
      <!-- 编辑弹框 -->
      <!-- 短链转换 -->
      <el-dialog title="短链转换" :visible.sync="temshortVisible" width="520px">
        <div class="short-box">
          <p class="short-title" style="padding-top: 10px">长网址链接</p>
          <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
            <el-button slot="append" type="primary" icon="el-icon-refresh" @click="transformation()">转换</el-button>
          </el-input>
          <div class="font-sizes font-sizes1">
            <span style="color: red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。
          </div>
          <div class="font-sizes">
            <span style="color: red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
          </div>
        </div>
        <div class="short-box">
          <p class="short-title" style="padding-top: 20px">短网址链接</p>
          <el-input v-model="shortConUrl" class="width-l" :disabled="true">
            <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share">预览</el-button>
          </el-input>
        </div>
        <div style="text-align: right; margin-top: 16px">
          <el-button @click="handleCancles()">取 消</el-button>
          <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="批量导入" :visible.sync="batchShow" width="520px">
        <div style="display: flex; align-items: center">
          <!-- <span>批量导入</span> -->
          <el-upload class="upload-demo" :headers="header" :action="this.API.cpus + 'v3/consumersmstemplate/upload'"
            :file-list="fileList" :on-remove="handleRemove" :on-success="handleSuccess" :limit="1">
            <el-button type="primary">批量导入</el-button>
            <!-- <div slot="tip" class="el-upload__tip">仅支持非变量的行业、营销模版批量导入。</div> -->
          </el-upload>
        </div>
        <div style="margin-top: 10px">
          <a style="margin: 10px; color: #409eff"
            href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/13ed9d887b30bbe4afeba5625c987b92"
            rel="noopener noreferrer">模版下载</a>
        </div>
        <!-- <div style="text-align: right; margin-top: 16px">
          <el-button @click="batchShow = false">取 消</el-button>
          <el-button type="primary" @click="batchQuery()">确 定</el-button>
        </div> -->
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from "@/components/publicComponents/TableTem";
import templateRule from "./TemplateRule";
import editDiv from "./editDiv";
import {
  TEMPLATE_TYPES,
  LABEL_TYPES,
  VALIDATION_RULES,
  REAL_NAME_STATUS,
  REAL_NAME_STATUS_LABELS,
  REAL_NAME_STATUS_COLORS,
  CARRIERS
} from './template.js'
let _ = require("lodash");
export default {
  name: "TemplateManagement",
  components: { TableTem, templateRule, editDiv },
  data() {
    //模板名称验证
    var checkName = (rule, value, callback) => {
      if (this.editTemDialog_status != 1) callback();
      var regs = /^[\S]{1,20}$/;
      if (!regs.test(value)) {
        return callback(new Error("长度在 1 到 20 个字符"));
      } else {
        var reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/g;
        if (!reg.test(value)) {
          return callback(new Error("不允许输入空格等特殊符号"));
        } else {
          this.$api.post(
            this.API.cpus + "consumersmstemplate/validateUserTempExist/",
            { temName: value },
            (res) => {
              if (res.data == "0") {
                callback();
              } else {
                return callback(new Error("此模板已存在"));
              }
            }
          );
        }
      }
    };
    return {
      temshortVisible: false, //短链弹框
      originalUrl: "", //长链接的值
      header: {},
      batchShow: false,
      fileList: [],
      shortConUrl: "", //短连接的值
      shortCode: "", //短链的code码
      isShort: false, //是否短链转换
      name: "TemplateManagement",
      // mostImportant:'',
      insertStatus: false,
      insertTem: true, //插入模板的按钮 的显示
      divInputVal: "", //div输入框的内容
      tabelAlllist: {
        //------发送表格请求的对象
        param: "",
        status: "",
        currentPage: 1, //当前页
        pageSize: 10, //每一页条数
      },
      param: "", //搜索条件--改变监听
      searchPage: {
        //搜索的页数--改变监听
        currentPage: 1,
        pageSize: 10,
      },
      pageTotal: 0, //总共条数
      TemDialogVisible: false, //---添加模板的弹窗
      formAdd: {
        temFormat: "", //模板格式
        temContent: "", //内容
        temType: "", //模板类型
        remark: "", //备注
        temName: "", //模板名
      },
      form: {
        temStatus: "",
        temId: "",
        temFormat: "2",
        name: "",
        desc: "",
        resource: "1",
        input1: {
          //-----普通模板的输入框内容
          commonInputVal: "", //输入框内容
          numTextMsg: "0", //短信条数
          wordCount: "0", //字数
        },
        input2: {
          //-----变量模板的输入框内容
          commonInputVal: "", //输入框内容
          numTextMsg: "0", //短信条数
          wordCount: "0", //字数
        },
      },
      formEmpty: {
        //清空对象
        temId: "",
        temFormat: "2",
        name: "",
        desc: "",
        resource: "1",
        input1: {
          //-----普通模板的输入框内容
          commonInputVal: "", //输入框内容
          numTextMsg: "0", //短信条数
          wordCount: "0", //字数
        },
        input2: {
          //-----变量模板的输入框内容
          commonInputVal: "", //输入框内容
          numTextMsg: "0", //短信条数
          wordCount: "0", //字数
        },
      },
      temFormRules: {
        //添加模板内容的验证
        name: [
          { required: true, validator: checkName, trigger: "blur" },
          // { pattern:/^[A-Za-z0-9\u4e00-\u9fa5]+$/ ,message: '不允许输入空格等特殊符号' },
          // { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' },
        ],
        resource: [
          { required: true, message: "请选择模板类型", trigger: "change" },
        ],
        temContent: [
          { required: true, message: "请输入模板内容", trigger: "blur" },
        ],
        desc: [
          {
            min: 1,
            max: 200,
            message: "长度在 1 到 200个字符",
            trigger: "blur",
          },
        ],
      },
      //插入参数弹窗
      insertVal: {
        formData: {
          val: "",
          len: "1",
          min: "",
          max: "",
          fixed: "",
        },
        formRule: {
          val: [{ required: true, message: "请选择参数", trigger: "change" }],
          min: [{ required: true, message: "请输入最小长度", trigger: "blur" }],
          max: [{ required: true, message: "请输入最大长度", trigger: "blur" }],
          fixed: [
            { required: true, message: "请输入固定长度", trigger: "blur" },
          ],
        },
      },
      tableDataObj: {
        //列表数据
        tableData: [],
      },
      myTemlist: {
        //   templateFormat:1,
        tempName: "",
        temFormat: 1,
        isDisable: 1,
        status: 2,
        param: "",
        currentPage: 1,
        pageSize: 10,
      },
      editVarCount: "",
      myTemSearch: "", //搜索我的模板
      myTemPageTotal: "", //总条数
      myTemTableDataObj: {
        //我的模板列表数据
        loading2: true,
        tableData: [],
        tableLabel: [
          {
            prop: "temId",
            showName: "ID",
            fixed: false,
          },
          {
            prop: "temType",
            showName: "模板类型",
            fixed: false,
            width: "150",
            formatData: function (val) {
              let type = "";
              if (val == 1) {
                type = "验证码";
              } else if (val == 2) {
                type = "通知";
              } else if (val == 3) {
                type = "营销推广";
              }
              return type;
            },
          },
          {
            prop: "temName",
            showName: "模板名称",
            fixed: false,
          },
        ],
        // 表头--折叠的
        tableLabelExpand: [
          {
            prop: "temContent",
            showName: "模板内容",
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: true, //是否是折叠的
          isDefaultExpand: true, //默认打开折叠
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "选取",
            type: "",
            size: "mini",
            optionMethod: "getIt",
            icon: "el-icon-success",
          },
        ],
      },
      dialogTemRule: false,
      dialogMyTems: false, //我的模板库弹窗
      dialogInsertVal: false, //插入参数弹窗
      dialogInsertValStatus: "1", //参数弹窗的状态---1：新增   0：编辑
      insertDom: "", //添加的dom
      getCurIndex: "", //光标位置
      editTemDialog_status: 1, //模板是否是新增---1为新增，0为编辑
    };
  },
  computed: {

    //普通模板计数
    numTextMsg1: function () {
      //1短信条数
      let numMsg = 0;
      //字数和短信条数的显示
      if (this.wordCount1 == 0) {
        numMsg = 0;
      } else if (
        parseInt(this.wordCount1) <= 70 &&
        parseInt(this.wordCount1) > 0
      ) {
        numMsg = 1;
      } else {
        numMsg = Math.ceil(parseInt(this.wordCount1) / 67);
      }
      return numMsg;
    },
    wordCount1: function () {
      //1字数
      if (this.form.input1.commonInputVal == " ") {
        return 0;
        // console.log("computed111",this.form.input1.commonInputVal)
      } else {
        if (this.form.input1.commonInputVal.indexOf(" ") != -1) {
          if ((this.form.input1.commonInputVal, length == 2)) {
            // console.log("computed222",this.form.input1.commonInputVal)
            return 1;
          } else {
            // console.log("computed333",this.form.input1.commonInputVal)
            if (this.form.input1.commonInputVal.indexOf("&nbsp;" != -1))
              return this.countNum1(
                this.form.input1.commonInputVal.replace(/&nbsp;/g, "a")
              );
            return this.form.input1.commonInputVal.length - 1;
          }
        }
        // console.log("computed444",this.form.input1.commonInputVal)
        return this.countNum1(this.form.input1.commonInputVal);
      }
    },
    numTextMsg2: function () {
      //2短信条数
      let numMsg = 0;
      //字数和短信条数的显示
      if (this.wordCount2 == 0) {
        numMsg = 0;
      } else if (
        parseInt(this.wordCount2) <= 70 &&
        parseInt(this.wordCount2) > 0
      ) {
        numMsg = 1;
      } else {
        numMsg = Math.ceil(parseInt(this.wordCount2) / 67);
      }
      return numMsg;
    },
    wordCount2: function () {
      //2字数
      if (this.form.input2.commonInputVal == " ") {
        return 0;
      } else {
        let reg = /^<span/g;
        let reg1 = /<\/span> $/g;
        if (
          this.form.input2.commonInputVal.length == 2 &&
          this.form.input2.commonInputVal.indexOf(" ") != -1
        ) {
          return 1;
        }
        if (
          reg.test(this.form.input2.commonInputVal) &&
          reg1.test(this.form.input2.commonInputVal)
        ) {
          return 1;
        }
        return this.countNum1(this.form.input2.commonInputVal);
      }
    },
    dialogInsertValTitle: function () {
      //插入参数弹窗的标题
      return this.dialogInsertValStatus == "1" ? "插入参数" : "编辑参数";
    },
    editTemDialog_title: function () {
      //模板内容弹窗的标题
      return this.editTemDialog_status == "1" ? "新增短信模板" : "编辑短信模板";
    },
  },
  watch: {
    "insertVal.formData.val": {
      handler: function () {
        if (
          this.insertVal.formData.val == "c" &&
          (this.insertVal.formData.min > 5 || this.insertVal.formData.max > 5)
        ) {
          this.insertVal.formData.min = 0;
          this.insertVal.formData.max = 1;
        }
      },
    },
    //监听变量模板的内容变化(主要控制两个参数不能挨着)
    "form.input2.commonInputVal": {
      handler: function (newVal, oldVal) {
        var reg = /<\/span><span/g;
        if (reg.test(newVal)) {
          this.$message({
            type: "warning",
            message: "两个参数不可挨在一起，中间至少有一个字符隔开!",
          });
          this.$refs.divInput2.$refs.input2.innerHTML = oldVal;
          this.form.input2.commonInputVal = oldVal;
          //    this.mostImportant=oldVal
          //    this.$refs.divInput2.$refs.input2.focus();//自动聚焦
        }
        // this.form.input2.commonInputVal=this.form.input2.commonInputVal+''
      },
      immediate: true,
    },
    /**---------------监听参数弹窗是否开启关闭------------- */
    dialogInsertVal(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.insertVal.resetFields(); //置空参数弹窗
        this.insertVal.formData.val = ""; //初始参数弹窗
        this.insertVal.formData.len = "1";
        this.insertVal.formData.min = "";
        this.insertVal.formData.max = "";
        this.insertVal.formData.fixed = "";
      }
    },
    /**----------监听添加模板弹窗是否关闭------------- */
    TemDialogVisible(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.temForm.resetFields();
        Object.assign(this.form, this.formEmpty);
        // this.addVarCount=0;
        this.editVarCount = "";
        this.isShort = false;
        this.$refs.divInput2.$refs.input2.innerHTML = "";
        this.$refs.divInput1.$refs.input1.innerHTML = "";
      }
    },
    /**---------监听模板的查询条件是否改变--------------- */
    param() {
      this.tabelAlllist.param = this.param;
      this.tabelAlllist.currentPage = 1;
      this.tabelAlllist.pageSize = 10;
      this.getCompany();
    },
    /**-------监听我的模板查询条件是否改变------------- */
    myTemSearch() {
      this.myTemlist.param = this.myTemSearch;
      this.myTemlist.currentPage = 1;
      this.myTemlist.pageSize = 10;
      this.getMyTem_delay();
    },
    /**-----------监听我的模板库 弹窗是否关闭(将内容清空和选择page置为初始值) */
    dialogMyTems() {
      this.myTemSearch = "";
      this.myTemlist.param = "";
      this.myTemlist.currentPage = 1;
      this.myTemlist.pageSize = 10;
      this.getMyTemTableData();
    },
    // /**-----------监听插入参数弹框，为新增还是编辑 */
    // dialogInsertValStatus(newVal,oldVal){s
    //     if(newVal=="1"){
    //         this.insertVal.formData.min='4'
    //     }
    // }
    //短连接弹框是否关闭
    temshortVisible(val) {
      if (val == false) {
        this.originalUrl = ""; //长链接的值
        this.shortConUrl = ""; //短连接的值
      }
    },
  },
  methods: {
    // 获取实名状态标签
    getRealNameStatusLabel(status) {
      return REAL_NAME_STATUS_LABELS[status] || '未知'
    },

    // 获取实名状态颜色
    getRealNameStatusColor(status) {
      return REAL_NAME_STATUS_COLORS[status] || 'info'
    },

    // 获取运营商状态颜色（用于图标显示）
    getCarrierStatusColor(status) {
      if (status === REAL_NAME_STATUS.PENDING) {
        return '#D9D9D9' // 黄色 - 报备中
      } else if (status === REAL_NAME_STATUS.APPROVED) {
        return '#1890FF' // 蓝色 - 通过
      } else {
        return '#D9D9D9' // 灰色 - 未知状态
      }
    },

    // 获取状态数据属性（用于CSS选择器）
    getStatusDataAttr(status) {
      if (status === REAL_NAME_STATUS.PENDING) {
        return '0'
      } else if (status === REAL_NAME_STATUS.APPROVED) {
        return '1'
      } else {
        return 'unknown'
      }
    },
    calcelTem() {
      this.$refs.divInput1.$refs.input1.innerHTML = "";
      this.$refs.divInput2.$refs.input2.innerHTML = "";
      this.TemDialogVisible = false;
    },
    clickAddTem() {
      this.$router.push({ path: "/NewCreateTemplate", query: { i: "1" } });

      // this.form.input1.commonInputVal=' ';
      // this.form.input2.commonInputVal=' ';
      // this.TemDialogVisible=true;
      // this.editTemDialog_status=1;
      //点击新增模板的时候-----清空模板内容
    },
    /* --------------- 列表展示 ------------------*/
    getTableData() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      let formDatas = this.tabelAlllist;
      this.$api.post(
        this.API.cpus + "v3/consumersmstemplate/page",
        formDatas,
        (res) => {
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.loading2 = false;
          this.pageTotal = res.total;
          // for(var i = 0 ; i<res.records.length ; i++){
          //     if(res.records[i].temFormat==2){
          //         this.tableDataObj.tableData[i].temContent = res.records[i].initialContent
          //     }
          // }
        }
      );
    },
    handleSuccess(res) {
      // console.log(res)
      if (res.code == 200) {
        this.$message({
          type: "success",
          duration: "2000",
          message: "导入成功",
        });
        this.batchShow = false;
        this.fileList = [];
        this.getTableData();
      } else {
        this.$message({
          type: "error",
          duration: "2000",
          message: res.msg,
        });
      }
    },
    handleRemove() {
      this.fileList = [];
    },
    // 批量导入
    batchQu() {
      this.batchShow = true;
    },
    //导出
    exportNums1() {
      let obj = {
        param: this.param,
        productType: 12,
        status: this.tabelAlllist.status,
      };
      this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        if (res.code == 200) {
          this.$message({
            type: "success",
            duration: "2000",
            message: "已加入到文件下载中心!",
          });
        } else {
          this.$message({
            type: "error",
            duration: "2000",
            message: res.msg,
          });
        }
      });
    },
    /**-----------------列表展示我的模板------------- */
    getMyTemTableData() {
      //获取列表数据
      this.myTemTableDataObj.loading2 = true;
      let formDatas = this.myTemlist;
      this.$api.post(
        this.API.cpus + "v3/consumersmstemplate/selectClientTemplate",
        formDatas,
        (res) => {
          this.myTemTableDataObj.tableData = res.records;
          this.myTemTableDataObj.loading2 = false;
          this.myTemPageTotal = res.total;
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.getTableData();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.getTableData();
    },
    handleSizeChangeMyTem(size) {
      //分页每一页的有几条
      this.myTemlist.pageSize = size;
      this.getMyTemTableData();
    },
    handleCurrentChangeMyTem: function (currentPage) {
      //分页的第几页
      this.myTemlist.currentPage = currentPage;
      this.getMyTemTableData();
    },
    openValDialog() {
      //点击“插入参数”按钮，打开弹窗--添加参数的弹窗
      if (this.insertStatus) {
        this.$message({
          message: '"两个参数不可挨在一起，中间至少有一个字符隔开。"',
          type: "warning",
        });
      } else {
        this.dialogInsertVal = true;
        this.dialogInsertValStatus = 1;
        this.$refs.divInput2.$refs.input2.focus(); //自动聚焦
        let selection = getSelection();
        // 设置最后光标对象
        this.getCurIndex = selection.getRangeAt(0);
      }
    },
    insertValOk(val) {
      //提交参数--  val=0为编辑   val=1为新增
      this.$refs.insertVal.validate((valid) => {
        if (valid) {
          let showVal = ""; //显示在输入框中的参数
          if (
            this.insertVal.formData.val == "$" ||
            this.insertVal.formData.val == "c" ||
            this.insertVal.formData.val == "d" ||
            this.insertVal.formData.val == "w"
          ) {
            if (this.insertVal.formData.len == 1) {
              if (this.insertVal.formData.min > this.insertVal.formData.max) {
                this.$message({
                  message: '"最小长度"不能大于"最大长度"',
                  type: "warning",
                });
                return false;
              } else if (
                this.insertVal.formData.min == this.insertVal.formData.max
              ) {
                this.$message({
                  message: '"最小长度"不能等于"最大长度"',
                  type: "warning",
                });
                return false;
              } else {
                showVal =
                  this.insertVal.formData.val +
                  this.insertVal.formData.min +
                  "-" +
                  this.insertVal.formData.max;
              }
              // if(this.insertVal.formData.val=="$" && this.insertVal.formData.min <=4){
              //     this.$message({
              //         message: '"金额最小长度"不能小于4',
              //         type: 'warning'
              //     })
              //     return false
              // }
            } else {
              showVal =
                this.insertVal.formData.val + this.insertVal.formData.fixed;
            }
          } else {
            showVal = this.insertVal.formData.val;
          }
          if (val == 1) {
            //新增--参数
            let regx = /<[^>]+>([^<]+)<\/[^>]+>/g;
            let result1 = this.form.input2.commonInputVal.match(regx); //取出含标签的内容
            //判断参数个数--不能超过32
            let countNum = [];
            let addVarCount = 1;
            if (result1) {
              if (result1.length >= 16) {
                this.$message({
                  message: '"添加的参数个数不能超过16个"',
                  type: "warning",
                });
                return false;
              }
              // result1.forEach((item, index)=>
              //     countNum.push(item.substr(item.indexOf('|')-1,1))//获取所有的var变量后的数字
              // )
              for (let i = 1; i < result1.length + 1; i++) {
                countNum.push(i);
              }
            }
            if (countNum.length > 0) {
              let maxNum = Math.max.apply(Math, countNum);
              addVarCount = maxNum + 1;
            } else {
              addVarCount = 1;
            }
            let varCount = addVarCount;
            let insertVal =
              '<span style="display:-moz-inline-box;display:inline-block;" contenteditable="false" data="' +
              showVal +
              '" constVar="' +
              varCount +
              '"  class="text-adaNum">{var' +
              varCount +
              "|" +
              showVal +
              "}</span>";
            let selection = window.getSelection
              ? window.getSelection()
              : document.selection;

            if (this.getCurIndex) {
              // 存在最后光标对象，选定对象清除所有光标并添加最后光标还原之前的状态
              selection.removeAllRanges();
              selection.addRange(this.getCurIndex);
            }
            this.insertHtmlAtCaret(insertVal);
          } else {
            //编辑--参数
            if (this.insertDom) {
              this.insertDom.setAttribute("data", showVal);
              this.insertDom.innerHTML =
                "{var" + this.editVarCount + "|" + showVal + "}";
            }
          }

          var a = this.$refs.divInput2.$refs.input2.innerHTML.split("|");
          var str = "";
          for (var i = 1; i < a.length + 1; i++) {
            str += a[i - 1].replace(/var\d[0-9]{0,1}/g, "var" + i + "") + "|";
          }
          this.form.input2.commonInputVal = str.slice(0, str.length - 1); //值更改 --方便计数
          this.dialogInsertVal = false; //关闭弹窗
        } else {
          return false;
        }
      });
    },
    //判断模板内容里面前后是否有签名
    checkSignature(value) {
      let ret = false;
      let beg = value.indexOf("【");
      let end = value.indexOf("】");
      let lastbeg = value.lastIndexOf("【");
      let lastend = value.lastIndexOf("】");
      let valLength = value.length;
      //前有签名
      if (beg > -1 && end > -1 && end > beg && beg == 0) {
        ret = false;
      } else if (
        lastbeg > -1 &&
        lastend > -1 &&
        lastend > lastbeg &&
        lastend == valLength - 1
      ) {
        //后有签名
        ret = false;
      } else {
        ret = true;
      }
      return ret;
    },
    //添加编辑模板--确定
    addTemOk(val) {
      this.$refs.temForm.validate((valid) => {
        if (valid) {
          let aa = false;
          let addForm = {}; //接收传参的对象
          if (this.form.temFormat == "2") {
            //全文模板
            //内容不可为空
            if (this.form.input1.commonInputVal.trim().length != 0) {
              if (this.checkSignature(this.form.input1.commonInputVal)) {
                aa = true;
                addForm.initialContent = this.form.input1.commonInputVal
                  .replace(/<div>/g, "")
                  .replace(/<\/div>/g, "")
                  .replace(/&amp;/g, "")
                  .replace(/&gt;/g, "")
                  .replace(/&lt;/g, "")
                  .replace(/undefined/g, "");
              } else {
                this.$message({
                  message: "模板内容前后不可有签名！",
                  type: "warning",
                });
              }
            } else {
              this.$message({
                message: "全文模板内容不可为空！",
                type: "warning",
              });
            }
          } else if (this.form.temFormat == "1") {
            //变量模板
            //判断是否有签名
            this.form.input2.commonInputVal =
              this.$refs.divInput2.$refs.input2.innerHTML; //值更改 --方便计数
            if (this.checkSignature(this.form.input2.commonInputVal)) {
              aa = true;
              addForm.initialContent =
                this.$refs.divInput2.$refs.input2.innerHTML
                  .replace(/<div>/g, "")
                  .replace(/<\/div>/g, "")
                  .replace(/&amp;/g, "")
                  .replace(/&gt;/g, "")
                  .replace(/&lt;/g, "")
                  .replace(/undefined/g, "");
              let regx = /<[^>]+>([^<]+)<\/[^>]+>/g;
              let result1 = [];
              result1 = addForm.initialContent.match(regx); //取出含标签的内容
              //限制参数个数
              if (!result1) {
                this.$message({
                  message: '"变量模板至少添加一个参数!"',
                  type: "warning",
                });
                return false;
              } else if (result1.length > 32) {
                this.$message({
                  message: '"变量模板参数不能超过32个"',
                  type: "warning",
                });
                return false;
              }
              if (result1) {
                let result = [];
                result1.forEach((item, index) =>
                  result.push(
                    item.match(/>([^<]+)</)[1].replace(/var[1-9]\d?[|]/gi, "")
                  )
                ); //数组形式取出标签里面的内容
                addForm.text = result.join(","); //数组转换成字符串
              }
            } else {
              this.$message({
                message: "模板内容前后不可有签名！",
                type: "warning",
              });
            }
          }
          addForm.temFormat = this.form.temFormat;
          addForm.temName = this.form.name;
          addForm.temType = this.form.resource;
          addForm.remark = this.form.desc;
          //判断是否短链转换
          if (this.isShort) {
            addForm.shortCode = this.shortCode;
          } else {
            addForm.shortCode = "";
          }
          if (val == 1 && aa == true) {
            //新增模板
            //发送请求
            var a = this.$refs.divInput2.$refs.input2.innerHTML.split("|");
            var str = "";
            for (var i = 1; i < a.length + 1; i++) {
              str += a[i - 1].replace(/var\d[0-9]{0,1}/g, "var" + i + "") + "|";
            }
            if (addForm.temFormat == "1") {
              addForm.initialContent = str.slice(0, str.length - 1);
            }
            if (addForm.initialContent.indexOf("</span><span") == -1) {
              // var ch = "&nbsp;";
              // var regch = "/"+ch+"/g";
              addForm.initialContent = addForm.initialContent
                .replace(/<div>/g, "")
                .replace(/<\/div>/g, "")
                .replace(/&nbsp;/g, " ")
                .replace(/&amp;/g, "")
                .replace(/&gt;/g, "")
                .replace(/&lt;/g, "")
                .replace(/undefined/g, "");
              this.$confirms.confirmation(
                "post",
                "确认新增模板？",
                this.API.cpus + "consumersmstemplate/template/add",
                addForm,
                (res) => {
                  this.getTableData();
                  this.TemDialogVisible = false; //关闭弹窗
                  this.$refs.divInput1.$refs.input1.innerHTML = "";
                  this.$refs.divInput2.$refs.input2.innerHTML = "";
                }
              );
            } else {
              this.$message({
                message: '"两个参数不可挨在一起，中间至少有一个字符隔开。"',
                type: "warning",
              });
            }
          } else if (val == 0) {
            //编辑模板
            //发送请求
            addForm.temId = this.form.temId;
            //判断是否短链转换
            if (this.isShort) {
              addForm.shortCode = this.shortCode;
            } else {
              addForm.shortCode = "";
            }
            if (addForm.initialContent.indexOf("</span><span") == -1) {
              // if(this.form.temStatus=="3")addForm.temStatus=1;
              addForm.temStatus = this.form.temStatus;
              if (addForm.temFormat == "1") {
                var a = this.$refs.divInput2.$refs.input2.innerHTML.split("|");
                var str = "";
                for (var i = 1; i < a.length + 1; i++) {
                  str +=
                    a[i - 1].replace(/var\d[0-9]{0,1}/g, "var" + i + "") + "|";
                }
                addForm.initialContent = str.slice(0, str.length - 1);
              }
              // addForm.temId=this.form.temId
              addForm.initialContent = addForm.initialContent
                .replace(/<div>/g, "")
                .replace(/<\/div>/g, "")
                .replace(/&nbsp;/g, " ")
                .replace(/&amp;/g, "")
                .replace(/&gt;/g, "")
                .replace(/&lt;/g, "")
                .replace(/undefined/g, "");
              this.$confirms.confirmation(
                "put",
                "确认编辑模板？",
                this.API.cpus + "consumersmstemplate/template/update",
                addForm,
                (res) => {
                  this.getTableData();
                  this.TemDialogVisible = false; //关闭弹窗
                  this.$refs.divInput1.$refs.input1.innerHTML = "";
                  this.$refs.divInput2.$refs.input2.innerHTML = "";
                }
              );
            } else {
              this.$message({
                message: '"两个参数不可挨在一起，中间至少有一个字符隔开。"',
                type: "warning",
              });
            }
          }
        } else {
          return false;
        }
      });
    },
    //编辑参数弹窗--打开
    editVal(e) {
      this.dialogInsertValStatus = 0; //弹窗是编辑
      let dom = e.target;
      let val = dom.getAttribute("data");
      this.editVarCount = dom.getAttribute("constVar");
      if (val) {
        this.insertDom = dom;
        let valSame = val;
        let regVar1 = /\-/g; //可变长度--
        let regVar2 = /\-/g; //固定长度--
        let regVar3 = /(\w)\1{1,}/g;
        let regFirstVar = /[d|w|$|c]/g; //参数首字母匹配
        let regIsNum = /[^0-9]/g; //验证数字
        let firstVar; //变量的首字母
        let fixLen; //固定长度的数字
        let variableLen1; //可变长度1
        let variableLen2; //可变长度2
        let formatDate; //日期格式
        if (!regVar3.test(val)) {
          if (!regVar2.test(valSame)) {
            //固定长度
            firstVar = valSame.match(regFirstVar)[0]; //首字母
            fixLen = valSame.replace(/[^0-9]/gi, ""); //固定位数的数字
            this.insertVal.formData.val = firstVar;
            this.insertVal.formData.len = "2"; //固定长度-单选
            this.insertVal.formData.fixed = fixLen; //固定的长度
            this.dialogInsertVal = true; //插入参数弹窗--打开
            this.dialogInsertValStatus = 0; //弹窗是编辑
          } else if (regVar1.test(val)) {
            //可变长度
            firstVar = val.match(regFirstVar)[0]; //首字母
            variableLen1 = val.split("-")[0].replace(/[^0-9]/gi, ""); //可变长度1
            variableLen2 = val.split("-")[1].replace(/[^0-9]/gi, ""); //可变长度2
            this.insertVal.formData.val = firstVar;
            this.insertVal.formData.len = "1"; //可变长度-单选
            this.insertVal.formData.min = variableLen1; //可变长度的min
            this.insertVal.formData.max = variableLen2; //可变长度的max
            this.dialogInsertVal = true; //插入参数弹窗--打开
            this.dialogInsertValStatus = 0; //弹窗是编辑
          }
        } else {
          this.insertVal.formData.val = val;
        }

        this.dialogInsertVal = true; //插入参数弹窗--打开
        this.dialogInsertValStatus = 0; //弹窗是编辑
      }
    },
    /**---------插入数据----------- */
    insertHtmlAtCaret(html) {
      let sel, range;
      if (window.getSelection) {
        // IE9 and non-IE
        sel = window.getSelection();
        if (sel.getRangeAt && sel.rangeCount) {
          range = sel.getRangeAt(0);
          range.deleteContents();
          // Range.createContextualFragment() would be useful here but is
          // non-standard and not supported in all browsers (IE9, for one)
          let el = document.createElement("div");
          el.innerHTML = html;
          let frag = document.createDocumentFragment(),
            node,
            lastNode;
          while ((node = el.firstChild)) {
            lastNode = frag.appendChild(node);
          }
          range.insertNode(frag); //设置选择范围的内容为插入的内容
          // Preserve the selection
          if (lastNode) {
            range = range.cloneRange();
            range.setEndAfter(lastNode); //把编辑光标放到我们插入内容之后
            range.setStartAfter(lastNode); //设置光标位置为插入内容的末尾
            range.collapse(true); //移动光标位置到末尾
            sel.removeAllRanges(); //移出所有选区
            sel.addRange(range); //添加修改后的选区
          }
        }
      } else if (document.selection && document.selection.type != "Control") {
        // IE < 9
        document.selection.createRange().pasteHTML(html);
      }
    },
    getCaretPosition(oField) {
      //获取光标位置
      var iCaretPos = 0;
      var doc = oField.ownerDocument || oField.document;
      var win = doc.defaultView || doc.parentWindow;
      var sel;
      if (typeof win.getSelection != "undefined") {
        sel = win.getSelection();
        if (sel.rangeCount > 0) {
          var range = win.getSelection().getRangeAt(0);
          var preCaretRange = range.cloneRange();
          preCaretRange.selectNodeContents(oField);
          preCaretRange.setEnd(range.endContainer, range.endOffset);
          iCaretPos = preCaretRange.toString().length;
        }
      } else if ((sel = doc.selection) && sel.type !== "Control") {
        var textRange = sel.createRange();
        var preCaretTextRange = doc.body.createTextRange();
        preCaretTextRange.moveToElementText(oField);
        preCaretTextRange.setEndPoint("EndToEnd", textRange);
        iCaretPos = preCaretTextRange.text.length;
      }
      return iCaretPos;
    },
    //监听字数
    countNum1(val) {
      let len = 0; //字数
      if (val != null) {
        let regVar1 =
          /[{](var[1-9]\d?[|])[d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g; //可变长度
        let regVar2 = /[{](var[1-9]\d?[|])[d|w|$|c]([1-9]\d?)[}]/g; //固定长度
        if (regVar1.test(val) || regVar2.test(val)) {
          let regVar1Arr = val.match(regVar1);
          let regVar2Arr = val.match(regVar2);
          if (regVar1Arr) {
            //如果是可变长度类型的参数，要取出长度
            for (let i = 0; i < regVar1Arr.length; i++) {
              let variableLen = Number(
                regVar1Arr[i].split("-")[1].replace(/[^0-9]/gi, "")
              );
              len += variableLen;
            }
          }
          if (regVar2Arr) {
            for (let i = 0; i < regVar2Arr.length; i++) {
              let fixedLen = Number(regVar2Arr[i].match(/\d+/g)[1]); //字符串中找出数字，组中的第二个数字为固定长度
              len += fixedLen;
            }
          }
          val = val.replace(regVar1, "").replace(regVar2, "");
        }
        // .replace(/\{/gi,'').replace(/\}/gi,'')

        val = val
          .replace(/<br>/g, "")
          .replace(/\n/g, "")
          .replace(/\r/g, "")
          .replace(/<\/?[^>]*>/g, "")
          .replace(/&nbsp;/g, " ")
          .replace(/var[1-9]\d?[|]/gi, "");
        len += val.length;

        return len;
      } else {
        return (len = 0);
      }
    },
    //分割模板内容的参数 验证参数间是否有间隔
    arrSplit(contentString) {
      var arrs = [];
      var a = contentString.split(/<[^>]+>([^<]+)<\/[^>]+>/g);
      if (a) {
        for (var i = 0; i < a.length - 1; i++) {
          if (i != 0) {
            arrs[arrs.length] = a[i].split(/<[^>]+>([^<]+)<\/[^>]+>/g)[0];
          }
        }
      }
      return arrs;
    },
    //监听字数
    countNum(dom) {
      let recentVal = ""; //目前的内容
      let len = 0; //字数
      let smsNum = 0; //短信条数
      recentVal = this.$refs[dom].innerText || this.$refs[dom].textContent;
      let regVar1 = /[{][d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g; //可变长度
      let regVar2 = /[{][d|w|$|c]([1-9]\d?)[}]/g; //固定长度
      if (regVar1.test(recentVal) || regVar2.test(recentVal)) {
        let regVar1Arr = recentVal.match(regVar1);
        let regVar2Arr = recentVal.match(regVar2);
        if (regVar1Arr) {
          //如果是长度类型的参数，要取出长度
          for (let i = 0; i < regVar1Arr.length; i++) {
            let variableLen = Number(
              regVar1Arr[i].split("-")[1].replace(/[^0-9]/gi, "")
            );
            len += variableLen;
          }
        }
        if (regVar2Arr) {
          for (let i = 0; i < regVar2Arr.length; i++) {
            let fixedLen = Number(regVar2Arr[i].replace(/[^0-9]/gi, ""));
            len += fixedLen;
          }
        }
        recentVal = recentVal.replace(regVar1, "").replace(regVar2, "");
      }
      recentVal = recentVal
        .replace(/<br>/g, "")
        .replace(/\n/g, "")
        .replace(/\r/g, "")
        .replace(/<\/?[^>]*>/g, "");
      if (recentVal.length >= 450) {
        //超过450个字字数截取
        this.$refs[dom].innerHTML = recentVal.substr(0, 450); //截取后文字内容放在里面--由于有插入的标签内容，所以使用innerHTML
        this.keepLastIndex(this.$refs[dom]); //光标放在最后
        len = 450;
      } else {
        len += recentVal.length;
      }
      //字数和短信条数的显示
      if (len == 0) {
        smsNum = 0;
      } else if (parseInt(len) <= 70 && parseInt(len) > 0) {
        smsNum = 1;
      } else {
        smsNum = Math.floor((parseInt(len) - 70) / 67) + 1;
      }
      if (dom == "input1") {
        this.form.input1.wordCount = len;
        this.form.input1.numTextMsg = smsNum;
      } else if (dom == "input2") {
        this.form.input2.wordCount = len;
        this.form.input2.numTextMsg = smsNum;
      }
      let num = {};
      num.wordCount = len;
      num.numTextMsg = smsNum;
      return num;
    },
    // changeInput(input){//监听输入框---手动输入内容变化
    //     // this.$refs[input].innerHTML
    //     console.log(event.target)
    //     event.target.innerText.replace(/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/gi,'');
    //     this.countNum(input)//计数
    // },
    //--------------操作栏的按钮--------------------
    handelOptionButton: function (val) {
      if (val.methods == "getIt") {
        //----------------我的模板库里的“选取模板”
        let temContent = val.row.initialContent; //获取模板内容
        this.$refs.divInput2.$refs.input2.innerHTML = temContent;
        this.form.input2.commonInputVal = temContent;
        this.dialogMyTems = false; //关闭弹窗
      }
    },
    //编辑模板
    editTem(index, val) {
      let rouer = {
        path: "/NewCreateTemplate",
        query: { param: val.temId },
      };
      this.$router.push(
        rouer,
        () => { },
        (e) => {
          console.log(e, "e");
        }
      );

      // this.TemDialogVisible = true;//打开弹窗
      // this.editTemDialog_status=0;//模板状态
      // //编辑框里赋值
      // let rowData = val;
      // this.form.name=rowData.temName;
      // this.form.temFormat=rowData.temFormat;
      // this.form.temStatus=rowData.temStatus;
      // this.form.temId = rowData.temId
      // if(rowData.temFormat==2){//全文模板
      //     this.form.input1.commonInputVal=rowData.initialContent.replace(/ /g,"&nbsp;")
      //     setTimeout(() => {
      //         this.$refs.divInput1.$refs.input1.innerHTML=rowData.initialContent.replace(/ /g,"&nbsp;")
      //     }, 1);
      // }else if(rowData.temFormat==1){//变量模板
      //     if(rowData.initialContent){
      //         // var a   = rowData.initialContent.split('|')
      //         // var str = ''
      //         // for(var i = 1;i<a.length+1;i++){
      //         //     str+=a[i-1].replace(/var\d[0-9]{0,1}/g,'var'+i)+"|"
      //         // }
      //         // this.mostImportant = str.slice(0,str.length-1)
      //         // console.log(this.mostImportant)
      //         this.form.input2.commonInputVal= rowData.initialContent;//值更改 --方便计数
      //         setTimeout(() => {
      //             this.$refs.divInput2.$refs.input2.innerHTML=rowData.initialContent.replace(/ /g,"&nbsp;").replace(/&nbsp;contenteditable/g," contenteditable").replace(/&nbsp;constvar/g," constvar").replace(/&nbsp;data/g," data").replace(/&nbsp;class/g," class")
      //         }, 1);
      //     }else {
      //         this.form.input2.commonInputVal = rowData.temContent
      //     }
      // }
      // this.form.resource=rowData.temType;
      // this.form.desc=rowData.remark;
      // this.form.temId=rowData.temId;
    },
    copySmsTemp(val) {
      if (val) {
        this.$confirms.confirmation(
          "post",
          "确定复制当前模板？",
          this.API.cpus + `v3/consumersmstemplate/template/copy`,
          {
            temId: val.temId
          },
          (res) => {
            // this.InquireList()
            if (res.code == 200) {
              this.$router.push({
                path: "/NewCreateTemplate",
                query: { param: res.data },
              });
            }
          }
        );
      }
    },
    // 删除
    delTem(index, val) {
      let temId = val.temId;
      this.$confirms.confirmation(
        "delete",
        "此操作将永久删除该数据, 是否继续？",
        this.API.cpus + "v3/consumersmstemplate/delete/" + temId,
        {},
        (res) => {
          this.getTableData();
        }
      );
    },
    openTemRule() {
      //点击模板规则
      this.dialogTemRule = true;
    },
    //提交
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (val == "insertVal") {
          if (valid) {
            this.dialogInsertVal = false;
          } else {
            console.log("error submit!!");
            return false;
          }
        }
      });
    },
    //点击短链转换
    shortReset() {
      this.temshortVisible = true;
    },
    //短连接转换
    transformation() {
      if (this.originalUrl != "") {
        this.$api.post(
          this.API.cpus + "shortlink/changeUrl",
          { originalUrl: this.originalUrl },
          (res) => {
            if (res.code == 200) {
              this.shortConUrl = res.data.shortLinkUrl;
              this.shortCode = res.data.shortCode;
              this.$message({
                message: "短链接转换成功！",
                type: "success",
              });
            } else {
              this.originalUrl = "";
              this.$message({
                message: res.msg,
                type: "warning",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "长链接不可为空",
          type: "warning",
        });
      }
    },
    //打开新窗口预览
    handlePreview() {
      if (this.shortConUrl != "") {
        window.open("https://" + this.shortConUrl, "_blank");
      } else {
        this.$message({
          message: "短连接为空，无法预览",
          type: "warning",
        });
      }
    },
    //短连接弹框的确定
    shortConDetermine() {
      if (this.shortConUrl != "") {
        this.form.input1.commonInputVal +=
          "&nbsp;" + this.shortConUrl + "&nbsp;";
        this.isShort = true;
        this.temshortVisible = false;
      } else {
        this.$message({
          message: "短链接不可为空",
          type: "warning",
        });
      }
    },
    //短链取消
    handleCancles() {
      this.temshortVisible = false;
      this.isShort = false;
    },
  },
  mounted() {
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.getTableData();
    this.getMyTemTableData();
    this.getCompany = _.debounce(this.getTableData, 500);
    this.getMyTem_delay = _.debounce(this.getMyTemTableData, 500);
  },
  // activated(){
  //     this.getTableData();
  //     this.getMyTemTableData();
  // }
};
</script>

<style lang="less" scoped>
.Templat-box {
  padding: 20px;
}

.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
}

.Templat-matter>p {
  padding: 5px 0;
}

.Templat-set {
  color: #0066cc;
}

.Templat-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Templat-list-header {
  position: absolute;
  font-weight: bold;
  left: 0px;
  top: 12px;
}

.Templat-table {
  padding-bottom: 40px;
}

.Templat-search-fun {
  display: flex;
  justify-content: space-between;
  margin: 18px 0;
  /* padding-right: 0px; */
  /* position: relative;
  height: 40px;
  margin-top: 20px;
  padding-bottom: 6px; */
}

.Templat-search-box {
  /* position: absolute;
  right: 0px; */
}

.tem-font {
  font-size: 12px;
}

.tem-font span {
  color: red;
}

.tem-be-careful {
  width: 127%;
  font-size: 12px;
  color: #999;
  line-height: 20px;
  margin: 10px 0;
}

.tem-title {
  position: absolute;
  left: 32px;
  top: 220px;
}

.tem-title:before {
  position: absolute;
  content: "*";
  color: #f56c6c;
  left: -10px;
}

.tem-subTitle {
  position: absolute;
  left: 32px;
  top: 255px;
  color: #16a589;
  cursor: pointer;
}

.template-btn-1 {
  position: absolute;
  right: -100px;
  top: 5px;
}

.template-btn-2 {
  position: absolute;
  right: -100px;
  top: 50px;
}

/* div输入框 */
.common-template-val,
.variable-template-val {
  min-height: 100px;
  _height: 100px;
  /* border: 1px solid #ddd; */
  border-right: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  outline: none;
  word-wrap: break-word;
  padding: 4px 10px;
  color: #000;
}

.f-basic {
  color: #16a589;
  font-weight: bold;
}

.short-title {
  font-weight: bolder;
  padding-bottom: 5px;
}

.font-sizes {
  padding-top: 2px;
  font-size: 12px;
  color: rgb(163, 163, 163);
}

.font-sizes1 {
  margin-top: 10px;
}
/* 运营商状态容器样式 */
.carrier-status-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  padding: 4px 0;
}

.carrier-status-item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
}

.carrier-icon {
  font-size: 20px !important;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

.carrier-status-item:hover .carrier-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}
</style>
<style>
.el-table .cell.el-tooltip {
  white-space: pre-wrap !important;
}

.myTems .el-dialog__body {
  padding-bottom: 50px !important;
}

.text-adaNum {
  color: #16a589;
  cursor: pointer;
}

.TemDialog .el-tabs__item {
  height: 32px;
  line-height: 32px;
}

.TemDialog .el-tabs__header {
  margin: 0px;
}

.TemDialog .el-tabs__content {
  overflow: inherit;
}

.el-table--small th {
  background: #f5f5f5;
}
</style>
