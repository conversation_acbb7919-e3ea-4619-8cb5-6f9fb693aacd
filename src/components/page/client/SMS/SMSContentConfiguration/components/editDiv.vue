<template>
    <div class="edit-div"
         v-html="innerText"
         contenteditable=true
         :ref="id"
         @keydown="keyupInput($event)"
         @paste="pasteText($event,id)"
         @input="changeText($event)"
         @focus="isLocked = true"
         @blur="isLocked = false"
         >
         
    </div>
</template>
<script>
export default {
    name:'editDiv',
    props:{
        value:{
            type:String,
            default:''
        },
        id:{
            type :String
          },
    },
    data(){
        return {
            innerText: this.value,
            isLocked: false,
        }
    },
    watch:{
        'value'(newVal,oldVal){//失去焦点或者输入框为空的时候
        // console.log(newVal,'newVal');
                if (!this.isLocked || !this.innerText) {
                    this.innerText = this.value;
                    // console.log(this.innerText)
                }
                let newCount=this.countNum(newVal);
                let oldCount=this.countNum(oldVal);
                if(newCount>800 && oldCount<=800){
                    // console.log(newVal)
                    this.$el.innerHTML=oldVal;
                    this.keepLastIndex(this.$refs[this.id]);//光标放在最后
                    this.$emit('input', oldVal);
                }
            },
        'innerText'(newVal,oldVal){
            // console.log(newVal)
            // console.log("123",newVal)
            // if(this.innerText.length==2){
            //     console.log("1231231231231231231")
            // } 
        }
    },
    methods: {
            keyupInput(e){//键盘输入事件---禁止输入{}和换行符
            //   if((e.shiftKey && (e.keyCode==221 || e.keyCode==219) ) || (e.keyCode==13) ) {
            //       console.log(1111111)
            //       window.event.returnValue = false;
            //       e.preventDefault();
            //   } 
            // console.log(this.$el.innerHTML,'e');
                  // e.keyCode==32 空格
                //   let val = this.$el.innerHTML
                if( e.keyCode==13){
                    window.event.returnValue = false;
                    e.preventDefault();
                }
            },
            changeText(e){
                let handelhtml='';
                handelhtml=this.$el.innerHTML;
                this.$emit('input', handelhtml);
            },
            getCaretPosition(oField) {//获取光标位置
            var iCaretPos = 0;
            var doc = oField.ownerDocument || oField.document;
            var win = doc.defaultView || doc.parentWindow;
            var sel;
            if (typeof win.getSelection != "undefined") {
                sel = win.getSelection();
                if (sel.rangeCount > 0) {
                    var range = win.getSelection().getRangeAt(0);
                    var preCaretRange = range.cloneRange();
                    preCaretRange.selectNodeContents(oField);
                    preCaretRange.setEnd(range.endContainer, range.endOffset);
                    iCaretPos = preCaretRange.toString().length;
                }
            } else if ( (sel = doc.selection) && sel.type !== 'Control') {
                var textRange = sel.createRange();
                var preCaretTextRange = doc.body.createTextRange();
                preCaretTextRange.moveToElementText(oField);
                preCaretTextRange.setEndPoint('EndToEnd', textRange);
                iCaretPos = preCaretTextRange.text.length;
            }
            return (iCaretPos);
        },
            pasteText(e,id){
                let handelhtml='';
                handelhtml=this.$el.innerHTML;
                this.$emit('input', handelhtml);
                this.textInit(e);//粘贴的时候--去除粘贴自带的样式，去除特殊样式
                this.keepLastIndex(this.$refs[id]);//光标在最后
            },
            keepLastIndex(obj) {//光标放最后
              console.log(obj)
                if (window.getSelection) {
                    //ie11 10 9 ff safari
                    obj.focus(); //解决ff不获取焦点无法定位问题
                    var range = window.getSelection(); //创建range
                    range.selectAllChildren(obj); //range 选择obj下所有子内容
                    range.collapseToEnd(); //光标移至最后
                } else if (document.selection) {
                    //ie10 9 8 7 6 5
                    var range = document.selection.createRange(); //创建选择对象
                    //var range = document.body.createTextRange();
                    range.moveToElementText(obj); //range定位到obj
                    range.collapse(false); //光标移至最后
                    range.select();
                }
            },
            textInit(e) {//去除粘贴带的样式
                e.preventDefault();
                var text;
                var clp = (e.originalEvent || e).clipboardData;
                if (clp === undefined || clp === null) {
                    console.log(1,text)
                    text = window.clipboardData.getData("text") || "";
                    text=text.replace(/<br>/g, '').replace('{','').replace('}','');//去除特殊字符
                    if (text !== "") {
                        if (window.getSelection) {
                            var newNode = document.createElement("span");
                            newNode.innerHTML = text;
                            window.getSelection().getRangeAt(0).insertNode(newNode);
                        } else {
                            document.selection.createRange().pasteHTML(text);
                        }
                    }
                } else {
                    text = clp.getData('text/plain') || "";
                    console.log(2,text)
                    text = text.replace(/[]/g,"")
                    // console.log(3,text[3])
                    text=text.replace(/<br>/g,'');//去除特殊字符
                    if (text !== "") {
                        document.execCommand('insertText', false, text);
                    }
                } 
            },
                //监听字数
            countNum(val){    
                // console.log(val,val.length)     
                let len=0;//字数
                let regVar1=/[{](var[1-9]\d?[|])[d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g ;//可变长度
                let regVar2=/[{](var[1-9]\d?[|])[d|w|$|c]([1-9]\d?)[}]/g;//固定长度
                if(regVar1.test(val) || regVar2.test(val)) {
                    let regVar1Arr=val.match(regVar1);              
                    let regVar2Arr=val.match(regVar2); 
                    if(regVar1Arr){  //如果是可变长度类型的参数，要取出长度
                        for (let i=0;i<regVar1Arr.length;i++){
                            let variableLen= Number(regVar1Arr[i].split('-')[1].replace(/[^0-9]/ig,""));
                            len+=variableLen;
                        }
                    }
                    if(regVar2Arr) {
                        for(let i=0;i<regVar2Arr.length;i++){  
                            let fixedLen=Number(regVar2Arr[i].match(/\d+/g)[1])//字符串中找出数字，组中的第二个数字为固定长度
                            len+=fixedLen;
                        }
                    }
                    val=val.replace(regVar1,'').replace(regVar2,'')
                }
                val=val.replace(/<br>/g, '').replace(/\n/g, '').replace(/\r/g, '').replace(/<\/?[^>]*>/g, '').replace(/&nbsp;/g,' ').replace(/var[1-9]\d?[|]/ig,'');
                // console.log("EDIT val中固定参数+长度",val,val.length)
                len+=val.length;
                // console.log(len)
                return len;  
            },
    },
}
</script>
<style>
.edit-div {
    min-height: 100px;
     _height: 100px;
     /* border: 1px solid #ddd; */
     border-right:1px solid #e4e7ed;
    border-left:1px solid #e4e7ed;
    border-bottom:1px solid #e4e7ed;
     outline: none;
     word-wrap: break-word;
     padding: 4px 10px;
     color: #000;

}
</style>


