// 模板相关常量定义

// 模板类型
export const TEMPLATE_TYPES = {
    VERIFICATION: 1,  // 验证码
    NOTIFICATION: 2,  // 通知
    MARKETING: 3      // 营销推广
  }
  
  // 模板类型标签映射
  export const TEMPLATE_TYPE_LABELS = {
    [TEMPLATE_TYPES.VERIFICATION]: '验证码',
    [TEMPLATE_TYPES.NOTIFICATION]: '通知',
    [TEMPLATE_TYPES.MARKETING]: '营销推广'
  }
  
  // 模板格式
  export const TEMPLATE_FORMATS = {
    VARIABLE: 1,  // 变量模板
    FULL_TEXT: 2  // 全文模板
  }
  
  // 模板格式标签映射
  export const TEMPLATE_FORMAT_LABELS = {
    [TEMPLATE_FORMATS.VARIABLE]: '变量模板',
    [TEMPLATE_FORMATS.FULL_TEXT]: '全文模板'
  }
  
  // 标签类型
  export const LABEL_TYPES = {
    UNSUBSCRIBE_FORMAT: 55,  // 退订格式
    TYPE_A: 4,
    TYPE_B: 5
  }
  
  // 冷热模板状态
  export const HEAT_STATUS = {
    COLD: 0,  // 冷
    HOT: 1    // 热
  }
  
  // 冷热模板状态标签
  export const HEAT_STATUS_LABELS = {
    [HEAT_STATUS.COLD]: '冷',
    [HEAT_STATUS.HOT]: '热'
  }
  
  // 实名状态
  export const REAL_NAME_STATUS = {
    PENDING: 0,  // 报备中
    APPROVED: 1  // 通过
  }
  
  // 实名状态标签映射
  export const REAL_NAME_STATUS_LABELS = {
    [REAL_NAME_STATUS.PENDING]: '报备中',
    [REAL_NAME_STATUS.APPROVED]: '通过'
  }
  
  // 实名状态颜色映射 (使用运营商图标样式)
  export const REAL_NAME_STATUS_COLORS = {
    [REAL_NAME_STATUS.PENDING]: '#FFA500', // 黄色 - 报备中
    [REAL_NAME_STATUS.APPROVED]: '#1890FF' // 蓝色 - 通过
  }
  
  // 运营商信息
  export const CARRIERS = {
    LT: {
      name: '联通',
      icon: '🔵', // 可以替换为实际的联通图标
      color: '#E60012'
    },
    YD: {
      name: '移动',
      icon: '🟢', // 可以替换为实际的移动图标
      color: '#00A0E9'
    },
    DX: {
      name: '电信',
      icon: '🔴', // 可以替换为实际的电信图标
      color: '#00A651'
    }
  }
  
  // 参数检测状态
  export const PARAM_CHECK_STATUS = {
    ENABLED: false,   // 启用检测
    DISABLED: true    // 禁用检测
  }
  
  // 分页默认配置
  export const PAGINATION_CONFIG = {
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZES: [10, 20, 50, 100],
    DEFAULT_CURRENT_PAGE: 1
  }
  
  // 文件上传配置
  export const UPLOAD_CONFIG = {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ],
    ALLOWED_EXTENSIONS: ['.xlsx', '.xls']
  }
  
  // 表单验证规则
  export const VALIDATION_RULES = {
    TEMPLATE_ID: {
      pattern: /^(?!0)[0-9]+$/,
      message: '模板ID只能输入数字'
    }
  }
  
  export default {
    TEMPLATE_TYPES,
    TEMPLATE_TYPE_LABELS,
    TEMPLATE_FORMATS,
    TEMPLATE_FORMAT_LABELS,
    LABEL_TYPES,
    HEAT_STATUS,
    HEAT_STATUS_LABELS,
    REAL_NAME_STATUS,
    REAL_NAME_STATUS_LABELS,
    REAL_NAME_STATUS_COLORS,
    CARRIERS,
    PARAM_CHECK_STATUS,
    PAGINATION_CONFIG,
    UPLOAD_CONFIG,
    VALIDATION_RULES
  }
  