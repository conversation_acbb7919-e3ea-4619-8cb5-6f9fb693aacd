<template>
  <div class="modern-signature-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button type="text" @click="goBack()" class="back-btn">
            <i class="el-icon-arrow-left"></i>
            返回
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <h1 class="page-title">{{ statusOf }}</h1>
        </div>
        <div class="header-right">
          <el-tag :type="statusOf === '添加签名' ? 'success' : 'warning'" size="medium">
            {{ statusOf === '添加签名' ? '新建签名' : '编辑签名' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 签名配置表单 -->
        <el-form :model="signatureFrom.formData" :rules="signatureFrom.formRule" label-width="120px" ref="signatureFrom"
          class="modern-form">
          <!-- 签名类型选择区域（参照CreateTemplate1风格） -->
          <div class="template-type-section">
            <el-form-item label="签名类型" prop="signatureSubType" class="compact-form-item">
              <el-radio-group v-model="signatureFrom.formData.signatureSubType" class="compact-radio-group">
                <el-radio :label="0" class="compact-radio">
                  <i class="el-icon-check"></i>
                  全称
                  <el-tag type="success" size="mini" style="margin-left: 8px;">推荐</el-tag>
                </el-radio>
                <el-radio :label="1" class="compact-radio">
                  <i class="el-icon-edit"></i>
                  简称
                  <el-tag type="warning" size="mini" style="margin-left: 8px;">需证明</el-tag>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 签名配置卡片（参照CreateTemplate1的template-config-card风格） -->
          <el-card shadow="hover" class="form-card template-config-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-edit-outline"></i>
                签名配置
              </span>
              <div class="card-header-actions">
                <el-button type="text" @click="showImg" class="example-btn">
                  <i class="el-icon-view"></i>
                  查看示例
                </el-button>
                <el-tooltip content="签名将显示在短信开头，用于标识发送方" placement="top">
                  <i class="el-icon-question help-icon"></i>
                </el-tooltip>
              </div>
            </div>

            <!-- 基本信息区域 -->
            <div class="basic-info-section">
              <div class="form-row">
                <el-form-item label="签名内容" prop="signature" class="form-item-compact">
                  <div class="signature-input-wrapper">
                    <span class="signature-bracket left-bracket">【</span>
                    <el-input v-text-cleaner="{ onInput: true, onPaste: true }" v-model="signatureFrom.formData.signature" placeholder="签名限制中文16个字，英文32个字"
                      class="compact-input signature-input" maxlength="32" show-word-limit>
                      <i slot="prefix" class="el-icon-edit"></i>
                    </el-input>
                    <span class="signature-bracket right-bracket">】</span>
                  </div>
                  <div class="signature-preview">
                    <span class="preview-label">预览：</span>
                    <span class="preview-text">【{{ signatureFrom.formData.signature || '您的签名' }}】您的短信内容...</span>
                  </div>
                </el-form-item>

                <el-form-item label="签名来源" prop="signatureType" class="form-item-compact">
                  <el-select v-model="signatureFrom.formData.signatureType" placeholder="请选择签名来源"
                    class="compact-select">
                    <el-option :value="1" label="企业名称">
                      <span style="float: left">企业名称</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">推荐全称</span>
                    </el-option>
                    <el-option :value="2" label="事业单位">
                      <span style="float: left">事业单位</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">机关学校等</span>
                    </el-option>
                    <el-option :value="3" label="商标">
                      <span style="float: left">商标</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">需商标证书</span>
                    </el-option>
                    <el-option :value="4" label="App应用">
                      <span style="float: left">App应用</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">需备案截图</span>
                    </el-option>
                    <el-option :value="5" label="小程序">
                      <span style="float: left">小程序</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">需备案截图</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="上传证明材料" class="form-item-compact">
                  <div class="upload-section">
                    <div class="upload-controls">
                      <!-- <el-button type="text" @click="showImg" class="example-btn">
                        <i class="el-icon-view"></i>
                        查看示例
                      </el-button>
                      <el-divider direction="vertical"></el-divider> -->
                      <div class="custom-upload-wrapper">
                        <file-upload v-permission :action="this.API.cpus + 'v3/file/upload'" :limit="3"
                          listType="picture" tip="支持jpg、png等格式，大小不超过2M，最多3张" :fileStyle="fileStyle" :del="del1"
                          :fileListS="fileListS" :showfileList="true" @fileup="fileup" @fileupres="fileupres"
                          class="compact-upload" ref="fileUpload">
                          选择上传文件
                        </file-upload>
                        <!-- 只在上传按钮上添加覆盖层，不影响已上传的文件操作 -->
                        <div class="upload-button-overlay" @click="handleUploadClick" v-if="!uploadAllowed"></div>
                      </div>
                    </div>
                    <div class="upload-tip">
                      <el-alert title="点击上传文件时会提示查看示例，确保上传正确的证明材料。" type="info" :closable="false" show-icon
                        size="small" />
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>

            <!-- 短信内容区域 -->
            <div class="content-section">
              <el-divider content-position="left">短信示例与备注</el-divider>

              <el-form-item label="短信示例" prop="contentExample" class="content-form-item">
                <div class="content-input-wrapper">
                  <el-input v-model="signatureFrom.formData.contentExample" type="textarea"
                    placeholder="例如：【xxx】欢迎使用xxx，祝您使用愉快！" maxlength="800" show-word-limit :rows="3"
                    class="compact-textarea" />
                  <!-- 短信示例提示 -->
                  <div class="sms-example-warning">
                    <el-alert title="审核提醒" type="warning" :closable="false" show-icon class="compact-alert">
                      <template slot="default">
                        <div class="warning-content">
                          <p class="warning-text">
                            <i class="el-icon-time"></i>
                            <strong>如果短信示例中包含电话号码或链接，可能需要额外的审核时间</strong>
                          </p>
                          <p class="suggestion-text">
                            💡 <strong>建议：</strong>为了加快审核速度，短信示例中请避免填写具体的电话号码和网址链接
                          </p>
                          <div class="example-texts">
                            <div class="good-example">
                              <span class="example-label good">✅ 推荐示例：</span>
                              <span class="example-content">【公司名】欢迎使用我们的服务，如有问题请联系客服，祝您使用愉快！</span>
                            </div>
                            <div class="bad-example">
                              <span class="example-label bad">❌ 避免示例：</span>
                              <span class="example-content">【公司名】欢迎使用，客服电话：400-123-4567，官网：www.example.com</span>
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-alert>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="备注内容" prop="remark" class="content-form-item">
                <div class="content-input-wrapper">
                  <el-input type="textarea" v-model="signatureFrom.formData.remark" maxLength="50"
                    placeholder="请输入备注内容,50字以内" :rows="2" show-word-limit class="compact-textarea" />
                </div>
              </el-form-item>
            </div>
          </el-card>

          <!-- 企业信息卡片（参照CreateTemplate1风格） -->
          <el-card shadow="hover" class="form-card template-config-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-office-building"></i>
                企业信息
              </span>
              <div class="card-header-actions">
                <el-button type="text" @click="clearPrincipalInfo" class="clear-btn">
                  <i class="el-icon-delete"></i>
                  清空信息
                </el-button>
                <el-tooltip content="请填写真实有效的企业信息" placement="top">
                  <i class="el-icon-question help-icon"></i>
                </el-tooltip>
              </div>
            </div>

            <!-- 基本信息区域 -->
            <div class="basic-info-section">
              <div class="form-row">
                <el-form-item label="企业名称" prop="companyName" class="form-item-compact">
                  <el-input v-model="signatureFrom.formData.companyName" placeholder="请输入企业名称"
                    class="compact-input">
                    <i slot="prefix" class="el-icon-office-building"></i>
                  </el-input>
                </el-form-item>

                <el-form-item label="社会统一信用代码" prop="creditCode" class="form-item-compact">
                  <el-input v-model="signatureFrom.formData.creditCode" placeholder="请输入统一社会信用代码"
                    class="compact-input">
                    <i slot="prefix" class="el-icon-postcard"></i>
                  </el-input>
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="企业法人" prop="legalPerson" class="form-item-compact">
                  <el-input v-model="signatureFrom.formData.legalPerson" placeholder="请输入企业法人姓名"
                    class="compact-input">
                    <i slot="prefix" class="el-icon-user"></i>
                  </el-input>
                </el-form-item>

                <el-form-item label="责任人姓名" prop="principalName" class="form-item-compact">
                  <el-input v-model="signatureFrom.formData.principalName" placeholder="请输入负责人姓名"
                    class="compact-input">
                    <i slot="prefix" class="el-icon-user-solid"></i>
                  </el-input>
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="责任人证件号码" prop="principalIdCard" class="form-item-compact">
                  <el-input v-model="signatureFrom.formData.principalIdCard" placeholder="请输入负责人身份证号"
                    class="compact-input">
                    <i slot="prefix" class="el-icon-postcard"></i>
                  </el-input>
                </el-form-item>

                <el-form-item label="责任人手机号" prop="principalMobile" class="form-item-compact">
                  <el-input v-model="signatureFrom.formData.principalMobile" placeholder="请输入责任人手机号"
                    class="compact-input">
                    <i slot="prefix" class="el-icon-mobile-phone"></i>
                  </el-input>
                  <div class="input-tip">
                    <span class="tip-text">须填写身份证号本人使用的手机号，否则报备失败</span>
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-card>
        </el-form>

        <!-- 操作按钮区域（参照CreateTemplate1风格） -->
        <div class="action-buttons">
          <div class="button-group">
            <el-button v-permission type="primary" size="large"
              @click="signature_add('signatureFrom', 'signatureFrom.title', '1')" class="submit-btn">
              <i class="el-icon-check"></i>
              提交审核
            </el-button>
            <el-button v-permission type="success" size="large"
              @click="signature_add('signatureFrom', 'signatureFrom.title', '0')" class="save-btn">
              <i class="el-icon-document"></i>
              保存但不提交
            </el-button>
            <el-button size="large" @click="goBack" class="cancel-btn">
              <i class="el-icon-close"></i>
              取消
            </el-button>
          </div>
        </div>

        <!-- 签名规范说明（参照CreateTemplate1的template-rules-card风格） -->
        <el-card shadow="hover" class="form-card template-rules-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-info"></i>
              签名规范说明
            </span>
          </div>
          <div class="rules-content">
            <!-- 内容规范 -->
            <el-alert title="内容规范" type="warning" :closable="false" show-icon>
              <template slot="default">
                <div class="rules-list">
                  <div class="rule-item">
                    <strong>签名内容：</strong>公司名称或产品名称，字数要求中文在<span class="highlight">2-16个字符</span>，英文在<span
                      class="highlight">2-32个字符</span>，不能使用空格和特殊符号" - + = * & % # @ ~等
                  </div>
                  <div class="rule-item">
                    <strong>签名审核：</strong>签名由客服人工审核，审核通过后可使用
                  </div>
                  <div class="rule-item">
                    <strong>签名规范：</strong>无须在签名内容前后添加【】、（）、{}，系统会自动添加
                  </div>
                </div>
              </template>
            </el-alert>

            <!-- 审核规则 -->
            <el-alert title="审核规则" type="info" :closable="false" show-icon style="margin-top: 16px;">
              <template slot="default">
                <div class="audit-rules">
                  <div class="audit-item">
                    <span class="audit-time">工作日审核：</span>
                    <span class="audit-desc">9点至21点，每30分钟审核一次</span>
                  </div>
                  <div class="audit-item">
                    <span class="audit-time">休息日审核：</span>
                    <span class="audit-desc">每天9-18点，每1小时审核一次</span>
                  </div>
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>
      </div>
    </div>
    <!-- 现代化图片示例弹窗 -->
    <el-dialog title="证明材料示例" :visible.sync="showImgDialogVisible" width="1000px" :before-close="handleClose"
      :close-on-press-escape="false" class="modern-dialog">
      <div class="dialog-content">
        <!-- 温馨提示 -->
        <div class="tips-section">
          <el-alert title="温馨提示" type="info" :closable="false" show-icon>
            <template slot="default">
              <div class="tips-content">
                <p><strong>中国商标网查询网址：</strong>
                  <a href="https://sbj.cnipa.gov.cn/sbj/index.html" target="_blank" class="link">
                    https://sbj.cnipa.gov.cn/sbj/index.html
                  </a>
                </p>
                <p><strong>工信部备案管理系统：</strong>
                  <a href="https://beian.miit.gov.cn/#/Integrated/recordQuery" target="_blank" class="link">
                    https://beian.miit.gov.cn/#/Integrated/recordQuery
                  </a>
                </p>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- 示例图片展示 -->
        <div class="examples-grid">
          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-search"></i>
                <span class="example-title">唯一性示例图</span>
              </div>
              <div class="example-image">
                <el-image :src="require('../../../../../../assets/images/qcc.png')"
                  :preview-src-list="[require('../../../../../../assets/images/qcc.png')]" fit="cover"
                  class="demo-image">
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-office-building"></i>
                <span class="example-title">营业执照示例</span>
              </div>
              <div class="example-image">
                <el-image :src="require('../../../../../../assets/images/business.png')"
                  :preview-src-list="[require('../../../../../../assets/images/business.png')]" fit="cover"
                  class="demo-image">
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-medal"></i>
                <span class="example-title">商标示例</span>
              </div>
              <div class="example-image">
                <el-image :src="require('../../../../../../assets/images/shangbiao.png')"
                  :preview-src-list="[require('../../../../../../assets/images/shangbiao.png')]" fit="cover"
                  class="demo-image">
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-mobile-phone"></i>
                <span class="example-title">App/小程序示例</span>
              </div>
              <div class="example-image">
                <el-image :src="require('../../../../../../assets/images/productapp.png')"
                  :preview-src-list="[require('../../../../../../assets/images/productapp.png')]" fit="cover"
                  class="demo-image">
                </el-image>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
export default {
  components: { FileUpload },
  name: "CreateSign",
  data() {
    return {
      statusOf: "", //是新增还是编辑
      fileListS: "", //回显图片的地址
      signatures: "", //签名
      roleId: "",
      uploadAllowed: false, // 控制是否允许上传
      signatureFrom: {
        title: "",
        formData: {
          signature: "",
          signatureType: 1,
          signatureSubType: 0,
          remark: "",
          imgUrl: "",
          contentExample: "",
          signatureId: "",
          direct: 1,
          companyName: "",
          creditCode: "",
          legalPerson: "",
          principalName: "",
          principalIdCard: "",
          principalMobile: "",
        },
        imgUrl: [],
        formRule: {
          //验证规则
          // signature: [
          //   { required: true, message: "该输入项为必填项!", trigger: "blur" },
          //   {
          //     min: 2,
          //     max: 15,
          //     message: "长度在 2 到 15 个字符",
          //     trigger: ["blur", "change"],
          //   },
          //   {
          //     pattern: /^(?!\d+$)([a-zA-Z\u4e00-\u9fa5\s\d]+)$/,
          //     message: "不能使用空格和特殊符号" - + = * & % # @ ~"等",
          //   },
          // ],
          signature: [
            { required: true, message: "该输入项为必填项!", trigger: "blur" },
            {
              validator: (_, value, callback) => {
                const englishPattern = /^[a-zA-Z\s]*$/; // 仅英文字符
                const chinesePattern = /^[\u4e00-\u9fa5\s]*$/; // 仅中文字符
                const mixedPattern = /^(?!\d+$)([a-zA-Z\u4e00-\u9fa5\s\d]+)$/; // 不包含特殊字符（仅限字母和汉字）
                if (value) {
                  // 检查是否为纯英文
                  if (englishPattern.test(value)) {
                    if (value.length > 32) {
                      callback(new Error("英文长度不能超过32个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  }
                  // 检查是否为纯中文
                  else if (chinesePattern.test(value)) {
                    if (value.length > 16) {
                      callback(new Error("中文长度不能超过16个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  }
                  // 检查是否为中英组合
                  else if (mixedPattern.test(value)) {
                    if (value.length > 16) {
                      callback(new Error("中英文组合长度不能超过16个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  } else {
                    callback(new Error("请输入有效的字符（英文或中文）"));
                  }
                } else {
                  callback(new Error("该输入项为必填项!"));
                }
              },
              trigger: ["blur", "change"],
            },
          ],
          signatureType: [
            { required: true, message: "请选择签名来源", trigger: "change" },
          ],
          signatureSubType: [
            { required: true, message: "请选择签名类型", trigger: "change" },
          ],
          imgUrl: [
            // { required: true, message: "请选择上传图片", trigger: "change" },
          ],
          contentExample: [
            { required: true, message: "该输入短信示例", trigger: "blur" },
            {
              min: 1,
              max: 800,
              message: "长度在 1 到 800 个字符",
              trigger: ["blur", "change"],
            },
          ],
          direct: [
            { required: true, message: "请选择主体", trigger: "change" },
          ],
          companyName: [
            { required: true, message: '请输入企业名称', trigger: 'change' },
          ],
          creditCode: [
            { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
          ],
          legalPerson: [
            { required: true, message: '请输入法人姓名', trigger: 'change' },
          ],
          principalIdCard: [
            { required: true, message: '请输入负责人身份证号', trigger: 'change' },
          ],
          principalName: [
            { required: true, message: '请输入负责人姓名', trigger: 'change' },
          ],
          principalMobile: [
            { required: true, message: '请输入责任人手机号', trigger: 'change' },
          ],
        },
        signature: "", //签名
        signatureId: "", //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: "img",
        style: ["jpg", "jpeg", "bmp", "gif", "png"],
      },
      del1: true, //关闭弹框时清空图片
      showImgDialogVisible: false, //查看图片示例弹窗
    };
  },
  created() {
    let userInfo = JSON.parse(localStorage.getItem("userInfo"));
    this.roleId = userInfo.roleId;
    if (this.$route.query.d) {
      this.statusOf = "编辑签名";
      this.handleEdit();
    } else {
      this.statusOf = "添加签名";
      this.getRealNameInfo()
    }
  },
  methods: {
    //获取编辑信息
    //编辑的赋值
    getRealNameInfo() {
      try {
        this.$api.get(
          this.API.cpus + "signature/getRealNameInfo",
          {},
          (res) => {
            if (res.code == 200) {
              if (res.data) {
                this.signatureFrom.formData.companyName = res.data.companyName;
                this.signatureFrom.formData.creditCode = res.data.creditCode;
                this.signatureFrom.formData.legalPerson = res.data.legalPerson;
                this.signatureFrom.formData.principalName = res.data.principalName;
                this.signatureFrom.formData.principalIdCard = res.data.principalIdCard;
              }
            } else {
              this.$message.error(res.msg);
            }
          }
        );
      } catch (error) {
        console.log(error, "err");
      }
    },
    clearPrincipalInfo() {
      this.signatureFrom.formData.companyName = "";
      this.signatureFrom.formData.creditCode = "";
      this.signatureFrom.formData.legalPerson = "";
      this.signatureFrom.formData.principalName = "";
      this.signatureFrom.formData.principalIdCard = "";
      this.signatureFrom.formData.principalMobile = "";
      // if (val == 1) {
      //   this.getRealNameInfo()
      // } else {
      //   this.signatureFrom.formData.companyName = "";
      //   this.signatureFrom.formData.creditCode = "";
      //   this.signatureFrom.formData.legalPerson = "";
      //   this.signatureFrom.formData.principalName = "";
      //   this.signatureFrom.formData.principalIdCard = "";
      // }

    },
    handleEdit() {
      let b = this.$route.query.d;
      //获取上传的图片地址
      this.$api.get(
        this.API.cpus + "signature/findModel?signatureId=" + b,
        {},
        (res) => {
          try {
            let aa = res.data.signature;
            this.signatureFrom.signature = aa.slice(1, aa.length - 1);
            if (res.data.imgUrl) {
              this.fileListS = res.data.imgUrl;
              // console.log(this.fileListS,'fileListS');
              let a = res.data.imgUrl.split(",");
              for (let i = 0; i < a.length; i++) {
                if (a[i] != "") {
                  this.signatureFrom.imgUrl.push(a[i]);
                }
              }
            } else {
              this.fileListS = '';
            }
            //存储签名
            this.signatures = res.data.signature;
            this.signatureFrom.formData.imgUrl =
              this.signatureFrom.imgUrl.join(",");
            Object.assign(this.signatureFrom.formData, res.data);
            this.signatureFrom.formData.signature =
              this.signatureFrom.signature;
            this.signatureFrom.formData.signatureType =
              res.data.signatureType;
          } catch (error) {
            console.log(error, "err");
          }
        }
      );
      // console.log(val);
      // this.$nextTick(()=>{
      //     this.$refs.signatureFrom.resetFields(); //清空表单
      //     Object.assign(this.signatureFrom.formData,val);

      // });
    },
    //返回
    goBack() {
      this.$router.push({ path: "SignatureManagement" });
    },
    //提交表单
    signature_add(formName, _, val) {
      try {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            // 如果是提交审核，需要先阅读报备规则
            if (val === '1') {
              this.showReportingRules(() => {
                this.doSubmitSignature(formName, val);
              });
            } else {
              this.doSubmitSignature(formName, val);
            }
          } else {
            console.log("表单验证失败");
            return false;
          }
        });
      } catch (error) {
        console.error('提交表单错误:', error);
        this.$message.error('提交失败，请重试');
      }
    },

    // 显示报备规则确认对话框
    showReportingRules(callback) {
      const reportingRulesContent = `
        <div style="max-height: 400px; overflow-y: auto; text-align: left; line-height: 1.6;">
          <h3 style="color: #409eff; margin-bottom: 16px;">签名实名制报备</h3>
          
          <p style="margin-bottom: 12px;">为了进一步规范短信市场秩序、保障用户权益，依据工信部相关文件要求及各级运营商的具体部署，在完成运营商侧实名制报备流程后，方可发送短信。请您根据本文指引，完成相关资质信息补充并发起报备。</p>
          
          <div style="background: #fff2e8; border: 1px solid #ffb340; border-radius: 4px; padding: 12px; margin: 16px 0;">
            <h4 style="color: #ff8800; margin-bottom: 8px;">⚠️ 重要提醒</h4>
            <p style="margin-bottom: 8px;">在未完全完成报备之前，您的短信可能会发送失败，返回错误码如下：</p>
            <p style="color: #ff4d4f; font-weight: bold;">ZT:0310 (子端口未实名)</p>
            <p style="margin-top: 8px;">运营商实名报备流程平均需要1-3个工作日，基于近期观测，部分运营商（如联通）实名报备流程需要5-7个工作日，但运营商未对此时效进行承诺，实际可能需要更长时间。请您合理规划业务计划，并在正式使用前提前申请相关资质和签名，预留时间进行发送测试，以确保在正式使用前有充足的时间完成实名报备。</p>
          </div>
          
          <h4 style="color: #409eff; margin: 16px 0 8px 0;">签名实名合规要求</h4>
          <p style="margin-bottom: 12px;">运营商的短信实名制报备对象是签名，实名要求的是签名所属企业的资质信息。无论存量短信签名或待新增的短信签名，都需要符合如下两方面合规要求：</p>
          
          <h3 style="color: #333; margin: 12px 0 8px 0;">1. 签名实名准确</h3>
          <p style="margin-bottom: 8px;">需要同时确保签名实名的资质"企证准确"、"人证准确"：</p>
          <ul style="margin-left: 20px; margin-bottom: 12px;">
            <li style="margin-bottom: 8px;"><strong>企证准确：</strong>即企业名称和企业社会统一信用代码需要和企业证照上的信息保持一致，您可以登录国家企业信用信息公示系统自查。</li>
            <li style="margin-bottom: 8px;"><strong>人证准确：</strong>包含"法定代表人信息"和"管理员信息"两部分</li>
            <li style="margin-bottom: 8px;"><strong>法定代表人信息：法定代表人信息需要和企业证照上的法定代表人保持一致，且法定代表人姓名和证件号码信息准确。</li>
            <li style="margin-bottom: 8px;"><strong>责任人信息：若管理员非法定代表人本人，则要求“责任人”必须符合运营商平台“一人一企”校验规则，即一个责任人只能出现在一个企业资质中，否则为“多企业经办人”，会导致运营商侧签名报备失败。</li>
          </ul>
          
          <h3 style="color: #333; margin: 12px 0 8px 0;">2. 签名命名符合要求</h3>
          <p style="margin-bottom: 8px;">短信签名命名要求符合以下任一条件：</p>
          
          <div style="background: #fff2e8; border: 1px solid #ffb340; border-radius: 4px; padding: 12px; margin: 12px 0;">
            <h4 style="color: #ff8800; margin-bottom: 8px;">重要提醒</h4>
            <p>历史签名若使用"公众号"、"电商平台店铺名"、"已备案网站"、"测试或学习"或"线上试用"作为签名来源也可能极大程度面临发送失败问题。建议您及时停用，并申请符合以下任一条件的短信签名。</p>
          </div>
          
          <ul style="margin-left: 20px; margin-bottom: 12px;">
            <li style="margin-bottom: 8px;">
              <strong>1.企事业单位名（推荐使用）：</strong>签名为企业名称的全称或简称
              <p>全称：企业名称的全称。</p>
              <p>简称：简称需要包含在公司名称中，包含企业品牌名并且能唯一标识企业主体，企业简称不能为中性词或对应到多家企业，不能跳字、不能乱序、不能省略主要信息。</p>
              <p>合规示例：【上海助通信息科技有限公司】、【上海助通信息科技】。</p>
              <p>不合规示例：【助通】、【助通信息】：存在多个地区的阿里公司，无法体现独特性和唯一性；【上通信息】、【上海助通网】：签名没有完全包含在公司全称中，存在跳字情况。</p>
              </li>
            <li style="margin-bottom: 8px;">
              <strong>2.已注册商标名：</strong>签名与企业已注册商标名一致
              <p>商标应在国家知识产权局商标局-中国商标网中可查，且商标所有方与企业名称一致。</p>
              </li>
            <li style="margin-bottom: 8px;">
              <strong>3.已上线APP/小程序：</strong>签名与备案通过的APP/小程序名称一致
              <p>APP/小程序应在工信部 ICP/IP地址/域名信息备案管理系统中可查，且该APP的ICP备案主体与企业名称一致。</p>
              <p>如果“已注册商标名称”或“备案通过的已上线APP名称”为运营商定义的“中性签名”，即该商标无法体现企业特性或唯一性，无法表明和实名主体企业的关联关系，则属于不合规签名，不支持申请。建议使用实名企业的全称或简称作为签名。</p>
              </li>
          </ul>
          
          <div style="background: #f6f6f6; border-radius: 4px; padding: 12px; margin: 12px 0;">
            <h5 style="color: #52c41a; margin-bottom: 8px;">✅ 合规示例</h5>
            <p>【上海助通信息科技有限公司】、【上海助通信息科技】</p>
            <h5 style="color: #ff4d4f; margin: 8px 0;">❌ 不合规示例</h5>
            <p>【会议助手】、【服务提醒】等</p>
            <p>【上通信息】、【上海助通网】：签名没有完全包含在公司全称中，存在跳字情况</p>
          </div>
        </div>
      `;

      this.$confirm(reportingRulesContent, '签名实名制报备规则', {
        confirmButtonText: '我已阅读并同意',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        customClass: 'reporting-rules-dialog',
        closeOnClickModal: false,
        closeOnPressEscape: false,
        showClose: false
      }).then(() => {
        // 用户确认阅读并同意
        console.log('用户已确认报备规则，开始提交签名');
        callback();
      }).catch(() => {
        // 用户取消
        console.log('用户取消了报备规则确认');
      });
    },

    // 执行签名提交
    doSubmitSignature(formName, val) {
      let str = JSON.stringify(this.signatureFrom.formData);
      let formDates = JSON.parse(str);
      // Object.assign(formDates, this.signatureFrom.formData);
      // delete formDates.direct;
      formDates.signature =
        "【" + this.signatureFrom.formData.signature + "】";
      if (this.statusOf == "添加签名") {
        //新增
        delete formDates.signatureId;
        formDates.auditStatus = val;
        if (!((this.signatureFrom.formData.signatureType == 1 || this.signatureFrom.formData.signatureType == 2) &&
          this.signatureFrom.formData.signatureSubType == 0)) {
          if (this.signatureFrom.formData.imgUrl != "") {
            // 直接发送请求，不再弹出确认对话框
            this.submitSignatureDirectly("post", this.API.cpus + "signature/saveInfo", formDates);
          } else {
            this.$message({
              message: "请上传图片！",
              type: "warning",
            });
          }
        } else {
          // 直接发送请求，不再弹出确认对话框  
          this.submitSignatureDirectly("post", this.API.cpus + "signature/saveInfo", formDates);
        }
      } else {
        //编辑
        //判断是否有图片
        formDates.auditStatus = val; //只要编辑，签名的状态就改为待审核状态
        if (!((this.signatureFrom.formData.signatureType == 1 || this.signatureFrom.formData.signatureType == 2) &&
          this.signatureFrom.formData.signatureSubType == 0)) {
          if (this.signatureFrom.formData.imgUrl != "") {
            // 直接更新签名，不再弹出确认对话框
            this.updateSignatureDirectly("put", this.API.cpus + "signature/update", formDates);
          } else {
            this.$message({
              message: "请上传图片！",
              type: "warning",
            });
          }
        } else {
          // 直接更新签名，不再弹出确认对话框
          this.updateSignatureDirectly("put", this.API.cpus + "signature/update", formDates);
        }
      }
    },

    // 直接提交签名，不弹出确认对话框
    submitSignatureDirectly(type, action, formDates) {
      console.log('直接提交签名，数据:', formDates);

      // 验证签名是否存在
      this.$api.get(
        this.API.cpus + "signature/findModelBySignature?signature=" + encodeURIComponent(formDates.signature),
        {},
        (res) => {
          if (res.code == 200 && res.data == "0") {
            // 签名不存在，可以创建
            // this.$api.post(action, formDates, (response) => {
            //   if (response.code == 200) {
            //     this.$message.success('签名提交成功！');
            //     this.$router.push({ path: "SignatureManagement" });
            //   } else {
            //     this.$message.error(response.msg || '提交失败');
            //   }
            // });
            this.$confirms.confirmation(
              type,
              "确定新增签名",
              action,
              formDates,
              (res) => {
                if (res.code == 200) {
                  // this.$message.success('签名提交成功！');
                  this.$router.push({ path: "SignatureManagement" });
                }
              }
            )
          } else {
            this.$message.error("此签名已存在，请更换签名内容！");
          }
        }
      );
    },

    // 直接更新签名，不弹出确认对话框
    updateSignatureDirectly(type, action, formDates) {
      // console.log('直接更新签名，数据:', formDates);
      this.$confirms.confirmation(
        type,
        "确定修改签名",
        action,
        formDates,
        (res) => {
          if (res.code == 200) {
            // this.$message.success('签名更新成功！');
            this.$router.push({ path: "SignatureManagement" });
          }
        }
      )
      // this.$api.put(action, formDates, (response) => {
      //   if (response.code == 200) {
      //     this.$message.success('签名更新成功！');
      //     this.$router.push({ path: "SignatureManagement" });
      //   } else {
      //     this.$message.error(response.msg || '更新失败');
      //   }
      // });
    },

    //发送编辑和新增的请求
    sendRequest(type, title, action, formDates) {
      //验证签名是否存在
      this.$api.get(
        this.API.cpus +
        "signature/findModelBySignature?signature=【" +
        this.signatureFrom.formData.signature +
        "】",
        {},
        (res) => {
          if (res.code == 200 && res.data == "0") {
            //发送编辑和新增的请求
            this.$confirms.confirmation(
              type,
              title,
              action,
              formDates,
              (ress) => {
                if (ress.code == 200) {
                  this.$router.push({ path: "SignatureManagement" });
                }

              }
            );
          } else {
            this.$message({
              message: "签名已存在，切勿重复！",
              type: "warning",
            });
          }
        }
      );
    },
    //移除文件
    fileup(file, _) {
      if (file.response) {
        this.signatureFrom.imgUrl.splice(this.signatureFrom.imgUrl.indexOf(file.response.data.fullpath), 1);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      } else {
        this.signatureFrom.imgUrl.splice(this.signatureFrom.imgUrl.indexOf(file.name), 1);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      }
      // if (val2.length) {
      //   if (
      //     this.signatureFrom.formData.imgUrl.indexOf(
      //       val2[0].response.data.fullpath
      //     ) == -1
      //   ) {
      //     let aa = this.signatureFrom.formData.imgUrl.split(",");
      //     if (val.response) {
      //       aa.splice(aa.indexOf(val.response.fullPath), 1);
      //     } else {
      //       let c = val.url;
      //       let d = c.slice(c.indexOf("group1"));
      //       aa.splice(aa.indexOf(d), 1);
      //     }
      //     this.signatureFrom.imgUrl = aa;
      //     console.log(this.signatureFrom.imgUrl, 'this.signatureFrom.imgUrl');

      //     this.signatureFrom.formData.imgUrl = aa.join(",");
      //   }
      // }else{
      //   this.signatureFrom.formData.imgUrl = "";
      //   this.signatureFrom.imgUrl = [];
      // }

      // 文件移除后重置状态
      this.uploadAllowed = false;
    },
    //文件上传成功
    fileupres(val) {
      if (val && val.data && val.data.fullpath) {
        this.signatureFrom.imgUrl.push(val.data.fullpath);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      } else {
        console.error('文件上传响应数据格式不正确', val);
        this.$message.error('文件上传失败，请重试');
      }
      // 上传完成后重置状态
      this.uploadAllowed = false;
    },
    handleClose() {
      this.showImgDialogVisible = false;
    },
    showImg() {
      this.showImgDialogVisible = true;
    },

    // 处理上传点击事件 - 先显示示例
    handleUploadClick() {
      this.$confirm(
        '上传证明材料前，建议您先查看示例，了解所需证明材料的格式要求。是否继续上传？',
        '上传提醒',
        {
          confirmButtonText: '查看示例',
          cancelButtonText: '直接上传',
          distinguishCancelAndClose: true,
          type: 'info'
        }
      ).then(() => {
        // 用户选择查看示例
        this.showImgDialogVisible = true;

        // 监听示例弹窗关闭，用户关闭示例后继续上传流程
        this.$nextTick(() => {
          const checkDialogClosed = () => {
            if (!this.showImgDialogVisible) {
              this.$confirm('现在开始上传文件吗？', '确认上传', {
                confirmButtonText: '开始上传',
                cancelButtonText: '取消',
                type: 'info'
              }).then(() => {
                this.allowUpload();
              }).catch(() => {
                // 用户取消上传
              });
            } else {
              setTimeout(checkDialogClosed, 500);
            }
          };
          setTimeout(checkDialogClosed, 500);
        });
      }).catch((action) => {
        if (action === 'cancel') {
          // 用户选择直接上传
          this.allowUpload();
        }
        // 其他情况不做处理（用户关闭弹窗）
      });
    },

    // 允许上传
    allowUpload() {
      this.uploadAllowed = true;
      this.$nextTick(() => {
        // 触发文件选择
        const uploadInput = this.$refs.fileUpload.$el.querySelector('input[type="file"]');
        if (uploadInput) {
          uploadInput.click();
        }
        // 上传完成后重置状态
        setTimeout(() => {
          this.uploadAllowed = false;
        }, 100);
      });
    },

    // showImgDialogVisible() {
    //   this.showImgDialogVisible = false;
    // },
  },
};
</script>
<style lang="less" scoped>
// 引入通用签名样式
@import '~@/styles/signature-common.less';

/* 短信示例警告提示样式 */
.sms-example-warning {
  margin-top: 12px;

  .compact-alert {
    border-radius: 8px;
    border-left: 4px solid #e6a23c;
    background-color: #fdf6ec;

    ::v-deep .el-alert__icon {
      color: #e6a23c;
      font-size: 18px;
    }

    ::v-deep .el-alert__title {
      color: #e6a23c;
      font-weight: 600;
      font-size: 14px;
    }

    ::v-deep .el-alert__content {
      padding-top: 8px;
    }
  }

  .warning-content {
    .warning-text {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      color: #e6a23c;
      font-size: 13px;

      i {
        margin-right: 6px;
        font-size: 16px;
      }
    }

    .suggestion-text {
      color: #606266;
      font-size: 13px;
      margin-bottom: 12px;
      line-height: 1.5;
    }

    .example-texts {

      .good-example,
      .bad-example {
        margin-bottom: 8px;
        padding: 8px;
        border-radius: 6px;
        font-size: 12px;
        line-height: 1.4;

        .example-label {
          font-weight: 600;
          display: inline-block;
          min-width: 80px;

          &.good {
            color: #67c23a;
          }

          &.bad {
            color: #f56c6c;
          }
        }

        .example-content {
          color: #606266;
          margin-left: 8px;
        }
      }

      .good-example {
        background-color: #f0f9ff;
        border: 1px solid #e1f5fe;
      }

      .bad-example {
        background-color: #fff2f0;
        border: 1px solid #ffebee;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sms-example-warning {
    .warning-content {
      .example-texts {

        .good-example,
        .bad-example {
          .example-label {
            display: block;
            margin-bottom: 4px;
            min-width: auto;
          }

          .example-content {
            margin-left: 0;
            display: block;
          }
        }
      }
    }
  }
}

// 上传区域样式
.upload-section {
  .upload-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;

    .example-btn {
      color: #409eff;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #66b1ff;
      }

      i {
        margin-right: 4px;
      }
    }

    .el-divider--vertical {
      height: 20px;
      margin: 0 8px;
    }
  }

  .upload-tip {
    margin-top: 8px;

    ::v-deep .el-alert {
      padding: 8px 12px;

      .el-alert__content {
        font-size: 12px;
      }
    }
  }
}

// 报备规则对话框样式
::v-deep .el-message-box {
  width: 1000px !important;
  max-width: 95vw;
}

::v-deep .reporting-rules-dialog {
  .el-message-box__content {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
  }

  .el-message-box__btns {
    padding: 15px 20px 20px;
    text-align: right;

    .el-button--primary {
      background: #409eff;
      border-color: #409eff;
    }
  }
}

// 自定义上传包装器样式
.custom-upload-wrapper {
  display: inline-block;
  position: relative;

  .upload-button-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px; // 只覆盖上传按钮的高度
    z-index: 10;
    cursor: pointer;
    background: transparent;
    pointer-events: auto;

    &:hover {
      background: rgba(64, 158, 255, 0.05);
      border-radius: 4px;
    }
  }

  // 确保文件列表不被覆盖，允许正常的点击和删除操作
  ::v-deep .el-upload-list {
    position: relative;
    z-index: 11;
    pointer-events: auto;

    .el-upload-list__item {
      pointer-events: auto;

      .el-icon-close {
        pointer-events: auto;
      }

      .el-upload-list__item-thumbnail {
        pointer-events: auto;
      }
    }
  }

  // 确保上传按钮区域可以被覆盖层拦截
  ::v-deep .el-upload {
    position: relative;
    z-index: 5;
  }
}

// 上传提醒弹窗样式
::v-deep .el-message-box {
  &.upload-reminder-dialog {
    .el-message-box__header {
      padding: 20px 20px 10px;

      .el-message-box__title {
        color: #409eff;
        font-weight: 600;
      }
    }

    .el-message-box__content {
      padding: 10px 20px 20px;

      .el-message-box__message {
        font-size: 14px;
        line-height: 1.6;
        color: #606266;
      }
    }

    .el-message-box__btns {
      padding: 15px 20px 20px;

      .el-button {
        margin-left: 10px;
        border-radius: 4px;

        &:first-child {
          margin-left: 0;
        }

        &.el-button--primary {
          background: #409eff;
          border-color: #409eff;

          &:hover {
            background: #66b1ff;
            border-color: #66b1ff;
          }
        }

        &.el-button--default {
          &:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
          }
        }
      }
    }
  }
}
</style>
<style>
/* .el-message-box {
  width: 1000px !important;
  max-width: 95vw;
} */
</style>
