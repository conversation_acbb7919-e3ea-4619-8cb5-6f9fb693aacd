<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 简约提醒区域 -->
        <div class="notice-section">
          <h3 class="notice-title">温馨提醒</h3>
          <div class="notice-list">
            <div class="notice-item">
              <span class="notice-label">短信组成：</span>一个完整的短信由短信签名和短信正文内容两部分组成，您可以根据业务需求分别设置不同的短信正文内容模板，然后进行组合形成最终展示。短信签名+短信正文内容=最终显示内容。
            </div>
            <div class="notice-item">
              <span class="notice-label">审核时间：</span>签名提交审核，工作日预计2小时内完成，非工作日预计4小时内完成。审核时间：周一至周日9:30-22:00（法定节假日顺延）。
            </div>
            <div class="notice-item">
              <span class="notice-label">通知设置：</span>您可设置常用手机和邮箱，用于即时接收该应用短信内容审核通知。
            </div>
            <div class="notice-item">
              <span class="notice-label">批量导入：</span>支持签名批量导入和实名信息批量导入功能。
            </div>
          </div>
        </div>

        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button @click="clickAddSig" class="action-btn primary" icon="el-icon-plus">
                  创建签名
                </el-button>
                <el-button v-permission @click="batchQu" class="action-btn" icon="el-icon-upload2">
                  签名批量导入
                </el-button>
                <el-button v-permission @click="realNameBatchImport" class="action-btn" icon="el-icon-user">
                  实名信息批量导入
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ tableDataObj.tablecurrent.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <div class="advanced-search-form">
                <div class="search-row">
                  <div class="search-item">
                    <label class="search-label">签名内容</label>
                    <el-input placeholder="请输入签名内容" v-model="tableDataObj.tablecurrent.signature" 
                      class="search-input" clearable prefix-icon="el-icon-search" />
                  </div>

                  <div class="search-item">
                    <label class="search-label">审核状态</label>
                    <el-select v-model="tableDataObj.tablecurrent.auditStatus" placeholder="全部状态" 
                      class="search-select" @change="submitForm" clearable>
                      <el-option label="编辑中" value="0" />
                      <el-option label="待审核" value="1" />
                      <el-option label="审核通过" value="2" />
                      <el-option label="审核不通过" value="3" />
                    </el-select>
                  </div>

                  <div class="search-item">
                    <label class="search-label">实名信息</label>
                    <el-select v-model="tableDataObj.tablecurrent.isComplete" placeholder="全部状态" 
                      class="search-select" @change="submitForm" clearable>
                      <el-option label="已完善" :value="true" />
                      <el-option label="未完善" :value="false" />
                    </el-select>
                  </div>

                  <div class="search-buttons">
                    <el-button type="primary" @click="submitForm" class="search-btn primary" icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button @click="resetForm" class="search-btn" icon="el-icon-refresh">
                      重置
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 签名列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">签名列表</h3>
          </div>

          <div class="table-container">
            <el-table v-loading="tableDataObj.loading2" element-loading-text="正在加载..."
              element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)"
              ref="multipleTable" border :data="tableDataObj.tableData" class="enhanced-table" stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }" :row-style="{ height: '60px' }"
              empty-text="暂无签名数据">
              <el-table-column prop="signatureId" label="签名ID" width="80" align="center">
                <template slot-scope="scope">
                  {{ scope.row.signatureId }}
                </template>
              </el-table-column>

              <el-table-column label="签名内容" width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <div class="content-text">{{ scope.row.signature }}</div>
                    <div v-if="scope.row.auditStatus == '3' && scope.row.auditReason" class="reject-reason">
                      驳回原因：{{ scope.row.auditReason }}
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="短信示例" width="300">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <Tooltip v-if="scope.row.contentExample" :content="scope.row.contentExample" 
                      className="wrapper-text" effect="light">
                    </Tooltip>
                    <span v-else class="no-variables">暂无示例</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="审核状态" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag :type="getStatusType(scope.row.auditStatus)" effect="light">
                    {{ getStatusText(scope.row.auditStatus) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column label="实名信息" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.reportId ? 'success' : 'warning'" effect="light">
                    {{ scope.row.reportId ? '已完善' : '未完善' }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 移动实名状态 -->
              <el-table-column width="120">
                <template slot="header" slot-scope="scope">
                  <div class="operator-header">
                    <i class="iconfont icon-yidong" style="color: #409eff; font-size: 20px; margin-right: 5px;"></i>
                    <span>移动</span>
                  </div>
                </template>
                <template slot-scope="scope">
                  <div class="single-operator-status">
                    <!-- 根据 ydAvalibleType 判断是否有实名类型 -->
                    <div v-if="scope.row.ydAvalibleType && scope.row.ydAvalibleType.trim()" class="status-tags-container">
                      <el-tag 
                        v-for="(tag, index) in scope.row.ydAvalibleType.split(',')" 
                        :key="index"
                        size="small" 
                        type="success"
                        class="status-tag success fixed-width">
                        {{ tag.trim() }}
                      </el-tag>
                    </div>
                    <!-- 无 AvalibleType 显示未实名 -->
                    <div v-else class="single-operator-status">
                      <el-tooltip content="未实名" placement="top" effect="dark">
                        <el-tag size="small" type="info" class="status-tag">
                          未实名
                        </el-tag>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 联通实名状态 -->
              <el-table-column width="120">
                <template slot="header" slot-scope="scope">
                  <div class="operator-header">
                    <i class="iconfont icon-liantong" style="color: #f56c6c; font-size: 20px; margin-right: 5px;"></i>
                    <span>联通</span>
                  </div>
                </template>
                <template slot-scope="scope">
                  <div class="single-operator-status">
                    <!-- 根据 ltAvalibleType 判断是否有实名类型 -->
                    <div v-if="scope.row.ltAvalibleType && scope.row.ltAvalibleType.trim()" class="status-tags-container">
                      <el-tag 
                        v-for="(tag, index) in scope.row.ltAvalibleType.split(',')" 
                        :key="index"
                        size="small" 
                        type="success"
                        class="status-tag success fixed-width">
                        {{ tag.trim() }}
                      </el-tag>
                    </div>
                    <!-- 无 AvalibleType 显示未实名 -->
                    <div v-else class="single-operator-status">
                      <el-tooltip content="未实名" placement="top" effect="dark">
                        <el-tag size="small" type="info" class="status-tag">
                          未实名
                        </el-tag>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 电信实名状态 -->
              <el-table-column width="120">
                <template slot="header" slot-scope="scope">
                  <div class="operator-header">
                    <i class="iconfont icon-dianxin" style="color: #409EFF; font-size: 20px; margin-right: 5px;"></i>
                    <span>电信</span>
                  </div>
                </template>
                <template slot-scope="scope">
                  <div class="single-operator-status">
                    <!-- 根据 dxAvalibleType 判断是否有实名类型 -->
                    <div v-if="scope.row.dxAvalibleType && scope.row.dxAvalibleType.trim()" class="status-tags-container">
                      <el-tag 
                        v-for="(tag, index) in scope.row.dxAvalibleType.split(',')" 
                        :key="index"
                        size="small" 
                        type="success"
                        class="status-tag success fixed-width">
                        {{ tag.trim() }}
                      </el-tag>
                    </div>
                    <!-- 无 AvalibleType 显示未实名 -->
                    <div v-else class="single-operator-status">
                      <el-tooltip content="未实名" placement="top" effect="dark">
                        <el-tag size="small" type="info" class="status-tag">
                          未实名
                        </el-tag>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="申请时间" width="170" align="center">
                <template slot-scope="scope">
                  {{ scope.row.createTime | fmtDate }}
                </template>
              </el-table-column>

              <el-table-column label="首次通过时间" width="170" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.row.firstAuditTime">
                    {{ scope.row.firstAuditTime | fmtDate }}
                  </div>
                  <span v-else class="no-variables">-</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="220" fixed="right">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-tooltip content="编辑签名" placement="top"
                      v-if="scope.row.auditStatus == '0' || scope.row.auditStatus == '3'">
                      <el-button type="text" @click="details(scope.$index, scope.row)" class="action-btn-small edit"
                        icon="el-icon-edit">
                        编辑
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="实名认证" placement="top" v-if="scope.row.auditStatus == '2'">
                      <el-button v-permission type="text" @click="getRealNameInfo(scope.row)" class="action-btn-small info"
                        icon="el-icon-user">
                        实名认证
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="实名明细" placement="top">
                      <el-button type="text" @click="handelCheck(scope.row)" class="action-btn-small primary"
                        icon="el-icon-view">
                        实名明细
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="删除签名" placement="top">
                      <el-button v-permission type="text" @click="dele(scope.$index, scope.row)" class="action-btn-small delete"
                        icon="el-icon-delete">
                        删除
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="tableDataObj.tablecurrent.currentPage" :page-size="tableDataObj.tablecurrent.pageSize"
              :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" 
              :total="tableDataObj.total" class="simple-pagination" />
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑弹框 -->
    <el-dialog :title="signatureFrom.title" :visible.sync="SigDialogVisible" width="680px"
      :close-on-click-modal="false">
      <el-form :model="signatureFrom.formData" :rules="signatureFrom.formRule" label-width="80px"
        style="padding-right: 14px" ref="signatureFrom">
        <el-form-item label="签名内容" prop="signature" style="position: relative">
          <el-input v-model="signatureFrom.formData.signature" :disabled="signatureFrom.title == '编辑短信签名'"
            placeholder="签名限制20个字"></el-input>
          <span style="position: absolute; top: 2px; left: 0px">【</span>
          <span style="position: absolute; top: 2px; right: 0px">】</span>
        </el-form-item>
        <el-form-item label="签名类型" class="sig-type" prop="signatureType">
          <el-radio-group v-model="signatureFrom.formData.signatureType" :disabled="signatureFrom.title == '编辑短信签名'">
            <el-radio label="1" style="padding-bottom: 6px">
              <span class="sig-type-title-tips">公司名全称或简称：</span>须提供营业执照截图
            </el-radio>
            <el-radio label="2" style="padding-bottom: 6px">
              <span class="sig-type-title-tips">APP名全称或简称：</span>须提供任一应用商店的下载链接与该应用商店的后台管理截图
            </el-radio>
            <el-radio label="3" style="padding-bottom: 6px">
              <span class="sig-type-title-tips">工信部备案的网站名全称或简称：</span>提供域名备案服务商的后台备案截图
            </el-radio>
            <el-radio label="4" style="padding-bottom: 6px">
              <span class="sig-type-title-tips">公众号或小程序名全称或简称：</span>须提供公众号（小程序）微信开放平台截图
            </el-radio>
            <el-radio label="5" style="padding-bottom: 6px">
              <span class="sig-type-title-tips">商标全称或简称：</span>须提供商标注册证书截图
            </el-radio>
            <el-radio label="6" style="padding-bottom: 6px">
              <span class="sig-type-title-tips">其他</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上传图片" v-if="signatureFrom.formData.signatureType != 6">
          <file-upload style="display: inline-block" :action="this.API.cpus + 'file/upload'" :limit="3"
            listType="picture" tip="格式要求：支持.jpg .jpeg .bmp .gif .png等格式照片，大小不超过2M" :fileStyle="fileStyle" :del="del1"
            :fileListS="fileListS" :showfileList="true" @fileup="fileup" @fileupres="fileupres">选择上传文件</file-upload>
        </el-form-item>
        <el-form-item label="备注内容" prop="remark">
          <el-input type="textarea" v-model="signatureFrom.formData.remark" maxLength="50"
            placeholder="请输入备注内容,50字以内"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="signature_add('signatureFrom', 'signatureFrom.title')">确 定</el-button>
        <el-button @click="SigDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    
    <!-- 签名批量导入弹框 -->
    <el-dialog title="签名批量导入" :visible.sync="batchShow" width="520px" :close-on-press-escape="false">
      <div>
        <div style="margin: 8px 0;">
          <span style="font-weight: bold;margin-right: 10px;">历史签名是否覆盖</span>
          <el-radio-group v-model="coverType">
            <el-radio :label="false">否</el-radio>
            <el-radio :label="true">是</el-radio>
          </el-radio-group>
        </div>
        
        <el-upload class="upload-demo" :headers="header" :action="this.API.cpus + 'signature/batchUploadAttachment'"
          :limit="1" :file-list="zipfileList" :on-success="handleSuccessZip" :on-remove="handleRemoveZip"
          :before-upload="beforeAvatarUploadc">
          <el-button size="small" type="primary">上传图片</el-button>
        </el-upload>
        
        <div style="background-color: #eee;padding: 10px;border-radius: 5px;margin: 10px 0;">
          <span style="font-size: 14px;color: red;">提示：仅允许导入".zip"格式文件！请将同一个签名的附件，
            放在同一个文件夹下，并且文件夹要以签名命名。</span>
        </div>
        
        <el-upload class="upload-demo" :headers="header" v-bind:data="{
          FoldPath: '上传目录',
          SecretKey: '安全验证',
          cover: coverType,
        }" :action="this.API.cpus + 'signature/upload'" :file-list="fileListBatch" :on-remove="handleRemoveBatch"
          :on-success="handleSuccessBatch" :limit="1">
          <el-button style="margin-left: 10px" type="primary">批量导入</el-button>
          <div slot="tip" class="el-upload__tip">
            <a style="margin: 10px; color: #409eff"
              href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/8081069d0b27c941a74af744a4e0b18c"
              rel="noopener noreferrer">模版下载</a>
          </div>
        </el-upload>
      </div>
    </el-dialog>
    
    <!-- 实名信息弹框 -->
    <el-dialog title="实名信息补充" :visible.sync="realNameDialogVisible" width="750px"
      class="LoginCellPhoneDialog" :close-on-click-modal="false" :before-close="handelClose">
      <el-form :inline="false" :model="realNameInfo" :rules="formRules" ref="realNameInfo" 
        class="demo-realNameInfo" label-width="140px">
        <el-form-item label="企业名称" prop="companyName">
          <el-input class="input-w" placeholder="请输入企业名称" v-model="realNameInfo.companyName"></el-input>
        </el-form-item>
        <el-form-item label="社会统一信用代码" prop="creditCode">
          <el-input class="input-w" placeholder="请输入统一社会信用代码" v-model="realNameInfo.creditCode"></el-input>
        </el-form-item>
        <el-form-item label="企业法人" prop="legalPerson">
          <el-input class="input-w" placeholder="请输入企业法人姓名" v-model="realNameInfo.legalPerson"></el-input>
        </el-form-item>
        <el-form-item label="责任人姓名" prop="principalName">
          <el-input class="input-w" placeholder="请输入负责人姓名" v-model="realNameInfo.principalName"></el-input>
        </el-form-item>
        <el-form-item label="责任人证件号码" prop="principalIdCard">
          <el-input class="input-w" placeholder="请输入负责人身份证号" v-model="realNameInfo.principalIdCard"></el-input>
        </el-form-item>
        <el-form-item label="责任人手机号" prop="principalMobile">
          <el-input class="input-w" placeholder="请输入责任人手机号" v-model="realNameInfo.principalMobile"></el-input>
          <el-tooltip class="item" effect="dark" content="须填写身份证号本人使用的手机号，否则报备失败" placement="top">
            <i style="margin-left: 10px;color: #409EFF;font-size: 15px;cursor: pointer;" class="el-icon-info"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="签名来源" prop="signatureType">
          <el-radio-group style="display: flex;flex-direction: column;align-items: self-start;"
            v-model="realNameInfo.signatureType">
            <el-radio style="margin-top: 10px;" :label="1">
              <span class="sig-type-title-tips">企业名称</span>
              <span style="color: #999;font-size: 12px;margin-left: 8px;">推荐全称，简称字数须为全称字数60%以上</span>
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="2">
              <span class="sig-type-title-tips">事业单位：如机关，学校，科研单位，街道社区等</span>
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="3">
              <span class="sig-type-title-tips">商标</span>
              （须提供商标注册证书图片或在中国商标网的商标查询截图）
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="4">
              <span class="sig-type-title-tips">App</span>
              （须提供app在ICP/IP/域名备案管理系统的截图）
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="5">
              <span class="sig-type-title-tips">小程序</span>
              （须提供小程序在ICP/IP/域名备案管理系统的截图）
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签名类型" prop="signatureSubType">
          <el-radio-group v-model="realNameInfo.signatureSubType"
            style="display: flex;flex-direction: column;align-items: self-start;">
            <el-radio style="margin-top: 10px;" :label="0">
              <span>全称</span>
              <span style="color: #999;font-size: 12px;margin-left: 8px;">推荐，报备快</span>
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="1">
              <span>简称</span>
              <span style="color: #999;font-size: 12px;margin-left: 8px;">请用企业/单位简称签名在企查查搜索企业唯一的图片</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件上传"
          :prop="(realNameInfo.signatureType == 1 || realNameInfo.signatureType == 2) && realNameInfo.signatureSubType == 0 ? '' : 'imgUrl'">
          <el-upload class="upload-demo" :headers="header" :action="this.API.cpus + 'v3/file/upload'" :limit="3"
            :file-list="fileList" list-type="picture-card" :on-preview="handlePictureCardPreview"
            :on-success="handleSuccess" :on-remove="handleRemove1">
            <div>
              <i class="el-icon-plus"></i>
            </div>
          </el-upload>
          <div class="el-upload__tip">支持上传jpg, jpeg, png文件，且不超过2M</div>
        </el-form-item>
        <el-form-item label="短信示例" prop="contentExample">
          <el-input type="textarea" v-model="realNameInfo.contentExample" placeholder="请输入短信示例" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormRealNameInfo('realNameInfo')">确 定</el-button>
        <el-button @click="realNameDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <!-- 实名信息批量导入弹框 -->
    <el-dialog title="实名信息批量导入" :visible.sync="realNameBatchShow" width="560px" :close-on-press-escape="false">
      <div class="batch-import-content">
        <div class="step-section">
          <div class="step-header">
            <span class="step-number">1</span>
            <span class="step-title">导出模板文件</span>
          </div>
          <div class="step-content">
            <el-button type="primary" @click="exportRealNameTemplate" size="medium">
              <i class="el-icon-download"></i> 导出信息文件表格
            </el-button>
            <span class="step-desc">先下载包含当前签名的Excel模板</span>
          </div>
        </div>

        <div class="info-section">
          <div class="info-header">
            <i class="el-icon-info" style="color: #409eff;"></i>
            <span>操作说明</span>
          </div>
          <div class="info-content">
            <div class="info-item">📁 下载Excel模板文件，请移步到文件下载中心查看</div>
            <div class="info-item">✏️ 填写完整的实名信息（企业名称、统一社会信用代码等）</div>
            <div class="info-item">💾 保存Excel文件</div>
            <div class="info-item">📤 上传已填写的Excel文件完成批量导入</div>
          </div>
        </div>

        <div class="step-section">
          <div class="step-header">
            <span class="step-number">2</span>
            <span class="step-title">上传填写完成的Excel文件</span>
          </div>
          <div class="step-content">
            <el-upload class="upload-demo" :headers="header" :action="this.API.cpus + 'signature/realName/import'"
              :limit="1" :file-list="realNameFileList" :on-success="handleRealNameImportSuccess"
              :on-remove="handleRealNameFileRemove" :before-upload="beforeRealNameUpload" accept=".xlsx,.xls">
              <el-button size="medium" type="success">
                <i class="el-icon-upload2"></i> 选择Excel文件
              </el-button>
              <div slot="tip" class="el-upload__tip">
                仅支持 .xlsx 或 .xls 格式，文件大小不超过5MB
              </div>
            </el-upload>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览弹框 -->
    <el-dialog v-model="dialogVisible">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/publicComponents/FileUpload";
import TableTem from "@/components/publicComponents/TableTem";
import Tooltip from "@/components/publicComponents/tooltip";
import { formatDate } from "@/assets/js/date.js";
import getNoce from '../../../../../../plugins/getNoce';
import Vue from "vue";

export default {
  name: "SignatureManagement",
  components: { TableTem, FileUpload, Tooltip },
  data() {
    return {
      name: "SignatureManagement",
      SigDialogVisible: false,
      fileListS: "",
      header: {},
      zipfileList: [],
      fileListBatch: [],
      batchShow: false,
      fileList: [],
      flieULR: [],
      copyUrl: "",
      copyUrlList: [],
      coverType: false,
      dialogVisible: false,
      dialogImageUrl: "",
      realNameBatchShow: false,
      realNameFileList: [],
      signatureFrom: {
        title: "",
        formData: {
          signature: "",
          signatureType: "1",
          remark: "",
          imgUrl: "",
          signatureId: "",
        },
        imgUrl: [],
        formRule: {
          signature: [
            { required: true, message: "该输入项为必填项!", trigger: "blur" },
            { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
          ],
          signatureType: [
            { required: true, message: "请选择签名类型", trigger: "change" },
          ],
          imgUrl: [
            { required: true, message: "请选择上传图片", trigger: "change" },
          ],
        },
        signature: "",
        signatureId: "",
      },
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          signature: "",
          auditStatus: "",
          isComplete: null,
          currentPage: 1,
          pageSize: 10,
        },
        total: 0,
        tableData: [],
      },
      fileStyle: {
        size: 5,
        type: "img",
        style: ["jpg", "jpeg", "bmp", "gif", "png"],
      },
      del1: true,
      realNameDialogVisible: false,
      realNameInfo: {
        companyName: "",
        creditCode: "",
        legalPerson: "",
        principalIdCard: "",
        principalName: "",
        principalMobile: "",
        signatureId: "",
        imgUrl: "",
        signatureType: "",
        signatureSubType: 0,
        contentExample: "",
      },
      formRules: {
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'change' },
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
        ],
        legalPerson: [
          { required: true, message: '请输入法人姓名', trigger: 'change' },
        ],
        principalIdCard: [
          { required: true, message: '请输入负责人身份证号', trigger: 'change' },
        ],
        principalName: [
          { required: true, message: '请输入负责人姓名', trigger: 'change' },
        ],
        principalMobile: [
          { required: true, message: '请输入责任人手机号', trigger: 'change' },
        ],
        imgUrl: [
          { required: true, message: '请上传文件', trigger: 'change' },
        ],
        signatureType: [
          { required: true, message: '请选择签名来源', trigger: 'change' },
        ],
        signatureSubType: [
          { required: true, message: '请选择签名类型', trigger: 'change' },
        ],
        contentExample: [
          { required: true, message: '请输入短信示例', trigger: 'change' },
        ],
      },
    };
  },
  methods: {
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '0': 'info',     // 编辑中
        '1': 'warning',  // 待审核
        '2': 'success',  // 已通过
        '3': 'danger'    // 驳回
      };
      return statusMap[status] || 'info';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        '0': '编辑中',
        '1': '待审核',
        '2': '已通过',
        '3': '驳回'
      };
      return textMap[status] || '未知';
    },

    // 获取标签类型
    getTagType(content) {
      const trimmed = content.trim();
      if (trimmed === '验证码') {
        return 'success';
      } else if (trimmed === '行业') {
        return 'success';
      } else if (trimmed === '通知') {
        return 'success';
      }
      return 'success'; // 默认蓝色
    },

    // 查看实名明细
    handelCheck(row) {
      // 构造实名明细信息
      const realNameDetails = [];
      
      // 移动实名信息
      if (row.ydAvalibleType && row.ydAvalibleType.trim()) {
        realNameDetails.push({
          operator: '🟦 中国移动',
          status: '✅ 已实名',
          types: row.ydAvalibleType.split(',').map(t => t.trim()),
          icon: '📱'
        });
      } else {
        realNameDetails.push({
          operator: '🟦 中国移动',
          status: '❌ 未实名',
          types: [],
          icon: '📱'
        });
      }
      
      // 联通实名信息
      if (row.ltAvalibleType && row.ltAvalibleType.trim()) {
        realNameDetails.push({
          operator: '🟦 中国联通',
          status: '✅ 已实名',
          types: row.ltAvalibleType.split(',').map(t => t.trim()),
          icon: '📞'
        });
      } else {
        realNameDetails.push({
          operator: '🟦 中国联通',
          status: '❌ 未实名',
          types: [],
          icon: '📞'
        });
      }
      
      // 电信实名信息
      if (row.dxAvalibleType && row.dxAvalibleType.trim()) {
        realNameDetails.push({
          operator: '🟦 中国电信',
          status: '✅ 已实名',
          types: row.dxAvalibleType.split(',').map(t => t.trim()),
          icon: '📡'
        });
      } else {
        realNameDetails.push({
          operator: '🟦 中国电信',
          status: '❌ 未实名',
          types: [],
          icon: '📡'
        });
      }

      // 构造详细信息HTML
      let detailHtml = `<div style="text-align: left; line-height: 1.8;">
        <h4 style="color: #409eff; margin-bottom: 15px; text-align: center;">📋 签名实名状态详情</h4>
        <p style="margin-bottom: 15px; padding: 8px; background: #f0f9ff; border-radius: 4px; color: #303133;">
          <strong>签名内容：</strong>${row.signature}
        </p>`;
      
      realNameDetails.forEach(detail => {
        detailHtml += `<div style="margin-bottom: 12px; padding: 10px; border: 1px solid #ebeef5; border-radius: 6px; background: #fafafa;">`;
        detailHtml += `<div style="display: flex; align-items: center; margin-bottom: 8px;">`;
        detailHtml += `<span style="font-weight: 600; color: #303133;">${detail.operator}</span>`;
        detailHtml += `<span style="margin-left: 10px;">${detail.status}</span>`;
        detailHtml += `</div>`;
        
        if (detail.types.length > 0 && detail.status.includes('已实名')) {
          detailHtml += `<div style="margin-top: 8px;">`;
          detailHtml += `<span style="color: #666; font-size: 13px;">可用业务类型：</span><br/>`;
          detail.types.forEach(type => {
            let color = '#409eff'; // 默认蓝色
            if (type === '行业') color = '#e6a23c'; // 橙色
            else if (type === '通知') color = '#67c23a'; // 绿色
            
            detailHtml += `<span style="display: inline-block; margin: 3px 4px 3px 0; padding: 2px 8px; background: ${color}; color: white; border-radius: 12px; font-size: 12px;">${type}</span>`;
          });
          detailHtml += `</div>`;
        }
        
        detailHtml += `</div>`;
      });
      
      detailHtml += `</div>`;

      this.$alert(detailHtml, '实名明细', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        customClass: 'real-name-detail-dialog'
      });
    },

    /* --------------- 列表展示 ------------------*/
    gettableData() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "signature/signatureList",
        this.tableDataObj.tablecurrent,
        (res) => {
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.total = res.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    submitForm() {
      this.tableDataObj.tablecurrent.currentPage = 1;
      this.gettableData();
    },
    resetForm() {
      this.tableDataObj.tablecurrent.currentPage = 1;
      this.tableDataObj.tablecurrent.pageSize = 10;
      this.tableDataObj.tablecurrent.signature = "";
      this.tableDataObj.tablecurrent.auditStatus = "";
      this.tableDataObj.tablecurrent.isComplete = null;
      this.gettableData();
    },
    handleSizeChange(size) {
      this.tableDataObj.tablecurrent.pageSize = size;
      this.gettableData();
    },
    handleCurrentChange: function (currentPage) {
      this.tableDataObj.tablecurrent.currentPage = currentPage;
      this.gettableData();
    },
    
    /* ---- 操作功能 -------*/
    clickAddSig() {
      this.$router.push({ path: "/CreateSign" });
    },
    details(index, val) {
      this.$router.push({ path: "/CreateSign", query: { d: val.signatureId } });
    },
    dele(index, val) {
      this.$confirms.confirmation(
        "delete",
        "此操作将永久删除该数据, 是否继续？",
        this.API.cpus + "signature/delete?signatureId=" + val.signatureId,
        {},
        (res) => {
          this.gettableData();
        }
      );
    },
    
    /* ---- 批量导入 -------*/
    batchQu() {
      this.batchShow = true;
    },
    handleSuccessBatch(res) {
      if (res.code == 200) {
        this.$message({
          type: "success",
          duration: "2000",
          message: "导入成功",
        });
        this.batchShow = false;
        this.fileListBatch = [];
        this.gettableData();
      } else {
        this.$message({
          type: "error",
          duration: "2000",
          message: res.msg,
        });
      }
    },
    handleRemoveBatch() {
      this.fileListBatch = [];
    },
    handleSuccessZip(res) {
      if (res.code == 200) {
        let timeStamp = new Date().getTime();
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = 'data:application/octet-stream;base64,' + res.data
        link.download = timeStamp + '.xlsx'
        link.click()
      }
    },
    handleRemoveZip(file, fileList) {
      this.zipfileList = [];
    },
    beforeAvatarUploadc(file) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const fileType = ["zip"];
      const isLt5M = file.size / 1024 / 1024 < 20;
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning("上传文件只能是zip格式!");
        return false;
      }
      if (!isLt5M) {
        this.$message.warning("上传缩略图大小不能超过20M！");
        return false;
      }
    },
    
    /* ---- 实名信息相关 -------*/
    getRealNameInfo(row) {
      this.realNameDialogVisible = true;
      this.realNameInfo.signatureId = row.signatureId;
      this.$api.get(this.API.cpus + 'signature/findModel/realName?signatureId=' + row.signatureId, {}, res => {
        if (res.code == 200) {
          this.realNameInfo.companyName = res.data.companyName;
          this.realNameInfo.creditCode = res.data.creditCode;
          this.realNameInfo.legalPerson = res.data.legalPerson;
          this.realNameInfo.principalIdCard = res.data.principalIdCard;
          this.realNameInfo.principalMobile = res.data.principalMobile;
          this.realNameInfo.principalName = res.data.principalName;
          this.realNameInfo.signatureType = res.data.signatureType;
          this.realNameInfo.signatureSubType = res.data.signatureSubType;
          this.realNameInfo.contentExample = res.data.contentExample;
          if (res.data.imgUrl) {
            this.realNameInfo.imgUrl = res.data.imgUrl;
            this.copyUrl = res.data.imgUrl;
            this.flieULR = res.data.imgUrl.split(",").filter(item => item.trim() !== '');
            this.fileList = this.flieULR.map((item) => {
              return {
                name: item,
                url: this.API.imgU + item,
              };
            });
            let imgUrlList = JSON.stringify(this.fileList)
            this.copyUrlList = JSON.parse(imgUrlList)
          } else {
            this.fileList = [];
          }
        }
      })
    },
    submitFormRealNameInfo(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation("post", "确认修改实名信息？", this.API.cpus + "signature/realName/edit ", this.realNameInfo, res => {
            if (res.code == 200) {
              this.realNameDialogVisible = false;
              this.gettableData();
            }
          })
        }
      })
    },
    handelClose() {
      this.realNameDialogVisible = false;
    },
    handleSuccess(res) {
      if (res.code == 200) {
        this.flieULR.push(res.data.fullpath);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemove1(file, fileList) {
      if (file.response) {
        this.flieULR.splice(this.flieULR.indexOf(file.response.data.fullpath), 1);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      } else {
        this.flieULR.splice(this.flieULR.indexOf(file.name), 1);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    
    /* ---- 实名信息批量导入 -------*/
    realNameBatchImport() {
      this.realNameBatchShow = true;
    },
    exportRealNameTemplate() {
      const loading = this.$loading({
        lock: true,
        text: '正在导出实名信息模板...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      this.$api.post(this.API.cpus + 'signature/realName/export', {}, res => {
        loading.close();
        if (res.code == 200) {
          this.$message.success('请去文件下载中心查看实名信息模板导出文件！');
          this.$router.push('/FileExport');
        }
      }, err => {
        loading.close();
        console.error('导出失败:', err);
        this.$message.error('导出失败，请检查网络连接');
      });
    },
    beforeRealNameUpload(file) {
      const fileName = file.name;
      const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
      const allowedExtensions = ['xlsx', 'xls'];
      const isValidType = allowedExtensions.includes(fileExtension);
      const isValidSize = file.size / 1024 / 1024 < 5;

      if (!isValidType) {
        this.$message.warning('只能上传 Excel 文件（.xlsx 或 .xls 格式）！');
        return false;
      }
      if (!isValidSize) {
        this.$message.warning('文件大小不能超过 5MB！');
        return false;
      }

      this.$message.info('文件上传中，请稍候...');
      return true;
    },
    handleRealNameImportSuccess(response, file, fileList) {
      if (response.code == 200) {
        this.$notify({
          title: '导入成功',
          message: `实名信息批量导入成功！${response.data ? response.data : ''}`,
          type: 'success',
          duration: 4000
        });
        this.realNameBatchShow = false;
        this.realNameFileList = [];
        this.gettableData();
      } else {
        this.$notify({
          title: '导入失败',
          message: response.msg || '导入失败，请检查文件格式和内容',
          type: 'error',
          duration: 6000
        });
      }
    },
    handleRealNameFileRemove(file, fileList) {
      this.realNameFileList = [];
    },
    
    /* ---- 其他方法 -------*/
    signature_add(formName, title) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let formDates = {};
          Object.assign(formDates, this.signatureFrom.formData);
          formDates.signature = "【" + this.signatureFrom.formData.signature + "】";
          
          if (this.signatureFrom.title == "创建短信签名") {
            delete formDates.signatureId;
            if (this.signatureFrom.formData.signatureType != 6) {
              if (this.signatureFrom.formData.imgUrl != "") {
                this.sendRequest("post", "确定新增签名", this.API.cpus + "signature/saveInfo", formDates);
              } else {
                this.$message({ message: "请上传图片！", type: "warning" });
              }
            } else {
              this.sendRequest("post", "确定新增签名", this.API.cpus + "signature/saveInfo", formDates);
            }
          } else {
            formDates.auditStatus = "1";
            if (this.signatureFrom.formData.signatureType != 6) {
              if (this.signatureFrom.formData.imgUrl != "") {
                this.$confirms.confirmation("put", "确定修改签名", this.API.cpus + "signature/update", formDates, (res) => {
                  this.gettableData();
                  this.SigDialogVisible = false;
                  this.signatureFrom.formData = {
                    signature: "",
                    signatureType: "1",
                    remark: "",
                    imgUrl: "",
                    signatureId: "",
                  };
                });
              } else {
                this.$message({ message: "请上传图片！", type: "warning" });
              }
            } else {
              this.$confirms.confirmation("put", "确定修改签名", this.API.cpus + "signature/update", formDates, (res) => {
                this.gettableData();
                this.SigDialogVisible = false;
                this.signatureFrom.formData = {
                  signature: "",
                  signatureType: "1",
                  remark: "",
                  imgUrl: "",
                  signatureId: "",
                };
              });
            }
          }
        } else {
          return false;
        }
      });
    },
    sendRequest(type, title, action, formDates) {
      this.$api.get(
        this.API.cpus + "signature/findModelBySignature?signature=【" + this.signatureFrom.formData.signature + "】",
        {},
        (res) => {
          if (res.code == 200 && res.data == "0") {
            this.$confirms.confirmation(type, title, action, formDates, (res) => {
              this.gettableData();
              this.SigDialogVisible = false;
            });
          } else {
            this.$message({ message: "签名已存在，切勿重复！", type: "warning" });
          }
        }
      );
    },
    fileup(val) {
      let aa = this.signatureFrom.formData.imgUrl.split(",");
      if (val.response) {
        aa.splice(aa.indexOf(val.response.fullPath), 1);
      } else {
        let c = val.url;
        let d = c.slice(c.indexOf("group1"));
        aa.splice(aa.indexOf(d), 1);
      }
      this.signatureFrom.imgUrl = aa;
      this.signatureFrom.formData.imgUrl = aa.join(",");
    },
    fileupres(val) {
      this.signatureFrom.imgUrl.push(val.fullPath);
      this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
    },
  },
  watch: {
    SigDialogVisible(val) {
      if (val == false) {
        this.signatureFrom.imgUrl = [];
        this.signatureFrom.formData.imgUrl = "";
        this.del1 = false;
        this.$refs.signatureFrom.resetFields();
        this.fileListS = "";
      }
    },
    batchShow(val) {
      if (!val) {
        this.coverType = false;
        this.fileList = [];
        this.zipfileList = [];
        this.fileListBatch = [];
      }
    },
    realNameDialogVisible: function (val) {
      if (!val) {
        this.realNameInfo.companyName = "";
        this.realNameInfo.creditCode = "";
        this.realNameInfo.legalPerson = "";
        this.realNameInfo.principalIdCard = "";
        this.realNameInfo.principalName = "";
        this.realNameInfo.signatureId = "";
        this.flieULR = [];
        this.fileList = [];
        this.copyUrl = "";
        this.copyUrlList = [];
        this.realNameInfo.imgUrl = "";
        this.realNameInfo.signatureType = "";
        this.realNameInfo.signatureSubType = "";
      }
    },
    realNameBatchShow: function (val) {
      if (!val) {
        this.realNameFileList = [];
      }
    },
  },
  mounted() {
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.gettableData();
  },
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* SignatureManagement 特有样式 */
.input-w {
  width: 300px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

/* 运营商表头样式 - 与目标项目一致 */
.operator-header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 13px;
  color: #303133;
  height: 100%;
  padding: 8px 4px;
}

/* 单个运营商状态样式 - 确保垂直居中 */
.single-operator-status {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 2px;
  min-height: 40px;
}

/* 多标签容器 - 垂直排列并居中 */
.status-tags-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
  width: 100%;
}

/* 标签基础样式 - 完全按照目标项目设置 */
.status-tag {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: auto !important;
  line-height: 1.3 !important;
  border-radius: 4px !important;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 !important;
  border: 1px solid transparent;
}

/* 固定宽度标签 - 保持一致的宽度 */
.status-tag.fixed-width {
  width: 64px !important;
  min-width: 64px !important;
  max-width: 64px !important;
  padding: 4px 6px !important;
}

/* 自适应宽度标签 - 根据内容调整 */
.status-tag.auto-width {
  min-width: 48px !important;
  max-width: 120px !important;
  padding: 4px 8px !important;
  width: auto !important;
}

/* 成功状态标签样式 */
.status-tag.success {
  font-weight: 500;
}

/* 悬停效果 */
.status-tag:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Element UI 标签类型覆盖 - 确保颜色正确 */
.status-tag.el-tag--primary {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.status-tag.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #f5dab1;
  color: #e6a23c;
}

.status-tag.el-tag--success {
  background-color: #f0f9ec;
  border-color: #c2e7b0;
  color: #67c23a;
}

.status-tag.el-tag--info {
  background-color: #f4f4f5;
  border-color: #d3d4d6;
  color: #909399;
}

/* 实名明细弹窗样式 */
.real-name-detail-dialog {
  width: 500px !important;
}

.real-name-detail-dialog .el-message-box__content {
  padding: 20px !important;
  max-height: 400px;
  overflow-y: auto;
}

.real-name-detail-dialog .el-message-box__message {
  color: #303133 !important;
  font-size: 14px;
}

/* 批量导入弹窗样式 */
.batch-import-content {
  padding: 20px;
}

.step-section {
  margin-bottom: 24px;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409eff;
  color: #fff;
  border-radius: 50%;
  font-size: 14px;
  font-weight: bold;
  margin-right: 8px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.step-content {
  margin-left: 32px;
}

.step-desc {
  margin-left: 12px;
  color: #666;
  font-size: 13px;
}

.info-section {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #409eff;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #303133;
  font-weight: 600;
}

.info-header i {
  margin-right: 8px;
  font-size: 16px;
}

.info-content {
  margin-left: 24px;
}

.info-item {
  color: #606266;
  font-size: 13px;
  line-height: 1.8;
  margin-bottom: 4px;
}
</style>
<style>
.el-table--small th {
  background: #f5f5f5;
}
</style>