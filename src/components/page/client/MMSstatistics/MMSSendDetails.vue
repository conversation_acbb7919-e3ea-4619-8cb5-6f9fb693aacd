<template>
  <div id="shortMessage" style="background: #fff">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 发送详情</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="fillet ShortMessageRecording-chart-box">
      <!-- <div class=" ShortMessageRecording-chart-title">
                <slot name="sele"></slot>
                <el-radio-group v-model="specificTime" size="small" text-color="#fff" fill="#16a589">
                    <el-radio-button label="1" >近1小时</el-radio-button>
                    <el-radio-button label="2" class="threeDay">近7天</el-radio-button>
                </el-radio-group>
            </div> -->
      <div class="short-message-recording-type">
        <el-form ref="SHformList" :model="form" label-width="86px" class="query-frame">
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入手机号" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="发送状态" prop="smsStatus" v-if="this.$store.state.isDateState == 1">
            <el-select v-model="form.smsStatus" placeholder="发送状态" class="input-w">
              <el-option label="全部" value=""></el-option>
              <el-option label="成功" value="1"></el-option>
              <el-option label="失败" value="2"></el-option>
              <el-option label="待返回" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="发送时间" prop="time1">
            <el-date-picker class="input-time" v-model="form.time1" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" :picker-options="pickerOptions" :clearable="false" @change="getTimeOperating"
              start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain @click="querySending()">查 询</el-button>
            <el-button type="primary" plain @click="resetSending('SHformList')">重 置</el-button>
            <el-button type="primary" @click="exportNums1()">导出数据</el-button>
            <!-- <el-button type="primary" v-if="specificTime==2" @click="exportNums()">导出数据</el-button> -->
          </div>
        </el-form>
        <div style="padding-bottom: 40px">
          <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
            border :data="tableDataObj.tableData" style="width: 100%">
            <el-table-column width="350" label="消息ID">
              <template slot-scope="scope">
                {{ scope.row.msgid }}
                <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy"
                  @click="handleCopy(scope.row.msgid, $event)"></i>
              </template>
            </el-table-column>
            <el-table-column label="标题">
              <template slot-scope="scope">{{ scope.row.title }}</template>
            </el-table-column>
            <el-table-column width="70" label="预览">
              <template slot-scope="scope">
                <span style="cursor: pointer; color: #16a589" @click="View(scope.row)"><i
                    class="el-icon-picture"></i>预览</span>
              </template>
            </el-table-column>
            <el-table-column width="70" label="计费条数">
              <template slot-scope="scope">{{ scope.row.chargeNum }}</template>
            </el-table-column>
            <el-table-column label="手机号码">
              <template slot-scope="scope">
                <div style="color: #16a589; cursor: pointer" @click="tableContent(scope.row, scope.$index)">
                  <span>{{ scope.row.mobile }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="发送时间" width="170">
              <template slot-scope="scope">{{ scope.row.sendTime }}</template>
            </el-table-column>
            <el-table-column label="状态上报时间" width="170">
              <template slot-scope="scope">{{ scope.row.reportTime }}</template>
            </el-table-column>
            <el-table-column label="发送状态" v-if="this.$store.state.isDateState == 1">
              <template slot-scope="scope">
                <span v-if="scope.row.status == 1">成功</span>
                <span v-else-if="scope.row.status == 2">失败</span>
                <span v-else-if="scope.row.status == 3">待返回</span>
                <span v-else>未知</span>
              </template>
            </el-table-column>
          </el-table>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
            style="background: #fff; padding: 10px 0; text-align: right">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="1" :page-size="formData.pageSize" :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow">
            </el-pagination>
          </el-col>
        </div>
      </div>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" :visible.sync="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../../assets/images/phone.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  ">{{ title }}</span>
              </div>
              <div style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                " v-for="(item, index) in viewData" :key="index">
                <img v-if="item.media == 'jpg' || item.media == 'gif'"
                  :src="API.imgU + item.mediaGroup + '/' + item.mediaPath" style="width: 235px"
                  class="avatar video-avatar" ref="avatar" />
                <video v-if="item.type == 'video'" style="width: 235px"
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath" class="avatar video-avatar"
                  controls="controls"></video>
                <audio v-if="item.type == 'audio'" style="width: 235px" autoplay="autoplay" controls="controls"
                  preload="auto" v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"></audio>
                <div style="white-space: pre-line">
                  {{ item.txt }}
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- <el-dialog
            title="手机验证"
            :visible.sync="exportFlag"
            width="40%"
            top="30vh"
        >  
            <DownLoadExport ref="ExportChild" :formData1='formData' productType='3' :isDownload='formData.isDownload' :phoneList="phoneList"/>
        </el-dialog> -->
    <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
  </div>
</template>

<script>
import DatePlugin from "@/components/publicComponents/DatePlugin";
import DownLoadExport from "@/components/publicComponents/downLodExport";
import clip from '../../utils/clipboard'
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../common/bus";
import common from "../../../../assets/js/common";
import moment from "moment";
export default {
  name: "MMSSendDetails",
  components: {
    DatePlugin,
    DownLoadExport,
    ResetNumberVue
  },
  data() {
    return {
      name: "MMSSendDetails",
      dialogVisible: false,
      phoneList: [],
      exportFlag: false,
      viewData: [], // 查看内容
      title: "",
      pickerOptions: {
        disabledDate(time) {
          //   return time.getTime() > Date.now();
          return time.getTime() < Date.now() - 30 * 24 * 3600 * 1000 || time.getTime() > Date.now();
        },
      },
      //复选框的值
      selectId: "",
      // 单条存值
      AddBlackVal: "",
      // 备注
      AddBlackform: {
        remark: "",
      },
      AddBlacks: false,
      AddBlacksStatus: false,
      //发送查询的值
      form: {
        mobile: "",
        smsStatus: "",
        title: "",
        temId: "",
        sendBeginTime: moment()
          .subtract(1, "months")
          .format("YYYY-MM-DD HH:mm:ss"),
        sendEndTime: moment(Date.now()).format("YYYY-MM-DD HH:mm:ss"),
        time1: [
          moment().subtract(1, "months").format("YYYY-MM-DD HH:mm:ss") + "",
          moment(Date.now()).format("YYYY-MM-DD HH:mm:ss"),
        ],
        currentPage: 1,
        pageSize: 10,
        time: "",
        isDownload: 2,
      },
      //复制发送查询的值
      formData: {
        mobile: "",
        smsStatus: "",
        title: "",
        temId: "",
        sendBeginTime: moment()
          .subtract(1, "months")
          .format("YYYY-MM-DD HH:mm:ss"),
        sendEndTime: moment(Date.now()).format("YYYY-MM-DD HH:mm:ss"),
        time1: [
          moment().subtract(1, "months").format("YYYY-MM-DD HH:mm:ss") + "",
          moment(Date.now()).format("YYYY-MM-DD HH:mm:ss"),
        ],
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      tableDataObj: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
      },
      resetVideo: false,
      infoData: {},
    };
  },
  methods: {
    //发送请求
    sendReport() {
      let data = Object.assign({}, this.formData);
      // data.flag = flag;
      this.$api.post(this.API.cpus + "v1/consumermms/messages", data, (res) => {
        this.tableDataObj.tableData = res.data.records;
        this.tableDataObj.totalRow = res.data.total;
        this.tableDataObj.loading2 = false;
      });
    },
    handleCopy(name, event) {
      clip(name, event)
    },
    // 预览
    View(val) {
      this.viewData = val.contents;
      this.title = val.title;
      this.dialogVisible = true;
    },
    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.form.sendBeginTime = val[0] + " 00:00:00";
        this.form.sendEndTime = val[1] + " 23:59:59";
      } else {
        this.form.sendBeginTime = "";
        this.form.sendEndTime = "";
      }
    },
    //查询 （发送）
    querySending() {
      Object.assign(this.formData, this.form);
      this.sendReport();
      // this.sendReport()
    },
    //重置 （发送）
    resetSending(formName) {
      this.$refs[formName].resetFields();
      (this.form.sendBeginTime = moment()
        .subtract(1, "months")
        .format("YYYY-MM-DD HH:mm:ss")),
        (this.form.sendEndTime = moment(Date.now()).format(
          "YYYY-MM-DD HH:mm:ss"
        )),
        Object.assign(this.formData, this.form);
      this.sendReport();
    },
    //获取分页的每页数量
    handleSizeChange(size) {
      this.formData.pageSize = size;
      this.sendReport();
    },
    //获取分页的第几页
    handleCurrentChange(currentPage) {
      this.formData.currentPage = currentPage;
      this.sendReport();
    },
    // 点击手机号
    tableContent(row, index) {
      this.$api.post(
        this.API.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          smsInfoId: row.decryptMobile,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
          } else if (res.code == 4004002) {
            common.fetchData().then((res) => {
              if (res.code == 200) {
                if (res.data.isAdmin == 1) {
                  this.resetVideo = true;
                  this.infoData = res.data;
                } else {
                  this.$message({
                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                    type: "warning",
                  });
                }
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }

            })
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
          // this.tableDataObj.tableData[index].mobile=res.data
          // this.tableDataObj1.tableData[index].mobile=res.data
        }
      );
    },
    handleClose() {
      this.exportFlag = false;
    },
    getLoginPhone() {
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data;
          }
        }
      );
    },
    exportNums1() {
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
      } else {
        // this.exportFlag = true
        //         this.getLoginPhone()
        let data = Object.assign({}, this.formData);
        data.productType = 3;
        //  this.$File.export(this.API.cpus +'statistics/export', data,`彩信发送明显.zip`)
        this.$api.post(this.API.cpus + "statistics/export", data, (res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              duration: "2000",
              message: "已加入到文件下载中心!",
            });
          } else {
            this.$message({
              type: "error",
              duration: "2000",
              message: res.msg,
            });
          }
        });
        // let first = this.form.sendBeginTime; //开始时间
        // let second = this.form.sendEndTime; //结束时间
        // var data1 = Date.parse(first.replace(/-/g, "/"));
        // var data2 = Date.parse(second.replace(/-/g, "/"));
        // var datadiff = data2 - data1;
        // var time = 30 * 24 * 60 * 60 * 1000;
        // console.log(first.length);
        // console.log(second.length);
        // if (first.length > 0 && second.length > 0) {
        //   if (datadiff < 0 || datadiff > time) {
        //     this.$message({
        //       message: "前后时间间隔不能大于30天",
        //       type: "warning",
        //     });
        //     return false;
        //   } else {

        //   }
        // }
      }
    },
  },
  created() {
    bus.$on("closeVideo", (msg) => {
      this.resetVideo = msg;
    });
    this.sendReport();
  },
  // activated(){
  //     this.sendReport();
  // },
  watch: {
    //监听查询框的值是否改变
    // formData:{
    //     handler(val){
    //         this.sendReport();
    //     },
    //     deep:true,
    //     immediate: true,
    // },
    AddBlacks(val) {
      if (val == false) {
        this.AddBlacksStatus = false;
        this.$refs.AddBlackformRef.resetFields();
      }
    },
  },
};
</script>

<style scoped>
.ShortMessageRecording-chart-box {
  margin: 10px 0;
  padding: 20px 20px 20px 20px;
}

.ShortMessageRecording-chart-title {
  display: flex;
}

.ShortMessageRecording-chart {
  height: 360px;
}

.ShortMessageRecording-title {
  padding-top: 40px;
  font-weight: bold;
}

.look-at-more {
  color: #16a589;
}

.ShortMessageRecording-select {
  position: relative;
  margin-top: 10px;
}

.short-message-recording-type {
  margin-top: 40px;
}

.query-frame {
  margin-bottom: 40px;
}

.query-frame .el-form-item {
  display: inline-block;
  margin-bottom: 12px;
}
</style>
<style>
.ShortMessageRecording-chart-box .el-radio-button:last-child .el-radio-button__inner {
  border-radius: 0;
}

.ShortMessageRecording-chart-box .el-range-editor.el-input__inner {
  border-radius: 0px 4px 4px 0;
}

.ShortMessageRecording-chart-box .el-table--small td,
.ShortMessageRecording-chart-box .el-table--small th {
  padding-top: 8px;
  padding-bottom: 8px;
}

.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}

.send-mobel-box img {
  width: 300px;
}

.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
</style>
