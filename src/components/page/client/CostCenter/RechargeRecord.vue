<template>
    <div class="simple-signature-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 标题信息区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <div class="page-info">
                                    <i class="el-icon-coin"></i>
                                    <span class="page-title">充值记录</span>
                                </div>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <el-form 
                                ref="chaxunObj" 
                                :model="chaxunObj" 
                                class="search-form">
                                <div class="search-row">
                                    <el-form-item label="充值日期" class="search-item" label-width="80px">
                                        <date-plugin 
                                            class="search-date"  
                                            :datePluginValueList="datePluginValueList"  
                                            @handledatepluginVal="handledatepluginVal">
                                        </date-plugin>
                                    </el-form-item>
                                    
                                    <el-form-item label="充值类型" class="search-item" label-width="80px">
                                        <el-select 
                                            v-model="chaxunObj.productId" 
                                            placeholder="请选择类型"
                                            class="search-select">
                                            <el-option label="全部" value=""></el-option>
                                            <el-option
                                                v-for="item in toptions"
                                                :key="item.id"
                                                :label="item.name"
                                                :value="item.id">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>

                                    <div class="search-buttons">
                                        <el-button 
                                            type="primary" 
                                            @click="query()" 
                                            class="action-btn primary"
                                            icon="el-icon-search">
                                            查询
                                        </el-button>
                                        <el-button 
                                            @click="reset()" 
                                            class="action-btn"
                                            icon="el-icon-refresh">
                                            重置
                                        </el-button>
                                    </div>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>

                <!-- 表格内容 -->
                <div class="table-container">
                    <el-table
                        v-loading="tableDataObj.loading2"
                        element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading"
                        element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable"
                        border
                        :stripe="true"
                        :data="tableDataObj.tableData"
                        class="enhanced-table">
                        
                        <el-table-column label="充值日期" width="180" align="center">
                            <template slot-scope="scope">
                                <div class="recharge-date">
                                    <i class="el-icon-time"></i>
                                    <span>{{ scope.row.rechargeTime }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="充值类型" width="160" align="center">
                            <template slot-scope="scope">
                                <el-tag 
                                    :type="getProductTypeTag(scope.row.productName)" 
                                    size="small">
                                    {{ scope.row.productName }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="充值数量" width="150" align="center">
                            <template slot-scope="scope">
                                <div class="recharge-amount">
                                    <i class="el-icon-plus"></i>
                                    <span class="amount-number">{{ scope.row.rechargeNum }}</span>
                                    <span class="amount-unit">({{ scope.row.unit }})</span>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="订单号" min-width="200" align="center">
                            <template slot-scope="scope">
                                <div class="order-number">
                                    <i class="el-icon-document-copy"></i>
                                    <span>{{ scope.row.orderNumber }}</span>
                                    <el-button 
                                        type="text" 
                                        size="mini" 
                                        @click="copyOrderNumber(scope.row.orderNumber)"
                                        class="copy-btn">
                                        <i class="el-icon-copy-document"></i>
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                        
                        <el-table-column label="备注" min-width="150">
                            <template slot-scope="scope">
                                <div class="recharge-note">
                                    <span v-if="scope.row.rechargeNote">{{ scope.row.rechargeNote }}</span>
                                    <span v-else class="no-note">暂无备注</span>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div class="pagination-container">
                        <el-pagination 
                            @size-change="handleSizeChange" 
                            @current-change="handleCurrentChange" 
                            :current-page="chaxunObj.currentPage"  
                            :page-size="chaxunObj.pageSize" 
                            :page-sizes="[10, 20, 50, 100, 300]"  
                            layout="total, sizes, prev, pager, next, jumper" 
                            :total="tableDataObj.total"
                            class="enhanced-pagination">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import TableTem from '../../../publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import { mapState, mapMutations,mapActions } from "vuex";
export default {
  name: "RechargeRecord",
  components: {TableTem,DatePlugin},
  computed:{
      ...mapState({  //比如'movies/hotMovies
          roleId:state=>state.userId,
        })
  },
  data() {
    return {
      datePluginValueList: { //日期选择器
          type:"daterange",
          start:"",
          end:'',
          range:'-',
          
          clearable:true,
          pickerOptions:{
                disabledDate: (time) => {
                    return time.getTime() > Date.now() ;
                }
              },
          datePluginValue: ''
      },
      toptions:[],
      formInline:{
        currentPage:1,
        pageSize:10,
        beginTime:'',
        endTime:'',
        productId:''
      },
      chaxunObj:{
        currentPage:1,
        pageSize:10,
        beginTime:'',
        endTime:'',
        productId:''
      },
      tableDataObj:{
        total:0,
        tableData: [],
      },
    };
  },
  methods: {
    reset(){//重置   
      this.datePluginValueList.datePluginValue ='';
      this.formInline.beginTime='';
      this.formInline.endTime='';
      this.formInline.productId='';
      this.formInline.pageSize=10;
      this.formInline.currentPage=1;
      Object.assign(this.chaxunObj,this.formInline);
      this.getDate();
    },
    // 获取产品类型标签颜色
    getProductTypeTag(productName) {
      const typeMap = {
        '短信': 'primary',
        '彩信': 'success', 
        '视频短信': 'warning',
        '国际短信': 'info',
        '语音': 'danger',
        '闪验': 'success'
      };
      
      for (let key in typeMap) {
        if (productName && productName.includes(key)) {
          return typeMap[key];
        }
      }
      return 'primary';
    },
    // 复制订单号
    copyOrderNumber(orderNumber) {
      if (!orderNumber) {
        this.$message.warning('订单号为空');
        return;
      }
      
      // 创建临时textarea元素来复制文本
      const textarea = document.createElement('textarea');
      textarea.value = orderNumber;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
      
      this.$message.success('订单号已复制到剪贴板');
    },
    // 查询
    query(){
      Object.assign(this.formInline,this.chaxunObj);
      this.getDate();
    },
    getDate(){
      this.tableDataObj.loading2 = true;
        this.$api.get(this.API.recharge+'client/recharge/page?'+'currentPage='+this.chaxunObj.currentPage+'&pageSize='+this.chaxunObj.pageSize+'&beginTime='+this.chaxunObj.beginTime+'&endTime='+this.chaxunObj.endTime+'&productId='+this.chaxunObj.productId,{},res=>{
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
        })
    },
    handleSizeChange(size) { //分页一页的size
      this.chaxunObj.pageSize = size
      this.getDate();
    },
    handleCurrentChange: function(currentPage){//分页第几页
      this.chaxunObj.currentPage = currentPage;
      this.getDate();
    },
    handledatepluginVal: function(val1,val2){
      if(val1){
          this.chaxunObj.beginTime = val1;
          this.chaxunObj.endTime = val2;
      }else{
        this.chaxunObj.beginTime = '';
          this.chaxunObj.endTime = '';
      }
    },
    getList(){
        this.$api.get(
        this.API.recharge + "products",
        {},
        (res) => {
          console.log(res);
          this.toptions = res.data
        }
      );
    },
  },
  created(){
    this.getDate();
    this.getList()
  },
  // activated(){
  //   this.getDate();
  //   this.getList()
  // },
  // watch:{
  //   chaxunObj:{
  //     handler(){
  //         this.getDate();
  //     },
  //     deep:true,
  //     immediate:true
  //   }
  // }
}
</script>

<style lang="less" scoped>
@import "~@/styles/template-common.less";

.search-section {
    margin-top: 16px;

    .search-form {
        .search-row {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;

            .search-item {
                margin-bottom: 0;

                ::v-deep .el-form-item__label {
                    color: #333;
                    font-weight: 500;
                    white-space: nowrap;
                }

                .search-date,
                .search-select {
                    min-width: 220px;
                }
            }

            .search-buttons {
                display: flex;
                gap: 12px;
                margin-left: auto;

                .action-btn {
                    min-width: 80px;
                    height: 36px;
                    border-radius: 6px;
                    font-weight: 500;
                    transition: all 0.3s ease;

                    &.primary {
                        background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
                        border: none;
                        color: #ffffff;

                        &:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
                        }
                    }

                    &:not(.primary) {
                        background: #ffffff;
                        border: 1px solid #d9d9d9;
                        color: #333;

                        &:hover {
                            border-color: #409eff;
                            color: #409eff;
                        }
                    }
                }
            }
        }
    }
}

.table-container {
    margin-top: 20px;

    .enhanced-table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .recharge-date {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;

            i {
                color: #409eff;
            }

            span {
                font-weight: 500;
            }
        }

        .recharge-amount {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;

            i {
                color: #67c23a;
                font-size: 14px;
            }

            .amount-number {
                font-weight: 600;
                color: #409eff;
                font-size: 15px;
            }

            .amount-unit {
                color: #666;
                font-size: 12px;
            }
        }

        .order-number {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            i {
                color: #666;
            }

            span {
                font-family: 'Courier New', monospace;
                font-size: 13px;
                color: #333;
            }

            .copy-btn {
                padding: 4px;
                color: #409eff;
                transition: all 0.2s ease;

                &:hover {
                    background: #ecf5ff;
                    transform: scale(1.1);
                }

                i {
                    font-size: 14px;
                }
            }
        }

        .recharge-note {
            text-align: left;
            padding: 0 8px;

            .no-note {
                color: #c0c4cc;
                font-style: italic;
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
        background: #ffffff;
        padding: 16px 0;

        .enhanced-pagination {
            ::v-deep .el-pagination__total {
                color: #409eff;
                font-weight: 500;
            }

            ::v-deep .el-pager li.active {
                background: #409eff;
                color: #ffffff;
            }

            ::v-deep .el-pagination__jump {
                color: #666;
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .search-section {
        .search-form {
            .search-row {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;

                .search-item {
                    .search-date,
                    .search-select {
                        width: 100%;
                    }
                }

                .search-buttons {
                    margin-left: 0;
                    justify-content: center;
                }
            }
        }
    }

    .table-container {
        .enhanced-table {
            font-size: 12px;

            .recharge-date,
            .recharge-amount,
            .order-number {
                flex-direction: column;
                gap: 2px;
            }

            .order-number {
                span {
                    font-size: 11px;
                }
            }
        }

        .pagination-container {
            padding: 12px 0;

            .enhanced-pagination {
                ::v-deep .el-pagination {
                    text-align: center;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .search-section {
        .search-form {
            .search-row {
                .search-buttons {
                    .action-btn {
                        flex: 1;
                    }
                }
            }
        }
    }
}
</style>

<style>
/* 全局日期选择器样式优化 */
@media screen and (max-width: 1200px) {
    .el-picker-panel {
        width: 370px;
        height: 350px;
        overflow: auto;
    }
    .el-date-range-picker__content {
        width: 100%;
    }
}

/* 对话框标题样式 */
.el-dialog__title {
    font-size: 16px;
}
</style>


