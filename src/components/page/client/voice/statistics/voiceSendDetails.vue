<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
                <el-button
                  type="primary"
                  @click="exportNums1()"
                  class="action-btn primary"
                  icon="el-icon-download"
                >
                  导出数据
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.totalRow }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ formData.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="form" :inline="true" ref="SHformList" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="手机号码" prop="mobile" class="search-item">
                    <el-input
                      v-model="form.mobile"
                      placeholder="请输入手机号"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="发送状态" prop="status" v-if="this.$store.state.isDateState == 1" class="search-item">
                    <el-select v-model="form.status" placeholder="发送状态" class="search-input" clearable>
                      <el-option label="全部" value=""></el-option>
                      <el-option label="成功" value="1"></el-option>
                      <el-option label="失败" value="2"></el-option>
                      <el-option label="待返回" value="3"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="发送类型" prop="type" class="search-item">
                    <el-select v-model="form.type" placeholder="发送类型" class="search-input" clearable>
                      <el-option label="全部" value=""></el-option>
                      <el-option label="语音验证码" value="1"></el-option>
                      <el-option label="语音通知" value="2"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="语音内容" prop="content" class="search-item">
                    <el-input
                      v-model="form.content"
                      placeholder="请输入语音内容"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="发送时间" prop="time1" class="search-item date-item">
                    <el-date-picker
                      v-model="form.time1"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button
                      type="primary"
                      @click="querySending()"
                      class="search-btn primary"
                      icon="el-icon-search"
                    >
                      查询
                    </el-button>
                    <el-button
                      @click="resetSending('SHformList')"
                      class="search-btn"
                      icon="el-icon-refresh"
                    >
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 发送详情列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">
              <i class="el-icon-chat-dot-round"></i>
              语音发送详情列表
            </h3>
            <span class="table-subtitle">可查看语音发送的详细记录和状态信息</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载发送详情数据..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无语音发送详情数据"
            >
              <!-- 手机号码 -->
              <el-table-column prop="mobile" label="手机号码" width="140" align="center">
                <template slot-scope="scope">
                  <div class="mobile-cell">
                    <span class="mobile-text">{{ scope.row.mobile }}</span>
                    <el-tooltip content="点击解密手机号" placement="top">
                      <i 
                        class="el-icon-unlock decrypt-icon" 
                        @click="handPhone(scope.row, scope.$index)"
                      ></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 短信内容 -->
              <el-table-column prop="content" label="语音内容" width="350">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <el-tooltip :content="scope.row.content" placement="top">
                      <span class="content-text">{{ scope.row.content }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 播报次数 -->
              <el-table-column prop="playNum" label="播报次数" width="100" align="center">
                <template slot-scope="scope">
                  <span class="number-badge">{{ scope.row.playNum }}</span>
                </template>
              </el-table-column>

              <!-- 计费条数 -->
              <el-table-column prop="chargeNum" label="计费条数" width="100" align="center">
                <template slot-scope="scope">
                  <span class="number-badge primary">{{ scope.row.chargeNum }}</span>
                </template>
              </el-table-column>

              <!-- 消息ID -->
              <el-table-column prop="msgid" label="消息ID" width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="msgid-text">{{ scope.row.msgid }}</span>
                    <el-tooltip content="复制消息ID" placement="top">
                      <i 
                        class="el-icon-document-copy copy-icon"
                        @click="handleCopy(scope.row.msgid, $event)"
                      ></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 发送时间 -->
              <el-table-column prop="sendTime" label="发送时间" width="170" align="center">
                <template slot-scope="scope">
                  {{ scope.row.sendTime }}
                </template>
              </el-table-column>

              <!-- 发送类型 -->
              <el-table-column prop="type" label="发送类型" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getTypeTagType(scope.row.type)"
                    size="small"
                  >
                    {{ getTypeText(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 状态上报时间 -->
              <el-table-column prop="reportTime" label="状态上报时间" width="170" align="center">
                <template slot-scope="scope">
                  {{ scope.row.reportTime }}
                </template>
              </el-table-column>

              <!-- 发送状态 -->
              <el-table-column v-if="this.$store.state.isDateState == 1" prop="status" label="发送状态" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getStatusTagType(scope.row.status)"
                    size="small"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 备注 -->
              <el-table-column v-if="this.$store.state.isDateState == 1" prop="originalCode" label="备注" min-width="120">
                <template slot-scope="scope">
                  <div class="remark-cell">
                    <el-tooltip v-if="scope.row.originalCode" :content="scope.row.originalCode" placement="top">
                      <span class="remark-text">{{ scope.row.originalCode }}</span>
                    </el-tooltip>
                    <span v-else class="no-data">--</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination
                class="simple-pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formData.currentPage"
                :page-size="formData.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.totalRow"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出数据弹框 -->
    <el-dialog 
      title="导出数据" 
      :visible.sync="exportShow" 
      width="450px" 
      :before-close="handleClose"
      class="export-dialog"
    >
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="导出类型" prop="decode">
          <el-radio-group v-model="ruleForm.decode">
            <el-radio label="0">掩码</el-radio>
            <el-radio label="1">明码</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="export-tip" v-if="ruleForm.decode == '1'">
          <i class="el-icon-warning-outline"></i>
          明码文件将会发送到您的 {{ emailInfo.email }} 邮箱，请注意查收。
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="exportShow = false">取 消</el-button>
        <el-button type="primary" @click="submitExport">{{ ruleForm.decode == '0' ? '下载' : '发送' }}</el-button>
      </span>
    </el-dialog>

    <!-- 重置号码组件 -->
    <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import DownLoadExport from "@/components/publicComponents/downLodExport"
import moment from 'moment'
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../../common/bus"
import common from "../../../../../assets/js/common";
import clip from '../../../utils/clipboard'

export default {
  name: "voiceSendDetails",
  components: {
    TableTem,
    DatePlugin,
    DownLoadExport,
    ResetNumberVue
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 30 * 24 * 3600 * 1000 || time.getTime() > Date.now();
        }
      },
      phoneList: [],
      exportFlag: false,
      //复选框的值
      selectId: '',
      // 单条存值
      AddBlackVal: '',
      // 备注
      AddBlackform: {
        remark: ''
      },
      AddBlacksStatus: false,
      isDateState: '',
      //发送查询的值
      form: {
        mobile: '',
        status: '',
        type: "",
        signature: '',
        content: '',
        temId: '',
        area: '',
        beginTime: moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        time1: [moment().subtract(1, 'months').format('YYYY-MM-DD') + "", moment(Date.now()).format('YYYY-MM-DD')],
        currentPage: 1,
        pageSize: 10,
        time: '',
        isDownload: 2
      },
      //复制发送查询的值
      formData: {
        mobile: '',
        status: '',
        type: "",
        signature: '',
        content: '',
        temId: '',
        area: '',
        beginTime: moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        time1: [moment().subtract(1, 'months').format('YYYY-MM-DD') + "", moment(Date.now()).format('YYYY-MM-DD')],
        currentPage: 1,
        pageSize: 10,
        isDownload: 2
      },
      rules: {},
      tableDataObj: { //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
      },
      resetVideo: false,
      infoData: {},
      emailInfo: {
        email: "",
        username: ""
      },
      exportShow: false,
      ruleForm: {
        decode: "0",
      },
    }
  },
  methods: {
    // 刷新列表
    refreshList() {
      this.sendReport();
      this.$message.success('列表已刷新');
    },

    // 获取发送类型标签类型
    getTypeTagType(type) {
      switch (type) {
        case 1:
          return 'success';
        case 2:
          return 'primary';
        default:
          return '';
      }
    },

    // 获取发送类型文本
    getTypeText(type) {
      switch (type) {
        case 1:
          return '语音验证码';
        case 2:
          return '语音通知';
        default:
          return '未知';
      }
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case '1':
          return 'success';
        case '2':
          return 'danger';
        case '3':
          return 'warning';
        default:
          return '';
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case '1':
          return '成功';
        case '2':
          return '失败';
        case '3':
          return '待返回';
        default:
          return '未知';
      }
    },

    // 复制功能
    handleCopy(name, event) {
      clip(name, event);
    },

    //发送请求
    sendReport() {
      this.tableDataObj.loading2 = true;
      let data = Object.assign({}, this.formData);
      data.flag = 5;
      this.$api.post(this.API.cpus + 'consumervoicemessage/messages', data, res => {
        this.tableDataObj.tableData = res.data.records;
        this.tableDataObj.totalRow = res.data.total;
        this.tableDataObj.loading2 = false;
      })
    },

    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.form.beginTime = val[0] + " 00:00:00"
        this.form.endTime = val[1] + " 23:59:59"
      } else {
        this.form.beginTime = ''
        this.form.endTime = ''
      }
    },

    //查询 （发送）
    querySending() {
      Object.assign(this.formData, this.form);
      this.formData.currentPage = 1; // 重置到第一页
      this.sendReport();
    },

    //重置 （发送）
    resetSending(formName) {
      this.$refs[formName].resetFields();
      this.form.beginTime = moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss');
      this.form.endTime = moment(Date.now()).format('YYYY-MM-DD HH:mm:ss');
      this.form.time1 = [moment().subtract(1, 'months').format('YYYY-MM-DD'), moment(Date.now()).format('YYYY-MM-DD')];
      Object.assign(this.formData, this.form);
      this.sendReport();
    },

    //获取分页的每页数量
    handleSizeChange(size) {
      this.formData.pageSize = size;
      this.sendReport();
    },

    //获取分页的第几页
    handleCurrentChange(currentPage) {
      this.formData.currentPage = currentPage;
      this.sendReport();
    },

    handPhone(row, index) {
      this.$api.post(this.API.upms + '/generatekey/decryptMobile', {
        keyId: row.keyId,
        smsInfoId: row.decryptMobile,
        cipherMobile: row.cipherMobile
      }, res => {
        if (res.code == 200) {
          this.tableDataObj.tableData[index].mobile = res.data;
        } else if (res.code == 4004002) {
          common.fetchData().then((res) => {
            if (res.code == 200) {
              if (res.data.isAdmin == 1) {
                this.resetVideo = true;
                this.infoData = res.data;
              } else {
                this.$message({
                  message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                  type: "warning",
                });
              }
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          })
        } else {
          this.$message({
            message: res.msg,
            type: 'warning'
          });
        }
      })
    },

    handleClose() {
      this.exportShow = false;
    },

    getLoginPhone() {
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data;
          }
        }
      );
    },

    exportFn(obj) {
      this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        if (res.code == 200) {
          this.exportShow = false
          this.$message({
            type: "success",
            duration: "2000",
            message: "已加入到文件下载中心!",
          });
          this.$router.push('/FileExport');
        } else {
          this.$message({
            type: "error",
            duration: "2000",
            message: res.msg,
          });
        }
      });
    },

    exportNums1() {
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
      } else {
        this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
          if (res.code == 200) {
            this.emailInfo.email = res.data.email;
            this.emailInfo.username = res.data.username;
            if (res.data.decode) {
              this.exportShow = true
            } else {
              let data = Object.assign({}, this.formData);
              data.productType = 6
              this.exportFn(data)
            }
          }
        })
      }
    },

    submitExport() {
      let data = Object.assign({}, this.formData);
      data.productType = 6
      data.decode = this.ruleForm.decode == 0 ? false : true;
      this.exportFn(data)
    },
  },

  created() {
    bus.$on("closeVideo", (msg) => {
      this.resetVideo = msg;
    });
  },

  mounted() {
    this.$api.get(this.API.cpus + 'consumerclientinfo/getClientInfo', null, res => {
      this.isDateState = res.data.isDateState
    })
    this.sendReport();
  },
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* voiceSendDetails 特有样式 */

/* 内容单元格样式 */
.content-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 100%;
}

.mobile-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.mobile-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #333;
}

.decrypt-icon {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.3s ease;

  &:hover {
    color: #66b1ff;
  }
}

.msgid-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-text {
  color: #606266;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.copy-icon {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  margin-left: 4px;
  transition: color 0.3s ease;

  &:hover {
    color: #66b1ff;
  }
}

/* 数字徽章样式 */
.number-badge {
  display: inline-block;
  padding: 4px 8px;
  background: #f0f2f5;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  min-width: 24px;
  text-align: center;

  &.primary {
    background: #f0f9ff;
    color: #1890ff;
  }
}

/* 备注单元格样式 */
.remark-cell {
  .remark-text {
    color: #606266;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
  }

  .no-data {
    color: #d9d9d9;
    font-style: italic;
  }
}

/* 表格标题样式 */
.table-title {
  i {
    color: #52c41a;
    margin-right: 8px;
  }
}

/* 导出对话框样式 */
.export-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;
  }

  /deep/ .el-dialog__header {
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  /deep/ .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  /deep/ .el-dialog__body {
    padding: 24px;
  }
}

.export-tip {
  margin-top: 16px;
  padding: 12px 16px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  font-size: 14px;
  color: #856404;
  line-height: 1.5;

  i {
    margin-right: 8px;
    color: #e6a23c;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-text {
    max-width: 200px;
  }

  .msgid-text {
    max-width: 120px;
  }

  .remark-cell .remark-text {
    max-width: 80px;
  }
}

@media (max-width: 768px) {
  .mobile-cell {
    flex-direction: column;
    gap: 4px;
  }

  .number-badge {
    padding: 2px 6px;
    font-size: 11px;
  }

  .export-dialog {
    width: 90% !important;
  }
}
</style>
