<template>
    <div class="simple-sendtask-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <el-button
                                    @click="refreshList"
                                    class="action-btn"
                                    icon="el-icon-refresh"
                                >
                                    刷新列表
                                </el-button>
                                <el-button
                                    v-permission
                                    v-if="selectId.length"
                                    type="danger"
                                    @click="batchDeletion"
                                    class="action-btn danger"
                                    icon="el-icon-delete"
                                >
                                    批量取消
                                </el-button>
                                <!-- <el-button
                                    type="primary"
                                    @click="exportNums()"
                                    class="action-btn primary"
                                    icon="el-icon-download"
                                >
                                    导出数据
                                </el-button> -->
                            </div>

                            <!-- 统计信息 -->
                            <div class="stats-info">
                                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                                <el-divider direction="vertical"></el-divider>
                                <span class="stats-text">当前第 {{ formInlines.currentPage }} 页</span>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <el-form :model="formInline" :inline="true" ref="formInline" class="advanced-search-form">
                                <div class="search-row">
                                    <el-form-item label="发送状态" prop="timingStatus" class="search-item">
                                        <el-select v-model="formInline.timingStatus" placeholder="请选择状态" class="search-input" clearable>
                                            <el-option label="全部" value=""></el-option>
                                            <el-option label="未执行" value="1"></el-option>
                                            <el-option label="正在执行" value="2"></el-option>
                                            <el-option label="取消" value="3"></el-option>
                                            <el-option label="超时未执行" value="4"></el-option>
                                            <el-option label="执行完成" value="5"></el-option>
                                            <el-option label="执行失败" value="6"></el-option>
                                        </el-select>
                                    </el-form-item>

                                    <el-form-item label="消息ID" prop="msgid" class="search-item">
                                        <el-input
                                            v-model="formInline.msgid"
                                            placeholder="请输入消息ID"
                                            class="search-input"
                                            clearable
                                        />
                                    </el-form-item>

                                    <el-form-item label="发送时间" prop="time" class="search-item date-item">
                                        <el-date-picker
                                            v-model="formInline.time"
                                            value-format="yyyy-MM-dd"
                                            type="daterange"
                                            range-separator="-"
                                            :clearable="false"
                                            @change="getTimeOperating"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            class="search-date"
                                        />
                                    </el-form-item>

                                    <el-form-item class="search-buttons">
                                        <el-button
                                            type="primary"
                                            @click="ListSearch"
                                            class="search-btn primary"
                                            icon="el-icon-search"
                                        >
                                            查询
                                        </el-button>
                                        <el-button
                                            @click="Reset('formInline')"
                                            class="search-btn"
                                            icon="el-icon-refresh"
                                        >
                                            重置
                                        </el-button>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>

                <!-- 接口定时发送任务列表 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3 class="table-title">
                            <i class="el-icon-timer"></i>
                            接口定时发送任务列表
                        </h3>
                        <span class="table-subtitle">可刷新页面查看最新任务执行状态</span>
                    </div>

                    <div class="table-container">
                        <el-table
                            v-loading="tableDataObj.loading2"
                            element-loading-text="正在加载任务数据..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(255, 255, 255, 0.9)"
                            ref="multipleTable"
                            border
                            :data="tableDataObj.tableData"
                            class="enhanced-table"
                            stripe
                            :header-cell-style="{ background: '#fafafa', color: '#333' }"
                            :row-style="{ height: '60px' }"
                            empty-text="暂无接口定时发送任务数据"
                            @selection-change="handelSelection"
                        >
                            <!-- 选择列 -->
                            <el-table-column type="selection" width="55" align="center"></el-table-column>

                            <!-- 发送ID -->
                            <el-table-column prop="id" label="发送ID" width="80" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.id }}
                                </template>
                            </el-table-column>

                            <!-- 消息ID -->
                            <el-table-column label="消息ID" width="200">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <span class="msgid-text">{{ scope.row.msgid }}</span>
                                        <el-tooltip content="复制消息ID" placement="top">
                                            <i 
                                                class="el-icon-document-copy copy-icon"
                                                @click="handleCopy(scope.row.msgid, $event)"
                                            ></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 内容 -->
                            <el-table-column label="内容">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <el-tooltip :content="scope.row.content" placement="top">
                                            <span class="content-text">{{ scope.row.content }}</span>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 提交时间 -->
                            <el-table-column label="提交时间" width="160" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.createTime }}
                                </template>
                            </el-table-column>

                            <!-- 文件名/手机号 -->
                            <el-table-column label="文件名/手机号" width="160">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <div v-if="scope.row.filePath" class="file-info">
                                            <el-tooltip :content="scope.row.fileOriginalName" placement="top">
                                                <span class="file-name">{{ scope.row.fileOriginalName }}</span>
                                            </el-tooltip>
                                        </div>
                                        <div v-else class="mobile-info">
                                            <el-tooltip :content="scope.row.mobile" placement="top">
                                                <span class="mobile-text">{{ scope.row.mobile }}</span>
                                            </el-tooltip>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>

                            <!-- 发送时间 -->
                            <el-table-column label="发送时间" width="160" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.sendTime }}
                                </template>
                            </el-table-column>

                            <!-- 发送状态 -->
                            <el-table-column label="发送状态" width="120" align="center">
                                <template slot-scope="scope">
                                    <el-tag
                                        :type="getStatusTagType(scope.row.timingStatus)"
                                        size="small"
                                    >
                                        {{ getStatusText(scope.row.timingStatus) }}
                                    </el-tag>
                                </template>
                            </el-table-column>

                            <!-- 操作 -->
                            <el-table-column label="操作" width="120" fixed="right" align="center">
                                <template slot-scope="scope">
                                    <div class="action-buttons-cell">
                                        <el-tooltip content="取消定时发送" placement="top">
                                            <el-button
                                                v-permission
                                                v-if="scope.row.timingStatus == 1"
                                                type="text"
                                                @click="cancel(scope.row)"
                                                class="action-btn-small danger"
                                                icon="el-icon-close"
                                            >
                                                取消
                                            </el-button>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页 -->
                        <div class="pagination-section">
                            <el-pagination
                                class="simple-pagination"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                                :current-page="formInlines.currentPage"
                                :page-size="formInlines.pageSize"
                                :page-sizes="[10, 20, 50, 100]"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="tableDataObj.tablecurrent.total"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预览手机弹框 -->
        <el-dialog 
            title="内容预览" 
            :visible.sync="dialogVisible" 
            width="500px"
            class="preview-dialog"
        >
            <div class="preview-container">
                <div class="phone-mockup">
                    <div class="phone-screen">
                        <div class="phone-header">
                            <span class="phone-time">{{ getCurrentTime() }}</span>
                            <div class="phone-status">
                                <i class="el-icon-wifi"></i>
                                <i class="el-icon-battery-full"></i>
                            </div>
                        </div>
                        <div class="message-container">
                            <el-scrollbar class="message-scrollbar">
                                <div class="message-content-wrapper">
                                    <div class="message-bubble title-bubble">
                                        {{ title || '语音消息标题' }}
                                    </div>
                                    <div
                                        v-for="(item, index) in viewData"
                                        :key="index"
                                        class="message-bubble content-bubble"
                                    >
                                        <img
                                            v-if="item.media == 'jpg' || item.media == 'gif' || item.media == 'png' || item.media == 'jpeg'"
                                            :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                                            class="media-content image-content"
                                        />
                                        <video
                                            v-if="item.type == 'video'"
                                            :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                                            class="media-content video-content"
                                            controls
                                        />
                                        <audio
                                            v-if="item.type == 'audio'"
                                            :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                                            class="media-content audio-content"
                                            controls
                                            preload="auto"
                                        />
                                        <div class="text-content" v-if="item.txt">
                                            {{ item.txt }}
                                        </div>
                                    </div>
                                </div>
                            </el-scrollbar>
                        </div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
import clip from '../../../utils/clipboard'
import getNoce from '../../../../../plugins/getNoce';
export default {
    name:'VoiceInterfaceSend',
    components:{
        DatePlugin,
        TableTem
    },
    data() {
        return {
            name:'VoiceInterfaceSend',
            // 定时时间
            datePluginValueList: { //日期参数配置
                type:"datetime",
                pickerOptions:{
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    }
                },
                defaultTime:'', //默认起始时刻
                datePluginValue:''
            },
            sendTime:'',
            taskSmsId:'',
            dialogVisible:false,
            viewData:[],// 查看内容
            title:'',
            dialogVisibleTime:false,
            //复选框值
            selectId:'',
            // 搜索数据
            formInline: {
                timingStatus:'',
                msgid:'',
                beginTime:'',
                endTime:'',
                startTime:"",
                stopTime:"",
                time: [],
                time1:[],
                pageSize:10,
                currentPage:1,
            },
            // 存储搜索数据
            formInlines:{
                 timingStatus:'',
                msgid:'',
                beginTime:'',
                endTime:'',
                startTime:"",
                stopTime:"",
                time: [],
                time1:[],
                pageSize:10,
                currentPage:1,
            },
            //用户列表数据
            tableDataObj: {
                loading2:false,
                tablecurrent:{ //分页参数
                    total:0,
                },
                tableData: []
            }
        };
    },
    methods: {
        // 刷新列表
        refreshList() {
            this.InquireList();
            this.$message.success('列表已刷新');
        },

        // 导出数据
        exportNums() {
            if (this.tableDataObj.tableData.length === 0) {
                this.$message.warning('暂无数据可导出');
                return;
            }
            // 这里可以根据实际需求实现导出功能
            this.$message.info('导出功能开发中');
        },

        // 获取当前时间
        getCurrentTime() {
            const now = new Date();
            return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        },

        // 获取状态标签类型
        getStatusTagType(timingStatus) {
            switch (timingStatus) {
                case 1:
                    return 'info';
                case 2:
                    return 'warning';
                case 3:
                    return 'danger';
                case 4:
                    return 'danger';
                case 5:
                    return 'success';
                case 6:
                    return 'danger';
                default:
                    return '';
            }
        },

        // 获取状态文本
        getStatusText(timingStatus) {
            switch (timingStatus) {
                case 1:
                    return '未执行';
                case 2:
                    return '正在执行';
                case 3:
                    return '取消';
                case 4:
                    return '超时未执行';
                case 5:
                    return '执行完成';
                case 6:
                    return '执行失败';
                default:
                    return '未知';
            }
        },

        //批量取消
        batchDeletion(){
            this.$confirms.confirmation('post','确定取消定时发送？',this.API.cpus+'consumertimingvoice/cancelTimingVoice',{ids:this.selectId},res =>{
                this.InquireList()
            })
        },
        //列表复选框的值
        handelSelection(val){
            this.selectId = val.map((item,index)=>{
                return item.id
            })
        },
        // 发送请求方法
        InquireList(){
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumertimingvoice/selectTimingPage',this.formInlines,res=>{
                this.tableDataObj.loading2=false;
                this.tableDataObj.tableData=res.data.records
                this.tableDataObj.tablecurrent.total=res.data.total
            })
        },
        // 取消
        cancel(val){
            this.$confirms.confirmation('post','确定取消定时语音发送？',this.API.cpus+'consumertimingvoice/cancelTimingVoice',{ids:[val.id]},res =>{
                this.InquireList()                    
            })
        },
        handleCopy(name,event){
        clip(name, event)
      },
        // 下载
        download(val){
            // 时间过滤
            Date.prototype.format = function (format) {
            var args = {
                "M+": this.getMonth() + 1,
                "d+": this.getDate(),
                "h+": this.getHours(),
                "m+": this.getMinutes(),
                "s+": this.getSeconds(),
                "q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
                "S": this.getMilliseconds()
            };
            if (/(y+)/.test(format))
                format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var i in args) {
                var n = args[i];
                if (new RegExp("(" + i + ")").test(format))
                    format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length));
            }
            return format;
            };
            var that = this
            filedownload()
            async function filedownload() {
            const nonce = await getNoce.useNonce();
            fetch(that.API.cpus +'v3/file/download?fileName='+val.fileOriginalName+'&group=group1&path='+val.filePath.slice(7), {
                method: 'get',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization':"Bearer "  + window.Vue.$common.getCookie('ZTGlS_TOKEN'),
                    'Once': nonce,
                },
                // body: JSON.stringify({
                //     batchNo: val.row.batchNo,
                // })
            })
            .then(res => res.blob())
            .then(data => {
                let blobUrl = window.URL.createObjectURL(data);
                download(blobUrl);
            });
            }
            function download(blobUrl) {
                var a = document.createElement('a');
                a.style.display = 'none';
                a.download ="("+ new Date().format("yyyy-MM-dd hh:mm:ss")+") "+val.fileOriginalName
                a.href = blobUrl;
                a.click();
            }
        },
        // 查询
        ListSearch(){
            Object.assign(this.formInlines,this.formInline);
            this.formInlines.currentPage = 1; // 重置到第一页
            this.InquireList()
        },
        // 重置
        Reset(formName){
            this.$refs[formName].resetFields();
            this.formInline.time = [],
            this.formInline.beginTime = '';
            this.formInline.endTime = '';
            this.formInline.time1 = [],
            this.formInline.startTime=""
            this.formInline.stopTime=""
            Object.assign(this.formInlines,this.formInline)
            this.InquireList()
        },
        // 发送时间
        getTimeOperating(val){
            if(val){
                this.formInline.beginTime=val[0]+" 00:00:00"
                this.formInline.endTime=val[1]+" 23:59:59"
            }else{
                this.formInline.beginTime=""
                this.formInline.endTime=""
            }
        },
        // 提交时间
        getTimeOperating1(val){
            if(val){
                this.formInline.startTime=val[0]+" 00:00:00"
                this.formInline.stopTime=val[1]+" 23:59:59"
            }else{
                this.formInline.startTime=""
                this.formInline.stopTime=""
            }
        },
        // 定时时间
        handledatepluginVal: function(val1,val2){ //日期
            this.sendTime = val1
        },
        // 分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
            this.InquireList()
        },
        handleCurrentChange: function(currentPage){
            this.formInlines.currentPage = currentPage;
            this.InquireList()
        },
    },
    created(){
        this.InquireList()
    },
    watch:{
        // 监听搜索/分页数据
        // formInlines:{
        //     handler() {
        //         this.InquireList()
        //     },
        //     deep: true,
        //     immediate: true,
        // },
    },
}
</script>
<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* voiceInterfaceSend 特有样式 */

/* 内容单元格样式 */
.content-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 100%;
}

.msgid-text {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #606266;
    background: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.content-text {
    color: #606266;
    max-width: 350px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
}

.file-name,
.mobile-text {
    color: #606266;
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
}

.copy-icon {
    color: #409eff;
    cursor: pointer;
    font-size: 14px;
    margin-left: 4px;
    transition: color 0.3s ease;

    &:hover {
        color: #66b1ff;
    }
}

/* 操作按钮样式 */
.action-buttons-cell {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.action-btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &.danger {
        color: #ff4d4f;
        
        &:hover {
            color: #ff7875;
            background: #fff2f0;
        }
    }
}

/* 表格标题样式 */
.table-title {
    i {
        color: #faad14;
        margin-right: 8px;
    }
}

/* 预览对话框样式 */
.preview-dialog {
    /deep/ .el-dialog {
        border-radius: 12px;
    }

    /deep/ .el-dialog__header {
        background: #f8f9fa;
        border-radius: 12px 12px 0 0;
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;
    }

    /deep/ .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    /deep/ .el-dialog__body {
        padding: 0;
    }
}

.preview-container {
    display: flex;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
}

.phone-mockup {
    width: 320px;
    height: 568px;
    background: #333;
    border-radius: 30px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 4px;
        background: #666;
        border-radius: 2px;
    }
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.phone-header {
    height: 40px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid #e9ecef;
}

.phone-time {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.phone-status {
    display: flex;
    gap: 8px;

    i {
        color: #666;
        font-size: 14px;
    }
}

.message-container {
    flex: 1;
    overflow: hidden;
}

.message-scrollbar {
    height: 100%;

    /deep/ .el-scrollbar__wrap {
        overflow-x: hidden;
    }
}

.message-content-wrapper {
    padding: 16px;
    min-height: 100%;
}

.message-bubble {
    margin-bottom: 12px;
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 80%;
    word-wrap: break-word;

    &.title-bubble {
        background: #007aff;
        color: #fff;
        align-self: flex-end;
        margin-left: auto;
        font-weight: 500;
    }

    &.content-bubble {
        background: #e5e5ea;
        color: #333;
        align-self: flex-start;
    }
}

.media-content {
    max-width: 100%;
    border-radius: 8px;
    margin-bottom: 8px;

    &.image-content {
        max-height: 200px;
        object-fit: cover;
    }

    &.video-content {
        width: 100%;
        height: auto;
    }

    &.audio-content {
        width: 100%;
    }
}

.text-content {
    white-space: pre-line;
    line-height: 1.4;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .content-text {
        max-width: 200px;
    }

    .msgid-text {
        max-width: 120px;
    }

    .file-name,
    .mobile-text {
        max-width: 100px;
    }
}

@media (max-width: 768px) {
    .action-buttons-cell {
        flex-direction: column;
        gap: 4px;
    }

    .preview-container {
        padding: 10px;
    }

    .phone-mockup {
        width: 280px;
        height: 500px;
        padding: 15px;
    }
}
</style>
