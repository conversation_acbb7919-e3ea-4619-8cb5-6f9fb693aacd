<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
                <el-button
                  v-permission
                  v-if="selectId.length"
                  type="danger"
                  @click="batchDeletion"
                  class="action-btn danger"
                  icon="el-icon-delete"
                >
                  批量取消
                </el-button>
                <!-- <el-button
                  type="primary"
                  @click="exportNums()"
                  class="action-btn primary"
                  icon="el-icon-download"
                >
                  导出数据
                </el-button> -->
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ formInlines.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="formInline" :inline="true" ref="formInline" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="发送类型" prop="sendType" class="search-item">
                    <el-select v-model="formInline.sendType" placeholder="请选择类型" class="search-input" clearable>
                      <el-option label="全部" value=""></el-option>
                      <el-option label="验证码" value="1"></el-option>
                      <el-option label="语音群通知" value="2"></el-option>
                      <el-option label="个性化发送" value="3"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="消息ID" prop="msgid" class="search-item">
                    <el-input
                      v-model="formInline.msgid"
                      placeholder="请输入消息ID"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="内容" prop="content" class="search-item">
                    <el-input
                      v-model="formInline.content"
                      placeholder="请输入内容"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="发送时间" prop="time" class="search-item date-item">
                    <el-date-picker
                      v-model="formInline.time"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      :clearable="false"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button
                      type="primary"
                      @click="ListSearch"
                      class="search-btn primary"
                      icon="el-icon-search"
                    >
                      查询
                    </el-button>
                    <el-button
                      @click="Reset('formInline')"
                      class="search-btn"
                      icon="el-icon-refresh"
                    >
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- Web任务列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">
              <i class="el-icon-phone"></i>
              语音Web任务列表
            </h3>
            <span class="table-subtitle">可刷新页面查看最新发送进度</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载任务数据..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无语音Web任务数据"
              @selection-change="handelSelection"
            >
              <!-- 选择列 -->
              <el-table-column type="selection" width="55" align="center"></el-table-column>

              <!-- 发送ID -->
              <el-table-column prop="id" label="发送ID" width="90" align="center">
                <template slot-scope="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column>

              <!-- 消息ID -->
              <el-table-column label="消息ID" width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="msgid-text">{{ scope.row.msgid }}</span>
                    <el-tooltip content="复制消息ID" placement="top">
                      <i 
                        class="el-icon-document-copy copy-icon"
                        @click="handleCopy(scope.row.msgid, $event)"
                      ></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 内容 -->
              <el-table-column label="内容" width="400">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <el-tooltip :content="scope.row.content" placement="top">
                      <span class="content-text">{{ scope.row.content }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 发送类型 -->
              <el-table-column label="发送类型" width="110" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getTypeTagType(scope.row.sendType)"
                    size="small"
                  >
                    {{ getTypeText(scope.row.sendType) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 文件名/手机号 -->
              <el-table-column label="文件名/手机号" width="160">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <div v-if="scope.row.filePath" class="file-info">
                      <el-tooltip :content="scope.row.fileOriginalName" placement="top">
                        <span class="file-name">{{ scope.row.fileOriginalName }}</span>
                      </el-tooltip>
                    </div>
                    <div v-else class="mobile-info">
                      <el-tooltip :content="scope.row.mobile" placement="top">
                        <span class="mobile-text">{{ scope.row.mobile }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 发送时间 -->
              <el-table-column label="发送时间" width="160" align="center">
                <template slot-scope="scope">
                  {{ scope.row.sendTime }}
                </template>
              </el-table-column>

              <!-- 提交号码数 -->
              <el-table-column label="提交号码数" width="150" align="center">
                <template slot-scope="scope">
                  <span class="number-badge">{{ scope.row.totalNum }}</span>
                </template>
              </el-table-column>

              <!-- 有效号码 -->
              <el-table-column label="有效号码" width="150" align="center">
                <template slot-scope="scope">
                  <span class="number-badge success">{{ scope.row.effectiveNum }}</span>
                </template>
              </el-table-column>

              <!-- 无效号码 -->
              <el-table-column label="无效号码" width="150" align="center">
                <template slot-scope="scope">
                  <span 
                    v-if="scope.row.invalidNum > 0 && scope.row.invalidFilePath" 
                    class="number-badge danger clickable"
                    @click="download(scope.row, '2')"
                  >
                    <el-tooltip content="点击下载无效号码文件" placement="top">
                      <span>{{ scope.row.invalidNum }}</span>
                    </el-tooltip>
                  </span>
                  <span v-else class="number-badge">{{ scope.row.invalidNum }}</span>
                </template>
              </el-table-column>

              <!-- 发送状态 -->
              <el-table-column label="发送状态" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getStatusTagType(scope.row.status)"
                    size="small"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 操作 -->
              <el-table-column label="操作" width="120" fixed="right" align="center">
                <template slot-scope="scope">
                  <div class="action-buttons-cell">
                    <el-tooltip content="取消发送" placement="top">
                      <el-button
                        v-permission
                        v-if="scope.row.status == 0"
                        type="text"
                        @click="cancel(scope.row)"
                        class="action-btn-small danger"
                        icon="el-icon-close"
                      >
                        取消
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination
                class="simple-pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInlines.currentPage"
                :page-size="formInlines.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.tablecurrent.total"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem";
import clip from '../../../utils/clipboard'
import getNoce from "../../../../../plugins/getNoce";

export default {
  name: "VoiceWebTask",
  components: {
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      name: "VoiceWebTask",
      // 定时时间
      datePluginValueList: {
        //日期参数配置
        type: "datetime",
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          },
        },
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
      sendTime: "",
      taskSmsId: "",
      dialogVisible: false,
      viewData: [], // 查看内容
      title: "",
      dialogVisibleTime: false,
      //复选框值
      selectId: [],
      // 搜索数据
      formInline: {
        msgid: "",
        content: "",
        beginTime: "",
        endTime: "",
        time: [],
        sendType: "",
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        msgid: "",
        content: "",
        beginTime: "",
        endTime: "",
        time: [],
        sendType: "",
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    };
  },
  methods: {
    // 刷新列表
    refreshList() {
      this.InquireList();
      this.$message.success('列表已刷新');
    },

    // 导出数据
    exportNums() {
      if (this.tableDataObj.tableData.length === 0) {
        this.$message.warning('暂无数据可导出');
        return;
      }
      // 这里可以根据实际需求实现导出功能
      this.$message.info('导出功能开发中');
    },

    // 获取发送类型标签类型
    getTypeTagType(sendType) {
      switch (sendType) {
        case 1:
          return 'success';
        case 2:
          return 'primary';
        case 3:
          return 'warning';
        default:
          return '';
      }
    },

    // 获取发送类型文本
    getTypeText(sendType) {
      switch (sendType) {
        case 1:
          return '验证码';
        case 2:
          return '语音群通知';
        case 3:
          return '个性化发送';
        default:
          return '未知';
      }
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 0:
          return 'info';
        case 1:
          return 'warning';
        case 2:
          return 'success';
        case 3:
          return 'danger';
        case -1:
          return 'danger';
        default:
          return '';
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 0:
          return '待处理';
        case 1:
          return '处理中';
        case 2:
          return '已完成';
        case 3:
          return '取消发送';
        case -1:
          return '处理异常';
        default:
          return '未知';
      }
    },

    //批量取消
    batchDeletion() {
      this.$confirms.confirmation(
        "post",
        "确定取消定时发送？",
        this.API.cpus + "voice/cancel",
        { ids: this.selectId },
        (res) => {
          this.InquireList();
          this.selectId = [];
        }
      );
    },

    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id);
      }
      this.selectId = selectId; //批量操作选中id
    },

    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "voice/consumerWebTask",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tablecurrent.total = res.data.total;
        }
      );
    },

    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        "post",
        "确定取消定时语音短信？",
        this.API.cpus + "voice/cancel",
        { ids: [val.id] },
        (res) => {
          this.InquireList();
        }
      );
    },

    handleCopy(name, event) {
      clip(name, event);
    },

    // 下载
    download(val, tag) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          "M+": this.getMonth() + 1,
          "d+": this.getDate(),
          "h+": this.getHours(),
          "m+": this.getMinutes(),
          "s+": this.getSeconds(),
          "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        };
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var i in args) {
          var n = args[i];
          if (new RegExp("(" + i + ")").test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
            );
        }
        return format;
      };
      var that = this;
      filedownload();
      async function filedownload() {
        const nonce = await getNoce.useNonce();
        if (tag == "2") {
          let group = val.invalidFilePath.substring(0, 6);
          let invalidFilePath = val.invalidFilePath.slice(7);
          fetch(
            "/gateway/client-cpus/v3/file/download?fileName=" +
              new Date().getTime() +
              "&group=" +
              group +
              "&path=" +
              invalidFilePath,
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        } else {
          fetch(
            that.API.cpus +
              "v3/file/download?fileName=" +
              val.fileOriginalName +
              "&group=group1&path=" +
              val.filePath.slice(7),
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        }
      }
      function download(blobUrl) {
        if (tag == "2") {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            new Date().getTime() +
            ".txt";
          a.href = blobUrl;
          a.click();
        } else {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            val.fileOriginalName;
          a.href = blobUrl;
          a.click();
        }
      }
    },

    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline);
      this.formInlines.currentPage = 1; // 重置到第一页
      this.InquireList();
    },

    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      this.formInline.time = [];
      this.formInline.beginTime = "";
      this.formInline.endTime = "";
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },

    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },

    // 定时时间
    handledatepluginVal: function (val1, val2) {
      //日期
      this.sendTime = val1;
    },

    // 确定定时时间
    determine() {
      if (this.sendTime == "") {
        this.$message({
          message: "请选大于当前30分钟的定时时间！",
          type: "warning",
        });
      } else {
        let nowTiem = new Date(this.sendTime).getTime();
        if (nowTiem < Date.now() + 1800000) {
          this.$message({
            message: "定时时间应大于当前时间30分钟，需重新设置！",
            type: "warning",
          });
          this.datePluginValueList.datePluginValue = "";
          this.sendTime = "";
        } else {
          this.$confirms.confirmation(
            "put",
            "确定修改定时？",
            this.API.cpus + "voice/consumerWebTask",
            { sendTime: this.sendTime, taskSmsId: this.taskSmsId },
            (res) => {
              this.InquireList();
              this.dialogVisibleTime = false;
            }
          );
        }
      }
    },

    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
      this.InquireList();
    },

    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
  },

  created() {
    this.InquireList();
  },
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* voiceWebTask 特有样式 */

/* 内容单元格样式 */
.content-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 100%;
}

.msgid-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-text {
  color: #606266;
  max-width: 350px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.file-name,
.mobile-text {
  color: #606266;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.copy-icon {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  margin-left: 4px;
  transition: color 0.3s ease;

  &:hover {
    color: #66b1ff;
  }
}

/* 数字徽章样式 */
.number-badge {
  display: inline-block;
  padding: 4px 8px;
  background: #f0f2f5;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  min-width: 24px;
  text-align: center;

  &.success {
    background: #f0f9ff;
    color: #1890ff;
  }

  &.danger {
    background: #fff2f0;
    color: #ff4d4f;
  }

  &.warning {
    background: #fffbe6;
    color: #d4b106;
  }

  &.clickable {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
    }
  }
}

/* 成功率样式 */
.success-rate {
  color: #52c41a;
  font-weight: 500;
}

.no-data {
  color: #d9d9d9;
  font-style: italic;
}

/* 操作按钮样式 */
.action-buttons-cell {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &.danger {
    color: #ff4d4f;
    
    &:hover {
      color: #ff7875;
      background: #fff2f0;
    }
  }

  &.edit {
    color: #1890ff;
    
    &:hover {
      color: #40a9ff;
      background: #f0f9ff;
    }
  }
}

/* 表格标题样式 */
.table-title {
  i {
    color: #52c41a;
    margin-right: 8px;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-text {
    max-width: 200px;
  }

  .msgid-text {
    max-width: 120px;
  }

  .file-name,
  .mobile-text {
    max-width: 100px;
  }
}

@media (max-width: 768px) {
  .action-buttons-cell {
    flex-direction: column;
    gap: 4px;
  }

  .number-badge {
    padding: 2px 6px;
    font-size: 11px;
  }
}
</style>
