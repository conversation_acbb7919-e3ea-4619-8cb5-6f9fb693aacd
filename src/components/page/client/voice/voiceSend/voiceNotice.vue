<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 页面标题 -->
        <div class="page-header">
          <h2 class="page-title">语音群通知</h2>
          <div class="page-subtitle">批量发送语音通知到多个手机号码</div>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
          <div class="form-container">
            <el-form 
              :model="formData" 
              :rules="formRule" 
              ref="formRef" 
              label-width="120px" 
              class="voice-form"
            >
              <!-- 语音类型 -->
              <el-form-item label="语音类型" prop="" class="form-item">
                <div class="type-display">
                  <el-tag type="primary">语音群通知</el-tag>
                </div>
              </el-form-item>

              <!-- 语音内容 -->
              <el-form-item label="语音内容" prop="content" class="form-item">
                <el-input 
                  v-model="formData.content" 
                  placeholder="请输入语音播报内容"
                  type="textarea"
                  :rows="4"
                  maxlength="500"
                  show-word-limit
                  class="form-input"
                >
                  <template slot="prepend">
                    <i class="el-icon-microphone"></i>
                  </template>
                </el-input>
                <div class="form-tip">
                  <i class="el-icon-info"></i>
                  最多500字（含签名），85字计一条
                </div>
              </el-form-item>

              <!-- 发送方式 -->
              <el-form-item label="发送方式" prop="sendType" class="form-item">
                <el-radio-group v-model="formData.sendType" @change="handelSend" class="send-type-group">
                  <el-radio label="0">
                    <i class="el-icon-edit"></i>
                    号码发送
                  </el-radio>
                  <el-radio label="1">
                    <i class="el-icon-upload"></i>
                    文件发送
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 文件上传 -->
              <el-form-item v-if="formData.sendType=='1'" label="发送对象" prop="" class="form-item">
                <div class="upload-section">
                  <el-upload
                    v-permission
                    class="upload-dragger"
                    drag
                    :action="this.API.cpus + 'v3/file/upload'"
                    :headers="header"
                    :limit="1"
                    :on-remove="fileup"
                    :on-success="fileupres"
                    :before-upload="beforeAvatarUpload"
                    :file-list="fileList"
                    multiple
                  >
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将文件拖到此处，或<em>点击上传</em>
                    </div>
                  </el-upload>
                  <div v-if="RowNum" class="upload-info">
                    <i class="el-icon-document"></i>
                    本次共上传 <span class="number">{{ RowNum }}</span> 个号码
                  </div>
                </div>
                <div class="form-tip">
                  <i class="el-icon-info"></i>
                  支持 .xlsx .xls .txt 格式，文件大小不超过300M
                  <span style="color: #f56c6c">（上传文件将自动清除手动填写的手机号码）</span>
                </div>
                <div class="form-tip">
                  <i class="el-icon-document-copy"></i>
                  TXT格式：一行一个号码
                </div>
              </el-form-item>

              <!-- 手机号码输入 -->
              <el-form-item label="手机号码" prop="mobile" v-if="formData.sendType=='0'" class="form-item">
                <div class="mobile-input-wrapper">
                  <el-input
                    type="textarea"
                    v-model="formData.mobile"
                    @blur="FilterNumber"
                    @input="textChange"
                    class="form-input mobile-textarea"
                    :rows="5"
                    placeholder="手动最多输入200个手机号码，号码之间用英文逗号隔开"
                  />
                  <div class="mobile-count" :class="{ 'over-limit': limit > 200 }">
                    <span>{{ limit }}</span> / 200
                  </div>
                </div>
              </el-form-item>

              <!-- 发送时间 -->
              <el-form-item label="发送时间" prop="timingSend" class="form-item">
                <el-radio-group v-model="formData.timingSend" class="time-radio-group">
                  <el-radio label="0">
                    <i class="el-icon-s-promotion"></i>
                    立即发送
                  </el-radio>
                  <el-radio label="1">
                    <i class="el-icon-time"></i>
                    定时发送
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 定时时间选择 -->
              <el-form-item v-if="formData.timingSend == 1" prop="sendTime" class="form-item">
                <span slot="label" style="color: #909399">
                  <i class="el-icon-date"></i>
                  时间日期
                </span>
                <el-date-picker
                  v-model="formData.sendTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择发送时间"
                  align="right"
                  :picker-options="pickerOptions"
                  class="form-input date-picker"
                />
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item class="form-buttons">
                <el-button 
                  v-permission
                  type="primary" 
                  @click="submissionItem('formRef')"
                  icon="el-icon-s-promotion"
                  class="send-btn"
                  :loading="sending"
                >
                  立即发送
                </el-button>
                <el-button 
                  @click="resetForm"
                  icon="el-icon-refresh"
                  class="reset-btn"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 使用说明 -->
            <div class="usage-info">
              <h4 class="info-title">
                <i class="el-icon-question"></i>
                使用说明
              </h4>
              <ul class="info-list">
                <li>语音内容最多支持500字（含签名）</li>
                <li>每85字计费一条语音通知</li>
                <li>支持手动输入号码或文件批量导入</li>
                <li>文件导入支持 Excel 和 TXT 格式</li>
                <li>定时发送请选择未来时间</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实名认证提示弹框 -->
    <el-dialog
      title="实名认证提醒"
      :visible.sync="dialogSmrzFlag"
      width="450px"
      center
      class="auth-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog-content">
        <i class="el-icon-warning warning-icon"></i>
        <p class="dialog-text">
          尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goSmrz" icon="el-icon-user">
          前往实名认证
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传

export default {
  name: "voiceNotice",
  components: {
    FileUpload,
  },
  data() {
    var mobile = (rule, value, callback) => {
      if (value || this.RowNum) {
        if (this.limit > 200) {
          callback(new Error("已超过填写上限"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请上传文件或输入手机号"));
      }
    };
    return {
      header: {},
      endingName: "",
      fileList: [],
      dialogSmrzFlag: false,
      fileFlag: true,
      message: "",
      sending: false,
      formData: {
        content: "",
        mobile: "",
        sendType: "0",
        sendTime: "",
        timingSend: "0",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      formRule: {
        //验证规则
        content: [
          {
            required: true,
            min: 1,
            max: 500,
            message: "长度在 1 到 500 个字符",
            trigger: "blur",
          },
        ],
        mobile: [{ required: true, validator: mobile, trigger: "blur" }],
      },
      fileStyle: {
        size: 300,
        style: ["xlsx", "xls", "txt"],
      },
      del1: true, //关闭弹框时清空图片
      tip: "仅支持.xlsx .xls.txt 等格式",
      RowNum: null, //上传行数
      limit: 0, //号码限制
      textareaShow: true,
    };
  },
  created() {
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.checkCertification();
  },
  methods: {
    // 检查实名认证状态
    checkCertification() {
      this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
        (res) => {
          if (res.data.certificate == 0) {
            this.dialogSmrzFlag = true;
          }
        }
      );
    },
    goSmrz() {
      this.dialogSmrzFlag = false;
      this.$router.push("/authentication");
    },
    submissionItem(formName) {
      this.$refs[formName].validate((valid, val) => {
        if (valid) {
          if (!this.fileFlag) {
            this.$message({
              message: this.message,
              type: "error",
            });
            return;
          }
          this.sending = true;
          this.$confirms.confirmation(
            "post",
            "确认发送语音通知？",
            this.API.cpus + "v1/consumervoice/notice/send",
            this.formData,
            (res) => {
              this.sending = false;
              if (res.code == 200) {
                this.$message.success('发送成功！');
                this.resetForm();
                this.$router.push("/VoiceWebTask");
              }
            },
            () => {
              this.sending = false;
            }
          );
        }
      });
    },
    // 重置表单
    resetForm() {
      this.$refs['formRef'].resetFields();
      this.formData = {
        content: "",
        mobile: "",
        sendType: "0",
        timingSend: "0",
        sendTime: "",
      };
      this.RowNum = null;
      this.textareaShow = true;
      this.del1 = false;
      this.limit = 0;
      this.fileList = [];
    },
    //移除文件
    fileup(val) {
      this.RowNum = null;
      this.formData.group = "";
      this.formData.path = "";
      this.formData.fileName = "";
      this.formData.files = "";
      this.textareaShow = true;
      this.formData.mobile = "";
      this.limit = 0; //号码限制
      this.fileList = []
    },
    handelSend(val) {
      if (val == 0) {
        this.fileup()
      } else {
        this.formData.mobile = ''
        this.limit = 0;
      }
    },
    //限制用户上传文件格式和大小
    beforeAvatarUpload(file) {
      let endingCode = file.name;//结尾字符
      this.endingName = endingCode.slice(endingCode.lastIndexOf('.') + 1, endingCode.length);
      let isStyle = false; //文件格式
      const isSize = file.size / 1024 / 1024 < this.fileStyle.size; //文件大小
      
      for (let i = 0; i < this.fileStyle.style.length; i++) {
        if (this.endingName === this.fileStyle.style[i]) {
          isStyle = true;
          break;
        }
      }
      //不能重复上传文件
      let fileArr = this.fileList;
      let fileNames = [];
      if (fileArr.length > 0) {
        for (let k = 0; k < fileArr.length; k++) {
          fileNames.push(fileArr[k].name)
        }
      }
      if (fileNames.indexOf(endingCode) !== -1) {
        this.$message.error('不能重复上传文件');
        return false;
      } else if (!isStyle) { //文件格式判断
        this.$message.error(this.tip);
        return false;
      } else {
        //文件大小判断
        if (!isSize) {
          this.$message.error('上传文件大小不能超过' + this.fileStyle.size + 'M');
          return false;
        }
      }
    },
    //文件上传成功
    fileupres(val, val2) {
      if (val.code == 200) {
        this.fileFlag = true;
        if (val.data.total) {
          this.RowNum = val.data.total;
        } else {
          this.RowNum = null;
        }
        this.formData.fileName = val.data.fileName;
        this.formData.group = val.data.group;
        this.formData.path = val.data.path;
        this.formData.files = val.data.fileName;
        this.formData.mobile = "";
        this.textareaShow = false;
        this.del1 = true;
        this.$message({
          message: val.msg,
          type: "success",
        });
      } else {
        this.fileFlag = false;
        this.message = val.msg;
        this.$message({
          message: val.msg,
          type: "error",
        });
      }
    },
    // 过滤号码
    FilterNumber() {
      this.formData.mobile = this.formData.mobile.replace(/\D/g, ",");
      let NumberFilter = this.formData.mobile.split(",");
      let arrNumber = [];
      let hash = [];
      let reg = /^(?:\+?86)?1\d{10}$/;
      for (var i = 0; i < NumberFilter.length; i++) {
        for (var j = i + 1; j < NumberFilter.length; j++) {
          if (NumberFilter[i] === NumberFilter[j]) {
            ++i;
          }
        }
        arrNumber.push(NumberFilter[i]);
      }
      for (var i = 0; i < arrNumber.length; i++) {
        if (reg.test(arrNumber[i])) {
          hash.push(arrNumber[i]);
        }
      }
      this.formData.mobile = hash.join(",");
      this.filter = NumberFilter.length - arrNumber.length; //过滤
      if (arrNumber[0] == "") {
        this.invalid == 0;
      } else {
        this.invalid = arrNumber.length - hash.length; //无效
      }
      this.limit = hash.length;
    },
    textChange() {
      if (this.formData.mobile[this.formData.mobile.length - 1] == ",") {
        this.limit = this.formData.mobile.split(",").length - 1;
      } else {
        this.limit = this.formData.mobile.split(",").length;
      }
      if (
        this.formData.mobile.split(",").length == 1 &&
        this.formData.mobile.split(",")[0] == ""
      ) {
        this.limit = 0;
      }
    },
  },
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* voiceNotice 特有样式 */

/* 页面头部样式 */
.page-header {
  background: #fff;
  padding: 24px 32px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 表单区域样式 */
.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.voice-form {
  .form-item {
    margin-bottom: 28px;
  }

  .form-input {
    width: 100%;
    max-width: 500px;

    /deep/ .el-textarea__inner {
      font-family: inherit;
      font-size: 14px;
      line-height: 1.6;
      padding: 12px 15px;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }
    }

    /deep/ .el-input-group__prepend {
      background: #f5f7fa;
      border-color: #dcdfe6;
      padding: 0 15px;

      i {
        color: #909399;
        font-size: 16px;
      }
    }
  }

  .form-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    line-height: 1.5;

    i {
      margin-right: 4px;
    }
  }
}

/* 类型显示样式 */
.type-display {
  .el-tag {
    font-size: 14px;
    // padding: 8px 16px;
    border-radius: 20px;
  }
}

/* 发送方式选择 */
.send-type-group {
  /deep/ .el-radio {
    margin-right: 30px;
    
    .el-radio__label {
      display: flex;
      align-items: center;
      
      i {
        margin-right: 6px;
        font-size: 16px;
      }
    }
  }
}

/* 文件上传样式 */
.upload-section {
  width: 100%;
  max-width: 500px;
}

.upload-dragger {
  width: 100%;

  /deep/ .el-upload {
    width: 100%;
  }

  /deep/ .el-upload-dragger {
    width: 100%;
    height: 180px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      background: rgba(64, 158, 255, 0.02);
    }

    .el-icon-upload {
      font-size: 67px;
      color: #c0c4cc;
      margin: 40px 0 16px;
      line-height: 50px;
    }

    .el-upload__text {
      color: #606266;
      font-size: 14px;

      em {
        color: #409eff;
        font-style: normal;
      }
    }
  }
}

.upload-info {
  margin-top: 12px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 14px;
  color: #409eff;
  
  i {
    margin-right: 6px;
  }
  
  .number {
    font-weight: 600;
    color: #f56c6c;
  }
}

/* 手机号码输入 */
.mobile-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.mobile-textarea {
  /deep/ .el-textarea__inner {
    padding-bottom: 35px;
  }
}

.mobile-count {
  position: absolute;
  bottom: 8px;
  right: 8px;
  font-size: 12px;
  color: #909399;
  
  &.over-limit {
    color: #f56c6c;
    font-weight: 600;
  }
}

/* 时间选择样式 */
.time-radio-group {
  /deep/ .el-radio {
    margin-right: 30px;
    
    .el-radio__label {
      display: flex;
      align-items: center;
      
      i {
        margin-right: 6px;
        font-size: 16px;
      }
    }
  }
}

.date-picker {
  /deep/ .el-input__inner {
    border-radius: 6px;
  }
}

/* 按钮样式 */
.form-buttons {
  margin-top: 40px;
  margin-bottom: 0;
  
  /deep/ .el-form-item__content {
    margin-left: 0 !important;
  }
}

.send-btn {
  min-width: 120px;
  height: 40px;
  font-size: 15px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
}

.reset-btn {
  min-width: 100px;
  height: 40px;
  font-size: 15px;
  border-radius: 6px;
  margin-left: 12px;
}

/* 使用说明样式 */
.usage-info {
  margin-top: 48px;
  padding: 24px;
  background: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: #409eff;
    font-size: 18px;
  }
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 12px;
    font-size: 14px;
    color: #606266;
    line-height: 1.6;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 8px;
      width: 6px;
      height: 6px;
      background: #409eff;
      border-radius: 50%;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 认证对话框样式 */
.auth-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;
  }

  /deep/ .el-dialog__header {
    background: #fff3cd;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    border-bottom: 1px solid #ffeaa7;
  }

  /deep/ .el-dialog__title {
    color: #856404;
    font-weight: 600;
  }

  /deep/ .el-dialog__body {
    padding: 32px 24px;
  }
}

.dialog-content {
  text-align: center;
}

.warning-icon {
  font-size: 48px;
  color: #e6a23c;
  margin-bottom: 16px;
}

.dialog-text {
  font-size: 15px;
  line-height: 1.8;
  color: #606266;
  margin: 0;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section {
    padding: 20px;
  }

  .form-container {
    max-width: 100%;
  }

  .voice-form {
    .form-input {
      max-width: 100%;
    }
  }

  .upload-section {
    max-width: 100%;
  }

  .mobile-input-wrapper {
    max-width: 100%;
  }

  .form-buttons {
    .send-btn,
    .reset-btn {
      width: 100%;
      margin-left: 0;
      margin-bottom: 12px;
    }

    /deep/ .el-form-item__content {
      display: flex;
      flex-direction: column;
    }
  }

  .auth-dialog {
    width: 90% !important;
  }
}
</style>