<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 页面标题 -->
        <div class="page-header">
          <h2 class="page-title">语音验证码</h2>
          <div class="page-subtitle">发送语音验证码到指定手机号码</div>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
          <div class="form-container">
            <el-form 
              :model="formData" 
              :rules="formRule" 
              ref="formRef" 
              label-width="120px" 
              class="voice-form"
            >
              <!-- 语音类型 -->
              <el-form-item label="语音类型" prop="isTiming" class="form-item">
                <div class="type-display">
                  <el-tag type="primary">验证码</el-tag>
                </div>
              </el-form-item>

              <!-- 验证码输入 -->
              <el-form-item label="验证码" prop="content" class="form-item">
                <el-input 
                  v-model="formData.content" 
                  placeholder="请输入4-6位数字验证码"
                  type="textarea"
                  :rows="3"
                  maxlength="6"
                  show-word-limit
                  class="form-input"
                >
                  <template slot="prepend">
                    <i class="el-icon-key"></i>
                  </template>
                </el-input>
                <div class="form-tip">
                  <i class="el-icon-info"></i>
                  请输入4-6位数字，系统将通过语音播报给接收者
                </div>
              </el-form-item>

              <!-- 手机号输入 -->
              <el-form-item 
                label="手机号" 
                prop="mobile" 
                :rules="filter_rules({required:true,type:'fiveMobiles',message:'请输入手机号码'})"
                class="form-item"
              >
                <el-input 
                  v-model="formData.mobile" 
                  placeholder="请输入接收语音验证码的手机号码"
                  type="textarea"
                  :rows="2"
                  class="form-input"
                >
                  <template slot="prepend">
                    <i class="el-icon-mobile-phone"></i>
                  </template>
                </el-input>
                <div class="form-tip">
                  <i class="el-icon-info"></i>
                  仅支持输入一个手机号码
                </div>
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item class="form-buttons">
                <el-button 
                  v-permission
                  type="primary" 
                  @click="send"
                  icon="el-icon-s-promotion"
                  class="send-btn"
                  :loading="sending"
                >
                  测试发送
                </el-button>
                <el-button 
                  @click="resetForm"
                  icon="el-icon-refresh"
                  class="reset-btn"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 使用说明 -->
            <div class="usage-info">
              <h4 class="info-title">
                <i class="el-icon-question"></i>
                使用说明
              </h4>
              <ul class="info-list">
                <li>语音验证码支持4-6位数字</li>
                <li>系统会以电话形式播报验证码内容</li>
                <li>请确保手机号码正确且可接听电话</li>
                <!-- <li>测试发送不会产生费用</li> -->
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实名认证提示弹框 -->
    <el-dialog
      title="实名认证提醒"
      :visible.sync="dialogSmrzFlag"
      width="450px"
      center
      class="auth-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog-content">
        <i class="el-icon-warning warning-icon"></i>
        <p class="dialog-text">
          尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goSmrz" icon="el-icon-user">
          前往实名认证
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "voiceCode",
  data() {
    var content = (rule, value, callback) => {
      if (!/^\d{4,6}$/.test(value)) {
        callback(new Error('请输入4-6位的验证码'))
      } else {
        callback()
      }
    }
    return {
      dialogSmrzFlag: false,
      sending: false,
      formData: {
        content: '',
        mobile: '',
      },
      formRule: {
        content: [
          { required: true, validator: content, trigger: 'blur' },
        ],
      }
    }
  },
  created() {
    this.checkCertification();
  },
  methods: {
    // 检查实名认证状态
    checkCertification() {
      this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
        (res) => {
          if (res.data.certificate == 0) {
            this.dialogSmrzFlag = true
          }
        }
      );
    },
    // 前往实名认证
    goSmrz() {
      this.dialogSmrzFlag = false
      this.$router.push("/authentication")
    },
    // 发送验证码
    send() {
      this.$refs['formRef'].validate((valid, value) => {
        if (valid) {
          this.sending = true;
          this.$confirms.confirmation(
            'post',
            '确认发送验证码？',
            this.API.cpus + 'v1/consumervoice/captcha/send',
            this.formData,
            res => {
              this.sending = false;
              if (res.code == 200) {
                this.$message.success('发送成功！');
                this.$refs['formRef'].resetFields();
              }
            },
            () => {
              this.sending = false;
            }
          );
        }
      })
    },
    // 重置表单
    resetForm() {
      this.$refs['formRef'].resetFields();
    }
  },
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* voiceCode 特有样式 */

/* 页面头部样式 */
.page-header {
  background: #fff;
  padding: 24px 32px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 表单区域样式 */
.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.voice-form {
  .form-item {
    margin-bottom: 28px;
  }

  .form-input {
    width: 100%;
    max-width: 500px;

    /deep/ .el-textarea__inner {
      font-family: inherit;
      font-size: 14px;
      line-height: 1.6;
      padding: 12px 15px;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }
    }

    /deep/ .el-input-group__prepend {
      background: #f5f7fa;
      border-color: #dcdfe6;
      padding: 0 15px;

      i {
        color: #909399;
        font-size: 16px;
      }
    }
  }

  .form-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    line-height: 1.5;

    i {
      margin-right: 4px;
    }
  }
}

/* 类型显示样式 */
.type-display {
  .el-tag {
    font-size: 14px;
    // padding: 8px 16px;
    border-radius: 20px;
  }
}

/* 按钮样式 */
.form-buttons {
  margin-top: 40px;
  margin-bottom: 0;
  
  /deep/ .el-form-item__content {
    margin-left: 0 !important;
  }
}

.send-btn {
  min-width: 120px;
  height: 40px;
  font-size: 15px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
}

.reset-btn {
  min-width: 100px;
  height: 40px;
  font-size: 15px;
  border-radius: 6px;
  margin-left: 12px;
}

/* 使用说明样式 */
.usage-info {
  margin-top: 48px;
  padding: 24px;
  background: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: #409eff;
    font-size: 18px;
  }
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 12px;
    font-size: 14px;
    color: #606266;
    line-height: 1.6;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 8px;
      width: 6px;
      height: 6px;
      background: #409eff;
      border-radius: 50%;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 认证对话框样式 */
.auth-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;
  }

  /deep/ .el-dialog__header {
    background: #fff3cd;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    border-bottom: 1px solid #ffeaa7;
  }

  /deep/ .el-dialog__title {
    color: #856404;
    font-weight: 600;
  }

  /deep/ .el-dialog__body {
    padding: 32px 24px;
  }
}

.dialog-content {
  text-align: center;
}

.warning-icon {
  font-size: 48px;
  color: #e6a23c;
  margin-bottom: 16px;
}

.dialog-text {
  font-size: 15px;
  line-height: 1.8;
  color: #606266;
  margin: 0;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section {
    padding: 20px;
  }

  .form-container {
    max-width: 100%;
  }

  .voice-form {
    .form-input {
      max-width: 100%;
    }
  }

  .form-buttons {
    .send-btn,
    .reset-btn {
      width: 100%;
      margin-left: 0;
      margin-bottom: 12px;
    }

    /deep/ .el-form-item__content {
      display: flex;
      flex-direction: column;
    }
  }

  .auth-dialog {
    width: 90% !important;
  }
}
</style>