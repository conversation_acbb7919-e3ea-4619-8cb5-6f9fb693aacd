<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 页面标题 -->
        <div class="page-header">
          <h2 class="page-title">个性化通知</h2>
          <div class="page-subtitle">支持变量替换的个性化语音通知发送</div>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
          <div class="form-container">
            <el-form 
              :model="formData" 
              ref="formRef" 
              label-width="120px" 
              class="voice-form"
            >
              <!-- 语音类型 -->
              <el-form-item label="语音类型" prop="" class="form-item">
                <div class="type-display">
                  <el-tag type="success">语音个性化通知</el-tag>
                  <el-button 
                    type="text" 
                    @click="downloadTemplate"
                    icon="el-icon-download"
                    class="template-download"
                  >
                    下载模板文件
                  </el-button>
                </div>
              </el-form-item>

              <!-- 文件上传 -->
              <el-form-item label="语音内容" prop="" class="form-item">
                <div class="upload-section">
                  <file-upload
                    v-permission
                    :action="this.API.cpus + 'v3/file/upload'"
                    :limit="1"
                    :showfileList="true"
                    :fileStyle="fileStyle"
                    :del="del1"
                    :istip="false"
                    :tip="tip"
                    @fileup="fileup"
                    @fileupres="fileupres"
                    class="custom-upload"
                  >
                    <el-button type="primary" icon="el-icon-upload">
                      选择上传文件
                    </el-button>
                  </file-upload>
                  <div v-if="RowNum" class="upload-info">
                    <i class="el-icon-document"></i>
                    本次共上传 <span class="number">{{ RowNum }}</span> 个号码
                  </div>
                </div>
                <div class="form-tip">
                  <i class="el-icon-info"></i>
                  支持 .xlsx .xls .txt 格式，请使用标准模板格式
                </div>
                <!-- <div class="form-tip">
                  <i class="el-icon-warning"></i>
                  个性化通知需要在内容中使用变量标记（如：{name}、{code}等）
                </div> -->
              </el-form-item>

              <!-- 发送时间 -->
              <el-form-item label="发送时间" prop="timingSend" class="form-item">
                <el-radio-group v-model="formData.timingSend" class="time-radio-group">
                  <el-radio label="0">
                    <i class="el-icon-s-promotion"></i>
                    立即发送
                  </el-radio>
                  <el-radio label="1">
                    <i class="el-icon-time"></i>
                    定时发送
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 定时时间选择 -->
              <el-form-item v-if="formData.timingSend == 1" prop="sendTime" class="form-item">
                <span slot="label" style="color: #909399">
                  <i class="el-icon-date"></i>
                  时间日期
                </span>
                <el-date-picker
                  v-model="formData.sendTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择发送时间"
                  align="right"
                  :picker-options="pickerOptions"
                  class="form-input date-picker"
                />
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item class="form-buttons">
                <el-button 
                  v-permission
                  type="primary" 
                  @click="submissionItem('formRef')"
                  icon="el-icon-s-promotion"
                  class="send-btn"
                  :loading="sending"
                >
                  立即发送
                </el-button>
                <el-button 
                  @click="resetForm"
                  icon="el-icon-refresh"
                  class="reset-btn"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-form>

            <!-- 使用说明 -->
            <div class="usage-info">
              <h4 class="info-title">
                <i class="el-icon-question"></i>
                使用说明
              </h4>
              <ul class="info-list">
                <li>请先下载标准模板文件，按照格式填写数据</li>
                <!-- <li>支持在语音内容中使用变量，系统会自动替换</li>
                <li>常用变量：{name}（姓名）、{code}（验证码）、{amount}（金额）等</li>
                <li>Excel 文件请包含手机号列和变量内容列</li>
                <li>TXT 文件格式：手机号,变量1,变量2...</li> -->
                <li>定时发送请选择未来时间</li>
              </ul>
            </div>

            <!-- 模板示例 -->
            <!-- <div class="template-example">
              <h4 class="example-title">
                <i class="el-icon-document"></i>
                模板示例
              </h4>
              <div class="example-content">
                <div class="example-item">
                  <div class="example-label">语音内容模板：</div>
                  <div class="example-text">尊敬的{name}，您的验证码是{code}，请在5分钟内使用。</div>
                </div>
                <div class="example-item">
                  <div class="example-label">Excel 文件格式：</div>
                  <div class="example-table">
                    <table>
                      <thead>
                        <tr>
                          <th>手机号</th>
                          <th>内容</th>
                          <th>验证码</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>13800138000</td>
                          <td>张三</td>
                          <td>123456</td>
                        </tr>
                        <tr>
                          <td>13900139000</td>
                          <td>李四</td>
                          <td>789012</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传

export default {
  name: "voicePersonalise",
  components: {
    FileUpload,
  },
  data() {
    return {
      fileFlag: true,
      message: "",
      sending: false,
      RowNum: null,
      textareaShow: true,
      formData: {
        sendTime: "",
        timingSend: "0",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      fileStyle: {
        size: 300,
        style: ["xlsx", "xls", "txt"],
      },
      del1: true, //关闭弹框时清空图片
      tip: "仅支持.xlsx .xls.txt 等格式",
    };
  },
  methods: {
    submissionItem(formName) {
      this.$refs[formName].validate((valid, val) => {
        if (valid) {
          if (!this.fileFlag) {
            this.$message({
              message: this.message,
              type: "error",
            });
            return;
          }
          
          if (!this.formData.fileName) {
            this.$message({
              message: "请先上传文件",
              type: "warning",
            });
            return;
          }

          this.sending = true;
          this.$confirms.confirmation(
            "post",
            "确认发送个性化通知？",
            this.API.cpus + "v1/consumervoice/customize/send",
            this.formData,
            (res) => {
              this.sending = false;
              if (res.code == 200) {
                this.$message.success('发送成功！');
                this.resetForm();
                this.$router.push("/VoiceWebTask");
              }
            },
            () => {
              this.sending = false;
            }
          );
        }
      });
    },
    
    // 重置表单
    resetForm() {
      this.$refs['formRef'].resetFields();
      this.formData = {
        sendTime: "",
        timingSend: "0",
      };
      this.RowNum = null;
      this.textareaShow = true;
      this.del1 = false;
    },

    // 下载模板
    downloadTemplate() {
      window.open(
        "https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/d3ccbf40077f10ad131af7fc0a8aa330",
        "_blank"
      );
    },

    //移除文件
    fileup(val) {
      this.RowNum = null;
      this.formData.group = "";
      this.formData.path = "";
      this.formData.fileName = "";
      this.formData.files = "";
      this.textareaShow = true;
      this.formData.mobile = "";
      this.limit = 0; //号码限制
    },

    //文件上传成功
    fileupres(val, val2) {
      if (val.code == 200) {
        this.fileFlag = true;
        if (val.data.total) {
          this.RowNum = val.data.total;
        } else {
          this.RowNum = null;
        }
        this.formData.fileName = val.data.fileName;
        this.formData.group = val.data.group;
        this.formData.path = val.data.path;
        this.formData.files = val2[0].name;
        this.formData.mobile = "";
        this.textareaShow = false;
        this.del1 = true;
        this.$message({
          message: val.msg,
          type: "success",
        });
      } else {
        this.fileFlag = false;
        this.message = val.msg;
        this.$message({
          message: val.msg,
          type: "error",
        });
      }
    },

    formWorkDownload() {
      this.$File.export(
        this.API.cpus + "v3/consumersms/templateZipDownload",
        {},
        `自定义发送模板.zip`
      );
    },
  },
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* voicePersonalise 特有样式 */

/* 页面头部样式 */
.page-header {
  background: #fff;
  padding: 24px 32px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 表单区域样式 */
.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.voice-form {
  .form-item {
    margin-bottom: 28px;
  }

  .form-input {
    width: 100%;
    max-width: 500px;

    /deep/ .el-input__inner {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }
    }
  }

  .form-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    line-height: 1.5;

    i {
      margin-right: 4px;
    }
  }
}

/* 类型显示样式 */
.type-display {
  display: flex;
  align-items: center;
  gap: 16px;

  .el-tag {
    font-size: 14px;
    border-radius: 20px;
  }
}

.template-download {
  font-size: 14px;
  color: #409eff;
  text-decoration: none;

  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }

  i {
    margin-right: 4px;
  }
}

/* 文件上传样式 */
.upload-section {
  width: 100%;
  max-width: 500px;
}

.custom-upload {
  /deep/ .file-upload-box {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      background: rgba(64, 158, 255, 0.02);
    }
  }
}

.upload-info {
  margin-top: 12px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 14px;
  color: #409eff;
  
  i {
    margin-right: 6px;
  }
  
  .number {
    font-weight: 600;
    color: #f56c6c;
  }
}

/* 时间选择样式 */
.time-radio-group {
  /deep/ .el-radio {
    margin-right: 30px;
    
    .el-radio__label {
      display: flex;
      align-items: center;
      
      i {
        margin-right: 6px;
        font-size: 16px;
      }
    }
  }
}

.date-picker {
  /deep/ .el-input__inner {
    border-radius: 6px;
  }
}

/* 按钮样式 */
.form-buttons {
  margin-top: 40px;
  margin-bottom: 0;
  
  /deep/ .el-form-item__content {
    margin-left: 0 !important;
  }
}

.send-btn {
  min-width: 120px;
  height: 40px;
  font-size: 15px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }
}

.reset-btn {
  min-width: 100px;
  height: 40px;
  font-size: 15px;
  border-radius: 6px;
  margin-left: 12px;
}

/* 使用说明样式 */
.usage-info {
  margin-top: 48px;
  padding: 24px;
  background: #f5f7fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: #409eff;
    font-size: 18px;
  }
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 12px;
    font-size: 14px;
    color: #606266;
    line-height: 1.6;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 8px;
      width: 6px;
      height: 6px;
      background: #409eff;
      border-radius: 50%;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 模板示例样式 */
.template-example {
  margin-top: 32px;
  padding: 24px;
  background: #fff9e6;
  border-radius: 8px;
  border-left: 4px solid #e6a23c;
}

.example-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    color: #e6a23c;
    font-size: 18px;
  }
}

.example-content {
  .example-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .example-label {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .example-text {
    padding: 12px 16px;
    background: #f5f5f5;
    border-radius: 6px;
    font-size: 14px;
    color: #606266;
    border-left: 3px solid #e6a23c;
    font-family: 'Courier New', monospace;
  }
}

.example-table {
  table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    th,
    td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid #f0f0f0;
      font-size: 14px;
    }

    th {
      background: #f8f9fa;
      font-weight: 600;
      color: #333;
    }

    td {
      color: #606266;
    }

    tr:last-child td {
      border-bottom: none;
    }

    tr:hover {
      background: #f5f7fa;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section {
    padding: 20px;
  }

  .form-container {
    max-width: 100%;
  }

  .voice-form {
    .form-input {
      max-width: 100%;
    }
  }

  .upload-section {
    max-width: 100%;
  }

  .type-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .form-buttons {
    .send-btn,
    .reset-btn {
      width: 100%;
      margin-left: 0;
      margin-bottom: 12px;
    }

    /deep/ .el-form-item__content {
      display: flex;
      flex-direction: column;
    }
  }

  .example-table {
    overflow-x: auto;
    
    table {
      min-width: 400px;
    }
  }
}
</style>