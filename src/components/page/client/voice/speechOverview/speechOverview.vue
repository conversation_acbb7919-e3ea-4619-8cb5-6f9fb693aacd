<template>
    <div class="speech-overview-container">
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h2 class="page-title">语音统计分析</h2>
                    <div class="page-subtitle">查看语音验证码和语音通知的发送统计数据和分析报告</div>
                </div>

                <!-- 组件内容区域 -->
                <div class="component-content">
                    <!-- 今日数据区域 -->
                    <div class="overview-cards">
                        <div class="overview-card today-data-card" v-if="this.$store.state.isDateState == 1">
                            <div class="card-header">
                                <h3 class="card-title">今日数据</h3>
                                <span class="update-time">更新于：{{ updateTime }}</span>
                            </div>
                            <div class="card-content">
                                <div class="stats-grid">
                                    <div class="stat-item voice-code-stat">
                                        <div class="stat-value">{{ billingNumber }}<span class="unit">条</span></div>
                                        <div class="stat-label">语音验证码发送号码数</div>
                                    </div>
                                    <div class="stat-item voice-notice-stat">
                                        <div class="stat-value">{{ statisticss.billingNumber }}<span
                                                class="unit">条</span></div>
                                        <div class="stat-label">语音通知发送号码数</div>
                                    </div>
                                    <div class="stat-item voice-code-stat">
                                        <div class="stat-value">{{ sendAmounts }}<span class="unit">条</span></div>
                                        <div class="stat-label">语音验证码计费条数</div>
                                    </div>
                                    <div class="stat-item voice-notice-stat">
                                        <div class="stat-value">{{ statisticss.sendAmounts }}<span class="unit">条</span>
                                        </div>
                                        <div class="stat-label">语音通知计费条数</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="overview-card today-data-card" v-if="this.$store.state.isDateState == 2">
                            <div class="card-header">
                                <h3 class="card-title">今日数据</h3>
                            </div>
                            <div class="card-content">
                                <div class="stats-grid simple">
                                    <div class="stat-item general-stat">
                                        <div class="stat-value">{{ billingNumber }}<span class="unit">条</span></div>
                                        <div class="stat-label">今日发送号码数</div>
                                    </div>
                                    <div class="stat-item general-stat">
                                        <div class="stat-value">{{ sendAmounts }}<span class="unit">条</span></div>
                                        <div class="stat-label">计费条数</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 产品余额卡片 -->
                        <div class="overview-card balance-card">
                            <div class="card-header">
                                <h3 class="card-title">产品余额</h3>
                            </div>
                            <div class="card-content">
                                <div class="balance-info">
                                    <div class="balance-item">
                                        <div class="balance-label">
                                            <span class="vertical-line"></span>
                                            产品名称
                                        </div>
                                        <div class="balance-products">
                                            <div class="balance-value product-name">语音验证码</div>
                                            <div class="balance-value product-name">语音通知</div>
                                        </div>
                                    </div>
                                    <div class="balance-item">
                                        <div class="balance-label">
                                            <span class="vertical-line"></span>
                                            余额（条）
                                        </div>
                                        <div class="balance-amounts">
                                            <div class="balance-value highlight">{{ restNumSum }}</div>
                                            <div class="balance-value highlight">{{ statistics.restNumSum }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 语音验证码部分 -->
                    <div class="section-title">
                        <span>语音验证码</span>
                    </div>

                    <div class="chart-section">
                        <!-- 统计图查询条件 -->
                        <div class="section-header">
                           
                            <div class="chart-controls">
                                <span class="control-label">发送时间：</span>
                                <el-radio-group v-model="getObject.specificTime" size="small"
                                    @change="handleChangeTimeOptions" class="time-radio-group">
                                    <el-radio-button label="1">更新最近十天</el-radio-button>
                                </el-radio-group>
                            </div>
                        </div>

                        <div class="chart-container">
                            <chart id="DataSChert" :option='option' class="trend-chart"></chart>
                        </div>
                    </div>

                    <!-- 统计数据表格区域 -->
                    <div class="statistics-section">
                        <!-- 统计列表查询条件（天，月） -->
                        <!-- <div class="section-header">
                            <div class="statistics-controls">
                                <span class="control-label">发送时间：</span>
                                <el-radio-group v-model="getObjects.specificTime" size="small"
                                    @change="handleChangeTimeOptions1" class="time-radio-group">
                                    <el-radio-button label="1">统计月</el-radio-button>
                                    <el-radio-button label="2">统计年</el-radio-button>
                                </el-radio-group>
                                <date-plugin v-if="getObjects.specificTime == '1'" class="date-picker"
                                    :datePluginValueList="datePluginValueList1"
                                    @handledatepluginVal="handledatepluginVals" />
                                <el-date-picker v-if="getObjects.specificTime == '2'" v-model="year" type="monthrange"
                                    :clearable='false' value-format="yyyy-MM" @change="handelMonth"
                                    :picker-options="monthPickerOptions" range-separator="-" start-placeholder="开始月份"
                                    end-placeholder="结束月份" class="month-picker">
                                </el-date-picker>
                                <el-button type="primary" @click="download" class="export-btn" icon="el-icon-download">
                                    导出数据
                                </el-button>
                            </div>
                        </div> -->
                        <div class="section-header">
                                <h3 class="section-title1">语音验证码统计数据</h3>
                                <div class="statistics-controls">
                                    <span class="control-label">发送时间：</span>
                                    <el-radio-group v-model="getObjects.specificTime" size="small"
                                        @change="handleChangeTimeOptions1" class="time-radio-group">
                                        <el-radio-button label="1">统计月</el-radio-button>
                                        <el-radio-button label="2">统计年</el-radio-button>
                                    </el-radio-group>
                                    <date-plugin v-if="getObjects.specificTime == '1'" class="date-picker"
                                        :datePluginValueList="datePluginValueList1"
                                        @handledatepluginVal="handledatepluginVals" />
                                    <el-date-picker v-if="getObjects.specificTime == '2'" v-model="year"
                                        type="monthrange" :clearable='false' value-format="yyyy-MM"
                                        @change="handelMonth" :picker-options="monthPickerOptions" range-separator="-"
                                        start-placeholder="开始月份" end-placeholder="结束月份" class="month-picker">
                                    </el-date-picker>
                                    <el-button type="primary" @click="download" class="export-btn"
                                        icon="el-icon-download">
                                        导出数据
                                    </el-button>
                                </div>
                            </div>
                        <div class="table-container">
                            <table-tem :tableDataObj="tableDataObjs1"
                                @handelOptionButton="handelOptionButton"></table-tem>
                            <div class="pagination-wrapper">
                                <el-pagination @size-change="handleSizeChange1s" @current-change="handleCurrentChange1s"
                                    :current-page="getObjects.currentPage" :page-size="getObjects.pageSize"
                                    :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                                    :total="tableDataObjs1.totalRow" class="table-pagination"></el-pagination>
                            </div>
                        </div>
                    </div>

                    <!-- 语音通知统计组件 -->
                    <div class="speech-notice-section">
                        <div class="section-title">
                            <span>语音通知</span>
                        </div>
                        <div class="speech-notice-content">
                            <SpeehOver @childFn="parentFn" @childFnn="parentFnn" />
                        </div>
                    </div>

                    <!-- 报告的弹出框 -->
                    <el-dialog title="任务报告查看" :visible.sync="sendMsgDialogVisible" width="860px" class="report-dialog">
                        <template v-if="this.$store.state.isDateState == 1">
                            <div class="dialog-charts">
                                <div class="chart-item">
                                    <PieChart id="pie2" width="360px" height="300px" :basicOption="basicOption1">
                                    </PieChart>
                                </div>
                                <div class="chart-item">
                                    <PieChart id="pie1" width="420px" height="300px" :basicOption="basicOption2">
                                    </PieChart>
                                </div>
                            </div>
                            <div class="dialog-section">
                                <h4 class="dialog-section-title">发送明细列表</h4>
                                <table-tem :tableDataObj="tableDataObj1"></table-tem>
                            </div>
                        </template>
                        <template v-if="this.$store.state.isDateState == 2">
                            <div class="dialog-chart-single">
                                <PieChart id="pie1" height="300px" :basicOption="basicOption2"></PieChart>
                            </div>
                        </template>
                        <div class="dialog-section">
                            <h4 class="dialog-section-title">回执失败代码分析列表</h4>
                            <table-tem :tableDataObj="tableDataObj2"></table-tem>
                        </div>
                        <div slot="footer" class="dialog-footer">
                            <el-button type="primary" @click="sendMsgDialogVisible = false">知道了</el-button>
                        </div>
                    </el-dialog>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import PieChart from '@/components/publicComponents/PieChart' //饼图
import TableTem from '@/components/publicComponents/TableTem'
import Chart from '@/components/publicComponents/Chart'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import SpeehOver from '../components/ImsViewCharts'
import moment from 'moment'

export default {
    name: "voiceSpeech",
    components: {
        TableTem,
        Chart,
        DatePlugin,
        PieChart,
        SpeehOver
    },
    data() {
        return {
            name: "voiceSpeech",
            MsgNotice: '行业通知',
            updateTime: '',
            billingNumber: '',
            restNumSum: '',
            sendAmounts: '',
            successNum: '',
            successRates: '',
            aggregate: '',
            sendMsgDialogVisible: false, //报告弹出框
            value: '1',
            Time10: '',//储存近10天时间
            getObject: {
                specificTime: '1', //选择那一天
                value: '',
                beginTime: '',
                endTime: '',
                currentPage: 1,
                pageSize: 10,
                isDownload: 2,
                productId: 6,
            },
            getObject1: {
                specificTime: '1', //选择那一天
                beginTime: '',
                endTime: '',
                currentPage: 1,
                pageSize: 10,
                isDownload: 2,
                productId: 6,
                flag: ''
            },
            //统计日 统计月
            year: [moment().subtract(5, "Months").format("YYYY-MM"), moment().format('YYYY-MM')],
            getObjects: {
                specificTime: '1', //选择那一天
                beginMonth: moment().subtract(5, "Months").format("YYYY-MM"),
                endMonth: moment().format('YYYY-MM'),
                beginTime: '',
                endTime: '',
                currentPage: 1,
                pageSize: 10,
                isDownload: 2,
                productId: 6,
            },
            getObjects1: {
                specificTime: '1', //选择那一天
                beginMonth: moment().subtract(5, "Months").format("YYYY-MM"),
                endMonth: moment().format('YYYY-MM'),
                beginTime: '',
                endTime: '',
                currentPage: 1,
                pageSize: 10,
                isDownload: 2,
                productId: 6,
                flag: ''
            },
            monthPickerOptions: {
                disabledDate: (time) => {
                    function add0(m) { return m < 10 ? '0' + m : m }
                    function setDate() {
                        var time = new Date();
                        var y = time.getFullYear();
                        var m = time.getMonth() + 1;
                        return y + '.' + add0(m) + '.01'
                    }
                    const date = new Date(setDate())// 说一下，为啥要加setDate()，因为在使用的时候会出现初始值选不中的情况，需要把初始值设置成当月的第一天的0点，才能选中，否则不能选中
                    const year = date.getFullYear()
                    return time.getTime() > Date.now() || time.getTime() < date.setFullYear(year - 1)
                },
            },
            datePluginValueList: { //日期选择器
                type: "daterange",
                start: "",
                end: '',
                range: '-',
                clearable: false, //是否开启关闭按钮
                pickerOptions: {
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: (time) => {
                        if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
                            return false
                        }
                        if (this.pickerMinDate !== '') {
                            const day30 = 30 * 24 * 3600 * 1000
                            let maxTime = this.pickerMinDate + day30
                            if (maxTime > Date.now() - 86400000) {
                                maxTime = Date.now() - 86400000
                            }
                            const minTime = this.pickerMinDate - day30
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                        this.getObject.specificTime = '1'
                        return time.getTime() > Date.now() - 86400000;
                    }
                },
                datePluginValue: '',
            },
            datePluginValueList1: { //日期选择器
                type: "daterange",
                start: "",
                end: '',
                range: '-',
                clearable: false, //是否开启关闭按钮
                pickerOptions: {
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate(time) {
                        let t = new Date();
                        let Y = t.getFullYear();
                        let M = t.getMonth();
                        let D = t.getDate();
                        return (time.getTime() < new Date(Y - 1, M, D).getTime() || time.getTime() > new Date(Y, M, D).getTime())
                    }
                },
                datePluginValue: [moment().subtract(10, "days").format("YYYY-MM-DD"), moment().format('YYYY-MM-DD')],
            },
            tableDataObj1: {
                //列表数据
                tableData: [],
                tableLabel: [//列表表头
                    { prop: "sendAmount", showName: '提交号码计费数', fixed: false },
                    { prop: "successAmount", showName: '成功号计费数', fixed: false },
                    { prop: "failNumber", showName: '失败号计费数', width: 120, fixed: false },
                    { prop: "waitNumber", showName: '待返回号码计费数', width: 120, fixed: false }
                ],
                tableStyle: { //列表配置项
                    isSelection: false,//是否复选框
                    isExpand: false,//是否是折叠的
                    isDefaultExpand: false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '120',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                }
            },
            tableDataObj2: {
                tableData: [],
                tableLabel: [{ //列表表头
                    prop: "failureCodeNoteName",
                    showName: '失败原因',
                    fixed: false
                }, {
                    prop: "codeNoteNum",
                    showName: '数量',
                    fixed: false
                }, {
                    prop: "codeNoteProportion",
                    showName: '占比',
                    fixed: false,
                    formatData: function (val) {
                        if (val != '') {
                            return val + '%';
                        } else {
                            return val;
                        }
                    }
                }
                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    height: 200,//是否固定表头
                    isExpand: false,//是否是折叠的
                    isDefaultExpand: false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '120',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                }
            },
            basicOption1: {//发送明细图表
                data: [
                    { value: '', name: '成功' },
                    { value: '', name: '失败' },
                    { value: '', name: '待返回' }
                ],
                ledate: ['成功', '失败', '待返回'],
                bgColor: ['#8996E6', '#98D87D', '#FFD86E'],
                radius: '62%',
                title: {
                    textStyle: {
                        color: "#999",
                        fontSize: 14
                    },
                    text: '发送明细图表',
                    x: 'right'
                },

            },
            basicOption2: {//回执失败代码分析图表
                data: [],
                ledate: [],
                bgColor: ['#8996E6', '#49A9EE', '#98D87D', '#FFD86E', '#F3857C'],
                radius: '62%',
                title: {
                    text: '回执失败代码分析图表',
                    textStyle: {
                        color: "#999",
                        fontSize: 14
                    },
                    x: 'right'
                },

            },
            option: {
                color: ['#0099FF', '#67c23a'],
                grid: {
                    left: '2%',
                    right: '3%',
                    bottom: '1%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                },
                legend: {
                    data: ['计费数']
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#0099FF',//坐标值得具体的颜色
                        }
                    }
                },
                yAxis: [{
                    type: 'value',
                    name: '计费数',
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#0099FF',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                },
                {
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },

                    axisLabel: {
                        textStyle: {
                            color: '#67c23a',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },

                }
                ],

                series: [
                    {
                        name: '',
                        type: "bar",
                        barWidth: 50,
                        lineStyle: {
                            normal: {
                                width: 3,
                                shadowColor: 'rgba(0,0,0,0.4)',
                                shadowBlur: 6,
                                shadowOffsetY: 10
                            }
                        },
                        markPoint: {//最大值 最小值
                            data: [
                                { type: 'max', name: '最大值' },
                                { type: 'min', name: '最小值' }
                            ]
                        },
                        markLine: { //平均值
                            data: [
                                { type: 'average', name: '平均值' }
                            ],
                            label: {
                                position: "middle"  //将警示值放在哪个位置，三个值"start","middle","end"  开始  中点 结束
                            }
                        },
                        data: [],
                    }
                ]
            },
            sendAmount: [],
            successAmount: [],
            successRate: [],
            statistics: {},
            statisticss: {},
            tableDataObj: { //表格数据
                totalRow: 0,
                tableData: [],
                tableLabel: [  //列表表头
                    { prop: "createTime", showName: '统计时间', fixed: false },
                    { prop: "sendAmount", showName: '计费数', fixed: false },
                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    isExpand: false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '160',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                },
                tableOptions: [
                    {
                        optionName: '任务报告',
                        type: '',
                        size: 'mini',
                        optionMethod: 'missionReport',
                        icon: 'el-icon-success'
                    }
                ]
            },
            //月，日统计表
            tableDataObjs1: { //表格数据
                totalRow: 0,
                loading2: false,
                tableData: [],
                tableLabel: [  //列表表头
                    { prop: "sendTime", showName: '发送时间', fixed: false },
                    { prop: "sendAmount", showName: '发送号码数', fixed: false },
                    { prop: "chargeNum", showName: '发送计费数', fixed: false },
                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    isExpand: false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '160',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                },
            }
        }
    },
    created() {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        var nowDate = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day);
        var lastDate = new Date(date - 1000 * 60 * 60 * 24 * 10);//最后30天可以更改，意义：是获取多少天前的时间
        var lastY = lastDate.getFullYear();
        var lastM = lastDate.getMonth() + 1;
        var lastD = lastDate.getDate();
        var LDate = lastY + "-" + (lastM < 10 ? "0" + lastM : lastM) + "-" + (lastD < 10 ? "0" + lastD : lastD)
        this.getObjects.beginTime = LDate
        this.getObjects.endTime = nowDate
        this.Time10 = [LDate, nowDate]
    },
    methods: {
        getCustominfo() {
            this.$api.get(this.API.cpus + 'consumerclientinfo/getClientInfo?productId=6', null, res => {
                if (res.data.isDateState == 1) {
                    this.tableDataObjs1.tableLabel.push(
                        { prop: "chargeSuccessNum", showName: '成功计费数', fixed: false },
                        { prop: "chargeFailNum", showName: '失败计费数', fixed: false },
                        { prop: "chargeWaitNum", showName: '待返回计费数', fixed: false },

                        {
                            prop: "successRate", showName: '成功率',
                            formatData: function (val) { return val ? val + ' %' : val; },
                            fixed: false,
                            showColorTag: {
                                color: "red"
                            }
                        },
                        { prop: "waitRate", showName: '待返回率', fixed: false, formatData: function (val) { return val ? val + ' %' : val; }, })

                    this.tableDataObj.tableLabel.push({ prop: "successAmount", showName: '成功量', fixed: false },
                        { prop: "successRate", showName: '发送成功率', formatData: function (val) { return val ? val + ' %' : val; }, fixed: false })
                }
            })
        },
        parentFn(payload) {
            this.statistics = payload
        },
        parentFnn(payload) {
            this.statisticss = payload
        },
        getAlldatasMD() {
            Object.assign(this.getObjects1, this.getObjects);
            this.getObjects1.flag = this.getObjects.specificTime;
            this.tableDataObjs1.loading2 = true;
            this.$api.post(this.API.cpus + 'statistics/page', this.getObjects1, res => {
                this.tableDataObjs1.loading2 = false;
                this.tableDataObjs1.tableData = res.data.records;
                this.tableDataObjs1.totalRow = res.data.total;
            })
        },
        getDatas() {
            this.$api.get(this.API.cpus + 'consumerdataoverviewday/businessOverview?productId=6', {}, res => {
                this.billingNumber = res.data.billingNumber;
                this.sendAmounts = res.data.sendAmount;
                this.successNum = res.data.successNum;
                this.successRates = res.data.successRate;
                this.updateTime = res.data.updateTime;
            })
            this.$api.get(this.API.recharge + 'client/balance/6', {}, res => {
                this.restNumSum = res.data.num;
            })
        },
        handleSizeChange1(size) {
            this.getObject.pageSize = size;
        },
        handleCurrentChange1(currentPage) {
            this.getObject.currentPage = currentPage;
        },
        handleSizeChange1s(size) {
            this.getObjects.pageSize = size;
            this.getAlldatasMD();
        },
        handleCurrentChange1s(currentPage) {
            this.getObjects.currentPage = currentPage;
            this.getAlldatasMD();
        },
        //获取统计图表的数据
        getdataPage() {
            Object.assign(this.getObject1, this.getObject);
            this.getObject1.flag = this.getObject.specificTime;
            this.$api.get(this.API.cpus + 'statistics/sendCharts?productId=6', {}, res => {
                //重置统计图表的数据
                this.sendAmount = [];
                this.successAmount = [];
                this.successRate = [];
                this.option.xAxis.data = []; //横坐标

                for (let i = 0; i < res.data.length; i++) {
                    this.option.xAxis.data.push(res.data[i].statisticsTime); //横坐标
                    this.sendAmount.push(res.data[i].sendAmount); //计费数
                    this.successAmount.push(res.data[i].successAmount);//成功
                    this.successRate.push(res.data[i].successRate);//成功率
                }
                //获取今天的列表数据
                this.tableDataObj.tableData = res[1];//列表赋值
                this.getObject.value = ''
                this.setOptionItem();
            })
        },
        //设置统计图的展示数据
        setOptionItem() {
            if (this.value == '1') {
                this.option.series[0].data = this.sendAmount;
                this.option.series[0].name = '计费数';
                if (this.$store.state.isDateState == 1) {
                    // 这里可以添加更多的统计选项
                }
            }
        },
        handelOptionButton: function (val) {
            //任务报告
            if (val.methods == 'missionReport') {
                //发送明细（图表）
                this.basicOption1.data[0].value = val.row.successAmount;
                this.basicOption1.data[1].value = val.row.failNumber;
                this.basicOption1.data[2].value = val.row.waitNumber;
                //发送明细（列表）
                this.tableDataObj1.tableData = [val.row];

                //获取回执失败代码数据
                this.$api.get(this.API.cpus + 'statistics/statusReport?flag=' + this.getObject.specificTime + '&day=' + val.row.createTime + '&productId=6', {}, res => {
                    this.basicOption2.ledate = [];
                    this.basicOption2.data = [];
                    if (res) {
                        this.tableDataObj2.tableData = res.data;
                        for (let i = 0; i < res.data.length; i++) {
                            this.basicOption2.ledate.push(res.data[i].failureCodeNoteName);
                            this.basicOption2.data.push({ name: res.data[i].failureCodeNoteName, value: res.data[i].codeNoteNum });
                        }
                    }
                })
                this.sendMsgDialogVisible = true
            }
        },
        //时间范围选择
        handledatepluginVal: function (val1, val2) {
            if (val1) {
                this.getObject.specificTime = '5'
                this.getObject.beginTime = val1;
                this.getObject.endTime = val2;
            } else {
                this.getObject.specificTime = '1'
                this.getObject.beginTime = '';
                this.getObject.endTime = '';
            }
        },
        handelMonth(val) {
            if (val) {
                this.getObjects.beginMonth = val[0]
                this.getObjects.endMonth = val[1]
            } else {
                this.getObjects.beginMonth = ''
                this.getObjects.endMonth = ''
            }
        },
        //时间范围选择(月，天)
        handledatepluginVals: function (val1, val2) {
            if (val1) {
                this.getObjects.specificTime = '1'
                this.getObjects.beginTime = val1;
                this.getObjects.endTime = val2;
            } else {
                this.getObjects.specificTime = '2'
                this.getObjects.beginTime = '';
                this.getObjects.endTime = '';
            }
        },
        handleChangeTimeOptions: function () {
            this.datePluginValueList.datePluginValue = ''
        },
        handleChangeTimeOptions1: function () {
            this.datePluginValueList1.datePluginValue = [moment().subtract(10, "days").format("YYYY-MM-DD"), moment().format('YYYY-MM-DD')]
        },
        // 导出数据
        download() {
            if (this.tableDataObjs1.tableData.length == 0) {
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            } else {
                this.$File.export(this.API.cpus + 'statistics/exportData', this.getObjects1, '统计总览数据导出.xls');
            }
        },
    },
    mounted() {
        this.getDatas();
        this.getdataPage();
    },
    watch: {
        //监听选择时间的变化
        getObject: {
            handler(val) {
                window.Vue.cancel();
            },
            deep: true,
        },
        //监听 统计天，统计月 选择时间的变化
        getObjects: {
            handler(val) {
                this.getAlldatasMD();
            },
            deep: true,
        },
        //监听是否选择月
        'getObjects.specificTime'(val) {
            if (val == 2) {
                this.getObjects.beginMonth = moment().subtract(5, "Months").format("YYYY-MM")
                this.getObjects.endMonth = moment().format('YYYY-MM')
                this.getObjects.beginTime = '';
                this.getObjects.endTime = '';
            } else {
                this.getObjects.beginMonth = ''
                this.getObjects.endMonth = ''
                this.getObjects.beginTime = this.Time10[0];
                this.getObjects.endTime = this.Time10[1];
            }
        },
        //计费数，成功量的变换
        value: {
            handler(val) {
                this.setOptionItem();
            },
            deep: true,
            immediate: true
        }
    }
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* 语音统计概览页面特有样式 */

/* 概览卡片区域 */
.overview-cards {
    display: flex;
    flex-direction: column;
    gap: 32px;
    margin-bottom: 40px;
    padding: 8px;
}

/* 统计数据网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;

    &.simple {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 今日数据卡片 */
.today-data-card {
    background: linear-gradient(135deg, #fff8e1 0%, #fff3e0 100%);
    border: 1px solid #ffcc02;
    margin-top: 18px;
    padding: 8px;
    .card-content {
        padding: 16px 0;
    }
}

/* 统计项样式 */
.stat-item {
    text-align: center;
    padding: 20px 16px;
    border-radius: 10px;
    background: #fafafa;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;

    &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    &.voice-code-stat {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #90caf9;

        .stat-value {
            color: #1565c0;
        }

        .stat-label {
            color: #0d47a1;
        }

        &:hover {
            background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
            border-color: #64b5f6;
        }
    }

    &.voice-notice-stat {
        background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
        border: 1px solid #ce93d8;

        .stat-value {
            color: #6a1b9a;
        }

        .stat-label {
            color: #4a148c;
        }

        &:hover {
            background: linear-gradient(135deg, #e1bee7 0%, #ce93d8 100%);
            border-color: #ba68c8;
        }
    }

    &.general-stat {
        background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 100%);
        border: 1px solid #cbd5e0;

        .stat-value {
            color: #2d3748;
        }

        .stat-label {
            color: #4a5568;
        }

        &:hover {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
            border-color: #a0aec0;
        }
    }
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #409eff;
    margin-bottom: 12px;
    line-height: 1.2;

    .unit {
        font-size: 16px;
        font-weight: 500;
        margin-left: 4px;
        opacity: 0.8;
    }
}

.stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 600;
    line-height: 1.4;
    letter-spacing: 0.5px;
}

/* 余额卡片 */
.balance-card {
    background: linear-gradient(135deg, #f8fffe 0%, #f0f9ff 100%);
    border: 1px solid #e1f5fe;
    padding: 8px;
    .card-content {
        padding: 16px 0;
    }
}

.balance-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
}

.balance-item {
    text-align: center;
}

.balance-label {
    font-size: 14px;
    color: #546e7a;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;

    .vertical-line {
        width: 3px;
        height: 16px;
        background: #0288d1;
        margin-right: 8px;
        border-radius: 2px;
    }
}

.balance-products,
.balance-amounts {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.balance-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    padding: 12px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &.product-name {
        background: #e3f2fd;
        color: #1565c0;
        border: 1px solid #bbdefb;

        &:hover {
            background: #bbdefb;
            transform: translateY(-1px);
        }
    }

    &.highlight {
        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
        color: #2e7d32;
        font-size: 20px;
        border: 1px solid #a5d6a7;
        font-weight: 700;

        &:hover {
            background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(46, 125, 50, 0.2);
        }
    }
}

/* 区域标题 */
.section-title {
    background: #fff;
    padding: 16px 24px;
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-left: 4px solid #409eff;

    span {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }
}

/* 图表区域 */
.chart-section {
    background: #fff;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 40px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;

    .trend-chart {
        height: 360px;
        width: 100%;
    }
}

// .statistics-section {
//     background: #fff;
//     border-radius: 12px;
//     padding: 32px;
//     box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
//     border: 1px solid #f0f0f0;
//     margin-bottom: 0;
// }
/* 统计数据表格区域 */
.statistics-section {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f5f5f5;
}

.section-title1 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.chart-controls,
.statistics-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.control-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    white-space: nowrap;
}
.time-radio-group {
    /deep/ .el-radio-button__inner {
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        background: #fff;
        color: #606266;

        &:hover {
            color: #409eff;
            border-color: #c6e2ff;
        }
    }

    /deep/ .el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background: #409eff;
        border-color: #409eff;
        color: #fff;
    }
}

.chart-container {
    margin-top: 16px;
}

.date-picker,
.month-picker {
    min-width: 200px;
}

.export-btn {
    background: #67c23a;
    border-color: #67c23a;

    &:hover {
        background: #85ce61;
        border-color: #85ce61;
    }
}
.pagination-wrapper {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f5f5f5;
        display: flex;
        justify-content: flex-end;
    }
/* 语音通知统计组件区域 */
.speech-notice-section {
    margin-bottom: 40px;
    .section-title {
    background: #fff;
    padding: 16px 24px;
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-left: 4px solid #409eff;
    margin-top: 24px;

    span {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }
}
}

// .speech-notice-content {
//     margin-top: 24px;

//     /deep/ .overview-card {
//         margin-bottom: 0;
//     }

//     /deep/ .chart-section {
//         background: #fff;
//         border-radius: 12px;
//         padding: 32px;
//         box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
//         border: 1px solid #f0f0f0;
//         margin-bottom: 32px;
//     }

//     /deep/ .statistics-section {
//         background: #fff;
//         border-radius: 12px;
//         padding: 32px;
//         box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
//         border: 1px solid #f0f0f0;
//         margin-bottom: 0;
//     }

//     /deep/ .section-header {
//         display: flex;
//         justify-content: space-between;
//         align-items: center;
//         margin-bottom: 24px;
//         padding-bottom: 16px;
//         border-bottom: 1px solid #f5f5f5;
//     }

//     /deep/ .section-title {
//         font-size: 18px;
//         font-weight: 600;
//         color: #333;
//         margin: 0;
//     }

//     /deep/ .chart-controls,
//     /deep/ .statistics-controls {
//         display: flex;
//         align-items: center;
//         gap: 16px;
//         flex-wrap: wrap;
//     }

//     /deep/ .control-label {
//         font-size: 14px;
//         color: #666;
//         font-weight: 500;
//         white-space: nowrap;
//     }

//     /deep/ .time-radio-group {
//         .el-radio-button__inner {
//             border-radius: 6px;
//             border: 1px solid #dcdfe6;
//             background: #fff;
//             color: #606266;

//             &:hover {
//                 color: #409eff;
//                 border-color: #c6e2ff;
//             }
//         }

//         .el-radio-button__orig-radio:checked+.el-radio-button__inner {
//             background: #409eff;
//             border-color: #409eff;
//             color: #fff;
//         }
//     }

//     /deep/ .chart-container {
//         margin-top: 16px;
//     }

//     /deep/ .trend-chart {
//         height: 360px;
//         width: 100%;
//     }

//     /deep/ .date-picker,
//     /deep/ .month-picker {
//         min-width: 200px;
//     }

//     /deep/ .export-btn {
//         background: #67c23a;
//         border-color: #67c23a;

//         &:hover {
//             background: #85ce61;
//             border-color: #85ce61;
//         }
//     }

//     /deep/ .table-container {
//         margin-top: 16px;
//     }

//     /deep/ .pagination-wrapper {
//         margin-top: 16px;
//         padding-top: 16px;
//         border-top: 1px solid #f5f5f5;
//         display: flex;
//         justify-content: flex-end;
//     }

//     /deep/ .table-pagination {
//         .el-pagination__total {
//             color: #666;
//         }
//     }
// }

/* 弹窗样式 */
.report-dialog {
    /deep/ .el-dialog {
        border-radius: 12px;
    }

    /deep/ .el-dialog__header {
        background: #f8f9fa;
        border-radius: 12px 12px 0 0;
        padding: 20px 24px;
        border-bottom: 1px solid #f0f0f0;
    }

    /deep/ .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    /deep/ .el-dialog__body {
        padding: 24px;
    }
}

.dialog-charts {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;

    .chart-item {
        flex: 1;
        text-align: center;
        padding: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        background: #fafafa;
    }
}

.dialog-chart-single {
    text-align: center;
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    background: #fafafa;
}

.dialog-section {
    margin-bottom: 24px;

    &:last-child {
        margin-bottom: 0;
    }
}

.dialog-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
    }

    .balance-info {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .chart-controls,
    .statistics-controls {
        width: 100%;
        justify-content: flex-start;
    }

    .speech-notice-content {
        /deep/ .section-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
        }

        /deep/ .chart-controls,
        /deep/ .statistics-controls {
            width: 100%;
            justify-content: flex-start;
        }
    }

    .dialog-charts {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .speech-overview-container {
        padding: 16px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .overview-cards {
        gap: 24px;
    }

    .overview-card,
    .chart-section,
    .statistics-section {
        padding: 20px;
    }

    .balance-info {
        gap: 24px;
    }

    .stat-value {
        font-size: 28px;
    }

    .balance-value {
        font-size: 16px;

        &.highlight {
            font-size: 18px;
        }
    }

    .trend-chart {
        height: 280px !important;
    }

    .speech-notice-content {

        /deep/ .chart-section,
        /deep/ .statistics-section {
            padding: 16px;
        }

        /deep/ .trend-chart {
            height: 280px !important;
        }
    }
}
</style>
