<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 统计信息区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <div class="page-info">
                  <i class="el-icon-document"></i>
                  <span class="page-title">登录日志</span>
                </div>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ totalSize }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">第 {{ formInlines.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form 
                ref="formInline"
                :model="formInline"
                class="advanced-search-form" 
                inline 
                size="small">
                <div class="search-row">
                  <el-form-item label="登录IP" prop="ip" class="search-item">
                    <el-input
                      v-model="formInline.ip"
                      placeholder="请输入登录IP"
                      class="search-input"
                      clearable>
                    </el-input>
                  </el-form-item>
                  
                  <el-form-item label="登录状态" prop="isSucced" class="search-item">
                    <el-select
                      v-model="formInline.isSucced"
                      placeholder="请选择状态"
                      class="search-select"
                      clearable>
                      <el-option label="全部" value=""></el-option>
                      <el-option label="登录成功" value="1"></el-option>
                      <el-option label="登录失败" value="2"></el-option>
                    </el-select>
                  </el-form-item>
                  
                  <el-form-item label="操作时间" prop="time" class="search-item">
                    <el-date-picker
                      v-model="formInline.time"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="至"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 240px;">
                    </el-date-picker>
                  </el-form-item>
                  
                  <el-form-item class="search-buttons">
                    <el-button 
                      @click="ListSearch()" 
                      class="search-btn primary" 
                      icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button 
                      @click="Reset('formInline')" 
                      class="search-btn">
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 登录日志列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">登录日志列表</h3>
            <span class="table-count">共 {{ totalSize }} 条记录</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)"
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无登录日志数据">
              
              <el-table-column label="登录手机号" min-width="150">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <div v-if="scope.row.loginMobile">
                      <span v-if="pIndex == scope.$index" class="phone-number">
                        {{ scope.row.loginMobile }}
                      </span>
                      <span
                        v-else
                        class="masked-phone"
                        @click="phoneClickTable(scope.$index, scope.row)">
                        {{ scope.row.maskMobile }}
                        <i class="el-icon-view"></i>
                      </span>
                    </div>
                    <div v-else class="no-data">-</div>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="日志名称" min-width="120">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span>{{ scope.row.logName || '-' }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="登录IP" min-width="140">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <i class="el-icon-location-outline"></i>
                    <span class="ip-address">{{ scope.row.ip || '-' }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="IP区域" min-width="160">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="ip-area">{{ scope.row.ipArea || '-' }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="登录状态" min-width="100" align="center">
                <template slot-scope="scope">
                  <el-tag 
                    :type="scope.row.isSucced == 1 ? 'success' : 'danger'" 
                    size="small">
                    {{ scope.row.isSucced == 1 ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column label="操作时间" min-width="160">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <i class="el-icon-time"></i>
                    <span>{{ scope.row.createTime || '-' }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="详情描述" min-width="300">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="description-text">
                      {{ scope.row.logName }}:{{ scope.row.remark }}{{ scope.row.isSucced == 1 ? '成功' : '失败' }}
                    </span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页 -->
          <div class="pagination-section">
            <el-pagination
              class="simple-pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalSize">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
export default {
  name: "LoginLog",
  components: {},
  data() {
    return {
      pIndex: -1,
      formInline: {
        userName: "",
        ip: "",
        isSucced: "",
        begTime: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
        endTime: moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        time: [
          moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
          moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      formInlines: {
        userName: "",
        ip: "",
        isSucced: "",
        begTime: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
        endTime: moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        time: [
          moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
          moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      formAdd: {
        name: "",
        state: "",
        menu: "",
        description: "",
      },
      datePluginValueList: {
        type: "date",
        start: "",
        end: "",
        range: "-",
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
      totalSize: 0, //总条数
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
    };
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.pIndex = -1;
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "consumerclient/loginLog/page",
        this.formInlines,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.loginMobile;
          });
          this.totalSize = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    phoneClickTable(index, row) {
      this.pIndex = index;
      this.$api.post(
        this.API.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          cipherMobile: row.cipherMobile,
          smsInfoId: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].loginMobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.begTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.begTime = "";
        this.formInline.endTime = "";
      }
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      this.formInline.begTime = moment()
        .startOf("day")
        .format("YYYY-MM-DD 00:00:00");
      this.formInline.endTime = moment(Date.now()).format(
        "YYYY-MM-DD 23:59:59"
      );
      Object.assign(this.formInlines, this.formInline);
    },
    //分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
    },
  },
  activated() {
    this.InquireList();
  },
  watch: {
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList();
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/styles/template-common.less';

// 组件特有的样式定制
.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  i {
    font-size: 20px;
    color: #409eff;
  }
}

.content-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .phone-number {
    color: #606266;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 500;
  }
  
  .masked-phone {
    cursor: pointer;
    color: #409eff;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    
    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
    
    i {
      font-size: 14px;
    }
  }
  
  .ip-address {
    color: #606266;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
  
  .ip-area {
    color: #666;
  }
  
  .description-text {
    color: #666;
    line-height: 1.4;
    word-break: break-word;
  }
  
  .no-data {
    color: #c0c4cc;
    font-style: italic;
  }
  
  i {
    color: #909399;
    margin-right: 4px;
  }
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
    gap: 16px;
    
    .action-buttons {
      justify-content: center;
    }
    
    .stats-info {
      justify-content: center;
    }
  }
  
  .search-section {
    .search-row {
      flex-direction: column;
      gap: 12px;
      
      .search-item {
        width: 100%;
      }
      
      .search-buttons {
        width: 100%;
        margin-left: 0;
        
        .search-btn {
          flex: 1;
        }
      }
    }
  }
  
  .page-info {
    justify-content: center;
    
    .page-title {
      font-size: 16px;
    }
  }
}
</style>