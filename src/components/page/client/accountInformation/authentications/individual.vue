<template>
  <div class="simple-signature-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div>
          <h1 class="page-title">{{ roleId == '22' ? '个体工商户认证' : '个体工商户实名认证' }}</h1>
          <p class="page-subtitle">请按照流程完成认证信息填写</p>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 步骤条 -->
        <div class="notice-section">
          <el-steps :active="active" finish-status="success" align-center>
            <el-step title="企业认证"></el-step>
            <el-step title="个体工商户认证"></el-step>
            <el-step v-if="roleId == '22'" title="签署合同"></el-step>
            <el-step title="完成"></el-step>
          </el-steps>
        </div>

        <!-- 企业认证步骤 -->
        <div v-if="active == 1" class="table-section">
          <div class="table-header">
            <h3 class="table-title">
              <i class="el-icon-office-building"></i>
              企业实名信息
            </h3>
          </div>
          
          <div class="auth-content">
            <div class="upload-section">
              <div class="upload-item">
                <div class="upload-area" @click.capture="fileType = 'fd_L'">
                  <el-upload
                    :action="action"
                    :headers="token"
                    :limit="1"
                    list-type="picture-card"
                    :before-upload="beforeAvatarUpload"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :on-success="handlewqsSuccess"
                    :file-list="PhotoLege">
                    <i class="el-icon-plus"></i>
                  </el-upload>
                </div>
                <div class="upload-title">营业执照照片 <span class="required">*</span></div>
                <div class="upload-tips">
                  <div class="tips-title">提示：</div>
                  <div>1. 证件照应清晰可见容易识别，且边框完整</div>
                  <div>2. 必须真实拍摄，不能使用复印件</div>
                  <div>3. 大小不超过5M，仅支持.jpg格式</div>
                </div>
              </div>
            </div>

            <div class="form-section">
              <div class="form-title">请上传营业执照照片，系统将自动读取，您可更正读取内容</div>
              
              <el-form
                v-loading="loading2"
                :rules="formRule"
                ref="formRefs"
                :model="formInline"
                class="auth-form"
                label-width="165px">
                
                <el-form-item label="企业名称" prop="compName">
                  <el-input
                    v-model="formInline.compName"
                    placeholder="请输入企业名称">
                  </el-input>
                </el-form-item>
                
                <el-form-item label="企业资质编号类型" prop="qualificationType">
                  <el-select
                    v-model="formInline.qualificationType"
                    placeholder="请选择"
                    style="width: 100%;">
                    <el-option
                      label="企业营业执照统一社会信用代码"
                      value="1">
                    </el-option>
                  </el-select>
                </el-form-item>
                
                <el-form-item label="企业资质编号" prop="number">
                  <el-input
                    v-model="formInline.number"
                    placeholder="请输入企业资质编号">
                  </el-input>
                </el-form-item>
                
                <el-form-item label="企业法人姓名" prop="corporateName">
                  <el-input
                    v-model="formInline.corporateName"
                    placeholder="请输入企业法人姓名">
                  </el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 个体工商户认证步骤 -->
        <div v-else-if="active == 2" class="table-section">
          <div class="table-header">
            <h3 class="table-title">
              <i class="el-icon-user"></i>
              个体工商户实名信息
            </h3>
          </div>
          
          <div class="auth-content">
            <div class="upload-section">
              <div class="upload-grid">
                <div class="upload-item">
                  <div class="upload-label">身份证正面 <span class="required">*</span></div>
                  <div class="upload-area" @click.capture="fileType = 'fd_0'">
                    <el-upload
                      :action="action"
                      :headers="token"
                      :limit="1"
                      list-type="picture-card"
                      :before-upload="beforeAvatarUpload"
                      :on-preview="handlePictureCardPreview"
                      :on-remove="handleRemove"
                      :on-success="handlewqsSuccess"
                      :file-list="PhotoFront">
                      <i class="el-icon-plus"></i>
                    </el-upload>
                  </div>
                  <div class="upload-tips">
                    <div>证件照应清晰可见容易识别，且边框完整</div>
                    <div>必须真实拍摄，不能使用复印件</div>
                  </div>
                </div>

                <div class="upload-item">
                  <div class="upload-label">身份证反面 <span class="required">*</span></div>
                  <div class="upload-area" @click.capture="fileType = 'fd_1'">
                    <el-upload
                      :action="action"
                      :headers="token"
                      :limit="1"
                      list-type="picture-card"
                      :before-upload="beforeAvatarUpload"
                      :on-preview="handlePictureCardPreview"
                      :on-remove="handleRemove"
                      :on-success="handlewqsSuccess"
                      :file-list="PhotoReverse">
                      <i class="el-icon-plus"></i>
                    </el-upload>
                  </div>
                </div>

                <div class="upload-item">
                  <div class="upload-label">办公照片 <span class="required">*</span></div>
                  <div class="upload-area" @click.capture="fileType = 'fd_R'">
                    <el-upload
                      :action="action"
                      :headers="token"
                      :limit="1"
                      list-type="picture-card"
                      :before-upload="beforeAvatarUpload"
                      :on-preview="handlePictureCardPreview"
                      :on-remove="handleRemove"
                      :on-success="handlewqsSuccess"
                      :file-list="officeFile">
                      <i class="el-icon-plus"></i>
                    </el-upload>
                  </div>
                  <div class="upload-tips">
                    <div>办公照片大小不超过5M</div>
                    <div>仅支持.jpg格式</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-section">
              <el-form
                v-loading="loading2"
                :rules="formRule"
                ref="formRefs"
                :model="formInline"
                class="auth-form"
                label-width="120px">
                
                <el-form-item label="姓名" prop="name">
                  <el-input
                    v-model="formInline.name"
                    placeholder="请输入姓名">
                  </el-input>
                </el-form-item>
                
                <el-form-item label="身份证号码" prop="idCard">
                  <el-input
                    v-model="formInline.idCard"
                    placeholder="请输入身份证号码">
                  </el-input>
                </el-form-item>
                
                <el-form-item label="手机号" prop="phone">
                  <el-input
                    v-model="formInline.phone"
                    placeholder="请输入手机号">
                  </el-input>
                </el-form-item>
                
                <el-form-item label="验证码" prop="captcha">
                  <div class="verify-input">
                    <el-input
                      v-model="formInline.captcha"
                      placeholder="请输入验证码">
                    </el-input>
                    <el-button
                      v-if="nmb == 60"
                      type="primary"
                      @click="sendCode(formInline.phone)"
                      class="verify-btn">
                      获取验证码
                    </el-button>
                    <el-button
                      v-else
                      disabled
                      type="primary"
                      class="verify-btn">
                      重新获取{{ nmb }}
                    </el-button>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 签署合同步骤 -->
        <div v-else-if="active == 3" class="table-section">
          <div class="table-header">
            <h3 class="table-title">
              <i class="el-icon-document"></i>
              签署合同
            </h3>
          </div>
          
          <div class="contract-section">
            <Word />
            <div class="contract-confirm">
              <el-checkbox v-model="checked">我已阅读并同意</el-checkbox>
            </div>
          </div>
        </div>

        <!-- 完成步骤 -->
        <div v-else class="table-section">
          <div class="completion-section">
            <div class="completion-icon">
              <img class="success-img" src="../../../../../assets/images/RZXX.png" alt="认证成功">
            </div>
            <div class="completion-text">
              认证信息已提交
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <div class="action-buttons">
            <el-button
              v-if="active == 3"
              @click="nexts"
              class="action-btn">
              <i class="el-icon-arrow-left"></i>
              上一步
            </el-button>
            
            <el-button
              v-if="active == 2"
              @click="nexts"
              class="action-btn">
              <i class="el-icon-arrow-left"></i>
              上一步
            </el-button>
          </div>

          <div class="action-buttons">
            <el-button
              v-if="active == 1"
              type="primary"
              @click="next"
              class="action-btn primary">
              下一步
              <i class="el-icon-arrow-right"></i>
            </el-button>
            
            <el-button
              v-if="active == 2"
              type="primary"
              @click="nextTow"
              class="action-btn primary">
              {{ roleId == '22' ? '下一步' : '提交' }}
              <i class="el-icon-arrow-right"></i>
            </el-button>
            
            <el-button
              v-if="active == 3"
              type="primary"
              @click="send"
              class="action-btn primary">
              <i class="el-icon-check"></i>
              提交
            </el-button>
            
            <el-button
              v-if="active == 4"
              type="primary"
              @click="success"
              class="action-btn primary">
              <i class="el-icon-check"></i>
              完成
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="dialogVisible" class="image-preview-dialog">
      <img width="100%" :src="dialogImageUrl" alt="预览图片">
    </el-dialog>
  </div>
</template>

<script>
import Word from './word'
export default {
    components: { Word },
    name:"Individual",
    data() {
        var phone = (rule, value, callback) => {
            if (value) {
                if (!/^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(value)) {
                    return callback(new Error("请输入正确的手机号"));
                } else {
                    callback();
                }
            } else {
                return callback(new Error("请输入个人手机号"));
            }
        };
        var verify = (rule, value, callback) => {
            if (value) {
                callback();
            } else {
                return callback(new Error(rule.text || "请输入个人手机号"));
            }
        };
        return {
            active: 1,
            roleId: "",
            loading2: false,
            checked: false,
            dialogImageUrl: '',
            action: this.API.cpus + "v3/file/upload",
            fileType: "",
            PhotoFront: [],
            PhotoReverse: [],
            PhotoLege: [],
            authorizations: [],
            officeFile: [],
            dialogVisible: false,
            nmb: 60,
            formInline: {
                authorization: "",//委托授权书
                businessLicense: "", //营业执照
                officePhoto: "", //办公照片
                compName: "", //公司名称
                corporateIdCard: "", //企业法人身份证号
                corporateName: "", //企业法人姓名或个体工商户
                idCard: "", //身份证
                idCardBack: "", //身份证反面照
                idCardFront: "", //身份证正面照
                name: "", //个人姓名
                number: "", //证件编号
                phone: "", //个人手机号
                qualificationType: "1", //证件类型
                type: '',
                // serialNo: "",//流水号
                captcha: ""//验证码

            },
            formRule: {
                businessLicense: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传营业执照',
                        trigger: 'change'
                    },
                ],
                authorization: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传委托授权书',
                        trigger: 'change'
                    },
                ],
                idCardBack: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传身份证反面',
                        trigger: 'change'
                    },
                ],
                idCardFront: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传身份证正面',
                        trigger: 'change'
                    },
                ],
                officePhoto: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传办公照片',
                        trigger: 'blur'
                    },
                ],
                compName: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入公司名',
                        trigger: 'blur'
                    },
                ],
                corporateIdCard: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入企业法人身份证号',
                        trigger: 'blur'
                    },
                ],
                corporateName: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入企业法人姓名或个体工商户',
                        trigger: 'blur'
                    },
                ],
                idCard: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入身份证号码',
                        trigger: 'blur'
                    },
                ],
                name: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入个人姓名',
                        trigger: 'blur'
                    },
                ],
                number: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入证件编号',
                        trigger: 'blur'
                    },
                ],
                phone: [
                    {
                        required: true,
                        validator: phone,
                        trigger: 'blur'
                    },
                ],
                captcha: [
                    {
                        required: true,
                        validator: verify,
                        text: '验证码不能为空',
                        trigger: 'blur'
                    },
                ],
                qualificationType: [
                    {
                        required: true,
                        validator: verify,
                        text: '请选择证件类型',
                        trigger: 'change'
                    },
                ],
            }
            // success:"success",
            // error :"error "

        };
    },
    methods: {
        next() {
            // 首先验证营业执照照片是否已上传
            if (!this.formInline.businessLicense) {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: "请上传营业执照照片"
                });
                return;
            }
            
            this.$refs["formRefs"].validate((valid, value) => {
                if (valid) {
                    this.active++
                }
            })
        },
        nextTow() {
            // 验证所有必需的照片
            if (!this.formInline.idCardFront) {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: "请上传身份证正面照片"
                });
                return;
            }
            if (!this.formInline.idCardBack) {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: "请上传身份证反面照片"
                });
                return;
            }
            if (!this.formInline.officePhoto) {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: "请上传办公照片"
                });
                return;
            }
            
            let data = {
                captcha: this.formInline.captcha
            }
            this.$api.post(this.API.cpus + 'consumerCertificate', data, res => {
                if (res.code == 200) {
                    this.$message({
                        type: "success",
                        duration: "2000",
                        message: "提交成功!",
                    });
                    if (this.roleId == '22') {
                        this.active++
                    } else {
                        this.active = 4
                    }
                } else {
                    this.$message({
                        type: "error",
                        duration: "2000",
                        message: res.msg
                    });
                }
            })
        },
        nexts() {
            this.active--
        },
        success() {
            this.$router.push(
                { path: '/authentication' }
            )
        },
         beforeAvatarUpload(file) {
            // const istype = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg'
            const siJPGGIF = file.name.split(".")[1]
            const isLt5M = file.size / 1024 / 1024 < 5; //单位MB
            // const isSize = await new Promise(function (resolve, reject) {
            //     let width = 980
            //     let height = 1180
            //     let _URL = window.URL || window.webkitURL
            //     let image = new Image()

            //     image.onload = function () {
            //         const valid =
            //             image.width <= width &&
            //             image.height <= height &&
            //             image.width / image.height == 1
            //         valid ? resolve() : reject()
            //     }
            //     image.src = _URL.createObjectURL(file)
            // }).then(
            //     () => {
            //         return file
            //     },
            //     () => {
            //         this.$message.error(
            //             '最大分辨率不超过980*1180'
            //         )
            //         return Promise.reject(false)
            //     }
            // )
            if (siJPGGIF !== "jpg") {
                this.$message.error('上传图片只能是 jpg格式!')
                return false
            }
            if (!isLt5M) {
                this.$message.error(
                    '上传图片大小不能超过 10MB!'
                )
                return false
            }
            // return istype && isLt5M
        },
        handlewqsSuccess(res, file, fileList) {
            let _this = this;
            if (res.code == 200) {
                if (_this.fileType == "fd_0") {
                    // _this.PhotoFront = fileList;
                    // _this.PhotoFront[0].url = this.API.imgU + res.data.fullpath
                    this.loading2 = true
                    this.formInline.idCardFront = res.data.fullpath
                    this.$api.get(this.API.cpus + 'consumerCertificate/idCardInfo', { filePath: res.data.fullpath }, ress => {
                        if (ress.code == 200) {
                            this.formInline.name = ress.data.personName
                            this.formInline.idCard = ress.data.personIdCard
                            this.loading2 = false
                        } else {
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
                            this.loading2 = false
                        }
                    })
                } else if (_this.fileType == "fd_1") {
                    // _this.PhotoReverse = fileList;
                    // _this.PhotoReverse[0].url = this.API.imgU + res.data.fullpath
                    this.formInline.idCardBack = res.data.fullpath
                } else if (_this.fileType == "fd_L") {
                    // _this.PhotoLege = fileList;
                    this.loading2 = true
                    // _this.PhotoLege[0].url = this.API.imgU + res.data.fullpath
                    this.formInline.businessLicense = res.data.fullpath
                    this.$api.get(this.API.cpus + 'consumerCertificate/entLicenseInfo', { filePath: res.data.fullpath }, ress => {
                        if (ress.code == 200) {
                            this.formInline.corporateName = ress.data.corporateName
                            this.formInline.compName = ress.data.entName
                            this.formInline.number = ress.data.entQualificationNum
                            // this.formInline.qualificationType = ress.data.entQualificationType+''
                            this.loading2 = false
                        } else {
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
                            this.loading2 = false
                        }
                    })
                } else if (_this.fileType == "fd_S") {
                    // _this.authorizations = fileList;
                    // _this.authorizations[0].url = this.API.imgU + res.data.fullpath
                    this.formInline.authorization = res.data.fullpath
                } else if (_this.fileType == "fd_R") {
                    // _this.officeFile = fileList;
                    // _this.officeFile[0].url = this.API.imgU + res.data.fullpath
                    this.formInline.officePhoto = res.data.fullpath
                }
            } else {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: res.msg
                });
                this.loading2 = false
            }

        },
        handleRemove(file, fileList) {
            let _this = this;
            if (_this.fileType == "fd_0") {
                _this.PhotoFront = [];
                this.formInline.idCardFront = ''
                this.loading2 = false

            } else if (_this.fileType == "fd_1") {
                _this.PhotoReverse = [];
                this.formInline.idCardBack = ''

            } else if (_this.fileType == "fd_L") {
                _this.PhotoLege = [];
                this.formInline.businessLicense = ''
                this.loading2 = false

            } else if (_this.fileType == "fd_S") {
                _this.authorizations = [];
            } else if (_this.fileType == "fd_R") {
                _this.officeFile = [];
                this.formInline.officePhoto = ''
            }
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
        sendCode(val) {
            if (val && /^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(val)) {
                // let data = {
                //     compName: this.formInline.compName,
                //     corporateIdCard: this.formInline.corporateIdCard,
                //     corporateName: this.formInline.corporateName,
                //     number: this.formInline.number,
                //     phone: this.formInline.phone,
                // }
                this.formInline.corporateIdCard = this.formInline.idCard
                // this.formInline.corporateName = this.formInline.name
                this.$api.post(this.API.cpus + 'consumerCertificate/verify', this.formInline, res => {
                    this.$message({
                        type: "success",
                        duration: "2000",
                        message: res.msg
                    });
                    // this.formInline.serialNo = res.data.serialNo
                    if (res.code == 200) {
                        --this.nmb;
                        const timer = setInterval((res) => {
                            --this.nmb;
                            if (this.nmb < 1) {
                                this.nmb = 60;
                                clearInterval(timer);
                            }
                        }, 1000);
                    } else {
                        this.$message({
                            type: "error",
                            duration: "2000",
                            message: res.msg
                        });
                    }
                })
            } else {
                this.$message({
                    message: "请输入正确用手机号",
                    type: "warning",
                });
            }
        },
        send() {
            //   console.log(111);
            if (this.checked) {
                this.$api.post(this.API.cpus + 'consumerCertificate/signContract', {}, res => {
                    if (res.code == 200) {
                        this.$message({
                            type: "success",
                            duration: "2000",
                            message: res.msg,
                        });
                        this.active = 4
                    } else {
                        this.$message({
                            type: "error",
                            duration: "2000",
                            message: res.msg
                        });
                    }
                })
            } else {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: "请勾选我已阅读"
                });
            }

            //    this.$refs["formRefs"].validate((valid, value) => {
            //     if(valid){

            //     }
            //    })
        }
    },
    created() {
        let data = JSON.parse(localStorage.getItem("userInfo"))
        this.roleId = data.roleId
        this.formInline.type = this.$route.query.type;
        if (this.$route.query.status == '1') {
            this.active = 3
        }
        this.token = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };

    }

}
</script>

<style scoped>
/* 基础页面样式 */
.simple-signature-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 20px 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
}

.page-content {
  margin: 0 auto;
  padding: 20px 24px;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.notice-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.table-section {
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.table-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.table-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  background: white;
  padding: 16px 20px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: 6px;
  font-size: 14px;
  padding: 10px 20px;
  border: 1px solid #d9d9d9;
  background: white;
  color: #333;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  border-color: #409eff;
  color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.action-btn.primary {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  border-color: #409eff;
  color: white;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #85c1ff 100%);
  border-color: #66b1ff;
  color: white;
}

/* 认证内容区域 */
.auth-content {
  display: flex;
  gap: 30px;
  padding: 24px;
}

.upload-section {
  flex: 1;
  min-width: 300px;
}

.upload-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.upload-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-label {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.upload-label .required {
  color: #f56c6c;
  margin-left: 4px;
}

.upload-title {
  margin-top: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.upload-title .required {
  color: #f56c6c;
  margin-left: 4px;
}

.upload-tips {
  margin-top: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  font-size: 12px;
  line-height: 1.6;
  color: #666;
}

.upload-tips .tips-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
}

.upload-tips div {
  margin-bottom: 4px;
}

.upload-tips div:last-child {
  margin-bottom: 0;
}

.form-section {
  flex: 2;
  min-width: 400px;
  border-left: 1px solid #e8e8e8;
  padding-left: 30px;
}

.form-title {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  border-left: 3px solid #409eff;
}

.verify-input {
  display: flex;
  gap: 12px;
}

.verify-input .el-input {
  flex: 1;
}

.verify-btn {
  flex-shrink: 0;
  border-radius: 6px;
  font-size: 13px;
  padding: 10px 16px;
}

.contract-section {
  padding: 24px;
}

.contract-confirm {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: center;
  border: 1px solid #e8e8e8;
}

.completion-section {
  padding: 60px 24px;
  text-align: center;
}

.completion-icon {
  margin-bottom: 24px;
}

.success-img {
  width: 80px;
  height: 80px;
  filter: drop-shadow(0 4px 12px rgba(64, 158, 255, 0.3));
}

.completion-text {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.image-preview-dialog img {
  display: block;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-content {
    flex-direction: column;
    gap: 20px;
    padding: 16px;
  }

  .form-section {
    border-left: none;
    border-top: 1px solid #e8e8e8;
    padding-left: 0;
    padding-top: 20px;
  }

  .upload-grid {
    grid-template-columns: 1fr;
  }

  .success-img {
    width: 60px;
    height: 60px;
  }

  .completion-text {
    font-size: 20px;
  }

  .verify-input {
    flex-direction: column;
  }

  .verify-btn {
    width: 100%;
  }

  .action-section {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }
}

@media (min-width: 768px) {
  .upload-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

/* Element UI 样式覆盖 */
.auth-form .el-form-item {
  margin-bottom: 20px;
}

.auth-form .el-form-item__label {
  color: #333;
  font-weight: 500;
}

.auth-form .el-input__inner,
.auth-form .el-select .el-input__inner {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
}

.auth-form .el-input__inner:focus,
.auth-form .el-select .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.upload-area .el-upload--picture-card {
  width: 120px;
  height: 120px;
  line-height: 120px;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  transition: all 0.3s ease;
}

.upload-area .el-upload--picture-card:hover {
  border-color: #409eff;
  background: #f5f7fa;
}

.upload-area .el-upload-list--picture-card .el-upload-list__item {
  width: 120px;
  height: 120px;
  border-radius: 8px;
}

.contract-confirm .el-checkbox__label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.image-preview-dialog .el-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.image-preview-dialog .el-dialog__body {
  padding: 0;
}

.el-steps .el-step__title {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.el-steps .el-step__title.is-finish {
  color: #409eff;
}

.el-steps .el-step__title.is-process {
  color: #409eff;
  font-weight: 600;
}

.el-steps .el-step__icon {
  border-radius: 50%;
  transition: all 0.3s ease;
}

.el-steps .el-step__icon.is-icon {
  font-size: 16px;
}

.el-steps .el-step__line {
  background: #e8e8e8;
}

.el-steps .el-step__line.is-finish {
  background: #409eff;
}

@media (max-width: 1200px) {
  .auth-form .el-form-item__label {
    width: 140px;
  }
}

@media (max-width: 768px) {
  .upload-area .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }

  .upload-area .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
  }

  .auth-form .el-form-item__label {
    width: 100px;
  }
}
</style>