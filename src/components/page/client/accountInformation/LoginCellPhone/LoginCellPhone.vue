<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button v-if="loginInfo.isAdmin == 1" @click="addphone" class="action-btn primary"
                  icon="el-icon-plus">
                  添加登录手机号
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tableData.length }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">管理员用户数量</span>
              </div>
            </div>

            <!-- 提示信息区域 -->
            <div class="tips-section">
              <div class="tips-container">
                <div class="tip-item">
                  <i class="el-icon-info"></i>
                  <span>默认登录手机号为您注册账号时的手机号</span>
                </div>
                <div class="tip-item">
                  <i class="el-icon-warning"></i>
                  <span>该登录手机号至多可添加50个，至少一个。当只有一个手机号时不允许删除！</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 登录手机号列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">登录手机号列表</h3>
          </div>

          <div class="table-container">
            <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading" element-loading-background="rgba(255, 255, 255, 0.8)"
              ref="multipleTable" border :data="tableDataObj.tableData" class="enhanced-table" stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }" :row-style="{ height: '60px' }"
              empty-text="暂无登录手机号数据">

              <el-table-column prop="consumerName" label="用户名" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span>{{ scope.row.consumerName }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="mobile" label="手机号码" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <div v-if="loginInfo.isAdmin == 1" class="phone-cell">
                      <span v-if="scope.$index == mobileIndex" class="phone-number revealed">
                        {{ scope.row.mobile }}
                      </span>
                      <span v-else class="phone-number masked" @click="handelDecode(scope.row, scope.$index)">
                        {{ scope.row.maskMobile }}
                      </span>
                      <el-tooltip effect="dark" content="管理员" placement="top">
                        <i v-if="scope.row.isAdmin == 1" class="admin-icon el-icon-user"></i>
                      </el-tooltip>
                    </div>
                    <div v-else class="phone-cell">
                      <span class="phone-number">{{ scope.row.maskMobile }}</span>
                      <el-tooltip effect="dark" content="管理员" placement="top">
                        <i v-if="scope.row.isAdmin == 1" class="admin-icon el-icon-user"></i>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作权限" min-width="120">
                <template slot-scope="scope">
                  <el-tag 
                    :type="getPermissionTagType(scope.row.writePermission)" 
                    size="small"
                    effect="dark"
                  >
                    {{ getWritePermissionStatus(scope.row.writePermission) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="150">
                <template slot-scope="scope">
                  <div class="content-cell">
                    {{ scope.row.remark || '-' }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="280" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button v-if="loginInfo.isAdmin == 1" type="text" size="small" @click="editphone(scope.row)">
                    <i class="el-icon-edit"></i>
                    编辑
                  </el-button>
                  <el-button v-if="loginInfo.isAdmin == 1 && scope.row.isAdmin != 1" type="text" size="small"
                    style="color: #f56c6c;" @click="deletephone(scope.row)">
                    <i class="el-icon-delete"></i>
                    删除
                  </el-button>
                  <el-button v-if="loginInfo.isAdmin == 1 && scope.row.isAdmin != 1" type="text" size="small"
                    style="color: #409eff;" @click="transferAdmin(scope.row)">
                    <!-- <i class="el-icon-switch-button"></i> --><i class="el-icon-s-unfold"></i>
                    管理员转让
                  </el-button>
                  <el-button v-if="loginInfo.isAdmin == 1 && !isWritePermissionEnabled(scope.row.writePermission)" type="text" size="small"
                    style="color: #409eff;" @click="operationAuthority(scope.row, 1)">
                    <i class="el-icon-switch-button"></i>
                    开启操作权限
                  </el-button>
                  <el-button v-if="loginInfo.isAdmin == 1 && isWritePermissionEnabled(scope.row.writePermission)" type="text" size="small"
                    style="color: #f56c6c;" @click="operationAuthority(scope.row, 0)">
                    <i class="el-icon-switch-button"></i>
                    关闭操作权限
                  </el-button>
                  <!-- 非管理员用户提示 -->
                  <el-tooltip v-if="loginInfo.isAdmin != 1" effect="dark" content="只有管理员才能操作权限" placement="top">
                    <span class="permission-disabled-text">权限操作</span>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加手机号对话框 -->
    <el-dialog title="添加登录手机号" :visible.sync="LcpDialogVisible" width="800px" :close-on-click-modal="false"
      :before-close="handelClose" class="enhanced-dialog">

      <el-form :inline="true" :model="setphoneFrom.ruleForm2" :rules="setphoneFrom.rules2" ref="ruleForm2"
        class="dialog-form">

        <!-- 验证码部分 -->
        <div v-if="!codeStatus" class="verification-section">
          <div class="verification-tip">
            <i class="el-icon-info"></i>
            <span>验证码将会发送至您的手机号：{{ loginInfo.mobile }}，请注意查收！</span>
          </div>

          <el-form-item label="手机验证码" prop="verifyCode" class="verification-form-item">
            <div class="verification-input-group">
              <el-input v-model="setphoneFrom.ruleForm2.verifyCode" placeholder="请输入6位验证码"
                class="verification-code-input"></el-input>
              <el-button type="primary" :disabled="nmb !== 120" @click="CountdownCode" class="get-code-btn">
                {{ nmb === 120 ? '获取验证码' : `重新获取(${nmb})` }}
              </el-button>
            </div>
          </el-form-item>
        </div>

        <!-- 手机号列表部分 -->
        <div class="phone-list-section">
          <div v-if="setphoneFrom.ruleForm2.list.length" class="phone-list">
            <div v-for="(row, index) in setphoneFrom.ruleForm2.list" :key="index" class="phone-item">
              <div class="phone-item-content">
                <el-form-item label="登录手机号" :rules="setphoneFrom.rules2.phone" :prop="'list.' + index + '.phone'"
                  class="phone-input-item">
                  <el-input v-model="row.phone" placeholder="请输入手机号" class="phone-input"></el-input>
                </el-form-item>

                <el-form-item label="备注" :rules="setphoneFrom.rules2.remark" :prop="'list.' + index + '.remark'"
                  class="remark-input-item">
                  <el-input v-model="row.remark" placeholder="请输入备注信息" class="remark-input"></el-input>
                </el-form-item>
              </div>

              <el-button type="text" icon="el-icon-delete" class="remove-btn"
                @click="setphoneFrom.ruleForm2.list.splice(index, 1)">
                删除
              </el-button>
            </div>

            <div class="add-more-btn" @click="addWhiteListAction">
              <i class="el-icon-plus"></i>
              <span>添加更多手机号</span>
            </div>
          </div>

          <div v-else class="empty-phone-list" @click="addWhiteListAction">
            <i class="el-icon-plus"></i>
            <span>添加手机号</span>
          </div>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="LcpDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm2')">提交</el-button>
      </div>
    </el-dialog>

    <!-- 删除/编辑/转让对话框 -->
    <el-dialog :title="title" :visible.sync="DeletePhone" width="560px" :close-on-click-modal="false"
      :before-close="handelClose" class="enhanced-dialog">

      <el-form :model="delphone" :rules="setphoneFrom.rules2" ref="ruleForm3" class="dialog-form" label-width="120px">

        <!-- 验证码部分（非编辑模式） -->
        <div v-if="title != '编辑'" class="verification-section">
          <div class="verification-tip">
            <i class="el-icon-info"></i>
            <span>验证码将会发送至您的手机号：{{ loginInfo.mobile }}，请注意查收！</span>
          </div>

          <el-form-item label="手机验证码" prop="verifyCode">
            <div class="verification-input-group">
              <el-input v-model="delphone.verifyCode" placeholder="请输入6位验证码" class="verification-code-input"></el-input>
              <el-button type="primary" :disabled="nmb !== 120" @click="CountdownCode" class="get-code-btn">
                {{ nmb === 120 ? '获取验证码' : `重新获取(${nmb})` }}
              </el-button>
            </div>
          </el-form-item>
        </div>

        <!-- 编辑模式 -->
        <div v-else class="edit-section">
          <el-form-item label="手机号" prop="mobile">
            <el-input disabled v-model="delphone.mobile" class="disabled-input"></el-input>
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input v-model="delphone.remark" type="textarea" :rows="3" placeholder="请输入备注信息"
              class="textarea-input"></el-input>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="DeletePhone = false">取消</el-button>
        <el-button type="primary" @click="delSubmitForm('ruleForm3')">
          {{
            title == "删除手机号"
              ? "确认删除"
              : title == "管理员转让"
                ? "确认转让"
                : "确认编辑"
          }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 重置组件 -->
    <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo">
    </ResetNumberVue>
  </div>
</template>

<script>
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import TableTem from "@/components/publicComponents/TableTem";
import bus from "../../../../common/bus";
import common from "../../../../../assets/js/common";
export default {
  name: "LoginCellPhone",
  components: { TableTem, ResetNumberVue },
  data() {
    // 验证IP规则
    var code = (rule, value, callback) => {
      // if (!this.phoneData) {
      //     return callback(new Error("请选中手机号"));
      // } else
      if (value == "") {
        return callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    var phone = (rule, value, callback) => {
      if (!/^([，；,;]*1\d{10}[，；,;]*)*$/.test(value)) {
        return callback(new Error("请输入正确手机号"));
      } else if (value == "") {
        return callback(new Error("请输入手机号"));
      } else {
        callback();
      }
    };
    return {
      nmb: 120,
      timer: null,
      radio: "",
      title: "",
      setPhoneSteps: 1, // 设置手机号的步骤
      loginInfo: {
        isAdmin: null,
        consumerName: "",
        mobile: "",
        remark: "",
        id: "",
      },
      codeStatus: null,
      LcpDialogVisible: false, //弹出框显示隐藏
      DeletePhone: false,
      count: null,
      flag: false,
      // 存储选中手机号
      phoneData: "",
      // 存储原有手机号
      phoneOriginal: [],
      // 存储个人信息
      roleInfo: {},
      delphone: {
        verifyCode: "",
        flag: "2",
        id: "",
        isAdmin: "",
        mobile: "",
        remark: "",
      },
      setphoneFrom: {
        ruleForm1: {
          verCode: "",
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
        },
        ruleForm2: {
          verifyCode: "",
          list: [
            {
              phone: "",
              remark: "",
            },
          ],
          // phone: "",
          // remark: "",
        },
        rules2: {
          verifyCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
          remark: [
            { required: true, message: "请输入备注" },
            {
              min: 2,
              max: 10,
              message: "长度在 2 到 10 个字符",
              trigger: "blur",
            },
          ],
          phone: [
            { required: true, validator: phone, trigger: "blur" },
            // { min: 6, max: 6, message: '请输入6位数字验证码' }
          ],
        },
      },
      tableD: [],
      mobileIndex: null,
      resetVideo: false,
      infoData: {},
      tableDataObj: {
        //列表数据
        // loading2:false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          // {
          //     prop: "Numbering",
          //     showName: "编号",
          //     width: "60",
          //     fixed: false,
          // },
          {
            prop: "consumerName",
            showName: "用户名",
            fixed: false,
          },
          {
            prop: "mobile",
            showName: "手机号码",
            fixed: false,
          },
          {
            prop: "isAdmin",
            showName: "是否管理员",
            fixed: false,
            formatData: function (val) {
              if (val == 1) {
                return (val = "管理员");
              } else {
                return (val = "普通用户");
              }
            },
          },
          {
            prop: "remark",
            showName: "备注",
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //默认打开折叠
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "180", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "删除",
            type: "",
            size: "mini",
            optionMethod: "dele",
            icon: "el-icon-error",
          },
        ],
      },
      adminform: {
        destId: "",
        sourceId: "",
      },
      phoneId: "",
    };
  },
  methods: {
    getLoginInfo() {
      this.$api.get(
        this.API.cpus + "userLoginAdmin/loginPhoneInfo",
        {},
        (res) => {
          if (res.code == 200) {
            this.loginInfo = res.data;
          }
        }
      );
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          // console.log(res, "res");
          // let resdata = [];
          // let tabledata = [];
          // let phonelength = res.data.data;
          // this.phoneData = phonelength[0].mobile;
          // this.phoneOriginal = [];
          // console.log("------------------");
          // console.log(phonelength);
          // for (var i = 0; i < phonelength.length; i++) {
          //     // 列表数据
          //     let a = {};
          //     a.Numbering = i + 1;
          //     a.username = phonelength[i].consumerName;
          //     a.maskMobile = phonelength[i].maskMobile;
          //     a.phone = phonelength[i].mobile;
          //     resdata[resdata.length] = a;
          //     this.phoneOriginal.push(phonelength[i].mobile);
          //     // 登录手机号列表
          //     let b = {};
          //     b.index = i;
          //     b.name = i + 1;
          //     b.address = phonelength[i].mobile;
          //     tabledata[tabledata.length] = b;
          // }
          // this.tableDataObj.tableData = resdata;
          // this.tableD = tabledata;
          this.tableDataObj.tableData = res.data.data;
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.mobile;
          });
          this.tableDataObj.loading2 = false;
          // 存储个人信息
          this.roleInfo = res.data[0];
        }
      );
    },
    // 获取验证码倒计时
    CountdownCode() {
      this.$api.get(
        this.API.cpus +
        "userLoginAdmin/sendVerificationCode?flag=2&phoneId=" +
        this.loginInfo.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.flag = true;
            --this.nmb;
            this.timer = setInterval((res) => {
              --this.nmb;
              if (this.nmb < 1) {
                this.nmb = 120;
                this.flag = false;
                clearInterval(this.timer);
              } else {
                this.flag = true;
              }
            }, 1000);
            this.$message({
              type: "success",
              duration: "2000",
              message: "验证码已发送至手机!",
            });
          } else {
            this.flag = true;
            this.$message({
              type: "warning",
              message: "验证码未失效，需失效后重新获取!",
            });
          }
        }
      );
      // if (this.phoneData) {

      // } else {
      //     this.$message({
      //         message: "请先选中手机号码",
      //         type: "warning",
      //     });
      // }
    },
    handelDecode(val, index) {
      this.mobileIndex = index;
      this.$api.post(
        this.API.upms + "generatekey/decryptMobile",
        {
          keyId: val.keyId,
          smsInfoId: val.smsInfoId,
          cipherMobile: val.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
            // this.$nextTick(() => {
            //     this.$set(this.tableDataObj.tableData[index], "maskMobile", res.data);
            // });
            // console.log(this.tableDataObj.tableData, 'this.tableDataObj.tableData');
          } else if (res.code == 4004002) {
            common.fetchData().then((res) => {
              if (res.code == 200) {
                if (res.data.isAdmin == 1) {
                  this.resetVideo = true;
                  this.infoData = res.data;
                } else {
                  this.$message({
                    message:
                      "您今日解密次数已超限，如需重置解密次数，请联系管理员！",
                    type: "warning",
                  });
                }
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            });
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
          // this.tableDataObj.tableData[index].mobile=res.data
        }
      );
    },
    // detailsRow() {
    //     this.SigDialogVisible = true;
    // },
    // showRow(row) {
    //     //赋值给radio
    //     this.radio = this.tableD.indexOf(row);
    // },
    // getCurrentRow(val) {
    //     // console.log(val,'ll');
    //     // this.count = val
    //     // if(this.count == val){
    //     //     this.flag = true
    //     // }else{
    //     //     this.flag = false
    //     // }
    //     this.phoneData = this.tableD[val].address; //赋值手机号
    // },
    // handelOptionButton: function (val) {
    //     if (val.methods == "details") {
    //         this.detailsRow();
    //     }
    // },
    //登录手机号弹出层
    delFun(obj) {
      this.$confirms.confirmation(
        "post",
        "确认删除该手机号",
        this.API.cpus + "userLoginAdmin/deleteLoginPhoneV2",
        obj,
        (res) => {
          if (res.code == 200) {
            this.DeletePhone = false; //关闭弹出框
            this.nmb = 120;
            clearInterval(this.timer);
            this.InquireList();
          }
        }
      );
    },
    getCodeStatus(type, obj) {
      this.$api.get(
        this.API.cpus + "userLoginAdmin/verifiedStatus",
        {},
        (res) => {
          if (res.code == 200) {
            this.codeStatus = res.data;
            if (type == "del") {
              if (res.data === true) {
                let data = {
                  flag: this.delphone.flag,
                  phoneId: obj.id,
                };
                this.delFun(data);
              } else {
                this.delphone.id = obj.id;
                this.delphone.isAdmin = obj.isAdmin;
                this.DeletePhone = true;
              }
            } else if (type == "admin") {
              if (res.data === true) {
                let data = {
                  flag: this.delphone.flag,
                  destId: obj.destId,
                  sourceId: obj.sourceId,
                };
                this.adminFun(data);
              } else {
                this.adminform.destId = obj.destId;
                this.adminform.sourceId = obj.sourceId;
                this.DeletePhone = true;
              }
            }
          }
        }
      );
    },
    addphone() {
      if (this.tableDataObj.tableData.length >= 50) {
        this.$message({
          type: "error",
          duration: "2000",
          message: "用户最多添加50个手机号码",
        });
      } else {
        this.radio = 0;
        this.getCodeStatus();
        this.LcpDialogVisible = true;
      }
    },
    addWhiteListAction() {
      let obj = {
        phone: "",
        remark: "",
      };
      this.setphoneFrom.ruleForm2.list.push(obj);
    },
    // 新增登录手机号
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let data = {
            flag: 2,
            // phone: this.setphoneFrom.ruleForm2.phone,
            verifyCode: this.setphoneFrom.ruleForm2.verifyCode,
            phoneList: this.setphoneFrom.ruleForm2.list,
            // remark: this.setphoneFrom.ruleForm2.remark,
          };
          this.$api.post(
            this.API.cpus + "userLoginAdmin/addLoginPhoneV2",
            data,
            (res) => {
              if (res.code == 200) {
                this.LcpDialogVisible = false; //关闭弹出框
                // this.cancel();
                this.nmb = 120;
                clearInterval(this.timer);
                this.InquireList(); //刷新列表
              } else {
                this.$message({
                  type: "error",
                  duration: "2000",
                  message: res.msg,
                });
              }
            }
          );
        } else {
          return false;
        }
        // if (val == "ruleForm1") {
        //     if (valid) {
        //         this.$api.get(
        //             this.API.cpus +
        //             "code/checkVerificationCode?code=" +
        //             this.setphoneFrom.ruleForm1.verCode +
        //             "&flag=2",
        //             {},
        //             (res) => {
        //                 if (res.code == 200) {
        //                     clearInterval(this.timer);
        //                     this.nmb = 120;
        //                     this.flag = false;
        //                     this.setPhoneSteps = 2;
        //                 } else {
        //                     this.$message({
        //                         type: "error",
        //                         duration: "2000",
        //                         message: "验证码无效！",
        //                     });
        //                 }
        //             }
        //         );
        //     } else {
        //         console.log("error submit!!");
        //         return false;
        //     }
        // } else {
        //     if (valid) {
        //         let flag = true;
        //         for (var i = 0; i < this.phoneOriginal.length; i++) {
        //             if (
        //                 this.phoneOriginal[i] == this.setphoneFrom.ruleForm2.setNewPhone
        //             ) {
        //                 flag = false;
        //                 break;
        //             }
        //         }
        //         if (flag == true) {
        //             this.$confirms.confirmation(
        //                 "get",
        //                 "确认添加该手机号",
        //                 this.API.cpus +
        //                 "consumerclientinfo/addLoginPhone/" +
        //                 this.setphoneFrom.ruleForm2.setNewPhone,
        //                 {},
        //                 (res) => {
        //                     this.cancel();
        //                 }
        //             );
        //         } else {
        //             this.$message({
        //                 type: "error",
        //                 duration: "2000",
        //                 message: "该手机号已存在,不可重复添加",
        //             });
        //         }
        //     } else {
        //         console.log("error submit!!");
        //         return false;
        //     }
        // }
      });
    },
    delSubmitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title == "删除手机号") {
            let data = {
              verifyCode: this.delphone.verifyCode,
              flag: this.delphone.flag,
              phoneId: this.delphone.id,
            };
            this.delFun(data);
          } else if (this.title == "管理员转让") {
            let data = {
              verifyCode: this.delphone.verifyCode,
              destId: this.adminform.destId,
              sourceId: this.adminform.sourceId,
              flag: this.delphone.flag,
            };
            this.adminFun(data);
          } else if (this.title == "编辑") {
            let data = {
              phoneId: this.phoneId,
              remark: this.delphone.remark,
            };
            this.$api.post(
              this.API.cpus + "userLoginAdmin/updateLoginPhone",
              data,
              (res) => {
                if (res.code == 200) {
                  this.DeletePhone = false;
                  this.InquireList(); //刷新列表
                } else {
                  this.$message({
                    type: "error",
                    duration: "2000",
                    message: res.msg,
                  });
                }
              }
            );
          }

          // if (this.delphone.isAdmin == 1) {
          //     this.$message({
          //         type: "error",
          //         duration: "2000",
          //         message: "管理员账号不可删除",
          //     });
          // } else {
          //     this.$confirms.confirmation(
          //         "post",
          //         "确认删除该手机号",
          //         this.API.cpus +
          //         "userLoginAdmin/deleteLoginPhoneV2",
          //         data,
          //         (res) => {
          //             if (res.code == 200) {
          //                 this.DeletePhone = false; //关闭弹出框
          //                 this.nmb = 120;
          //                 clearInterval(this.timer);
          //                 this.InquireList();
          //             }
          //         }
          //     );
          // }
        } else {
          return false;
        }
      });
    },
    // cancel() {
    //     this.LcpDialogVisible = false; //关闭弹出框
    //     this.setPhoneSteps = 1; //步进改为1
    //     this.setphoneFrom.ruleForm1.verCode = ""; //验证码置空
    //     this.setphoneFrom.ruleForm2.setNewPhone = ""; //手机号置空
    //     this.InquireList();
    // },
    handelClose() {
      //×号关闭弹窗
      // 点击关闭
      this.LcpDialogVisible = false; //关闭弹出框
      this.DeletePhone = false;
    },
    /**
     * 表格公共的方法--start
     * **/
    // handelOptionButton(val) {
    //     //操作列表的点击
    //     if (val.methods == "dele") {
    //         //点击删除
    //         if (this.tableDataObj.tableData.length <= 1) {
    //             this.$message({
    //                 type: "error",
    //                 duration: "2000",
    //                 message: "最少存在一个手机号",
    //             });
    //         } else {
    //             this.$confirms.confirmation(
    //                 "get",
    //                 "确认删除该手机号",
    //                 this.API.cpus +
    //                 "consumerclientinfo/deleteLoginPhone/" +
    //                 val.row.phone,
    //                 {},
    //                 (res) => {
    //                     this.InquireList();
    //                 }
    //             );
    //         }
    //     }
    // },
    handleSizeChange(size) {
      this.pagesize = size;
    },
    handleCurrentChange: function (currentPage) {
      this.currentPage = currentPage;
    },
    editphone(row) {
      this.title = "编辑";
      this.phoneId = row.id;
      this.delphone.mobile = row.mobile;
      this.delphone.remark = row.remark || "";
      this.DeletePhone = true;
    },
    deletephone(row) {
      this.title = "删除手机号";
      if (row.isAdmin == 1) {
        this.$message({
          type: "error",
          duration: "2000",
          message: "管理员账号不可删除",
        });
      } else {
        let data = {
          id: row.id,
          isAdmin: row.isAdmin,
        };
        this.getCodeStatus("del", data);
      }
    },
    adminFun(data) {
      this.$confirms.confirmation(
        "post",
        "是否将该账号设置为管理员",
        this.API.cpus + "userLoginAdmin/transferAdmin",
        data,
        (res) => {
          if (res.code == 200) {
            this.DeletePhone = false;
            this.getLoginInfo();
            this.InquireList();
          }
        }
      );
    },
    transferAdmin(row) {
      let data = {
        destId: row.id,
        sourceId: "",
      };
      for (let i = 0; i < this.tableDataObj.tableData.length; i++) {
        if (this.tableDataObj.tableData[i].isAdmin == 1) {
          data.sourceId = this.tableDataObj.tableData[i].id;
          break;
        }
      }
      this.title = "管理员转让";
      this.getCodeStatus("admin", data);
    },
    /**
     * 判断写入权限是否开启
     * @param {number|null} writePermission - 权限值
     * @returns {boolean} 是否开启权限
     * 
     * 权限状态说明：
     * - null/undefined: 默认开启状态
     * - 1: 明确开启状态  
     * - 0: 明确关闭状态
     */
    isWritePermissionEnabled(writePermission) {
      // 使用 == 进行比较，兼容 null 和 undefined
      if (writePermission == null) {
        return true; // 默认开启
      }
      return writePermission == 1;
    },
    // 获取权限状态描述
    getWritePermissionStatus(writePermission) {
      if (writePermission == null) {
        return '已开启';
      }
      return this.isWritePermissionEnabled(writePermission) ? '已开启' : '已关闭';
    },
    // 获取权限标签类型
    getPermissionTagType(writePermission) {
      if (writePermission == null) {
        return 'success'; // 默认开启用警告色
      }
      return this.isWritePermissionEnabled(writePermission) ? 'success' : 'danger';
    },
    // 检查管理员权限
    checkAdminPermission() {
      if (this.loginInfo.isAdmin != 1) {
        this.$message({
          type: "warning",
          message: "只有管理员才能进行权限操作",
          duration: 3000
        });
        return false;
      }
      return true;
    },
    // 开启操作权限
    operationAuthority(row, status) {
      console.log('操作权限:', row, '状态:', status);
      
      // 检查管理员权限
      if (!this.checkAdminPermission()) {
        return;
      }
      
      try {
        let data = {
          phoneId: row.id,
          writePermission: status,
          userName: this.loginInfo.userName,
        };
        
        const actionText = status === 1 ? "开启" : "关闭";
        const currentStatus = this.isWritePermissionEnabled(row.writePermission) ? "已开启" : "已关闭";
        const confirmMessage = `当前权限状态：${currentStatus}\n是否${actionText}该账号的操作权限？`;
        
        this.$confirms.confirmation(
          "post",
          confirmMessage,
          this.API.cpus + "userLoginAdmin/updateLoginPhone",
          data,
          (res) => {
            if (res.code == 200) {
              // 记录权限操作日志
              console.log(`权限操作成功: 用户 ${row.consumerName} (${row.mobile}) 权限从 ${currentStatus} 变更为 ${actionText === '开启' ? '已开启' : '已关闭'}`);
              
              this.$message({
                type: "success",
                message: `操作权限${actionText}成功`,
                duration: 2000
              });
              this.getLoginInfo();
              this.InquireList();
            } else {
              this.$message({
                type: "error",
                message: res.msg || `操作权限${actionText}失败`,
                duration: 3000
              });
            }
          }
        );
      } catch (error) {
        console.error('操作权限失败:', error);
        this.$message({
          type: "error",
          message: error.message || "操作失败，请重试",
          duration: 3000
        });
      }
    },
    /**
     * 表格公共的方法--end
     * **/
  },
  created() {
    bus.$on("closeVideo", (msg) => {
      this.resetVideo = msg;
    });
    this.getLoginInfo();
    this.InquireList();
  },
  watch: {
    DeletePhone: function (val) {
      if (!val) {
        this.$refs.ruleForm3.resetFields();
      }
    },
    LcpDialogVisible: function (val) {
      if (!val) {
        // this.setphoneFrom.ruleForm1.verCode = ""; //验证码置空
        // this.setphoneFrom.ruleForm2.setNewPhone = ""; //手机号置空
        // this.setPhoneSteps = 1; //步进改为1
        // this.radio = "";
        // this.phoneData = this.phoneOriginal[0];
        // this.$refs.ruleForm1.resetFields();
        this.$refs.ruleForm2.resetFields();
        this.setphoneFrom.ruleForm2.list = [
          {
            phone: "",
            remark: "",
          },
        ];
      }
    },
    resetVideo(val) {
      if (!val) {
        this.mobileIndex = null;
      }
    },
  },
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* LoginCellPhone 特有样式 */

/* 提示信息区域样式 */
.tips-section {
  .tips-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;

    .tip-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      color: #6c757d;
      font-size: 14px;
      line-height: 1.5;

      i {
        margin-top: 2px;
        flex-shrink: 0;

        &.el-icon-info {
          color: #17a2b8;
        }

        &.el-icon-warning {
          color: #ffc107;
        }
      }

      span {
        flex: 1;
      }
    }
  }
}

/* 内容单元格样式 */
.content-cell {
  word-break: break-all;
  line-height: 1.4;
}

/* 手机号单元格样式 */
.phone-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.phone-number {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &.masked {
    background: #f8f9fa;
    color: #6c757d;
    cursor: pointer;

    &:hover {
      background: #e9ecef;
      color: #495057;
    }
  }

  &.revealed {
    background: #d4edda;
    color: #155724;
    font-weight: 600;
  }
}

.admin-icon {
  color: #409eff;
  font-size: 16px;
}

/* 对话框增强样式 */
.enhanced-dialog {
  /deep/ .el-dialog {
    border-radius: 8px;

    .el-dialog__header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px 24px;
      border-bottom: 1px solid #ebeef5;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
    }
  }
}

.dialog-form {
  /deep/ .el-form-item {
    margin-bottom: 24px;

    .el-input__inner,
    .el-textarea__inner {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

/* 验证码区域样式 */
.verification-section {
  margin-bottom: 24px;

  .verification-tip {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #e8f4fd;
    border-radius: 8px;
    border-left: 4px solid #409eff;
    margin-bottom: 16px;

    i {
      color: #409eff;
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      color: #2c3e50;
      font-size: 14px;
    }
  }

  .verification-form-item {
    margin-bottom: 0 !important;
  }

  .verification-input-group {
    display: flex;
    gap: 12px;
    align-items: center;

    .verification-code-input {
      width: 250px;
    }

    .get-code-btn {
      min-width: 120px;
      white-space: nowrap;
    }
  }
}

/* 手机号列表区域样式 */
.phone-list-section {
  margin-top: 24px;

  .phone-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .phone-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .phone-item-content {
        flex: 1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .phone-input-item,
        .remark-input-item {
          margin-bottom: 0 !important;
        }

        .phone-input,
        .remark-input {
          width: 100%;
        }
      }

      .remove-btn {
        color: #f56c6c !important;
        font-weight: 500;
        margin-top: 8px;

        &:hover {
          color: #f34f59 !important;
        }
      }
    }

    .add-more-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 16px;
      border: 2px dashed #409eff;
      border-radius: 8px;
      background: #f8f9fa;
      color: #409eff;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;

      &:hover {
        background: #e8f4fd;
        border-color: #337ab7;
        color: #337ab7;
      }

      i {
        font-size: 16px;
      }
    }
  }

  .empty-phone-list {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 40px;
    border: 2px dashed #409eff;
    border-radius: 8px;
    background: #f8f9fa;
    color: #409eff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;

    &:hover {
      background: #e8f4fd;
      border-color: #337ab7;
      color: #337ab7;
    }

    i {
      font-size: 20px;
    }
  }
}

/* 编辑区域样式 */
.edit-section {
  .disabled-input {
    background: #f8f9fa !important;
    color: #6c757d !important;
  }

  .textarea-input {
    min-height: 80px;
  }
}

/* 对话框底部样式 */
.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 12px;
  }
}

/* 权限标签样式优化 */
.el-table {
  .el-tag {
    font-weight: 500;
    border-radius: 4px;
    
    &.el-tag--success {
      background-color: #f0f9ff;
      border-color: #67c23a;
      color: #67c23a;
    }
    
    &.el-tag--danger {
      background-color: #fef0f0;
      border-color: #f56c6c;
      color: #f56c6c;
    }
    
    &.el-tag--warning {
      background-color: #fdf6ec;
      border-color: #e6a23c;
      color: #e6a23c;
    }
  }
}

/* 权限操作相关样式 */
.permission-disabled-text {
  color: #c0c4cc;
  font-size: 12px;
  cursor: not-allowed;
  user-select: none;
}

/* 管理员权限操作按钮样式 */
.el-button[style*="color: #409eff"] {
  &:hover {
    color: #337ab7 !important;
  }
}

.el-button[style*="color: #f56c6c"] {
  &:hover {
    color: #f34f59 !important;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tips-section {
    .tips-container {
      padding: 12px;

      .tip-item {
        font-size: 13px;
      }
    }
  }

  .phone-list-section {
    .phone-list {
      .phone-item {
        .phone-item-content {
          grid-template-columns: 1fr;
          gap: 12px;
        }
      }
    }
  }

  .verification-section {
    .verification-input-group {
      flex-direction: column;
      align-items: stretch;

      .verification-code-input {
        width: 100%;
      }
    }
  }
}

@media (max-width: 480px) {
  .dialog-footer {
    text-align: center;

    .el-button {
      margin: 0 6px;
    }
  }
}
</style>
