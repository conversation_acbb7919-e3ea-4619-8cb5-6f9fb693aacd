<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button 
                  v-permission
                  @click="addContacts" 
                  class="action-btn primary" 
                  icon="el-icon-plus">
                  添加告警联系人
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tableData.length }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">最多可添加 5 个指定联系人</span>
              </div>
            </div>

            <!-- 提示信息区域 -->
            <div class="tips-section">
              <div class="tips-container">
                <div class="tip-item">
                  <i class="el-icon-info"></i>
                  <span>告警联系人将接收系统通知，包括审核通知和余额变更通知</span>
                </div>
                <div class="tip-item">
                  <i class="el-icon-warning"></i>
                  <span class="highlight">最多可添加 5 个指定联系人</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 告警联系人列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">告警联系人列表</h3>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)"
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无告警联系人数据">
              
              <el-table-column prop="linkmanName" label="姓名" min-width="140">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span>{{ scope.row.linkmanName }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="linkmanPhone" label="手机号码" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="phone-number">{{ scope.row.linkmanPhone }}</span>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column prop="auditRemind" label="审核通知" min-width="120" align="center">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.auditRemind == '2' ? 'success' : 'info'" size="small">
                    {{ scope.row.auditRemind == '2' ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column prop="balanceRemind" label="余额变更&不足通知" min-width="180" align="center">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.balanceRemind == '2' ? 'success' : 'info'" size="small">
                    {{ scope.row.balanceRemind == '2' ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column 
                v-if="$store.state.roleId == '14' && $store.state.warningRemind == 2" 
                prop="warningRemind" 
                label="预警通知" 
                min-width="120" 
                align="center">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.warningRemind == '2' ? 'success' : 'info'" size="small">
                    {{ scope.row.warningRemind == '2' ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="180" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click="handleEdit(scope.row)">
                    <i class="el-icon-edit"></i>
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    style="color: #f56c6c;"
                    @click="handleDelete(scope.row)">
                    <i class="el-icon-delete"></i>
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑联系人对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      class="enhanced-dialog">
      
      <el-form
        :model="form"
        ref="addContactForm"
        :rules="formRule"
        class="dialog-form"
        label-width="150px">
        
        <el-form-item label="姓名" prop="linkmanName">
          <el-input
            v-model="form.linkmanName"
            placeholder="请输入姓名"
            autocomplete="off">
          </el-input>
        </el-form-item>
        
                  <el-form-item
            label="手机号码"
            prop="linkmanPhone"
            :rules="filter_rules({
              required: true,
              type: 'mobile',
              message: '手机号为必填项！',
            })">
            <el-input
              v-model="form.linkmanPhone"
              placeholder="请输入手机号码"
              autocomplete="off">
            </el-input>
          </el-form-item>
        
        <el-form-item
          label="接收预警通知"
          prop="warningRemind"
          v-if="$store.state.roleId == '14' && $store.state.warningRemind == 2">
          <el-radio-group v-model="form.warningRemind" class="radio-group">
            <el-radio label="2">是</el-radio>
            <el-radio label="1">否</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="人工审核通知" prop="auditRemind">
          <el-radio-group v-model="form.auditRemind" class="radio-group">
            <el-radio label="2">是</el-radio>
            <el-radio label="1">否</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="余额变更&不足通知" prop="balanceRemind">
          <el-radio-group v-model="form.balanceRemind" class="radio-group">
            <el-radio label="2">是</el-radio>
            <el-radio label="1">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button v-permission type="primary" @click="addContactOk(dialogStatus)">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  name: "NotificationAlert",
  components: {},
  data() {
    return {
      value_leixing: "", //发送类型
      value_phone: "", //手机号选择
      balanceState: false,
      tipsVisible: false,
      numjcStatus: "1", //空号检测是否开通
      companyName: "", //公司名称
      lastTime: "", //上次登录时间
      restNumSms: "", //剩余短信条数
      openedBusiness: "", //已开通业务
      username: "", //用户名
      portraitPath: "",
      portraitUrl: "", // 头像
      userBalance: "", //账户余额
      warnDialogVisible: false, //账户余额弹出层
      iphone: "", //当前绑定手机号
      NumberBalances: "", // 余额条数
      reminderBalances: "", //余额提醒条数
      activeName: "first", //昨日、本周、本月数据
      weekTime: "", //本周数据
      monthTime: "", //本月数据
      keyNum: "", //当前传递的日期关键词
      sendNums: "", //发送数量
      successNums: "", //成功数量
      tableRows: {}, //编辑的储存数据
      dialogStatus: 1, //告警联系人弹窗状态----1为新增，2为编辑
      dialogVisible: false,
      checked: true,
      tableDataObj: {
        loading2: false,
        tableData: [],
        tableLabel: [],
        tableStyle: {
          isSelection: false, //是否复选框
          height: 400, //是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "180", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "编辑",
            type: "",
            size: "mini",
            optionMethod: "editContact",
            icon: "el-icon-edit", //按钮图标
            color: "#16A589", //按钮颜色
          },
          {
            optionName: "删除",
            type: "",
            size: "mini",
            optionMethod: "delContact",
            icon: "el-icon-delete", //按钮图标
            color: "#f56c6c", //按钮颜色
          },
        ],
      },
      form: {
        conLinkmanId: "",
        linkmanName: "",
        linkmanPhone: "",
        // linkmanEmail: '',
        warningRemind: "1",
        auditRemind: "2",
        balanceRemind: "1",
        category: "2",
        productType: "1",
      },
      formRule: {
        linkmanName: [
          { required: true, message: "请输入姓名", trigger: "blur" },
          // {pattern:/^[\u4e00-\u9fa5]{2,4}$/gi,message:'姓名为2-4个中文字符!'}
        ],
        warningRemind: [
          {
            required: true,
            message: "请选择是否接收预警提通知",
            trigger: "change",
          },
        ],
        auditRemind: [
          {
            required: true,
            message: "请选择是否人工审核通知",
            trigger: "change",
          },
        ],
        balanceRemind: [
          {
            required: true,
            message: "请选择是否余额不足通知",
            trigger: "change",
          },
        ],
        productType: [
          {
            required: true,
            message: "请至少选择一个产品类别",
            trigger: "change",
          },
        ],
      },
    };
  },
  watch: {
    checked(newVal, oldVal) {
      if (newVal == true) {
        this.form.productType = "1";
      } else {
        this.form.productType = "";
      }
    },
    dialogVisible(newVal, oldVal) {
      //------监听告警联系人弹窗
      if (newVal == false) {
        this.$refs.addContactForm.resetFields(); //验证置空
        //置空
        this.form.conLinkmanId = "";
        this.form.linkmanName = "";
        this.form.linkmanPhone = "";
        this.form.warningRemind = "1";
        this.form.auditRemind = "2";
        this.form.productType = "1";
        this.checked = true;
      } else {
        this.checked = true;
      }
    },
    warningRemind(val) {
      this.getTableData();
    },
  },
  computed: {
    ...mapState({
      warningRemind: (state) => state.warningRemind,
      roleDesc: (state) => state.roleDesc,
      userId: (state) => state.userId,
    }),
    dialogTitle: function () {
      return this.dialogStatus == 1 ? "新增告警联系人" : "编辑告警联系人";
    },
  },
  created() {
    console.log(this.$store.state.roleId,'this.userId');
    
  },
  methods: {
    // handelClose() {//余额提醒 ×号关闭弹窗
    //      this.warnDialogVisible = false; //关闭弹出框
    //      this.getRemindNums();
    // },
    //点击取消
    // diaCancel(){
    //     this.warnDialogVisible = false;
    //     this.getRemindNums();
    // },
    //点击确定
    // determine(){
    //     const testNum= /^([1-9][0-9]{0,6}|10000000)$/;
    //     if(testNum.test(this.NumberBalances)){
    //         this.$confirms.confirmation('post','确定余额条数不足'+this.NumberBalances+'条时提醒',this.API.cpus + '/consumerClientBalanceNotice/save',{smsNum:this.NumberBalances},res =>{
    //             this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
    //                 this.reminderBalances=res.smsNum
    //             })
    //             this.warnDialogVisible=false;//隐藏弹窗
    //         });
    //     }else{
    //         this.$message({
    //             type: 'error',
    //             duration:'2000',
    //             message:"请填写1-10000000的余额条数"
    //         });
    //     },
    // getRemindNums(){//获取设置的月提醒条数；
    //     this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
    //         this.NumberBalances=res.smsNum
    //     })
    // },
    addContacts() {
      if (this.tableDataObj.tableData.length >= 5) {
        this.$message({
          message: "最多可添加 5 个指定联系人！",
          type: "warning",
        });
      } else {
        this.dialogStatus = 1;
        this.dialogVisible = true;
      }
    },
    /* --------------- 列表展示 ------------------*/
    getTableData() {
      //获取列表数据
      let balanceState;
      // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
      // if(res){
      //     if(res.smsIsOpen==2){
      //         balanceState=false
      //         this.balanceState=false
      //     }else{
      //         balanceState=true
      //         this.balanceState=true
      //     }
      // }else{
      //     balanceState=false
      //     this.balanceState=false
      // }
      // if (this.$store.state.warningRemind==2) {
      //     if(balanceState==false){
      //         this.tableDataObj.tableLabel=[{
      //             prop:"linkmanName",
      //             showName:'姓名',
      //             width:'140',
      //             fixed:false
      //         },{
      //             prop:"linkmanPhone",
      //             showName:'手机',
      //             fixed:false
      //         },{
      //             prop:"auditRemind",
      //             showName:'审核通知',
      //             fixed:false,
      //             formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      //         },{
      //             prop:"warningRemind",
      //             showName:'预警通知',
      //             fixed:false,
      //             formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      //         }]
      //     }else{
      this.tableDataObj.tableLabel = [
        {
          prop: "linkmanName",
          showName: "姓名",
          width: "140",
          fixed: false,
        },
        {
          prop: "linkmanPhone",
          showName: "手机",
          fixed: false,
        },
        {
          prop: "auditRemind",
          showName: "审核通知",
          fixed: false,
          formatData: function (val) {
            return val == "2" ? "Y" : "N";
          },
        },
        {
          prop: "balanceRemind",
          showName: "余额变更&不足通知",
          fixed: false,
          formatData: function (val) {
            return val == "2" ? "Y" : "N";
          },
        },
        // {
        //     prop:"warningRemind",
        //     showName:'预警通知',
        //     fixed:false,
        //     formatData: function(val) { return val == '2' ? 'Y' : 'N' },
        // }
      ];
      // }
      // } else {
      //     if(balanceState==false){
      // this.tableDataObj.tableLabel=[{
      //     prop:"linkmanName",
      //     showName:'姓名',
      //     width:'140',
      //     fixed:false
      // },{
      //     prop:"linkmanPhone",
      //     showName:'手机',
      //     fixed:false
      // },{
      //     prop:"auditRemind",
      //     showName:'审核通知',
      //     fixed:false,
      //     formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      // }]
      // }else{
      // this.tableDataObj.tableLabel=[{
      //     prop:"linkmanName",
      //     showName:'姓名',
      //     width:'140',
      //     fixed:false
      // },{
      //     prop:"linkmanPhone",
      //     showName:'手机',
      //     fixed:false
      // },{
      //     prop:"auditRemind",
      //     showName:'审核通知',
      //     fixed:false,
      //     formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      // },{
      //     prop:"balanceRemind",
      //     showName:'余额不足通知',
      //     fixed:false,
      //     formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      // }]
      // }
      // }
      this.tableDataObj.loading2 = true;
      this.$api.get(this.API.cpus + "consumerclientlinkman/page", {}, (res) => {
        this.tableDataObj.tableData = res.data;
        this.tableDataObj.loading2 = false;
      });
      // })
    },
    deleRow: function (index, rows) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$message({
            type: "primary",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    addContactOk: function (val) {
      //新增告警联系人--弹窗
      this.$refs.addContactForm.validate((valid) => {
        if (valid) {
          if (val == 1) {
            //新增
            this.form.conLinkmanId = ""; //id清空
            // if (this.form.linkmanPhone == '' && this.form.linkmanEmail == '') {
            if (this.form.linkmanPhone == "") {
              this.$message({
                message: "手机号或邮箱必须填一个",
                type: "warning",
              });
            } else {
              this.$api.get(
                this.API.cpus +
                  "consumerclientlinkman/validatePhoneExist/" +
                  this.form.linkmanPhone,
                {},
                (res) => {
                  if (res.code == 200) {
                    this.$confirms.confirmation(
                      "post",
                      "确定新增告警联系人",
                      this.API.cpus + "consumerclientlinkman/add",
                      this.form,
                      (res) => {
                        this.getTableData();
                        this.dialogVisible = false; //关闭弹窗
                      }
                    );
                  } else {
                    this.$message({
                      message: "手机号已重复，重新输入！",
                      type: "warning",
                    });
                  }
                }
              );
              //   if (this.form.linkmanPhone != "") {

              //   } else {
              //     this.$confirms.confirmation(
              //       "post",
              //       "确定新增告警联系人",
              //       this.API.cpus + "consumerclientlinkman/add",
              //       this.form,
              //       (res) => {
              //         this.getTableData();
              //         this.dialogVisible = false; //关闭弹窗
              //       }
              //     );
              //   }
            }
          } else if (val == 0) {
            if (this.form.linkmanPhone == "") {
              this.$message({
                message: "手机号或邮箱必须填一个",
                type: "warning",
              });
            } else {
              this.$api.get(
                this.API.cpus +
                  "consumerclientlinkman/validatePhoneExist/" +
                  this.form.linkmanPhone +
                  "?conLinkmanId=" +
                  this.form.conLinkmanId,
                {},
                (res) => {
                  if (res.code == 200) {
                    this.$confirms.confirmation(
                      "put",
                      "确定编辑告警联系人？",
                      this.API.cpus + "consumerclientlinkman/update",
                      this.form,
                      (res) => {
                        this.getTableData();
                        this.dialogVisible = false; //关闭弹窗
                      }
                    );
                  } else {
                    this.$message({
                      message: "手机号已重复，重新输入！",
                      type: "warning",
                    });
                  }
                }
              );
            }
            //编辑
            // if (
            //   this.tableRows.linkmanPhone == this.form.linkmanPhone &&
            //   this.tableRows.linkmanPhone != ""
            // ) {
            //   this.$confirms.confirmation(
            //     "put",
            //     "确定编辑告警联系人？",
            //     this.API.cpus + "consumerclientlinkman/update",
            //     this.form,
            //     (res) => {
            //       this.getTableData();
            //       this.dialogVisible = false; //关闭弹窗
            //     }
            //   );
            // } else {
            //   // if (this.form.linkmanPhone == '' && this.form.linkmanEmail == '') {
            // if (this.form.linkmanPhone != "") {
            // } else {
            //   this.$confirms.confirmation(
            //     "put",
            //     "确定编辑告警联系人？",
            //     this.API.cpus + "consumerclientlinkman/update",
            //     this.form,
            //     (res) => {
            //       this.getTableData();
            //       this.dialogVisible = false; //关闭弹窗
            //     }
            //   );
            // }

            // }
          }
        } else {
          return false;
        }
      });
    },
    handleEdit: function (row) {
      //--------------编辑联系人
      this.form.linkmanName = row.linkmanName;
      // this.form.linkmanPhone=val.row.linkmanPhone;
      // this.form.linkmanEmail=val.row.linkmanEmail;
      this.form.auditRemind = row.auditRemind + "";
      this.form.balanceRemind = row.balanceRemind + "";
      this.form.warningRemind = row.warningRemind;
      this.form.productType = row.productType;
      this.form.conLinkmanId = row.conLinkmanId;
      this.tableRows = row;
      this.dialogStatus = 0; //打开编辑告警联系人弹窗
      this.dialogVisible = true; //打开弹窗
      this.$api.get(
        this.API.cpus + "consumerclientlinkman/" + row.conLinkmanId,
        {},
        (res) => {
          if (res.code == 200) {
            this.form.linkmanPhone = res.data.linkmanPhone;
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        }
      );
    },
    handleDelete: function (row) {
      //------------删除联系人
      let conLinkmanId = row.conLinkmanId;
      this.$confirms.confirmation(
        "delete",
        "确定删除告警联系人？",
        this.API.cpus + "consumerclientlinkman/" + conLinkmanId,
        {},
        (res) => {
          this.getTableData();
        }
      );
    },
    // 获取项目绝对路径
    ...mapActions([
      //比如'movies/getHotMovies
      "saveUrl",
    ]),
    logUrl(val) {
      let logUrl = {
        logUrl: val,
      };
      this.saveUrl(logUrl);
      window.sessionStorage.setItem("logUrl", val);
    },
  },
  mounted() {
    this.getTableData();
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/template-common.less';

// 组件特有的样式定制
.content-cell {
  display: flex;
  align-items: center;
  
  .phone-number {
    color: #606266;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.highlight {
  color: #f56c6c;
  font-weight: 500;
}

.radio-group {
  .el-radio {
    margin-right: 24px;
  }
}

.dialog-form {
  .el-form-item__label {
    color: #333;
    font-weight: 500;
  }
  
  .el-input__inner {
    border-radius: 6px;
    
    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
  
  .el-radio__label {
    color: #606266;
    font-weight: normal;
  }
  
  .el-radio__input.is-checked .el-radio__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  
  .el-button {
    min-width: 80px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
    gap: 16px;
    
    .action-buttons {
      justify-content: center;
    }
    
    .stats-info {
      justify-content: center;
    }
  }
  
  .tips-section {
    .tips-container {
      .tip-item {
        flex-direction: column;
        text-align: center;
        gap: 8px;
      }
    }
  }
}
</style>
