<template>
    <div class="simple-signature-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <el-button 
                                    v-permission
                                    @click="AddBlack()" 
                                    class="action-btn primary" 
                                    icon="el-icon-plus">
                                    添加黑名单
                                </el-button>
                                <el-button 
                                    v-permission
                                    @click="amountImport = true" 
                                    class="action-btn" 
                                    icon="el-icon-upload2">
                                    批量导入
                                </el-button>
                                <el-button 
                                    v-permission
                                    v-if="selectId.length"
                                    @click="batchDeletion" 
                                    class="action-btn danger" 
                                    icon="el-icon-delete">
                                    批量删除 ({{ selectId.length }})
                                </el-button>
                                <el-button 
                                    v-permission
                                    @click="ExBlack()" 
                                    class="action-btn" 
                                    icon="el-icon-download">
                                    导出黑名单
                                </el-button>
                            </div>

                            <!-- 统计信息 -->
                            <div class="stats-info">
                                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                                <el-divider direction="vertical"></el-divider>
                                <span class="stats-text">已选择 {{ selectId.length }} 条</span>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <el-form 
                                class="advanced-search-form" 
                                inline 
                                size="small">
                                <div class="search-row">
                                    <el-form-item label="手机号" class="search-item">
                                        <el-input
                                            v-model="InputphoneNamber"
                                            placeholder="请输入手机号"
                                            class="search-input"
                                            clearable>
                                            <i slot="suffix" class="el-input__icon el-icon-search"></i>
                                        </el-input>
                                    </el-form-item>
                                    
                                    <el-form-item label="创建时间" class="search-item">
                                        <el-date-picker
                                            v-model="valueData"
                                            @change="timeChange"
                                            type="daterange"
                                            range-separator="至"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            value-format="yyyy-MM-dd"
                                            style="width: 240px;">
                                        </el-date-picker>
                                    </el-form-item>
                                    
                                    <el-form-item class="search-buttons">
                                        <el-button 
                                            @click="getphoneBlackList()" 
                                            class="search-btn primary" 
                                            icon="el-icon-search">
                                            搜索
                                        </el-button>
                                        <el-button 
                                            @click="resetSearch()" 
                                            class="search-btn">
                                            重置
                                        </el-button>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>

                <!-- 黑名单列表 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3 class="table-title">黑名单列表</h3>
                        <span class="table-count">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                    </div>

                    <div class="table-container">
                        <el-table
                            v-loading="tableDataObj.loading2"
                            element-loading-text="正在加载..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(255, 255, 255, 0.8)"
                            :data="tableDataObj.tableData"
                            class="enhanced-table"
                            stripe
                            :header-cell-style="{ background: '#fafafa', color: '#333' }"
                            :row-style="{ height: '60px' }"
                            empty-text="暂无黑名单数据"
                            @selection-change="handleSelectionChange">
                            
                            <el-table-column type="selection" width="50" align="center"></el-table-column>
                            
                            <el-table-column prop="phone" label="手机号" min-width="150">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <span class="phone-number">{{ scope.row.phone }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column prop="complaintType" label="号码类别" min-width="120" align="center">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.complaintType == '1' ? 'primary' : 'warning'" size="small">
                                        {{ scope.row.complaintType == '1' ? '用户自主加黑' : '上行内容加黑' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            
                            <el-table-column prop="createTime" label="创建时间" min-width="160">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <i class="el-icon-time"></i>
                                        <span>{{ scope.row.createTime }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column prop="remark" label="备注" min-width="200">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <span class="remark-text">{{ scope.row.remark || '暂无备注' }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="操作" width="100" align="center" fixed="right">
                                <template slot-scope="scope">
                                    <el-button
                                        type="text"
                                        size="small"
                                        style="color: #f56c6c;"
                                        @click="handleDelete(scope.row)">
                                        <i class="el-icon-delete"></i>
                                        删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination-section">
                        <el-pagination
                            class="simple-pagination"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="tableDataObj.tablecurrent.total">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量导入对话框 -->
        <el-dialog
            title="批量导入黑名单"
            :visible.sync="amountImport"
            width="600px"
            :close-on-click-modal="false"
            class="enhanced-dialog">
            
            <el-form class="dialog-form" label-width="100px">
                <el-form-item label="上传文件" prop="upload">
                    <el-upload
                        class="upload-demo"
                        ref="upload"
                        :action="action"
                        :headers="token"
                        :data="{ 'username': userName, 'remark': remark }"
                        :on-preview="handlePreview"
                        :on-remove="handleRemove"
                        :file-list="fileList"
                        :limit="1"
                        :on-success="handleAvatarSuccess"
                        :before-upload="beforeAvatarUpload"
                        :auto-upload="false">
                        <el-button slot="trigger" size="small" type="primary" icon="el-icon-folder-opened">
                            选择文件
                        </el-button>
                        <div slot="tip" class="el-upload__tip">
                            <p class="upload-tip">只支持 .txt 格式文件</p>
                            <p class="upload-tip">单次最多上传 10 万行数据</p>
                        </div>
                    </el-upload>
                </el-form-item>
                
                <el-form-item label="备注">
                    <el-input
                        type="textarea"
                        maxlength="70"
                        placeholder="请输入备注内容，不超过70个字"
                        v-model="remark"
                        :rows="3">
                    </el-input>
                </el-form-item>
            </el-form>
            
            <div slot="footer" class="dialog-footer">
                <el-button @click="amountImport = false">取消</el-button>
                <el-button v-permission type="primary" @click="submitUpload">确定上传</el-button>
            </div>
        </el-dialog>

        <!-- 添加黑名单对话框 -->
        <el-dialog
            title="添加黑名单"
            :visible.sync="AddBlacks"
            width="500px"
            :close-on-click-modal="false"
            class="enhanced-dialog">
            
            <el-form
                :model="AddBlackform"
                ref="AddBlackformRef"
                class="dialog-form"
                label-width="100px">
                
                <el-form-item
                    label="手机号码"
                    prop="phone"
                    :rules="filter_rules({ required: true, type: 'mobile', message: '手机号不能为空' })">
                    <el-input
                        v-model="AddBlackform.phone"
                        placeholder="请输入手机号码">
                    </el-input>
                </el-form-item>
                
                <el-form-item label="备注" prop="remark">
                    <el-input
                        type="textarea"
                        maxlength="70"
                        placeholder="请输入备注内容，不超过70个字"
                        v-model="AddBlackform.remark"
                        :rows="3">
                    </el-input>
                </el-form-item>
            </el-form>
            
            <div slot="footer" class="dialog-footer">
                <el-button @click="AddBlacks = false">取消</el-button>
                <el-button v-permission type="primary" @click="AddBlacklist('AddBlackformRef')">确定添加</el-button>
            </div>
        </el-dialog>

        <!-- 导出数据对话框 -->
        <el-dialog
            title="导出黑名单数据"
            :visible.sync="exportShow"
            width="500px"
            :close-on-click-modal="false"
            class="enhanced-dialog"
            :before-close="handleClose">
            
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                class="dialog-form"
                label-width="100px">
                
                <el-form-item label="导出类型" prop="decode">
                    <el-radio-group v-model="ruleForm.decode" class="radio-group">
                        <el-radio label="0">掩码格式</el-radio>
                        <el-radio label="1">明码格式</el-radio>
                    </el-radio-group>
                </el-form-item>
                
                <div class="export-tips" v-if="ruleForm.decode == '1'">
                    <i class="el-icon-warning"></i>
                    <span>明码文件将会发送到您的邮箱 {{ emailInfo.email }}，请注意查收。</span>
                </div>
            </el-form>
            
            <div slot="footer" class="dialog-footer">
                <el-button @click="exportShow = false">取消</el-button>
                <el-button v-permission type="primary" @click="submitExport">
                    {{ ruleForm.decode == '0' ? '立即下载' : '发送邮箱' }}
                </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";

let _ = require('lodash')
export default {
    name: "BlacklistManagement",
    components: {},
    data() {
        return {
            name: "BlackList",
            AddBlacks: false,
            // 手机号值
            InputphoneNamber: '',
            // 日期值
            valueData: "",
            // 添加黑名单
            AddBlackform: {
                phone: '',
                remark: ''
            },
            //复选框值
            selectId: '',
            pagesize: 10,
            currentPage: 1,
            datePluginValueList: {
                type: "date",
                start: "",
                end: '',
                range: '-',
                defaultTime: "2020-02-03", //默认起始时刻
                datePluginValue: ""
            },
            action: this.API.omcs + "/clientBlacklist/upload",
            dialogVisible: false,
            amountImport: false,
            token: {},
            fileList: [],
            remark: '',
            tableDataObj: {
                loading2: false,
                tablecurrent: { //分页参数
                    total: 0,
                },
                tableData: [],
                tableLabel: [{
                    prop: "phone",
                    showName: '手机号',
                    fixed: false
                }, {
                    prop: "complaintType",
                    showName: '号码类别',
                    fixed: false,
                    formatData: function (val) {
                        return val == '1' ? '用户自主加黑' : '上行内容加黑'
                    }
                }, {
                    prop: "createTime",
                    showName: '创建时间',
                    fixed: false
                }, {
                    prop: "remark",
                    showName: '备注',
                    fixed: false
                }],
                tableStyle: {
                    isSelection: true,//是否复选框
                    // height:250,//是否固定表头
                    isExpand: false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '80',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                },
                tableOptions: [
                    {
                        optionName: '删除',
                        type: '',
                        size: 'mini',
                        optionMethod: 'dele',
                        icon: 'el-icon-error',
                        optionButtonColor: 'red',//按钮颜色
                    }
                ]
            },
            exportShow: false, //是否显示导出按钮
            emailInfo: {
                email: "",
                username: ""
            },
            ruleForm: {
                decode: "0",
            },
            rules: {

            },
        };
    },
    computed: {
        ...mapState({  //比如'movies/hotMovies
            // portraitUrl:state=>state.portraitUrl,
            userName: state => state.userName
        })
    },
    methods: {
        getphoneBlackList() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.omcs + 'clientBlacklist/page', { username: this.userName, phone: this.InputphoneNamber, pageSize: this.pagesize, beginTime: this.valueData[0], endTime: this.valueData[1] }, res => {
                this.tableDataObj.tableData = res.records
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tablecurrent.total = res.total
            })
        },
        detailsRow: function (index, rows) {
            this.dialogVisible = true
        },
        //列表复选框的值
        handleSelectionChange(val) {
            let selectId = [];
            for (let i = 0; i < val.length; i++) {
                selectId.push(val[i].clientBlacklistId)
            }
            this.selectId = selectId; //批量操作选中id
        },
        //批量删除
        batchDeletion() {
            this.$confirms.confirmation('post', '确定删除黑名单', this.API.omcs + 'clientBlacklist/batchDelete', { ids: this.selectId }, res => {
                this.getphoneBlackList()
            })
        },
        // 单条删除黑名单
        handleDelete: function (row) {
            this.$confirms.confirmation('get', '确定删除黑名单', this.API.omcs + 'clientBlacklist/deleteById/' + row.clientBlacklistId, {}, res => {
                this.getphoneBlackList()
            })
        },
        // 重置搜索
        resetSearch() {
            this.InputphoneNamber = '';
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var nowDate = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day);
            var lastDate = new Date(date - 1000 * 60 * 60 * 24 * 30);
            var lastY = lastDate.getFullYear();
            var lastM = lastDate.getMonth() + 1;
            var lastD = lastDate.getDate();
            var LDate = lastY + "-" + (lastM < 10 ? "0" + lastM : lastM) + "-" + (lastD < 10 ? "0" + lastD : lastD)
            this.valueData = [LDate, nowDate];
            this.getphoneBlackList();
        },
        handleSizeChange(size) {
            this.pagesize = size;
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.omcs + 'clientBlacklist/page', { username: this.userName, phone: this.InputphoneNamber, beginTime: this.valueData[0], endTime: this.valueData[1], pageSize: size, currentPage: this.currentPage }, res => {
                this.tableDataObj.tableData = res.records
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tablecurrent.total = res.total
            })
        },
        handleCurrentChange: function (currentPage) {
            this.currentPage = currentPage;
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.omcs + 'clientBlacklist/page', { username: this.userName, phone: this.InputphoneNamber, beginTime: this.valueData[0], endTime: this.valueData[1], pageSize: this.pagesize, currentPage: currentPage }, res => {
                this.tableDataObj.tableData = res.records
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tablecurrent.total = res.total
            })
        },
        handledatepluginVal: function (val1, val2) {
            this.datePluginValue = [val1, val2];
        },
        timeChange: function (val) {
            // console.log(val);
            if (val == null) {
                this.valueData = ['', '']
            }
            this.getphoneBlackList()
        },
        // 添加黑名单
        AddBlack() {
            this.AddBlacks = true;
        },
        //导出黑名单
        ExBlack() {
            if (this.tableDataObj.tableData.length == 0) {
                this.$message({
                    message: "列表无数据，不可导出！",
                    type: "warning",
                });
            } else {
                this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
                    if (res.code == 200) {
                        this.emailInfo.email = res.data.email;
                        this.emailInfo.username = res.data.username;
                        if (res.data.decode) {
                            this.exportShow = true
                        } else {
                            let data = { phone: this.InputphoneNamber, beginTime: this.valueData[0], endTime: this.valueData[1] }
                            this.exportFn(data, '1')
                        }
                    }
                });
            }
            // this.$confirms.confirmation('post','确认导出全部黑名单',this.API.cpus+'v3/export/blacklist',{phone:this.InputphoneNamber,beginTime:this.valueData[0],endTime:this.valueData[1]},res =>{

            //     if(res.code == 200){
            //         this.getphoneBlackList()
            //         this.$message({
            //         type: 'success',
            //         duration:'2000',
            //         message:res.msg
            //     });
            //     this.$router.push({ path: '/FileExport'})
            //     }else{
            //         this.$message({
            //         type: 'error',
            //         duration:'2000',
            //         message:res.msg
            //     });
            //     }
            // })
        },
        exportFn(obj, type) {
            if (type == '1') {
                this.$confirms.confirmation('post', '确认导出全部黑名单', this.API.cpus + 'v3/export/blacklist', obj, res => {
                    if (res.code == 200) {
                        this.exportShow = false
                        this.$message({
                            type: "success",
                            duration: "2000",
                            message: "已加入到文件下载中心!",
                        });
                        this.$router.push('/FileExport');
                    }
                })
            } else {
                this.$api.post(this.API.cpus + "v3/export/blacklist", obj, (res) => {
                    if (res.code == 200) {
                        this.exportShow = false
                        this.$message({
                            type: "success",
                            duration: "2000",
                            message: "已加入到文件下载中心!",
                        });
                        this.$router.push('/FileExport');
                    }else{
                        this.$message({
                            type: "error",
                            duration: "2000",
                            message: res.msg,
                        });
                    }
                });
            }

        },
        submitExport() {
            let data = { phone: this.InputphoneNamber, beginTime: this.valueData[0], endTime: this.valueData[1] }
            data.decode = this.ruleForm.decode == 0 ? false : true;
            this.exportFn(data, '2')
        },
        handleClose() {
            this.exportShow = false
        },
        //确定添加黑名单
        AddBlacklist(val) {
            this.$refs[val].validate((valid) => {
                if (valid) {
                    this.AddBlackform.username = this.userName
                    this.$confirms.confirmation('post', '确定添加用户黑名单', this.API.omcs + 'clientBlacklist/add', this.AddBlackform, res => {
                        this.getphoneBlackList()
                        this.AddBlacks = false;
                    })
                }
            })
        },
        // 文件上传
        beforeAvatarUpload(file) {
            console.log(file, 'file');
            const isJPG1 = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            const isJPG2 = file.type === 'text/plain';
            if (!isJPG1 && !isJPG2) {
                this.$message.error('只能上传 .txt 格式!');
                return false;
            }
            if (file.size > 20 * 1024 * 1024) {
                this.$message.error('上传文件过大');
                return false
            }
        },
        handleAvatarSuccess(file) {
            console.log(file)
            if (file.code == "200") {
                this.$message({
                    type: 'success',
                    duration: '2000',
                    message: '上传成功'
                });
                setTimeout(() => {
                    this.getphoneBlackList()
                }, 1000)
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "上传失败"
                });
                this.getphoneBlackList()
            }
            this.fileList = []
            this.remark = ''
            this.amountImport = false;
        },
        submitUpload() {
            this.$refs.upload.submit();
        },
        handleRemove(file, fileList) {
            console.log(file, fileList);
        },
        handlePreview(file) {
            console.log(file);
        }
    },
    watch: {
        InputphoneNamber(val) {
            this.getPhone()
        },
        AddBlacks(val) {
            if (val == false) {
                this.$refs.AddBlackformRef.resetFields()
            }
        },
        amountImport(val) {
            if (val == false) {
                this.remark = ''
                this.$refs.upload.clearFiles();
            }
        }
    },
    created() {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var day = date.getDate();
        var nowDate = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day);
        var lastDate = new Date(date - 1000 * 60 * 60 * 24 * 30);//最后30天可以更改，意义：是获取多少天前的时间
        var lastY = lastDate.getFullYear();
        var lastM = lastDate.getMonth() + 1;
        var lastD = lastDate.getDate();
        var LDate = lastY + "-" + (lastM < 10 ? "0" + lastM : lastM) + "-" + (lastD < 10 ? "0" + lastD : lastD)
        this.valueData = [LDate, nowDate]

        this.token = { Authorization: "Bearer" + this.$common.getCookie('ZTGlS_TOKEN') };
        this.getphoneBlackList()
        this.getPhone = _.debounce(this.getphoneBlackList, 500)
    }
}
</script>

<style lang="less" scoped>
@import '~@/styles/template-common.less';

// 组件特有的样式定制
.content-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .phone-number {
    color: #606266;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 500;
  }
  
  .remark-text {
    color: #666;
    line-height: 1.4;
    word-break: break-word;
  }
}

.action-btn {
  &.danger {
    background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
    border-color: #f56c6c;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #f78989 0%, #f9a3a4 100%);
      border-color: #f78989;
      color: white;
    }
  }
}

.export-tips {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  color: #fa8c16;
  font-size: 14px;
  
  i {
    font-size: 16px;
  }
}

.upload-tip {
  margin: 0;
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.radio-group {
  .el-radio {
    margin-right: 24px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

.dialog-form {
  .el-form-item__label {
    color: #333;
    font-weight: 500;
  }
  
  .el-input__inner,
  .el-textarea__inner {
    border-radius: 6px;
    
    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
  
  .el-radio__label {
    color: #606266;
    font-weight: normal;
  }
  
  .el-radio__input.is-checked .el-radio__inner {
    background-color: #409eff;
    border-color: #409eff;
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  
  .el-button {
    min-width: 80px;
  }
}

.upload-demo {
  .el-upload__tip {
    margin-top: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
    gap: 16px;
    
    .action-buttons {
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .stats-info {
      justify-content: center;
    }
  }
  
  .search-section {
    .search-row {
      flex-direction: column;
      gap: 12px;
      
      .search-item {
        width: 100%;
      }
      
      .search-buttons {
        width: 100%;
        margin-left: 0;
        
        .search-btn {
          flex: 1;
        }
      }
    }
  }
}
</style>
