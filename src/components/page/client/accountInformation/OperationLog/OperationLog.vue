<template>
    <div class="simple-signature-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 标题信息区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <div class="page-info">
                                    <i class="el-icon-document-copy"></i>
                                    <span class="page-title">平台操作日志</span>
                                </div>
                            </div>

                            <!-- 统计信息 -->
                            <div class="stats-info">
                                <span class="stats-text">共 {{ totalSize }} 条记录</span>
                                <el-divider direction="vertical"></el-divider>
                                <span class="stats-text">第 {{ formInlines.currentPage }} 页</span>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <el-form 
                                ref="formInline"
                                :model="formInline"
                                class="advanced-search-form" 
                                inline 
                                size="small">
                                <div class="search-row">
                                    <el-form-item label="标题" prop="title" class="search-item">
                                        <el-input
                                            v-model="formInline.title"
                                            placeholder="请输入操作标题"
                                            class="search-input"
                                            clearable>
                                        </el-input>
                                    </el-form-item>
                                    
                                    <el-form-item label="操作时间" prop="time" class="search-item">
                                        <el-date-picker
                                            v-model="formInline.time"
                                            value-format="yyyy-MM-dd"
                                            type="daterange"
                                            range-separator="至"
                                            @change="getTimeOperating"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            style="width: 240px;">
                                        </el-date-picker>
                                    </el-form-item>
                                    
                                    <el-form-item class="search-buttons">
                                        <el-button 
                                            @click="ListSearch()" 
                                            class="search-btn primary" 
                                            icon="el-icon-search">
                                            查询
                                        </el-button>
                                        <el-button 
                                            @click="Reset('formInline')" 
                                            class="search-btn">
                                            重置
                                        </el-button>
                                    </el-form-item>
                                </div>
                            </el-form>
                        </div>
                    </div>
                </div>

                <!-- 操作日志列表 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3 class="table-title">操作日志列表</h3>
                        <span class="table-count">共 {{ totalSize }} 条记录</span>
                    </div>

                    <div class="table-container">
                        <el-table
                            v-loading="tableDataObj.loading2"
                            element-loading-text="正在加载..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(255, 255, 255, 0.8)"
                            :data="tableDataObj.tableData"
                            class="enhanced-table"
                            stripe
                            :header-cell-style="{ background: '#fafafa', color: '#333' }"
                            :row-style="{ height: '60px' }"
                            empty-text="暂无操作日志数据">
                            
                            <el-table-column label="操作手机号" min-width="150">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <span class="phone-number">{{ scope.row.loginMobile || '-' }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="操作标题" min-width="180">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <i class="el-icon-s-operation"></i>
                                        <span class="operation-title">{{ scope.row.title || '-' }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="操作IP" min-width="140">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <i class="el-icon-location-outline"></i>
                                        <span class="ip-address">{{ scope.row.remoteAddr || '-' }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="请求地址" min-width="200">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <i class="el-icon-link"></i>
                                        <span class="request-uri">{{ scope.row.requestUri || '-' }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="请求方式" min-width="100" align="center">
                                <template slot-scope="scope">
                                    <el-tag 
                                        :type="getMethodType(scope.row.method)" 
                                        size="small">
                                        {{ scope.row.method || '-' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="操作时间" min-width="160">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <i class="el-icon-time"></i>
                                        <span>{{ scope.row.createTime || '-' }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            
                            <el-table-column label="请求参数" min-width="300">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <el-tooltip 
                                            v-if="scope.row.params"
                                            class="item" 
                                            effect="dark" 
                                            placement="top"
                                            :content="scope.row.params">
                                            <div class="params-wrapper">
                                                <i class="el-icon-document"></i>
                                                <span class="params-text">{{ scope.row.params }}</span>
                                            </div>
                                        </el-tooltip>
                                        <span v-else class="no-data">-</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination-section">
                        <el-pagination
                            class="simple-pagination"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="formInlines.currentPage"
                            :page-size="formInlines.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="totalSize">
                        </el-pagination>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import moment from 'moment'
export default {
    name: 'OperationLog',
    components: {},
    data() {
        return {
            formInline: {
                userName: '',
                title: '',
                isSucced: '',
                begTime: moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
                endTime: moment(Date.now()).format('YYYY-MM-DD 23:59:59'),
                time: [moment().startOf('day').format('YYYY-MM-DD 00:00:00'), moment(Date.now()).format('YYYY-MM-DD 23:59:59')],
                pageSize: 10,
                currentPage: 1,
            },
            formInlines: {
                userName: '',
                title: '',
                isSucced: '',
                begTime: moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
                endTime: moment(Date.now()).format('YYYY-MM-DD 23:59:59'),
                time: [moment().startOf('day').format('YYYY-MM-DD 00:00:00'), moment(Date.now()).format('YYYY-MM-DD 23:59:59')],
                pageSize: 10,
                currentPage: 1,
            },
            formAdd: {
                name: '',
                state: '',
                menu: '',
                description: ''
            },
            datePluginValueList: {
                type: "date",
                start: "",
                end: '',
                range: '-',
                defaultTime: "", //默认起始时刻
                datePluginValue: ""
            },
            totalSize: 0,//总条数
            tableDataObj: { //列表数据
                loading2: false,//loading动画
                tableData: [],
            },
        }
    },
    methods: {
        // 发送请求方法
        InquireList() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumerclient/sysLog/page', this.formInlines, res => {
                this.tableDataObj.tableData = res.data.records;
                this.totalSize = res.data.total;
                this.tableDataObj.loading2 = false;
            })
        },
        // 操作时间
        getTimeOperating(val) {
            if (val) {
                this.formInline.begTime = val[0] + ' 00:00:00'
                this.formInline.endTime = val[1] + ' 23:59:59'
            } else {
                this.formInline.begTime = ""
                this.formInline.endTime = ""
            }
        },
        // 搜索
        ListSearch() {
            Object.assign(this.formInlines, this.formInline);
            this.InquireList();
        },
        // 重置
        Reset(formName) {
            this.$refs[formName].resetFields();
            this.formInline.begTime = moment().startOf('day').format('YYYY-MM-DD 00:00:00');
            this.formInline.endTime = moment(Date.now()).format('YYYY-MM-DD 23:59:59');
            Object.assign(this.formInlines, this.formInline)
        },
        //分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
        },
        handleCurrentChange: function (currentPage) {
            this.formInlines.currentPage = currentPage;
        },
        getMethodType(method) {
            // 根据请求方式返回不同的类型
            switch (method) {
                case 'GET':
                    return 'primary';
                case 'POST':
                    return 'success';
                case 'PUT':
                    return 'warning';
                case 'DELETE':
                    return 'danger';
                default:
                    return '';
            }
        }
    },
    activated() {
        this.InquireList();
    },
    watch: {
        // 监听搜索/分页数据
        formInlines: {
            handler() {
                this.InquireList();
            },
            deep: true,
            immediate: true,
        },
    }
}
</script>
<style lang="less" scoped>
@import '~@/styles/template-common.less';

// 组件特有的样式定制
.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  i {
    font-size: 20px;
    color: #409eff;
  }
}

.content-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .phone-number {
    color: #606266;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-weight: 500;
  }
  
  .operation-title {
    color: #333;
    font-weight: 500;
  }
  
  .ip-address {
    color: #606266;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
  
  .request-uri {
    color: #666;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    word-break: break-all;
  }
  
  .no-data {
    color: #c0c4cc;
    font-style: italic;
  }
  
  i {
    color: #909399;
    margin-right: 4px;
  }
}

.params-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: help;
  max-width: 280px;
  
  .params-text {
    color: #666;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
  }
  
  i {
    color: #409eff;
    flex-shrink: 0;
  }
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

// 请求方式标签样式定制
.enhanced-table {
  /deep/ .el-tag {
    font-weight: 500;
    border-radius: 4px;
    
    &.el-tag--primary {
      background: #e6f3ff;
      border-color: #b3d9ff;
      color: #409eff;
    }
    
    &.el-tag--success {
      background: #e8f5e8;
      border-color: #b3e0b3;
      color: #67c23a;
    }
    
    &.el-tag--warning {
      background: #fdf6ec;
      border-color: #f5c769;
      color: #e6a23c;
    }
    
    &.el-tag--danger {
      background: #fef0f0;
      border-color: #fbc4c4;
      color: #f56c6c;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
    gap: 16px;
    
    .action-buttons {
      justify-content: center;
    }
    
    .stats-info {
      justify-content: center;
    }
  }
  
  .search-section {
    .search-row {
      flex-direction: column;
      gap: 12px;
      
      .search-item {
        width: 100%;
      }
      
      .search-buttons {
        width: 100%;
        margin-left: 0;
        
        .search-btn {
          flex: 1;
        }
      }
    }
  }
  
  .page-info {
    justify-content: center;
    
    .page-title {
      font-size: 16px;
    }
  }
  
  .params-wrapper {
    max-width: 200px;
    
    .params-text {
      font-size: 12px;
    }
  }
}
</style>