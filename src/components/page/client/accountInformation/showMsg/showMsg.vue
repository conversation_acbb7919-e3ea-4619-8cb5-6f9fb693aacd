<template>
    <div class="simple-signature-page">
        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 标题信息区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <div class="page-info">
                                    <i class="el-icon-chat-line-square"></i>
                                    <span class="page-title">消息展示</span>
                                </div>
                            </div>

                            <!-- 统计信息 -->
                            <div class="stats-info">
                                <span class="stats-text">共 {{ pageTotal }} 条记录</span>
                                <el-divider direction="vertical"></el-divider>
                                <span class="stats-text">当前分类：{{ tableTitle }}</span>
                            </div>
                        </div>

                        <!-- 操作和搜索区域 -->
                        <div class="search-section">
                            <div class="operation-row">
                                <div class="operation-buttons">
                                    <el-button 
                                        v-permission
                                        @click="markRead" 
                                        class="action-btn primary" 
                                        icon="el-icon-circle-check">
                                        标记为已读
                                    </el-button>
                                    <el-button 
                                        v-permission
                                        @click="muchDel" 
                                        class="action-btn danger" 
                                        icon="el-icon-delete">
                                        删除消息
                                    </el-button>
                                </div>

                                <div class="search-controls">
                                    <el-input
                                        v-model="formInline.content"
                                        placeholder="请输入消息内容搜索"
                                        class="search-input"
                                        clearable>
                                        <i slot="suffix" class="el-input__icon el-icon-search"></i>
                                    </el-input>
                                    
                                    <el-date-picker
                                        v-model="formInline.createTime"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        @change="hande"
                                        style="width: 240px; margin-left: 12px;">
                                    </el-date-picker>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主要内容布局 -->
                <div class="content-layout">
                    <!-- 左侧消息分类 -->
                    <div class="sidebar-section">
                        <div class="sidebar-header">
                            <h3 class="sidebar-title">消息分类</h3>
                        </div>
                        <div class="sidebar-content">
                            <show-msg-left :showMsgData="msgData"></show-msg-left>
                        </div>
                    </div>

                    <!-- 右侧消息列表 -->
                    <div class="main-section">
                        <div class="table-section">
                            <div class="table-header">
                                <h3 class="table-title">{{ tableTitle }}</h3>
                                <span class="table-count">共 {{ pageTotal }} 条记录</span>
                            </div>

                            <div class="table-container">
                                <el-table
                                    v-loading="tableDataObj.loading2"
                                    element-loading-text="正在加载..."
                                    element-loading-spinner="el-icon-loading"
                                    element-loading-background="rgba(255, 255, 255, 0.8)"
                                    ref="multipleTable"
                                    :data="tableDataObj.tableData"
                                    :row-class-name="rowClass"
                                    class="enhanced-table"
                                    stripe
                                    :header-cell-style="{ background: '#fafafa', color: '#333' }"
                                    :row-style="{ height: '60px' }"
                                    empty-text="暂无消息数据"
                                    @selection-change="handleSelectionChange">
                                    
                                    <el-table-column type="selection" width="50" align="center"></el-table-column>
                                    
                                    <el-table-column 
                                        type="index" 
                                        :index="indexMethod" 
                                        label="序号" 
                                        width="80" 
                                        align="center">
                                    </el-table-column>
                                    
                                    <el-table-column 
                                        prop="messageType" 
                                        label="消息类别" 
                                        :formatter="formatData" 
                                        width="140" 
                                        align="center">
                                        <template slot-scope="scope">
                                            <el-tag 
                                                :type="getMessageTypeColor(scope.row.messageType)" 
                                                size="small">
                                                {{ formatData(scope.row, { property: 'messageType' }) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    
                                    <el-table-column 
                                        prop="messageContent" 
                                        label="消息内容" 
                                        min-width="300" 
                                        show-overflow-tooltip>
                                        <template slot-scope="scope">
                                            <div class="content-cell">
                                                <div class="message-content" :class="{ 'unread-message': scope.row.isReade == 2 }">
                                                    {{ scope.row.messageContent }}
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    
                                    <el-table-column 
                                        label="创建时间" 
                                        prop="createTime" 
                                        width="180">
                                        <template slot-scope="scope">
                                            <div class="content-cell">
                                                <i class="el-icon-time"></i>
                                                <span>{{ scope.row.createTime }}</span>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    
                                    <el-table-column label="状态" width="80" align="center">
                                        <template slot-scope="scope">
                                            <el-tag 
                                                :type="scope.row.isReade == 2 ? 'danger' : 'success'" 
                                                size="mini">
                                                {{ scope.row.isReade == 2 ? '未读' : '已读' }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>

                            <!-- 分页 -->
                            <div class="pagination-section">
                                <el-pagination
                                    class="simple-pagination"
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    :current-page="tabelAlllist.currentPage"
                                    :page-size="tabelAlllist.pageSize"
                                    :page-sizes="[10, 20, 50, 100]"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    :total="pageTotal">
                                </el-pagination>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import showMsgLeft from './components/showMsgLeft'
// 引入时间戳转换
import {formatDate} from '@/assets/js/date.js' 
let _ = require('lodash')
export default {
    name:'showMsg',
    components:{
        showMsgLeft
    },
    data(){
        return{
            tableTitle:'',//表格上面的展示消息
            hasSelectBox:[],//选择的复选框值
            msgData:{},//左侧msg消息条数
            isSelect:'',
            formInline:{
              content:'',
              type:'',//消息类型
              createTime:'',
              beginTime:'',
              endTime:'',
            },
            pageTotal:0,//分页的总条数
            tabelAlllist:{
              content:'',
              createTime:'',
              beginTime:'',
              endTime:'',
              type:'',//消息类别
              currentPage:1,//当前页
              pageSize:10,//每一页条数
            },
           
            //  时间插件配置
            datePluginValueList: {
                type:"date",
                placeholder:'请选择时间'
           },
                    tableDataObj:{
                        loading2:false,
                        // 表格的数据
                        tableData: [],    
            }, 
        }
    },
      watch:{
            formInline:{
                handler(val, oldVal){
                    for(let key in this.formInline){
                      this.tabelAlllist[key]= this.formInline[key] ;
                    }
                    this.tabelAlllist.currentPage=1;
                    this.tabelAlllist.pageSize=10;
                    this.getCompany();
                },
                deep:true
            },
           
             $route:{//监听路由的变化
                 handler(){
                    let msgType=this.$route.query.msg;
                    if(msgType==1){
                        this.formInline.type=''   
                        this.tableTitle='全部消息'
                    }else if(msgType==2){
                        this.formInline.type='2'
                        this.tableTitle='预警通知'
                    }else if(msgType==3){
                        this.formInline.type='1'
                        this.tableTitle='系统消息'
                    }else if(msgType==4){
                        this.formInline.type='4'
                        this.tableTitle='活动福利'
                    }else if(msgType==5){
                        this.formInline.type='3'
                        this.tableTitle='审批提醒'
                    }else if(msgType==6){
                        this.formInline.type='5'
                        this.tableTitle='公告通知'
                    }
                    this.searchEmpty();//清空查询条件
                 },
                immediate:true
            }
        },
    methods:{
        searchEmpty(){
              console.log(22)
                this.formInline.content='';
                this.formInline.createTime='';
                this.tabelAlllist.content='';
                this.tabelAlllist.beginTime='';
                this.tabelAlllist.endTime='';
                this.tabelAlllist.currentPage=1;
                this.tabelAlllist.pageSize=10;
                
                this.getTableData()
        },
         /* --------------- 列表展示 ------------------*/
        getTableData(){//获取列表数据
            this.tableDataObj.loading2 = true
            let formDatas = this.tabelAlllist; 
            this.$api.post(this.API.cpus+'consumerclientmessage/page',formDatas,res=>{
                this.tableDataObj.tableData = res.records;
                this.tableDataObj.loading2 = false;
                this.msgData=res.map;
                this.pageTotal=res.total;
            })
        },
        hande: function (val) { //获取查询时间框的值
            if(val){
                this.formInline.beginTime = this.moment(val[0]).format("YYYY-MM-DD ");
                this.formInline.endTime = this.moment(val[1]).format("YYYY-MM-DD ");  
            } else{
                this.formInline.beginTime = '';
                this.formInline.endTime = '';  
            }          
        }, 
         handleSizeChange(size) { //分页每一页的有几条
            this.tabelAlllist.pageSize = size;
            this.getTableData();
        },
        handleCurrentChange: function(currentPage){//分页的第几页
            this.tabelAlllist.currentPage = currentPage;
            this.getTableData();
        },
        //表格数据转换
        formatData:function(row,column){
            if(column.property=='messageType'){
                let msgType;
                if(row[column.property]==1){
                    msgType='系统消息'
                }else if(row[column.property]==2){
                    msgType='预警通知'
                }else if(row[column.property]==3){
                    msgType='审批提醒'
                }else if(row[column.property]==4){
                    msgType='活动福利'
                }else if(row[column.property]==5){
                    msgType='公告通知'
                }
               return msgType;
            }else if(column.property=='createTime'){
                return formatDate(new Date(row[column.property]*1000), 'yyyy-MM-dd hh:mm:ss');
            }   
        },
        // 设置行的颜色
        rowClass:function(row, rowIndex) {
            if(row.row.isReade==2){//2为未读
                return 'normalClass'
            }else {
                return 'noReadClass'
            }
        },
        //标记为已读
        markRead:function () {
            if(this.hasSelectBox.length==0){
               this.dialogAlert('请至少选择一条消息 "标记已读"')
            }else {
                let messageIds=[]
                for(let i=0;i<this.hasSelectBox.length;i++){
                    messageIds.push(this.hasSelectBox[i].messageId)
                }
                //发送请求
                this.$confirms.confirmation('get','确定标记已读？',this.API.cpus+'consumerclientmessage/markAsRead/'+messageIds,{},res =>{
                    this.getTableData();
                });
            }
            this.isSelect=false  
        },
        //删除
        muchDel:function () {
            if(this.hasSelectBox.length==0){
               this.dialogAlert('请至少选择一条消息 "删除"')
            }else {
              let messageIds=[]
                for(let i=0;i<this.hasSelectBox.length;i++){
                    messageIds.push(this.hasSelectBox[i].messageId)
                }
                //发送请求
                this.$confirms.confirmation('get','此操作将永久删除该数据, 是否继续？',this.API.cpus+'consumerclientmessage/delete/'+messageIds,{},res =>{
                    this.getTableData();
                });
            }
        },
        //弹窗
        dialogAlert:function(msg){
           this.$alert(msg, '温馨提示', {
                    confirmButtonText: '确定',
                    });
        },
         /**
       * 表格公共的方法--start
       * **/
      //复选框选择
      handleSelectionChange(val){
            const selectionArr = [];
            val.forEach(function (el) {
                selectionArr.push(el);
            });
            this.hasSelectBox=selectionArr

      }, 
       /**
       * 表格公共的方法--end
       * **/ 
        indexMethod(index) {
            return index += 1;
        },
        // 获取消息类型对应的标签颜色
        getMessageTypeColor(messageType) {
            const colorMap = {
                1: 'success',     // 系统消息
                2: 'warning',     // 预警通知
                3: 'info',        // 审批提醒
                4: 'primary',     // 活动福利
                5: ''             // 公告通知
            };
            return colorMap[messageType] || '';
        }
    },
    created(){
        this.getTableData(); 
        this.getCompany= _.debounce(function(){
            this.getTableData()
        }, 500)

    }
}
</script>
<style lang="less" scoped>
@import '~@/styles/template-common.less';

// 组件特有的样式定制
.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  
  i {
    font-size: 20px;
    color: #409eff;
  }
}

.content-layout {
  display: flex;
  gap: 20px;
  margin-top: 0;
}

.sidebar-section {
  width: 250px;
  flex-shrink: 0;
  background: #f7f8fa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.sidebar-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.sidebar-content {
  height: calc(100vh - 280px);
  overflow-y: auto;
}

.main-section {
  flex: 1;
  min-width: 0;
}

.operation-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.operation-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  &.danger {
    background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
    border-color: #f56c6c;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #f78989 0%, #f9a3a4 100%);
      border-color: #f78989;
      color: white;
    }
  }
}

.search-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-input {
  width: 250px;
}

.content-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .message-content {
    line-height: 1.4;
    color: #666;
    
    &.unread-message {
      font-weight: 600;
      color: #333;
    }
  }
  
  i {
    color: #909399;
    margin-right: 4px;
  }
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

// 表格行样式
.enhanced-table {
  /deep/ .normalClass {
    color: #999;
  }
  
  /deep/ .noReadClass {
    font-weight: 600;
    
    td {
      background-color: #f8f9ff !important;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .content-layout {
    flex-direction: column;
    gap: 16px;
  }
  
  .sidebar-section {
    width: 100%;
    height: auto;
  }
  
  .sidebar-content {
    height: auto;
    max-height: 300px;
  }
  
  .operation-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-controls {
    justify-content: center;
    
    .search-input {
      width: 100%;
      max-width: 300px;
    }
  }
}

@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
    gap: 16px;
    
    .action-buttons {
      justify-content: center;
    }
    
    .stats-info {
      justify-content: center;
    }
  }
  
  .operation-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .search-controls {
    flex-direction: column;
    gap: 12px;
  }
  
  .page-info {
    justify-content: center;
    
    .page-title {
      font-size: 16px;
    }
  }
}
</style>



