<template>
  <div class="enhanced-sidebar">
    <div class="message-categories">
      <div class="category-menu">
        <div 
          v-for="category in categories" 
          :key="category.index"
          :class="['category-item', { 'active': isActive(category.index) }]"
          @click="selectMsg(category.index)">
          
          <div class="category-content">
            <div class="category-icon">
              <i :class="category.icon" :style="{ backgroundColor: category.color }"></i>
            </div>
            
            <div class="category-info">
              <div class="category-title">{{ category.title }}</div>
              <div class="category-count">共 {{ getCategoryCount(category.dataKey) }} 条</div>
            </div>
            
            <div class="category-badge" v-if="getUnreadCount(category.unreadKey) > 0">
              <span class="badge-count">{{ getUnreadCount(category.unreadKey) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "showMsgNav",
  components: {},
  props: {
    showMsgData: {
      type: Object,
      default: () => ({})
    },
  },
  computed: {
    defaultActive() {
      return this.$route.query.msg ? this.$route.query.msg : "1";
    },
    categories() {
      return [
        {
          index: "1",
          title: "全部消息",
          icon: "el-icon-chat-line-square",
          color: "#409eff",
          dataKey: "count",
          unreadKey: "unread"
        },
        {
          index: "6",
          title: "公告通知",
          icon: "el-icon-bell",
          color: "#ffb800",
          dataKey: "advertisement",
          unreadKey: "advertisementUnread"
        },
        {
          index: "2",
          title: "预警通知",
          icon: "el-icon-warning",
          color: "#f56c6c",
          dataKey: "warning",
          unreadKey: "warningUnread"
        },
        {
          index: "3",
          title: "系统消息",
          icon: "el-icon-message",
          color: "#67c23a",
          dataKey: "system",
          unreadKey: "systemUnread"
        },
        {
          index: "4",
          title: "活动福利",
          icon: "el-icon-present",
          color: "#e6a23c",
          dataKey: "activity",
          unreadKey: "activityUnread"
        },
        {
          index: "5",
          title: "审批提醒",
          icon: "el-icon-circle-check",
          color: "#909399",
          dataKey: "audit",
          unreadKey: "auditUnread"
        }
      ];
    }
  },
  methods: {
    selectMsg(index) {
      this.$router.push({ path: "showMsg", query: { msg: index } });
    },
    isActive(index) {
      return this.defaultActive === index;
    },
    getCategoryCount(dataKey) {
      return this.showMsgData[dataKey] || 0;
    },
    getUnreadCount(unreadKey) {
      return this.showMsgData[unreadKey] || 0;
    }
  },
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
@import '~@/styles/template-common.less';

.enhanced-sidebar {
  height: 100%;
  background: #f7f8fa;
}

.message-categories {
  padding: 8px;
}

.category-menu {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-item {
  position: relative;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
  border: 1px solid #e8e8e8;
  
  &:hover {
    background: #f0f9ff;
    border-color: #409eff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }
  
  &.active {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border-color: #409eff;
    color: white;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    
    .category-title {
      color: white;
      font-weight: 600;
    }
    
    .category-count {
      color: rgba(255, 255, 255, 0.9);
    }
    
    .category-icon i {
      color: white !important;
      background: rgba(255, 255, 255, 0.2) !important;
    }
  }
}

.category-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.category-icon {
  flex-shrink: 0;
  
  i {
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
  }
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.category-count {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.category-badge {
  flex-shrink: 0;
  position: relative;
}

.badge-count {
  display: inline-block;
  min-width: 20px;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
  background: #f56c6c;
  color: white;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
  
  .category-item.active & {
    background: rgba(255, 255, 255, 0.9);
    color: #f56c6c;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .category-content {
    padding: 12px;
    gap: 10px;
  }
  
  .category-icon i {
    width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 14px;
  }
  
  .category-title {
    font-size: 13px;
  }
  
  .category-count {
    font-size: 11px;
  }
  
  .badge-count {
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    font-size: 10px;
  }
}

// 平滑过渡动画
.category-item {
  &:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
  }
}

// 特殊状态样式
.category-item:first-child {
  .category-icon i {
    background: #409eff;
  }
}

.category-item:nth-child(2) {
  .category-icon i {
    background: #ffb800;
  }
}

.category-item:nth-child(3) {
  .category-icon i {
    background: #f56c6c;
  }
}

.category-item:nth-child(4) {
  .category-icon i {
    background: #67c23a;
  }
}

.category-item:nth-child(5) {
  .category-icon i {
    background: #e6a23c;
  }
}

.category-item:nth-child(6) {
  .category-icon i {
    background: #909399;
  }
}
</style>