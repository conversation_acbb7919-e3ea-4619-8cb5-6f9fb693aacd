<template>
    <!-- 使用统一的页面布局容器 -->
    <div class="simple-signature-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">个人信息设置</h1>
            <p class="page-subtitle">管理您的账户基本信息、接口配置和余额提醒设置</p>
        </div>

        <!-- 页面内容区域 -->
        <div class="page-content">
            <!-- 基本信息卡片 -->
            <div class="info-card">
                <div class="card-header">
                    <i class="el-icon-user"></i>
                    <span class="card-title">基本信息</span>
                </div>
                
                <div class="card-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <label class="info-label">用户名</label>
                            <span class="info-value">{{ consumerName }}</span>
                        </div>
                        
                        <div class="info-item">
                            <label class="info-label">公司名称</label>
                            <span class="info-value">{{ compName }}</span>
                        </div>
                        
                        <div class="info-item">
                            <label class="info-label">创建时间</label>
                            <span class="info-value">{{ createTime }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 接口配置卡片 -->
            <div v-if="roleId != '12'" class="info-card">
                <div class="card-header">
                    <i class="el-icon-setting"></i>
                    <span class="card-title">接口配置</span>
                </div>
                
                <div class="card-content">
                    <div class="config-section">
                        <div class="config-item">
                            <label class="config-label">接口密码</label>
                            <div class="config-control">
                                <span class="password-display" v-if="ispwd == false">******</span>
                                <span class="password-display" v-if="ispwd == true">{{ password }}</span>
                                <el-button 
                                    :disabled="loginInfo.isAdmin == 1 ? false : true"
                                    type="text" 
                                    class="config-button"
                                    @click="modifyPsd('pwd')">
                                    修改接口密码
                                </el-button>
                                <el-tooltip class="item" effect="dark" content="修改接口密码请联系管理员！" placement="top">
                                    <i v-if="loginInfo.isAdmin != 1" class="el-icon-question help-icon"></i>
                                </el-tooltip>
                            </div>
                            <div class="config-tip">
                                接口密码是用来校验短信发送请求合法性的密码，与用户名对应，需要业务方高度保密，切勿把密码存储在客户端。
                            </div>
                        </div>

                        <div v-if="cipherMode == 2" class="config-item">
                            <label class="config-label">加密盐</label>
                            <div class="config-control">
                                <span class="password-display">******</span>
                                <el-button 
                                    :disabled="loginInfo.isAdmin == 1 ? false : true"
                                    type="text" 
                                    class="config-button"
                                    @click="editSalt('salt')">
                                    修改加密盐
                                </el-button>
                                <el-tooltip class="item" effect="dark" content="修改加密盐请联系管理员！" placement="top">
                                    <i v-if="loginInfo.isAdmin != 1" class="el-icon-question help-icon"></i>
                                </el-tooltip>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 余额提醒设置 -->
            <div v-if="roleId != '12'" class="info-card">
                <div class="card-header">
                    <i class="el-icon-warning"></i>
                    <span class="card-title">余额提醒设置</span>
                </div>
                
                <div class="card-content">
                    <div class="balance-grid">
                        <!-- 短信余额提醒 -->
                        <div class="balance-item">
                            <div class="balance-header">
                                <h4 class="balance-title">短信余额提醒</h4>
                                <el-button 
                                    v-if="requencyEnable == false" 
                                    type="primary" 
                                    size="small"
                                    @click="handleRequencyEnable">
                                    启用
                                </el-button>
                            </div>
                            
                            <div v-if="requencyEnable == true" class="balance-config">
                                <transition name="fade">
                                    <div class="balance-content" v-show="sendfrequency">
                                        <p class="balance-info">
                                            当前账号余额条数在不足 <span class="highlight">{{ reminderBalances }}</span> 条时提醒
                                        </p>
                                        <p class="balance-note">注：请去添加告警联系人，以便正常接收余额不足通知！</p>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="SetBalance">设置</el-button>
                                            <el-button v-permission type="primary" size="small" @click="shutDownSetBalance">关闭设置</el-button>
                                        </div>
                                    </div>
                                </transition>
                                
                                <transition name="fade">
                                    <div class="balance-content" v-show="!sendfrequency">
                                        <div class="balance-input-row">
                                            <span>当前账号余额条数在不足</span>
                                            <el-input v-model="NumberBalances" class="balance-input"></el-input>
                                            <span>条时提醒</span>
                                        </div>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="determine">确定</el-button>
                                            <el-button size="small" @click="sendfrequency = true">取消</el-button>
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>

                        <!-- 视频短信余额提醒 -->
                        <div class="balance-item">
                            <div class="balance-header">
                                <h4 class="balance-title">视频短信余额提醒</h4>
                                <el-button 
                                    v-if="requencyEnable3 == false" 
                                    type="primary" 
                                    size="small"
                                    @click="handleRequencyEnable3">
                                    启用
                                </el-button>
                            </div>
                            
                            <div v-if="requencyEnable3 == true" class="balance-config">
                                <transition name="fade">
                                    <div class="balance-content" v-show="sendfrequency3">
                                        <p class="balance-info">
                                            当前账号余额条数在不足 <span class="highlight">{{ reminderBalances3 }}</span> 条时提醒
                                        </p>
                                        <p class="balance-note">注：请去添加告警联系人，以便正常接收余额不足通知！</p>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="SetBalance3">设置</el-button>
                                            <el-button v-permission type="primary" size="small" @click="shutDownSetBalance3">关闭设置</el-button>
                                        </div>
                                    </div>
                                </transition>
                                
                                <transition name="fade">
                                    <div class="balance-content" v-show="!sendfrequency3">
                                        <div class="balance-input-row">
                                            <span>当前账号余额条数在不足</span>
                                            <el-input v-model="NumberBalances3" class="balance-input"></el-input>
                                            <span>条时提醒</span>
                                        </div>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="determine3">确定</el-button>
                                            <el-button size="small" @click="sendfrequency3 = true">取消</el-button>
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>

                        <!-- 国际短信余额提醒 -->
                        <div class="balance-item">
                            <div class="balance-header">
                                <h4 class="balance-title">国际短信余额提醒</h4>
                                <el-button 
                                    v-if="requencyEnable4 == false" 
                                    type="primary" 
                                    size="small"
                                    @click="handleRequencyEnable4">
                                    启用
                                </el-button>
                            </div>
                            
                            <div v-if="requencyEnable4 == true" class="balance-config">
                                <transition name="fade">
                                    <div class="balance-content" v-show="sendfrequency4">
                                        <p class="balance-info">
                                            当前账号余额条数在不足 <span class="highlight">{{ reminderBalances4 }}</span> 元时提醒
                                        </p>
                                        <p class="balance-note">注：请去添加告警联系人，以便正常接收余额不足通知！</p>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="SetBalance4">设置</el-button>
                                            <el-button v-permission type="primary" size="small" @click="shutDownSetBalance4">关闭设置</el-button>
                                        </div>
                                    </div>
                                </transition>
                                
                                <transition name="fade">
                                    <div class="balance-content" v-show="!sendfrequency4">
                                        <div class="balance-input-row">
                                            <span>当前账号余额条数在不足</span>
                                            <el-input v-model="NumberBalances4" class="balance-input"></el-input>
                                            <span>元时提醒</span>
                                        </div>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="determine4">确定</el-button>
                                            <el-button size="small" @click="sendfrequency4 = true">取消</el-button>
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>

                        <!-- 闪验余额提醒 -->
                        <div class="balance-item">
                            <div class="balance-header">
                                <h4 class="balance-title">闪验余额提醒</h4>
                                <el-button 
                                    v-if="requencyEnable5 == false" 
                                    type="primary" 
                                    size="small"
                                    @click="handleRequencyEnable5">
                                    启用
                                </el-button>
                            </div>
                            
                            <div v-if="requencyEnable5 == true" class="balance-config">
                                <transition name="fade">
                                    <div class="balance-content" v-show="sendfrequency5">
                                        <p class="balance-info">
                                            当前账号余额条数在不足 <span class="highlight">{{ reminderBalances5 }}</span> 条时提醒
                                        </p>
                                        <p class="balance-note">注：请去添加告警联系人，以便正常接收余额不足通知！</p>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="SetBalance5">设置</el-button>
                                            <el-button v-permission type="primary" size="small" @click="shutDownSetBalance5">关闭设置</el-button>
                                        </div>
                                    </div>
                                </transition>
                                
                                <transition name="fade">
                                    <div class="balance-content" v-show="!sendfrequency5">
                                        <div class="balance-input-row">
                                            <span>当前账号余额条数在不足</span>
                                            <el-input v-model="NumberBalances5" class="balance-input"></el-input>
                                            <span>条时提醒</span>
                                        </div>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="determine5">确定</el-button>
                                            <el-button size="small" @click="sendfrequency5 = true">取消</el-button>
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>

                        <!-- 语音验证码余额提醒 -->
                        <div class="balance-item">
                            <div class="balance-header">
                                <h4 class="balance-title">语音验证码余额提醒</h4>
                                <el-button 
                                    v-if="requencyEnable6 == false" 
                                    type="primary" 
                                    size="small"
                                    @click="handleRequencyEnable6">
                                    启用
                                </el-button>
                            </div>
                            
                            <div v-if="requencyEnable6 == true" class="balance-config">
                                <transition name="fade">
                                    <div class="balance-content" v-show="sendfrequency6">
                                        <p class="balance-info">
                                            当前账号余额条数在不足 <span class="highlight">{{ reminderBalances6 }}</span> 条时提醒
                                        </p>
                                        <p class="balance-note">注：请去添加告警联系人，以便正常接收余额不足通知！</p>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="SetBalance6">设置</el-button>
                                            <el-button v-permission type="primary" size="small" @click="shutDownSetBalance6">关闭设置</el-button>
                                        </div>
                                    </div>
                                </transition>
                                
                                <transition name="fade">
                                    <div class="balance-content" v-show="!sendfrequency6">
                                        <div class="balance-input-row">
                                            <span>当前账号余额条数在不足</span>
                                            <el-input v-model="NumberBalances6" class="balance-input"></el-input>
                                            <span>条时提醒</span>
                                        </div>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="determine6">确定</el-button>
                                            <el-button size="small" @click="sendfrequency6 = true">取消</el-button>
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>

                        <!-- 语音通知余额提醒 -->
                        <div class="balance-item">
                            <div class="balance-header">
                                <h4 class="balance-title">语音通知余额提醒</h4>
                                <el-button 
                                    v-if="requencyEnable7 == false" 
                                    type="primary" 
                                    size="small"
                                    @click="handleRequencyEnable7">
                                    启用
                                </el-button>
                            </div>
                            
                            <div v-if="requencyEnable7 == true" class="balance-config">
                                <transition name="fade">
                                    <div class="balance-content" v-show="sendfrequency7">
                                        <p class="balance-info">
                                            当前账号余额条数在不足 <span class="highlight">{{ reminderBalances7 }}</span> 条时提醒
                                        </p>
                                        <p class="balance-note">注：请去添加告警联系人，以便正常接收余额不足通知！</p>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="SetBalance7">设置</el-button>
                                            <el-button v-permission type="primary" size="small" @click="shutDownSetBalance7">关闭设置</el-button>
                                        </div>
                                    </div>
                                </transition>
                                
                                <transition name="fade">
                                    <div class="balance-content" v-show="!sendfrequency7">
                                        <div class="balance-input-row">
                                            <span>当前账号余额条数在不足</span>
                                            <el-input v-model="NumberBalances7" class="balance-input"></el-input>
                                            <span>条时提醒</span>
                                        </div>
                                        <div class="balance-actions">
                                            <el-button v-permission type="primary" size="small" @click="determine7">确定</el-button>
                                            <el-button size="small" @click="sendfrequency7 = true">取消</el-button>
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个性化设置对话框 -->
        <el-dialog title="个性化设置" :visible.sync="Personalization" :close-on-click-modal=false width="500px">
            <el-form :inline="true" :model="formLogo" :rules="formLogoRules" ref="formLogos"
                class="demo-form-inline IPForm">
                <el-form-item label="域名" label-width="100px" prop="domainName">
                    <el-input type="text" v-model="formLogo.domainName" disabled placeholder=" 请输入域名"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="登录提示语" label-width="100px" prop="reminder">
                    <el-input type="text" v-model="formLogo.reminder" disabled placeholder=" 请输入登录页面提示语"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="短信签名" label-width="100px" prop="signature">
                    <el-input type="text" v-model="formLogo.signature" disabled placeholder=" 请输入接收短信登录验证码的签名"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="登录title" label-width="100px" prop="title">
                    <el-input type="text" v-model="formLogo.title" disabled placeholder=" 请输入登录后title"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="备案信息" label-width="100px" prop="recordInformation">
                    <el-input type="text" v-model="formLogo.recordInformation" disabled placeholder=" 请输入备案信息"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="企业icon" label-width="100px" prop="">
                    <el-upload class="upload-demo" :action="actionHttp1" :headers="token" :limit="1"
                        :on-preview="handlePreview" :on-remove="handleRemove" :file-list="fileList"
                        :on-success="handleAvatarSuccess1" :before-upload="beforeAvatarUpload1" :disabled="true"
                        list-type="picture">
                    </el-upload>
                </el-form-item>
                <el-form-item label="企业logo" label-width="100px" prop="">
                    <el-upload class="upload-demo" :action="actionHttp1" :headers="token" :limit="1"
                        :on-preview="handlePreview1" :on-remove="handleRemove1" :file-list="fileList1"
                        :on-success="handleAvatarSuccess2" :before-upload="beforeAvatarUpload2" :disabled="true"
                        list-type="picture">
                    </el-upload>
                </el-form-item>
            </el-form>
        </el-dialog>

        <!-- 接口密码修改对话框 -->
        <el-dialog title="接口密码修改" :visible.sync="dialogFormVisible" width="520px" :close-on-click-modal="false"
            :before-close="beforec" class="dialogBasics">
            <el-form label-position="right" ref="passWordForm" :rules="formRules" :model="passWordForm.formData"
                label-width="100px">
                <div class="Login-c-p-getPhone">
                    验证码将会发送至管理员绑定的手机号：{{ loginInfo.mobile }}，请注意查收！
                </div>
                <el-form-item label="手机验证码" prop="verifyCode"
                    :rules="filter_rules({ required: true, type: 'code', message: '验证码必填！' })">
                    <el-input v-model="passWordForm.formData.verifyCode" style="width:250px;"></el-input>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" @click.prevent="send"
                        v-if="nmb == 120">获取验证码</el-button>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{ nmb
                    }})</el-button>
                </el-form-item>
                <el-form-item label="接口密码" prop="password">
                    <el-input show-password v-model="passWordForm.formData.password"
                        placeholder="密码由数字、大小写字母组成，密码长度8-16位"></el-input>
                </el-form-item>
                <div style="margin-top: 30px;">
                    <el-form-item label="确认密码" prop="confirmPwd">
                        <el-input show-password v-model="passWordForm.formData.confirmPwd"
                            placeholder="密码由数字、大小写字母组成，密码长度8-16位"></el-input>
                    </el-form-item>
                </div>
            </el-form>
            <div class="passWord-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button v-permission @click="updateFormOk('passWordForm')" type="primary">确定</el-button>
            </div>
        </el-dialog>

        <!-- 修改加密盐对话框 -->
        <el-dialog title="修改加密盐" :visible.sync="dialogSaltVisible" width="520px" :close-on-click-modal="false"
            :before-close="beforec" class="dialogBasics">
            <el-form label-position="right" ref="saltForm" :rules="saltForm.formRule" :model="saltForm.formData"
                label-width="100px">
                <div class="Login-c-p-getPhone">
                    验证码将会发送至管理员绑定的手机号：{{ loginInfo.mobile }}，请注意查收！
                </div>
                <el-form-item label="手机验证码" prop="verifyCode"
                    :rules="filter_rules({ required: true, type: 'code', message: '验证码必填！' })">
                    <el-input v-model="saltForm.formData.verifyCode" style="width:250px;"></el-input>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" @click.prevent="send"
                        v-if="nmb == 120">获取验证码</el-button>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{ nmb
                    }})</el-button>
                </el-form-item>
                <el-form-item label="加密盐" prop="salt">
                    <el-input v-model="saltForm.formData.salt"></el-input>
                </el-form-item>
            </el-form>
            <div class="passWord-footer">
                <el-button @click="dialogSaltVisible = false">取消</el-button>
                <el-button @click="updateSalt('saltForm')" type="primary">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
export default {
    name: 'PersonalInformation',
    data() {
        // 验证IP规则
        var code = (rule, value, callback) => {
            // if (!this.phoneData) {
            //     return callback(new Error('请选中手机号'));
            // } else 
            if (value == "") {
                return callback(new Error('请输入验证码'));
            } else {
                callback();
            }
        };
        var oldPassword = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('原密码不能为空'));
            } else {
                this.$api.get(this.API.cpus + 'consumerclientinfo/validatePassword/' + this.formPassword.oldPassword, {}, res => {
                    if (res.code == 200) {
                        callback();
                    } else {
                        callback(new Error('与原密码不相符'));
                    }
                })
            }
        };
        var validatePass = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('新密码不能为空'));
            } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g.test(value)) {
                callback(new Error('请输入正确的密码格式！密码由数字、大小写字母组成，密码长度8-16位，且不能包含中文'));
            } else {
                if (this.formPassword.passWord !== '') {
                    this.$refs.formRule.validateField('passWord');
                }
                callback();
            }
        };
        var validatePass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('密码不能为空'));
            } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g.test(value)) {
                callback(new Error('请输入正确的密码格式！密码由数字、大小写字母组成，密码长度8-16位，且不能包含中文'));
            } else if (value !== this.passWordForm.formData.password) {
                callback(new Error('两次输入密码不一致!'));
            } else {
                callback();
            }
        };
        return {
            SendSettings: {
                overrunCode: "",
                overrunIndustry: "",
                overrunMarket: ""
            },
            SendSettingsArry: ['1', '2', '3', '4', '5', '6'],
            SendSettingsArrys: ['1', '2', '3', '4', '5', '6', "7", "8", "9", "10"],
            sendfrequency: true,
            sendfrequency1: true,
            sendfrequency3: true,
            sendfrequency4: true,
            sendfrequency5: true,
            sendfrequency6: true,
            sendfrequency7: true,
            requencyEnable: false,
            requencyEnable1: false,
            requencyEnable3: false,
            requencyEnable4: false,
            requencyEnable5: false,
            requencyEnable6: false,
            requencyEnable7: false,
            ispwd: false,
            active: 1,
            activeSalt: 1,
            cipherMode: "",//加密方式
            LcpDialogVisible: false,//手机验证弹出框显示隐藏
            setPhoneSteps: 1, // 设置手机号的步骤
            saltForm: {
                formData: {
                    flag: "1",
                    verifyCode: "",
                    salt: "",
                },
                formRule: {
                    salt: [
                        { required: true, message: '请输入加密盐', trigger: 'blur' },
                    ],
                    verifyCode: [
                        { required: true, validator: code, trigger: 'blur' }
                    ]
                }
            },
            passWordForm: {//修改密码的表格--下面表格
                formData: {
                    verifyCode: "",
                    password: '',
                    confirmPwd:"",
                    flag: '1',
                    // product:[]
                },
                // formRule:{
                //     product: [
                //         { type: 'array', required: true, message: '请至少选择一个适用产品', trigger: 'change' }
                //     ]
                // }
            },
            // 余额条数
            NumberBalances: '',
            NumberBalances1: '',
            NumberBalances3: '',
            NumberBalances4: '',
            NumberBalances5: '',
            NumberBalances6: '',
            NumberBalances7: '',
            //余额提醒条数
            reminderBalances: '',
            reminderBalances1: '',
            reminderBalances3: '',
            reminderBalances4: '',
            reminderBalances5: '',
            reminderBalances6: '',
            reminderBalances7: '',
            updateFormDialog: {//修改接口密码--弹窗
                formData: {
                    code: ""//验证码
                },
            },
            dialogFormVisible: false,//弹窗显示状态
            dialogSaltVisible: false,
            //弹窗里的表格
            dialogTable: [],
            templateRadio: 0,
            templateSelection: '',
            nmb: 120,
            // ---------------------
            imageUrl: '',
            actionHttp: '',
            isIndividualization: '',
            token: {},
            passWordFlag: false,
            formPassword: {
                // oldPassword:"",
                newPassword: '',
                passWord: '',
                confirmPwd: "",//确认密码
            },
            formLogo: {
                userName: this.$store.state.userName,
                domainName: "",
                reminder: "",
                signature: "",
                title: "",
                recordInformation: "",
                icon: "",
                logo: ""
            },
            changePassword: false,
            Personalization: false,
            formRules: {
                oldPassword: [
                    // { required: true, message: '原密码不能为空', trigger: 'blur' },
                    { required: true, validator: oldPassword, trigger: 'blur' },
                    // ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$
                    // {pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/gi,message:'密码必须包含数字以及大小写字母8-16位'}
                ],
                newPassword: [
                    { required: true, validator: validatePass, trigger: 'blur' }
                ],
                password: [
                    { required: true, validator: validatePass2, trigger: 'blur' }
                ],
                confirmPwd: [
                    { required: true, validator: validatePass2, trigger: 'blur' }
                ],
                verifyCode: [
                    { required: true, validator: code, trigger: 'blur' }
                ]
            },
            formLogoRules: {
                domainName: [
                    { required: true, message: '请输入域名', trigger: 'blur' },
                    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
                ],
                reminder: [
                    { required: true, message: '请输入提示语', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
                signature: [
                    { required: true, message: '请输入签名', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
                title: [
                    { required: true, message: '请输入登录title', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
                recordInformation: [
                    { required: true, message: '请输入备案信息', trigger: 'blur' },
                    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
                ]
            },
            fileList: [],
            fileList1: [],

            nmb: 120,
            radio: "",
            tableD: [],
            setphoneFrom: {
                ruleForm1: {
                    verCode: '',
                },
                rules1: {
                    verCode: [
                        { required: true, validator: code, trigger: 'blur' },
                        { min: 6, max: 6, message: '请输入6位数字验证码' }
                    ]
                },
                ruleForm2: {
                    setNewPhone: '',
                },
            },
            timer: null,
            loginInfo: {
                isAdmin: null,
                consumerName: "",
                mobile: "",
                remark: "",
                id: ""
            },
        }
    },
    computed: {
        ...mapState({  //比如'movies/hotMovies
            compName: state => state.compName,
            consumerName: state => state.userName,
            createTime: state => state.createLocalDateTime,
            userID: state => state.userId,
            roleId: state => state.roleId,
        })
    },
    methods: {
        //点击修改密码按钮
        modifyPsd(type) {
            this.getPhoneArr(type);
        },
        editSalt(type) {
            this.getPhoneArr(type)

        },
        getLoginInfo() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/loginPhoneInfo",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginInfo = res.data;
                    }
                }
            );
        },
        getLoginInfo() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/loginPhoneInfo",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginInfo = res.data;
                    }
                }
            );
        },
        //获取弹出框手机号
        getPhoneArr(type) {
            this.dialogTable = [];
            this.$api.post(this.API.cpus + 'consumerclientinfo/loginTelephoneManager/list', {}, res => {
                let phonelength = res.data.data;
                this.templateSelection = phonelength[0].mobile; //把第一个手机号设置为默认选中

                for (var i = 0; i < phonelength.length; i++) {
                    this.dialogTable.push({ phone: phonelength[i].mobile })
                }
                if (type == 'pwd') {
                    this.dialogFormVisible = true;//显示弹窗
                } else {
                    this.dialogSaltVisible = true
                }

            })
        },
        //获取验证码
        send() {
            this.$api.get(this.API.cpus + "userLoginAdmin/sendVerificationCode?flag=1&phoneId=" + this.loginInfo.id, {}, res => {
                if (res.code == 200) {
                    this.$message({
                        type: 'success',
                        duration: '2000',
                        message: '验证码已发送至手机!'
                    });
                    --this.nmb;
                    this.timer = setInterval(res => {
                        --this.nmb;
                        if (this.nmb < 1) {
                            this.nmb = 120;
                            clearInterval(this.timer);
                        };
                    }, 1000);
                } else {
                    this.$message({
                        type: 'warning',
                        message: '验证码未失效，请失效后再次申请!'
                    });
                }

            })
            // if (this.templateSelection) {

            // } else {
            //     this.$message({
            //         message: '选择发送验证码的手机号码！',
            //         type: 'warning'
            //     });
            // }
        },
        ispeds() {
            this.ispwd = !this.ispwd;
        },
        //获取选中手机号码
        getTemplateRow(index, row) {
            this.templateSelection = row.phone;
        },
        beforec() {
            this.dialogFormVisible = false;//隐藏弹窗
            this.dialogSaltVisible = false
        },
        updateFormDialog_ok(formName, type) { //弹框第一步确认
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    //提交验证码
                    this.$api.get(this.API.cpus + "code/checkVerificationCode?code=" + this.updateFormDialog.formData.code + '&flag=1', {}, res => {
                        if (res.code == 200) {
                            this.nmb = 120;
                            clearInterval(this.timer);
                            if (type == 'pwd') {
                                this.active = 2;
                            } else {
                                this.activeSalt = 2
                            }

                        } else {
                            this.$message.error('验证码无效!');
                        }
                    })
                } else {
                    return false;
                }
            });
        },
        handelpwd(val) {
            if (this.passWordForm.formData.confirmPwd) {
                // console.log(this.passWordForm.formData.confirmPwd, 'll');
                if (val === this.passWordForm.formData.confirmPwd) {
                    this.passWordFlag = false
                } else {
                    this.passWordFlag = true
                }
            }
        },
        handelconfirm(val) {
            if (val === this.passWordForm.formData.password) {
                this.passWordFlag = false
            } else {
                this.passWordFlag = true
            }
        },
        updateFormOk(formName) {//弹框第二步确认
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        verifyCode: this.passWordForm.formData.verifyCode,
                        password: this.passWordForm.formData.password,
                        flag: this.passWordForm.formData.flag,
                    }
                    if (this.passWordForm.formData.password === this.passWordForm.formData.confirmPwd) {
                        this.$confirms.confirmation('put', '此操作将修改接口密码数据, 是否继续？', this.API.cpus + 'consumerclientinfo/updPasswordV2', data, res => {
                            if(res.code==200){
                                this.dialogFormVisible = false;//隐藏弹窗
                                this.nmb = 120;
                                clearInterval(this.timer);
                            }
                        });
                    } else {
                        this.$message({
                            message: '请确认两次密码是否一致！',
                            type: 'warning'
                        });
                    }

                } else {
                    return false;
                }
            });
        },
        //修改加密盐
        updateSalt(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        flag: this.saltForm.formData.flag,
                        verifyCode: this.saltForm.formData.verifyCode,
                        salt: this.saltForm.formData.salt,
                    }
                    this.$confirms.confirmation('put', '此操作将修改加密盐, 是否继续？', this.API.cpus + 'consumerclientinfo/saltV2', data, res => {
                        if(res.code==200){
                            this.dialogSaltVisible = false;//隐藏弹窗
                            this.nmb = 120;
                            clearInterval(this.timer);
                        }
                        
                    });

                } else {
                    return false;
                }
            });
        },
        // 点击启用
        handleRequencyEnable() {
            this.requencyEnable = true;
            this.sendfrequency = false;
        },
        handleRequencyEnable1() {
            this.requencyEnable1 = true;
            this.sendfrequency1 = false;
        },
        handleRequencyEnable3() {
            this.requencyEnable3 = true;
            this.sendfrequency3 = false;
        },
        handleRequencyEnable4() {
            this.requencyEnable4 = true;
            this.sendfrequency4 = false;
        },
        handleRequencyEnable5() {
            this.requencyEnable5 = true;
            this.sendfrequency5 = false;
        },
        handleRequencyEnable6() {
            this.requencyEnable6 = true;
            this.sendfrequency6 = false;
        },
        handleRequencyEnable7() {
            this.requencyEnable7 = true;
            this.sendfrequency7 = false;
        },
        //设置
        SetBalance() {
            this.sendfrequency = !this.sendfrequency;
        },
        SetBalance1() {
            this.sendfrequency1 = !this.sendfrequency1;
        },
        SetBalance3() {
            this.sendfrequency3 = !this.sendfrequency3;
        },
        SetBalance4() {
            this.sendfrequency4 = !this.sendfrequency4;
        },
        SetBalance5() {
            this.sendfrequency5 = !this.sendfrequency5;
        },
        SetBalance6() {
            this.sendfrequency6 = !this.sendfrequency6;
        },
        SetBalance7() {
            this.sendfrequency7 = !this.sendfrequency7;
        },
        //点击确定
        determine() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances, productId: '1', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances = res.data.num
                    // })
                    this.sendfrequency = !this.sendfrequency;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine1() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances1)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances1 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances1, productId: '2', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances1 = res.data.num
                    // })
                    this.sendfrequency1 = !this.sendfrequency1;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine3() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances3)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances3 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances3, productId: '3', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances3 = res.data.num
                    // })
                    this.sendfrequency3 = !this.sendfrequency3;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine4() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances4)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances4 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances4, productId: '4', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances4 = res.data.num
                    // })
                    this.sendfrequency4 = !this.sendfrequency4;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine5() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances5)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances5 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances5, productId: '5', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances5 = res.data.num
                    // })
                    this.sendfrequency5 = !this.sendfrequency5;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine6() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances6)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances6 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances6, productId: '6', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances6 = res.data.num
                    // })
                    this.sendfrequency6 = !this.sendfrequency6;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine7() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances7)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances7 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances7, productId: '7', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances7 = res.data.num
                    // })
                    this.sendfrequency7 = !this.sendfrequency7;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        shutDownSetBalance() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '1', username: this.consumerName }, res => {
                this.requencyEnable = !this.requencyEnable;
            });
        },
        shutDownSetBalance1() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '2', username: this.consumerName }, res => {
                this.requencyEnable1 = !this.requencyEnable1;
            });
        },
        shutDownSetBalance3() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '3', username: this.consumerName }, res => {
                this.requencyEnable3 = !this.requencyEnable3;
            });
        },
        shutDownSetBalance4() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '4', username: this.consumerName }, res => {
                this.requencyEnable4 = !this.requencyEnable4;
            });
        },
        shutDownSetBalance5() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '5', username: this.consumerName }, res => {
                this.requencyEnable5 = !this.requencyEnable5;
            });
        },
        shutDownSetBalance6() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '6', username: this.consumerName }, res => {
                this.requencyEnable6 = !this.requencyEnable6;
            });
        },
        shutDownSetBalance7() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '7', username: this.consumerName }, res => {
                this.requencyEnable7 = !this.requencyEnable7;
            });
        },
        // =======================================
        // 图片上传
        handleRemove(file, fileList) {
            this.formLogo.icon = ""
        },
        handlePreview(file) {
            // console.log(file);
        },
        handleRemove1(file, fileList) {
            this.formLogo.logo = ""
        },
        handlePreview1(file) {
            // console.log(file);
        },
        // 打上传成功
        handleAvatarSuccess(res, file) {
            this.imageUrl = URL.createObjectURL(file.raw);
            this.getImgUrl();
        },
        handleAvatarSuccess1(res, file) {
            this.formLogo.icon = res.data
        },
        handleAvatarSuccess2(res, file) {
            this.formLogo.logo = res.data
        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveImg',
        ]),
        beforeAvatarUpload(file) {
            console.log(file)
            const isJPG = file.type === 'image/jpg';
            const isjpeg = file.type === 'image/jpeg';
            const isJPG1 = file.type === 'image/png';
            const isJPG2 = file.type === 'image/gif';
            // const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isJPG && !isJPG1 && !isJPG2 && !isjpeg) {
                this.$message.error('上传头像图片只能是 jpg、png、gif 格式!');
                return false;
            }
            // if (!isLt2M) {
            //     this.$message.error('上传头像图片大小不能超过 2MB!');
            // }

            // return isJPG && isLt2M;
        },
        beforeAvatarUpload1(file) {
            console.log(file)
            const isico = file.type === 'image/x-icon';
            // const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isico) {
                this.$message.error('上传头像图片只能是 ico 格式!');
                return false;
            }
            // if (!isLt2M) {
            //     this.$message.error('上传头像图片大小不能超过 2MB!');
            // }

            // return isJPG && isLt2M;
        },
        beforeAvatarUpload2(file) {
            console.log(file)
            const isJPG1 = file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 1;
            if (!isJPG1) {
                this.$message.error('上传头像图片只能是 png 格式!');
                return false;
            }
            if (!isLt2M) {
                this.$message.error('上传头像图片大小不能超过 1MB!');
            }

            // return isJPG && isLt2M;
        },
        //获取img
        getImgUrl() {
            this.$api.get(this.API.cpus + 'consumerclientinfo/getClientInfo', null, res => {
                // this.imageUrl = this.API.imgU + res.data.portraitGroup+'/'+res.data.portraitPath; 
                this.imageUrl = 'https://' + res.data.portraitUrl;
                this.isIndividualization = res.data.isIndividualization
                if (res.data.cipherMode) {
                    this.cipherMode = res.data.cipherMode
                }
                //存在vuex里
                if (res.data.portraitUrl) {
                    let SET_PORTRAITURL = 'https://' + res.data.portraitUrl;
                    let param = {
                        portraitUrl: SET_PORTRAITURL
                    }
                    this.saveImg(param);
                }
            })
        },
        avatarClick() {
            this.$refs.avatar.click()
        },
        //确定修改密码
        changePwd(val) {
            this.$refs[val].validate((valid) => {
                if (valid) {
                    let param = {};
                    // param.oldPassword=this.formPassword.oldPassword;
                    param.newPassword = this.formPassword.newPassword;
                    this.$confirms.confirmation('get', '确定修改密码？', this.API.cpus + 'consumerclientinfo/updateLoginPassword', param, res => {
                        this.changePassword = false; //关闭弹出框
                    });
                } else {
                    return false;
                }
            });
        },
        LogoSetting() {
            this.$api.post(this.API.cpus + 'personalizationSettings/getSttingsByUserName', { userName: this.$store.state.userName }, res => {
                if (res.data != null) {
                    this.formLogo = res.data
                    this.fileList = [{ name: '', url: res.data.icon }]
                    this.fileList1 = [{ name: '', url: res.data.logo }]
                }
                this.Personalization = true;
            })
        },
        //登录手机号弹出层
        addphone() {
            this.$api.post(this.API.cpus + 'consumerclientinfo/loginTelephoneManager/list', {}, res => {
                let resdata = []
                let tabledata = []
                let phonelength = res.data.data;
                this.phoneData = phonelength[0].mobile
                this.phoneOriginal = [];
                for (var i = 0; i < phonelength.length; i++) {
                    // 列表数据
                    let a = {};
                    a.Numbering = i + 1;
                    a.username = phonelength[0].consumerName;
                    a.phone = phonelength[i].mobile;
                    resdata[resdata.length] = a;
                    this.phoneOriginal.push(phonelength[i].mobile);
                    // 登录手机号列表
                    let b = {};
                    b.index = i;
                    b.name = i + 1;
                    b.address = phonelength[i].mobile;
                    tabledata[tabledata.length] = b
                }
                this.tableD = tabledata
            })
            // if(this.tableDataObj.tableData.length>=5){
            //     this.$message({
            //         type: 'error',
            //         duration:'2000',
            //         message:"最多添加5个手机号码"
            //     });
            // }else{
            this.radio = 0
            this.LcpDialogVisible = true
            // }
        },
        handelClose() {//×号关闭弹窗
            // 点击关闭
            this.LcpDialogVisible = false; //关闭弹出框
        },
        cancel() {
            this.LcpDialogVisible = false; //关闭弹出框
            this.setPhoneSteps = 1; //步进改为1
            this.setphoneFrom.ruleForm1.verCode = ''; //验证码置空
            this.setphoneFrom.ruleForm2.setNewPhone = ''; //手机号置空
        },
        // 新增登录手机号
        submitForm(val) {
            this.$refs[val].validate((valid) => {
                if (val == "ruleForm1") {
                    if (valid) {
                        this.$api.get(this.API.cpus + "code/checkVerificationCode?code=" + this.setphoneFrom.ruleForm1.verCode + '&flag=2', {}, res => {
                            if (res.code == 200) {
                                this.setPhoneSteps = 2;
                            } else {
                                this.$message({
                                    type: 'error',
                                    duration: '2000',
                                    message: "验证码无效！"
                                });
                            }
                        })
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                } else {
                    this.$refs[val].validate((valid) => {
                        if (valid) {
                            let param = {};
                            // param.oldPassword=this.formPassword.oldPassword;
                            param.newPassword = this.formPassword.newPassword;
                            this.$confirms.confirmation('get', '确定修改密码？', this.API.cpus + 'consumerclientinfo/updateLoginPassword', param, res => {
                                this.LcpDialogVisible = false; //关闭弹出框
                            });
                        } else {
                            return false;
                        }
                    });
                }

            });
        },
        // 获取验证码倒计时
        CountdownCode() {
            if (this.phoneData) {
                --this.nmb;
                const timer = setInterval(res => {
                    --this.nmb;
                    if (this.nmb < 1) {
                        this.nmb = 120;
                        clearInterval(timer);
                    };
                }, 1000);
                this.$api.get(this.API.cpus + "code/sendVerificationCode?phone=" + this.phoneData + '&flag=2', {}, res => {
                    if (res.data.code == 200) {
                        this.$message({
                            type: 'success',
                            duration: '2000',
                            message: '验证码已发送至手机!'
                        });
                    } else {
                        this.$message({
                            type: 'warning',
                            message: '验证码未失效，需失效后重新获取!'
                        });
                    }

                })
            } else {
                this.$message({
                    message: '请先选中手机号码',
                    type: 'warning'
                });
            }
        },
        showRow(row) {
            //赋值给radio
            this.radio = this.tableD.indexOf(row);
        },
        getCurrentRow(val) {
            this.phoneData = this.tableD[val].address //赋值手机号
        },
    },
    watch: {
        dialogFormVisible(val) {
            if (!val) {
                // this.$refs.updateFormDialog.resetFields(); //清空表单
                this.$refs.passWordForm.resetFields(); //清空表单
                // this.active = 1;
                // this.templateRadio = 0
            }
        },
        dialogSaltVisible(val) {
            if (!val) {
                this.$refs.saltForm.resetFields(); //清空表单
            }
        },
        // ===============================
        changePassword(newVal, oldVal) {
            if (newVal == false) {
                this.$refs.formRule.resetFields();
            }
        },
        Personalization(newVal, oldVal) {
            if (newVal == false) {
                this.$refs.formLogos.resetFields();
                this.fileList = []
                this.fileList1 = []
            }
        },
        LcpDialogVisible: function (val) {
            if (val == false) {
                this.setphoneFrom.ruleForm1.verCode = ''; //验证码置空
                this.setphoneFrom.ruleForm2.setNewPhone = ''; //手机号置空
                this.setPhoneSteps = 1; //步进改为1
                this.radio = "";
                this.phoneData = this.phoneOriginal[0]
                this.$refs.ruleForm1.resetFields()
                this.$refs.ruleForm2.resetFields()
            }
        }
    },
    created() {
        this.getLoginInfo()
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '1' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable = false
                } else {
                    this.requencyEnable = true
                }
                this.reminderBalances = res.data.num
            } else {
                this.requencyEnable = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '2' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable1 = false
                } else {
                    this.requencyEnable1 = true
                }
                this.reminderBalances1 = res.data.num
            } else {
                this.requencyEnable1 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '3' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable3 = false
                } else {
                    this.requencyEnable3 = true
                }
                this.reminderBalances3 = res.data.num
            } else {
                this.requencyEnable1 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '4' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable4 = false
                } else {
                    this.requencyEnable4 = true
                }
                this.reminderBalances4 = res.data.num
            } else {
                this.requencyEnable4 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '5' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable5 = false
                } else {
                    this.requencyEnable5 = true
                }
                this.reminderBalances5 = res.data.num
            } else {
                this.requencyEnable5 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '6' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable6 = false
                } else {
                    this.requencyEnable6 = true
                }
                this.reminderBalances6 = res.data.num
            } else {
                this.requencyEnable6 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '7' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable7 = false
                } else {
                    this.requencyEnable7 = true
                }
                this.reminderBalances7 = res.data.num
            } else {
                this.requencyEnable1 = false
            }
        })
        // =====================================
        this.token = { Authorization: "Bearer" + this.$common.getCookie('ZTGlS_TOKEN') };
        this.actionHttp = this.API.cpus + "consumerclientinfo/updatePortrait";
        this.actionHttp1 = this.API.cpus + "consumerclientinfo/uploadImage"
        this.getImgUrl();
        this.$api.get(this.API.cpus + 'consumerclientsms/overrun', {}, res => {
            this.SendSettings.overrunCode = res.data.overrunCode
            this.SendSettings.overrunIndustry = res.data.overrunIndustry
            this.SendSettings.overrunMarket = res.data.overrunMarket
        })
    }
}
</script>
<style scoped>
/* 统一的页面布局样式 */
.simple-signature-page {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: #f5f7fa;
    min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
    text-align: center;
    margin-bottom: 32px;
    padding: 32px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 12px;
    color: white;
    box-shadow: 0 8px 32px rgba(44, 62, 80, 0.3);
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    letter-spacing: 1px;
}

.page-subtitle {
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
    font-weight: 300;
}

/* 页面内容区域 */
.page-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 信息卡片通用样式 */
.info-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.info-card:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

/* 卡片头部样式 */
.card-header {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-bottom: 1px solid #eee;
}

.card-header i {
    font-size: 20px;
    margin-right: 12px;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

/* 卡片内容样式 */
.card-content {
    padding: 24px;
}

/* 基本信息网格布局 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.info-label {
    font-weight: 600;
    color: #333;
    min-width: 100px;
    margin-right: 16px;
}

.info-value {
    color: #666;
    font-size: 15px;
}

/* 配置区域样式 */
.config-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.config-item {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.config-label {
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 12px;
    font-size: 16px;
}

.config-control {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.password-display {
    font-family: monospace;
    background: #f1f3f4;
    padding: 8px 12px;
    border-radius: 4px;
    color: #666;
    min-width: 80px;
}

.config-button {
    color: #2980b9 !important;
    font-weight: 500;
}

.config-button:hover {
    color: #3498db !important;
}

.help-icon {
    color: #409eff;
    cursor: help;
}

.config-tip {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
    background: #e8f4fd;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

/* 余额提醒网格布局 */
.balance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
}

.balance-item {
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.balance-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.balance-config {
    padding: 20px;
}

.balance-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.balance-info {
    margin: 0;
    font-size: 15px;
    color: #333;
}

.balance-note {
    margin: 0;
    font-size: 13px;
    color: #c0392b;
    background: #fdf2e9;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #e67e22;
}

.balance-input-row {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.balance-input {
    width: 120px;
}

.balance-actions {
    display: flex;
    gap: 12px;
}

.highlight {
    color: #c0392b;
    font-weight: 600;
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
    transition: all 0.3s ease;
}

.fade-enter, .fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

/* 对话框样式 */
.Login-c-p-getPhone {
    margin: 16px 0;
    color: #6c757d;
    font-size: 14px;
    padding: 12px;
    background: #e8f4fd;
    border-radius: 6px;
    border-left: 4px solid #2980b9;
}

.passWord-footer {
    padding-top: 20px;
    text-align: right;
    border-top: 1px solid #eee;
    margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .simple-signature-page {
        padding: 16px;
    }
    
    .page-header {
        padding: 24px 16px;
        margin-bottom: 24px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .page-subtitle {
        font-size: 14px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .balance-grid {
        grid-template-columns: 1fr;
    }
    
    .balance-input-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .balance-input {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 480px) {
    .card-content {
        padding: 16px;
    }
    
    .config-control {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .balance-actions {
        flex-direction: column;
    }
}
</style>



