<template>
    <div class="fillet-shortChain-box">
      <el-form
        :model="formData"
        :rules="formRule"
        ref="formRef"
        label-width="170px"
        style="padding: 20px 8px 0 8px"
      >
        <el-form-item label="公司名称" prop="compName">
          <el-input
            v-model="formData.compName"
            placeholder="名称"
            class="input-w"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="营业执照编号" prop="number">
          <el-input
            v-model="formData.number"
            placeholder="证照编号"
            class="input-w"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="被授权人姓名" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="被授权人姓名"
            class="input-w"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="被授权人身份证号" prop="idCard">
          <el-input
            v-model="formData.idCard"
            placeholder="身份证号码"
            class="input-w"
            
          ></el-input>
        </el-form-item> -->
        
        <el-form-item label="被授权人身份证正面照" prop="idCardFront">
          <el-upload
            class="upload-demo"
            :action="action"
            :headers="token"
            :file-list="fileList"
            :on-preview="handlePreview"
            :on-remove="handleRemove1"
            :on-success="idCardFrontZ"
            accept="image/jpeg,image/jpg,"
            :before-upload="beforidCardFrontZ"
            list-type="picture"
          >
            <el-button size="small" type="primary">上传照片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="被授权人身份证反面照" prop="idCardBack">
          <el-upload
            class="upload-demo"
            :action="action"
            :headers="token"
            :file-list="fileList"
            :on-preview="handlePreview"
            :on-remove="handleRemove2"
            :on-success="idCardFrontF"
            list-type="picture"
            accept="image/jpeg,image/jpg,"
            :before-upload="beforidCardFrontZ"
          >
            <el-button size="small" type="primary">上传照片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="上传营业执照" prop="businessLicense">
          <el-upload
            class="upload-demo"
            :action="action"
            :headers="token"
            :file-list="fileList"
            :on-preview="handlePreview"
            :on-remove="handleRemove3"
            :on-success="handleSuccess"
            list-type="picture"
            accept="image/jpeg,image/jpg,"
            :before-upload="beforidCardFrontZ"
          >
            <el-button size="small" type="primary">浏览文件...</el-button>
            <div style="width: 320px;color:red;margin-left:10px" slot="tip" class="el-upload__tip">
              机构/政府事业单位，请上传有效的《组织机构有效证照》扫描件或照片； 企业认证，请上传《营业执照》扫描件或照片，文件顺序不分先后；支持jpg,jpeg格式的照片
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="上传授权委托书" prop="authorization">
          <el-upload
            class="upload-demo"
            :action="action"
            :headers="token"
            :file-list="fileList"
            :on-preview="handlePreview"
            :on-remove="handleRemove4"
            :on-success="handlewqsSuccess"
            list-type="picture"
            accept="image/jpeg,image/jpg,"
            :before-upload="beforidCardFrontZ"
          >
            <el-button size="small" type="primary">浏览文件...</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="被授权人手机号码" prop="phone">
          <el-input
            v-model="formData.phone"
            placeholder="手机号码"
            class="input-w"
            @input="ncrj"
          ></el-input>
        </el-form-item>
        <el-form-item :class="!ncflag?'ncrj':''" prop="nc">
          <div style="height:30px">
            <div id="nc"></div>
          </div>
          </el-form-item>
        <el-form-item label="验证码" v-if="verify" prop="validCode">
          <el-input
            v-model="formData.validCode"
            placeholder="请输入验证码"
            class="input-w"
          ></el-input>
          <el-button
           @click="getPhoneCode(formData.phone)"
            type="primary"
            style="font-size: 14px; width: 110px; position: absolute; top: 1px"
            v-if="nmb == 120"
            >获取验证码</el-button
          >
          <el-button
            type="primary"
            style="font-size: 14px; width: 110px"
            disabled
            v-else
            >重新获取({{ nmb }})</el-button
          >
        </el-form-item>
        <!-- <el-form-item label="证件照片" prop="mobile" v-if="formData.authorization || formData.businessLicense">
          <img :src="API.imgU+formData.authorization" alt=""  width="100" height="100">
          <img :src="API.imgU+formData.businessLicense" alt=""  width="100" height="100">
        </el-form-item> -->
        <el-form-item label="证件和授权实例" prop="mobile">
          <a href="https://doc.zthysms.com/Public/Uploads/2021-03-12/604b2f5c43672.png" target="_blank" rel="noopener noreferrer">
          <img src="../../../../../assets/images/yyzz.png" alt="" srcset="" width="100" height="100">
          </a>
          <a href="https://doc.zthysms.com/Public/Uploads/2021-03-24/605af850c8491.png" target="_blank" rel="noopener noreferrer">
          <img src="../../../../../assets/images/squ.png" alt="" srcset="" width="100" height="100">
          </a>
          <a style="margin-left:10px;color:#2a6af2" href="https://doc.zthysms.com/Public/Uploads/2021-03-24/605ad686a9473.docx">模板下载</a>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button v-permission type="primary" @click="send">提交认证资料</el-button>
        </el-form-item>
      </el-form>
  </div>
</template>

<script>
var axios = require("axios");
import getNoce from "../../../../../plugins/getNoce";
export default {
    data() {
    var idCard = (rule, value, callback) => {
      // 1 "验证通过!", 0 //校验不通过 // id为身份证号码
      var format = /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/;
      //号码规则校验
      if (value) {
        if (!format.test(value)) {
          return callback(new Error("身份证号码不合规"));
        } else {
          //区位码校验
          //出生年月日校验  前正则限制起始年份为1900;
          var year = value.substr(6, 4), //身份证年
            month = value.substr(10, 2), //身份证月
            date = value.substr(12, 2), //身份证日
            time = Date.parse(month + "-" + date + "-" + year), //身份证日期时间戳date
            now_time = Date.parse(new Date()), //当前时间戳
            dates = new Date(year, month, 0).getDate(); //身份证当月天数
          if (time > now_time || date > dates) {
            return callback(new Error("出生日期不合规"));
          }
          //校验码判断
          var c = new Array(
            7,
            9,
            10,
            5,
            8,
            4,
            2,
            1,
            6,
            3,
            7,
            9,
            10,
            5,
            8,
            4,
            2
          ); //系数
          var b = new Array(
            "1",
            "0",
            "X",
            "9",
            "8",
            "7",
            "6",
            "5",
            "4",
            "3",
            "2"
          ); //校验码对照表
          var id_array = value.split("");
          var sum = 0;
          for (var k = 0; k < 17; k++) {
            sum += parseInt(id_array[k]) * parseInt(c[k]);
          }
          if (id_array[17].toUpperCase() != b[sum % 11].toUpperCase()) {
            return callback(new Error("身份证校验码不合规"));
          }
          callback();
        }
      } else {
        return callback(new Error("请输入身份证号码"));
      }
      
    };
    var phone = (rule, value, callback) => {
        if (value) {
          
          if (!/^((13\d)|(14[5,7,9])|(15[0-3,5-9])|(166)|(17[0,1,3,5,7,8])|(18[0-9])|(19[8,9]))\d{8}/.test(value)) {
            return callback(new Error("请输入正确的手机号"));
          } else {
            callback();
          }
        } else {
          return callback(new Error("请输入被授权人手机号"));
        }
      };
    return {
      ClickTrue: true,
      smrzFlag:false,
      ncflag:false,
      verify: false,
      nmb: 120,
      rzmsObj:{},
      action: this.API.cpus + "v3/file/upload",
      token: {},
      smrzObj:{},
      fileList: [],
      formData: {
        compName: "",
        idCard: "",
        businessLicense:"",
        authorization: "",
        idCardFront:"",
        idCardBack:"",
        name: "",
        number: "",
        phone: "",
        validCode: "",
      },
      registerData: {
        appKey: "",
        phone: "",
        scene: "",
        sessionId: "",
        sig: "",
        token: "",
      },
      
      formRule: {
        compName: [
          {
            required: true,
            message:
              "请输入公司/机构/政府企事业单位名称，并与您提交的资质保持一致",
            trigger: "blur",
          },
        ],
        idCard: [
          {
            required: true,
            message: "请输入身份证号",
            trigger: "blur",
          },
        ],
        // nc: [
        //   { required: true, message: "请滑动人机验证", trigger: "blur" },
        // ],
        phone: [{ required: true, validator: phone, trigger: "blur" }],
        number: [
          {
            required: true,
            message: "请输入证照编号，并与您提交的资质保持一致",
            trigger: "blur",
          },
        ],
        name: [
          { required: true, message: "请输入被授权人的姓名", trigger: "blur" },
        ],
        validCode: [
          { required: true, message: "请填写手机验证码", trigger: "blur" },
          { min: 6, max: 6, message: "短信验证码需6位字符", trigger: "blur" },
        ],
        idCardFront: [
          { required: true, message: "请上传被授权人身份证正面照", trigger: "blur" },
        ],
        idCardBack: [
          { required: true, message: "请上传被授权人身份证反面照", trigger: "blur" },
        ],
        businessLicense: [
          { required: true, message: "请上传营业执照", trigger: "blur" },
        ],
        authorization: [
          { required: true, message: "请上传授权书", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleRemove1(file, fileList) {
      // console.log(file, fileList);
      // console.log(111111);
      // this.formData.businessLicense = ""
      // this.formData.authorization  = ""
      this.formData.idCardFront = ""
      // this.formData.idCardBack = ""
    },
    handleRemove2(file, fileList) {
      // console.log(file, fileList);
      // console.log(111111);
      // this.formData.businessLicense = ""
      // this.formData.authorization  = ""
      this.formData.idCardBack = ""
      // this.formData.idCardBack = ""

    },
    handleRemove3(file, fileList) {
      // console.log(file, fileList);
      // console.log(111111);
      // this.formData.businessLicense = ""
      this.formData.authorization  = ""
      // this.formData.idCardFront = ""
      // this.formData.idCardBack = ""
    },
    handleRemove4(file, fileList) {
      // console.log(file, fileList);
      // console.log(111111);
      this.formData.businessLicense = ""
      // this.formData.authorization  = ""
      // this.formData.idCardFront = ""
      // this.formData.idCardBack = ""
    },
    handlePreview(file) {
      // console.log(file);
      // console.log(22222222);
    },
    handleSuccess(res, file, fileList) {
        //  console.log(res,'ll');
    //   this.formData.image.push(res.data.fullpath);
     this.formData.businessLicense = res.data.fullpath

      // console.log(file,'22');
      // console.log(fileList,'33');
    },
    handlewqsSuccess(res, file, fileList){
      this.formData.authorization = res.data.fullpath

    },
    idCardFrontZ(res, file, fileList){
      console.log();
       this.formData.idCardFront = res.data.fullpath
    },
    idCardFrontF(res, file, fileList){
       this.formData.idCardBack = res.data.fullpath
    },
    beforidCardFrontZ(file){
      // console.log(file);
      var testmsg = /^image\/(jpeg|jpg)$/.test(file.type)
      const isLt5M = file.size / 1024 / 1024 < 20
      // console.log(isLt5M,'lllllll');
       if (!testmsg) {
        this.$message.error('上传图片格式不对!')
        return
      }
      if (!isLt5M) {
        this.$message.error('上传头像图片大小不能超过 20MB!')
      }
      return testmsg && isLt5M
    },
    handSmrz(){
      this.smrzFlag = true
      this.initDrag()
      // window.nc.show();
    },
    // closeFlag(){
    //   console.log(111);
    //   // window.nc[hide()]
    //   // w
    //   var ncc = document.getElementById("thisNode")
    //    ncc.removeChild('nc_1_nocaptcha')
    //   window.nc.reset();
    //   // document.getElementById()
    //   console.log(window,'ll');
    //   this.smrzFlag = false
    // },
    ncrj(){
      this.registerData.phone = this.formData.phone
      this.ncflag = true
      let phone = this.formData.phone
      // if(phone.length == 11){
      //         this.ncflag = true
      //     }else{
      //       this.ncflag = false
      //       this.verify = false
      //     }
      // this.ncflag = true
    },
    //人机验证
    initDrag() {
      var that = this;
      // 实例化nc
      AWSC.use("nc", function (state, module) {
        // 初始化
        window.nc = module.init({
          // 应用类型标识。它和使用场景标识（scene字段）一起决定了滑动验证的业务场景与后端对应使用的策略模型。您可以在人机验证控制台的配置管理页签找到对应的appkey字段值，请务必正确填写。
          appkey: "FFFF0N00000000009C2B",
          //使用场景标识。它和应用类型标识（appkey字段）一起决定了滑动验证的业务场景与后端对应使用的策略模型。您可以在人机验证控制台的配置管理页签找到对应的scene值，请务必正确填写。
          scene: "nc_register",
          // 声明滑动验证需要渲染的目标ID。
          renderTo: "nc",
          //前端滑动验证通过时会触发该回调参数。您可以在该回调参数中将会话ID（sessionId）、签名串（sig）、请求唯一标识（token）字段记录下来，随业务请求一同发送至您的服务端调用验签。
          success: function (data) {
            // window.console && console.log(data.sessionId);
            // window.console && console.log(data.sig);
            // window.console && console.log(data.token);
            that.registerData.appKey = this.appkey;
            that.registerData.phone = that.formData.phone;
            that.registerData.scene = this.scene;
            that.registerData.sessionId = data.sessionId;
            that.registerData.sig = data.sig;
            that.registerData.token = data.token;
            that.verify = true; //  拖动状态，判断滑块是否拖动完成
            // window.nc.reset();
            that.ncflag = false
            // console.log(that.registerData);
          },
          // 滑动验证失败时触发该回调参数。
          fail: function (failCode) {
            window.console && console.log(failCode, "code");
          },
          // 验证码加载出现异常时触发该回调参数。
          error: function (errorCode) {
            window.console && console.log(errorCode);
          },
        });
      });
    },
    async getPhoneCode(val) {
      const nonce = await getNoce.useNonce();
      if (this.ClickTrue) {
        if (/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(this.formData.phone)) {
          console.log(1111);
          this.ClickTrue = false;
          axios({
            method: "post",
            url: this.API.upms + "code/certificate/smsCode",
            headers: {
              'Once': nonce,
              // 'Authorization':'Basic cGlnOnBpZw=='
            },
            data: this.registerData,
            withCredentials: false,
          }).then((res) => {
            window.nc.reset();
            if (res.data.code == 200) {
              --this.nmb;
              const timer = setInterval((res) => {
                --this.nmb;
                if (this.nmb < 1) {
                  this.nmb = 120;
                  this.ClickTrue = true;
                  clearInterval(timer);
                }
              }, 1000);
              this.$message({
                type: "success",
                duration: "2000",
                message: "验证码已发送至手机!",
              });
            } else {
              this.ClickTrue = true;
              this.ncflag = true
              window.nc.reset();
              if (res.data.msg == "验证码未失效，请失效后再次申请") {
                --this.nmb;
                const timer = setInterval((res) => {
                  --this.nmb;
                  if (this.nmb < 1) {
                    this.nmb = 120;
                    clearInterval(timer);
                  }
                }, 1000);
              }
              this.$message({
                type: "error",
                duration: "2000",
                message: '人机校验失效，请重新拉去！',
              });
            }
          }).catch(err=>{
                this.$message({
                        message: err.msg,
                        type: "error",
                    });
            });
        } else {
          // this.getcode();
          this.$message({
            message: "请输入正确手机号",
            type: "warning",
          });
        }
        // }).catch(err=>{
        //     this.getcode()
        // })
      } else {
        console.log(this.ClickTrue, "oo");
        this.$message({
          message: "请先填写正确手机号",
          type: "warning",
        });
      }
      // }
    },
     send() {
       
        window.nc.reset();
      this.$refs["formRef"].validate((valid, value) => {
        if (valid) {
          // console.log(this.formData,'ll');
        //   this.formData.image.join('')
          this.$api.post(this.API.cpus + 'consumerCertificate',this.formData,res=>{
              console.log(res,'res');
              if(res.code == 200){
                  this.$message({
                type: "success",
                duration: "2000",
                message: "提交成功!",
              });
             this.mockParent()
              
              // this.smrzFlag = false
              }else if(res.code == 400){
                  this.$message({
                type: "warning",
                duration: "2000",
                message: res.msg
              });
               
              }else{
                   this.$message({
                type: "error",
                duration: "2000",
                message: "提交失败!",
              });
              }
            })
        }
      });
    },
    // async init(){
    //   //  this.smrzObj = {}
    //   console.log(111);
    //   await this.$api.get(this.API.cpus + 'consumerCertificate/info',this.formData,res=>{
    //    this.$forceUpdate(Object.assign(this.smrzObj, res.data))
      
    //    console.log(this.smrzObj,';ll');
    //  })
    // }
  },
  created() {
    console.log(this.SMRZObj,'obj');
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.formData.phone = ''
    // console.log(this.SMRZObj,';;');
  if(this.SMRZObj.certificateInfo){
    this.formData = this.SMRZObj.certificateInfo
    this.formData.phone = ''
  }
  

    
    // this.init()
  },
   mounted() {
      this.initDrag();
  },
  props:{
      mockParent:{
    type: Function
  },
  SMRZObj:{
      type:Object
  }
  }
}
</script>

<style scoped>
.ncrj{
  display: none;
}
.fillet-shortChain-box{
    margin-left: 150px;
}
</style>