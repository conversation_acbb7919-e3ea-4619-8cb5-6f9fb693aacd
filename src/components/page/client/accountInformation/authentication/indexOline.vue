<template>
  <div class="simple-signature-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div>
          <h2 class="page-title">实名认证</h2>
          <div class="page-subtitle">根据法律法规要求，需要完成实名认证方可使用相关服务</div>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 法律须知区域 -->
        <div class="notice-section">
          <h3 class="notice-title">认证须知</h3>
          <div class="notice-list">
            <div class="notice-item">
              <span class="notice-label">法律依据：</span>
              根据《中华人民共和国网络安全法》及相关法律的规定，网络运营者为用户办理网络接入、域名注册服务，办理固定电话、移动电话等入网手续，或者为用户提供信息发布、即时通讯等服务，在与用户签订协议或者确认提供服务时，应当要求用户提供真实身份信息。用户不提供真实身份信息的，网络运营者不得为其提供相关服务。
            </div>
            <div class="notice-item">
              <span class="notice-label">认证要求：</span>
              在您使用助通服务前，需要进行实名认证。
            </div>
            <div class="notice-item">
              <span class="notice-label">认证时间：</span>
              周一至周六9:00--22:00（晚22点后提交会在次日9点后审核认证），认证时长20分钟内；周日提交认证则会延期至周一工作日。
            </div>
            <div class="notice-item">
              <span class="notice-label">紧急联系电话：</span>
              <span style="color: #409eff; font-weight: 500;">15601838771</span>
            </div>
          </div>
        </div>

        <!-- 认证信息区域 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">认证信息</h3>
          </div>
          <div class="auth-content">
            <div v-if="smrzObj">
              <div v-if="smrzObj.certificateInfo">
                <div class="auth-info-list">
                  <div class="auth-info-item">
                    <span class="info-label">状态</span>
                    <div class="info-value">
                      <el-tag :type="getStatusType(smrzObj.certificateInfo.status)" size="medium">
                        {{ getStatusText(smrzObj.certificateInfo.status) }}
                      </el-tag>
                    </div>
                  </div>

                  <div v-if="(roleId == '14' || roleId == '12') && smrzObj.certificateInfo.status == 3"
                    class="auth-info-item">
                    <span class="info-label">完善信息状态</span>
                    <div class="info-value">
                      <el-tag :type="showflag ? 'danger' : 'success'" size="medium">
                        {{ showflag ? '未完善' : '已完善' }}
                      </el-tag>
                    </div>
                  </div>

                  <div class="auth-info-item">
                    <span class="info-label">认证类型</span>
                    <span class="info-value">{{ getAuthType(smrzObj.certificateInfo.type) }}</span>
                  </div>

                  <div class="auth-info-item">
                    <span class="info-label">认证人姓名</span>
                    <span class="info-value">{{ smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.name ||
                      '-' }}</span>
                  </div>

                  <div class="auth-info-item">
                    <span class="info-label">公司名称</span>
                    <span class="info-value">{{ smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.compName
                      || '-' }}</span>
                  </div>

                  <div class="auth-info-item"
                    v-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.downloadUrl">
                    <span class="info-label">查看合同</span>
                    <div class="info-value">
                      <el-button type="text" @click="download(smrzObj.certificateInfo.downloadUrl)"
                        class="download-btn">
                        <i class="el-icon-download"></i>
                        点击下载
                      </el-button>
                    </div>
                  </div>

                  <div class="auth-info-item"
                    v-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.status == 2">
                    <span class="info-label">驳回原因</span>
                    <div class="info-value reject-reason">
                      {{ (smrzObj.certificateInfo.checkReason || smrzObj.certificateInfo.auditReason) || '暂无说明' }}
                    </div>
                  </div>
                </div>

                <div v-if="(roleId == '14' || roleId == '12') && smrzObj.certificateInfo.status == 3"
                  class="action-section">
                  <el-button v-permission v-if="showflag" type="primary" @click="$router.push('completeAuthenInfo')"
                    class="action-btn primary">
                    <i class="el-icon-edit"></i>
                    资料完善
                  </el-button>
                </div>
              </div>

              <div v-else>
                <div class="auth-info-list">
                  <div class="auth-info-item">
                    <span class="info-label">状态</span>
                    <div class="info-value">
                      <el-tag :type="certiFicate == '2' ? 'success' : 'danger'" size="medium">
                        {{ certiFicate == '2' ? '已认证' : '未认证' }}
                      </el-tag>
                    </div>
                  </div>

                  <div class="auth-info-item">
                    <span class="info-label">公司名称</span>
                    <span class="info-value">{{ CompName || '-' }}</span>
                  </div>

                  <div v-if="(roleId == '14' || roleId == '12') && certiFicate == 2" class="auth-info-item">
                    <span class="info-label">完善信息状态</span>
                    <div class="info-value">
                      <el-tag :type="showflag ? 'danger' : 'success'" size="medium">
                        {{ showflag ? '未完善' : '已完善' }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <div v-if="(roleId == '14' || roleId == '12') && certiFicate == 2" class="action-section">
                  <el-button v-permission v-if="showflag" type="primary" @click="$router.push('completeAuthenInfo')"
                    class="action-btn primary">
                    <i class="el-icon-edit"></i>
                    资料完善
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 认证操作按钮 -->
            <div class="action-section" v-if="smrzObj.certificate">
              <el-button v-permission v-if="smrzObj.certificateInfo && smrzObj.certificateInfo.status == 1" type="primary"
                @click="handSmrz('2')" class="action-btn primary">
                <i class="el-icon-edit-outline"></i>
                签署合同
              </el-button>
              <el-button v-permission v-else-if="certiFicate != '2'" type="primary" @click="handSmrz('1')"
                class="action-btn primary">
                <i class="el-icon-user"></i>
                开始认证
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 认证类型选择对话框 -->
    <el-dialog :visible.sync="smrzFlag" width="900px" :modal-append-to-body="false" @close="closeFlags"
      class="auth-type-dialog">
      <div slot="title" class="dialog-title">
        <i class="el-icon-user"></i>
        选择认证类型
      </div>

      <div class="auth-type-container">
        <div class="auth-type-card" @click="lega()">
          <div class="card-header">
            <div class="card-icon">
              <i class="el-icon-user-solid"></i>
            </div>
            <h3 class="card-title">企业法定代表人</h3>
          </div>
          <div class="card-content">
            <div class="prep-title">认证前需要准备：</div>
            <div class="prep-list">
              <div class="prep-item">
                <i class="el-icon-check"></i>
                本人身份证正反面
              </div>
              <div class="prep-item">
                <i class="el-icon-check"></i>
                营业执照原件的高清照片
              </div>
              <div class="prep-item">
                <i class="el-icon-check"></i>
                请确认您是企业法定代表人
              </div>
            </div>
          </div>
          <div class="card-footer">
            <el-button type="primary" class="auth-btn">
              <i class="el-icon-right"></i>
              去认证
            </el-button>
          </div>
        </div>

        <div class="auth-type-card" @click="Individual()">
          <div class="card-header">
            <div class="card-icon">
              <i class="el-icon-s-custom"></i>
            </div>
            <h3 class="card-title">个体工商户经营者</h3>
          </div>
          <div class="card-content">
            <div class="prep-title">认证前需要准备：</div>
            <div class="prep-list">
              <div class="prep-item">
                <i class="el-icon-check"></i>
                本人身份证正反面
              </div>
              <div class="prep-item">
                <i class="el-icon-check"></i>
                营业执照原件的高清照片
              </div>
              <div class="prep-item">
                <i class="el-icon-check"></i>
                请确认您是个体工商户的经营者
              </div>
            </div>
          </div>
          <div class="card-footer">
            <el-button type="primary" class="auth-btn">
              <i class="el-icon-right"></i>
              去认证
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import smrz from './smrz'
import axios from 'axios'
import getNoce from '../../../../../plugins/getNoce';
export default {
  name: "authentication",
  components: {
    smrz
  },
  data() {
    var idCard = (rule, value, callback) => {
      // 1 "验证通过!", 0 //校验不通过 // id为身份证号码
      var format = /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/;
      //号码规则校验
      if (value) {
        if (!format.test(value)) {
          return callback(new Error("身份证号码不合规"));
        } else {
          //区位码校验
          //出生年月日校验  前正则限制起始年份为1900;
          var year = value.substr(6, 4), //身份证年
            month = value.substr(10, 2), //身份证月
            date = value.substr(12, 2), //身份证日
            time = Date.parse(month + "-" + date + "-" + year), //身份证日期时间戳date
            now_time = Date.parse(new Date()), //当前时间戳
            dates = new Date(year, month, 0).getDate(); //身份证当月天数
          if (time > now_time || date > dates) {
            return callback(new Error("出生日期不合规"));
          }
          //校验码判断
          var c = new Array(
            7,
            9,
            10,
            5,
            8,
            4,
            2,
            1,
            6,
            3,
            7,
            9,
            10,
            5,
            8,
            4,
            2
          ); //系数
          var b = new Array(
            "1",
            "0",
            "X",
            "9",
            "8",
            "7",
            "6",
            "5",
            "4",
            "3",
            "2"
          ); //校验码对照表
          var id_array = value.split("");
          var sum = 0;
          for (var k = 0; k < 17; k++) {
            sum += parseInt(id_array[k]) * parseInt(c[k]);
          }
          if (id_array[17].toUpperCase() != b[sum % 11].toUpperCase()) {
            return callback(new Error("身份证校验码不合规"));
          }
          callback();
        }
      } else {
        return callback(new Error("请输入身份证号码"));
      }

    };
    var phone = (rule, value, callback) => {
      if (value) {
        if (!/^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(value)) {
          return callback(new Error("请输入正确的手机号"));
        } else {
          callback();
        }
      } else {
        return callback(new Error("请输入被授权人手机号"));
      }
    };
    return {
      hostname: window.location.hostname,
      nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
      hostflag: true,
      ClickTrue: true,
      smrzFlag: false,
      showflag: false,
      roleId: "",
      nmb: 120,
      btnflag: true,
      statusType: "1",
      certiFicate: "",
      CompName: "",
      action: this.API.cpus + "v3/file/upload",
      token: {},
      smrzObj: {},
      fileList: [],
      formData: {
        compName: "",
        idCard: "",
        image: "",
        name: "",
        number: "",
        phone: "",
        validCode: "",
      },
      registerData: {
        appKey: "",
        phone: "",
        scene: "",
        sessionId: "",
        sig: "",
        token: "",
      },
      formRule: {
        compName: [
          {
            required: true,
            message:
              "请输入公司/机构/政府企事业单位名称，并与您提交的资质保持一致",
            trigger: "blur",
          },
        ],
        idCard: [{ required: true, validator: idCard, trigger: "blur" }],
        phone: [{ required: true, validator: phone, trigger: "blur" }],
        number: [
          {
            required: true,
            message: "请输入证照编号，并与您提交的资质保持一致",
            trigger: "blur",
          },
        ],
        name: [
          { required: true, message: "请输入被授权人的姓名", trigger: "blur" },
        ],
        validCode: [
          { required: true, message: "请填写手机验证码", trigger: "blur" },
          { min: 6, max: 6, message: "短信验证码需6位字符", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    let data = JSON.parse(localStorage.getItem("userInfo"))
    this.roleId = data.roleId
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.nameTile.forEach((item) => {
      // console.log(item);
      if (this.hostname == item) {
        // console.log(1);
        this.hostflag = false;
      }
    });
    this.init(this.roleId)
  },
  // activated(){
  //   this.init()
  // },
  methods: {
    // handleRemove(file, fileList) {
    //   console.log(file, fileList);
    // },
    // handlePreview(file) {
    //   console.log(file);
    // },
    // handleSuccess(res, file, fileList) {

    //  this.formData.image += res.data.fullpath+','
    //  console.log(this.formData.image);

    // },
    // show(val){
    //   // console.log(1111,'llll');
    //   console.log(val,'zzzz');
    //   this.smrzObj = val
    //   // console.log(this.smrzObj ,'kkk');
    // },
    lega() {
      if (this.statusType == '1') {
        this.$router.push(
          {
            path: '/LegalPerson',
            query: {
              type: 1
            }
          }
        )
        this.smrzFlag = false
      } else {
        this.$router.push(
          {
            path: '/LegalPerson',
            query: {
              type: 1,
              status: 1
            }
          }
        )
        this.smrzFlag = false
      }

    },
    agent() {
      if (this.statusType == '1') {
        this.$router.push(
          {
            path: '/BusinessAgent',
            query: {
              type: 2
            }
          }
        )
        this.smrzFlag = false
      } else {
        this.$router.push(
          {
            path: '/BusinessAgent',
            query: {
              type: 2,
              status: 1
            }
          }
        )
        this.smrzFlag = false
      }

    },
    Individual() {
      this.$router.push(
        {
          path: '/Individual',
          query: {
            type: 3
          }
        }
      )
      this.smrzFlag = false
    },
    handSmrz(tag) {
      this.smrzFlag = true
      this.statusType = tag
      // this.initDrag()
      // window.nc.show();
    },
    closeFlags() {
      // window.nc.reset();
      this.smrzFlag = false
    },
    //合同下载
    async download(url) {
      let name = new Date().getTime();
      const nonce = await getNoce.useNonce();
      axios({
        method: "get",
        url:
          this.API.cpus +
          "v3/file/downloadFile?fileName=" +
          name +
          "&group=oss" +
          "&path=" +
          url + '&bucketName=contract',
        data: {},
        headers: {
          "Content-Type": "application/json",
          Authorization:
            "Bearer " + window.Vue.$common.getCookie("ZTADMIN_TOKEN"),
          'Once': nonce,
        },
        responseType: 'blob',
      })
        .then(function (res) {
          const blob = new Blob([res.data], { type: 'application/octet-stream;charset=utf-8' })
          let link = document.createElement("a");
          let href = window.URL.createObjectURL(blob); //下载链接
          link.href = href;
          link.text = "下载";
          link.download = name + ".PDF"; //下载后文件名
          document.body.appendChild(link);
          link.click(); //点击下载
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(href);
          that.$message({
            message: '下载成功',
            type: "success",
          });
        })
        .catch(err => {
          console.log(err, 'err');
          that.$message({
            message: '下载失败',
            type: "error",
          });
        })
    },
    // closeFlag(){


    //   this.$api.get(this.API.cpus + 'consumerCertificate/info',{},res=>{
    //     console.log(res,'res');
    //     this.smrzObj = res.data
    //         })

    //   this.btnflag = false
    //   this.smrzFlag = false
    // },
    init(roleId) {
      this.$api.get(this.API.cpus + 'consumerCertificate/info', {}, res => {
        if (res.code == 200) {
          this.smrzObj = res.data
          this.$api.get(
            this.API.cpus + "consumerclientinfo/getClientInfo",
            null,
            (res) => {
              // console.log(res, 'res');
              this.certiFicate = res.data.certificate;
              this.CompName = res.data.compName;
              // this.certificate = res.data.certificate;
            }
          );
        }
      })
      if (roleId == '14' || roleId == '12') {
        this.$api.get(this.API.cpus + "consumerclientinfo/checkRealStatus", {},    // 接口地址 {},
          (res) => {
            if (res.code === 200) {
              this.showflag = res.data
            }
          })
      }
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'warning', // 待认证
        1: 'info',    // 合同待签署
        2: 'danger',  // 认证不通过
        3: 'success', // 认证通过
        4: 'warning'  // 合同签署中
      };
      return statusMap[status] || 'danger';
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待认证',
        1: '合同待签署',
        2: '认证不通过',
        3: '认证通过',
        4: '合同签署中'
      };
      return statusMap[status] || '未认证';
    },

    // 获取认证类型文本
    getAuthType(type) {
      const typeMap = {
        1: '企业法定代表人',
        2: '企业代理人',
        3: '个体工商户经营者'
      };
      return typeMap[type] || '-';
    }

  },



  mounted() {
    // this.initDrag();
  },

  computed: {
    // smrzObj(){ 
    //   return this.smrzObj.certificateInfo.status  || ''
    // }
  },

  watch: {
    smrzObj(val, newval) {
      // console.log(val,'val');
      // console.log(newval,'new');
      // this.smrzObj = val
    }
  },
};
</script>
<style lang="less" scoped>
@import '~@/styles/template-common.less';

/* 认证信息展示样式 */
.auth-content {
  padding: 20px;
}

.auth-info-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.auth-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-weight: 500;
  color: #333;
  min-width: 120px;
  font-size: 14px;
}

.info-value {
  flex: 1;
  text-align: right;
  color: #666;
  font-size: 14px;

  .download-btn {
    color: #409eff;
    padding: 0;

    &:hover {
      color: #66b1ff;
    }

    i {
      margin-right: 4px;
    }
  }
}

.reject-reason {
  color: #f56c6c !important;
  font-size: 13px;
  line-height: 1.4;
  text-align: left;
  margin-top: 4px;
}

/* 认证类型选择对话框 */
.auth-type-dialog {
  /deep/ .el-dialog {
    border-radius: 8px;
    overflow: hidden;
  }

  /deep/ .el-dialog__header {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    padding: 20px;
    margin: 0;

    .el-dialog__title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  /deep/ .el-dialog__body {
    padding: 30px;
  }

  .dialog-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 20px;
    }
  }
}

.auth-type-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 24px;
  max-width: 100%;
}

.auth-type-card {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
    transform: translateY(-4px);

    .card-header .card-icon {
      background: #409eff;
      color: white;
      transform: scale(1.1);
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;

  .card-icon {
    width: 48px;
    height: 48px;
    background: #f8fafc;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    i {
      font-size: 24px;
      color: #409eff;
    }
  }

  .card-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

.card-content {
  margin-bottom: 24px;

  .prep-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
  }

  .prep-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .prep-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    line-height: 1.5;

    i {
      color: #67c23a;
      font-size: 16px;
      flex-shrink: 0;
    }
  }
}

.card-footer {
  display: flex;
  justify-content: center;

  .auth-btn {
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border: none;
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.9;
      transform: translateY(-1px);
    }

    i {
      margin-left: 4px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-type-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .auth-type-card {
    padding: 20px;
  }

  .card-header {
    gap: 12px;
    margin-bottom: 16px;

    .card-icon {
      width: 40px;
      height: 40px;

      i {
        font-size: 20px;
      }
    }

    .card-title {
      font-size: 16px;
    }
  }

  .auth-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .info-value {
      text-align: left;
      width: 100%;
    }
  }

  .auth-type-dialog {
    /deep/ .el-dialog {
      width: 95% !important;
      margin: 20px !important;
    }

    /deep/ .el-dialog__body {
      padding: 20px;
    }
  }
}
</style>