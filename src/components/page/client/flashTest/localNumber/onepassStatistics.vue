
<template>
  <div class="simple-template-page">
    <!-- 简约页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">数据统计</h1>
          <p class="page-subtitle">本机校验服务使用情况统计与分析</p>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <el-button type="primary" class="action-btn primary" @click="refreshData">
              <i class="el-icon-refresh"></i>
              刷新数据
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 查询条件区域 -->
        <div class="query-section">
          <div class="section-header">
            <h3 class="section-title">
              <i class="el-icon-search"></i>
              查询条件
            </h3>
          </div>
          <div class="section-content">
            <el-form 
              :inline="true" 
              :model="sensitiveCondition" 
              class="modern-form inline-form"
              label-width="80px" 
              ref="sensitiveCondition"
            >
              <el-form-item label="APP名称" prop="appid">
                <el-select 
                  v-model="sensitiveCondition.appid" 
                  placeholder="请选择APP" 
                  clearable 
                  class="compact-select"
                >
                  <el-option 
                    v-for="(item,index) in AppAll" 
                    :label="item.appName" 
                    :value="item.appid" 
                    :key="index"
                  ></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="时间范围" prop="flag">
                <el-select 
                  v-model="sensitiveCondition.flag" 
                  placeholder="请选择时间范围" 
                  clearable 
                  class="compact-select"
                >
                  <el-option label="今天" value="1"></el-option>
                  <el-option label="昨天" value="2"></el-option>
                  <el-option label="7天" value="3"></el-option>
                  <el-option label="30天" value="4"></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="具体时间" prop="time1">
                <el-date-picker 
                  class="compact-date-picker"
                  v-model="sensitiveCondition.time1"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="hande1"
                >
                </el-date-picker>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" class="action-btn primary" @click="getTableDtate">
                  <i class="el-icon-search"></i>
                  查询
                </el-button>
                <el-button class="action-btn" @click="resetQuery">
                  <i class="el-icon-refresh-left"></i>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 数据概览卡片 -->
        <!-- <div class="overview-section">
          <div class="overview-cards">
            <div class="overview-card total">
              <div class="card-icon">
                <i class="el-icon-data-analysis"></i>
              </div>
              <div class="card-content">
                <div class="card-title">总计费量</div>
                <div class="card-value">{{ getTotalCharge() }}</div>
                <div class="card-trend">
                  <i class="el-icon-arrow-up"></i>
                  <span>较昨日 +12.5%</span>
                </div>
              </div>
            </div>
            
            <div class="overview-card success">
              <div class="card-icon">
                <i class="el-icon-circle-check"></i>
              </div>
              <div class="card-content">
                <div class="card-title">匹配量</div>
                <div class="card-value">{{ getTotalSuccess() }}</div>
                <div class="card-trend">
                  <i class="el-icon-arrow-up"></i>
                  <span>较昨日 +8.3%</span>
                </div>
              </div>
            </div>
            
            <div class="overview-card fail">
              <div class="card-icon">
                <i class="el-icon-circle-close"></i>
              </div>
              <div class="card-content">
                <div class="card-title">不匹配量</div>
                <div class="card-value">{{ getTotalFail() }}</div>
                <div class="card-trend">
                  <i class="el-icon-arrow-down"></i>
                  <span>较昨日 -5.2%</span>
                </div>
              </div>
            </div>
            
            <div class="overview-card rate">
              <div class="card-icon">
                <i class="el-icon-pie-chart"></i>
              </div>
              <div class="card-content">
                <div class="card-title">匹配率</div>
                <div class="card-value">{{ getMatchRate() }}%</div>
                <div class="card-trend">
                  <i class="el-icon-arrow-up"></i>
                  <span>较昨日 +2.1%</span>
                </div>
              </div>
            </div>
          </div>
        </div> -->

        <!-- 图表展示区域 -->
        <div class="chart-section">
          <div class="section-header">
            <h3 class="section-title">
              <i class="el-icon-trend-charts"></i>
              数据趋势图
            </h3>
          </div>
          <div class="section-content">
            <div class="chart-container">
              <chart 
                id="DataSChert" 
                :option='option' 
                class="enhanced-chart"
              ></chart>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 引入时间戳转换
import {formatDate} from '@/assets/js/date.js' 
import Chart from '@/components/publicComponents/Chart'
export default {
    name:'onepassStatistics',
    components: {
        Chart,
    },
     data(){
        return {
            AddAPPID:false,
            //查询条件的值
            sensitiveCondition: {
                type:"onepass",
                flag:'1',
                appid:'',
                beginTime:'',
                endTime:''
            },
            AppAll:[],
            chartData: [], // 存储图表数据
            option:{
                color: ['#409eff','#67c23a','#f56c6c'],
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                    backgroundColor: 'rgba(255,255,255,0.95)',
                    borderColor: '#e8e8e8',
                    borderWidth: 1,
                    textStyle: {
                        color: '#333'
                    }
                },
                legend: {
                    data:['总计费量','匹配量','不匹配量'],
                    top: 10,
                    textStyle: {
                        color: '#666'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#e8e8e8',
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#666',
                        }
                    }
                },
                yAxis:[ {
                    type: 'value',
                    name:'计费数',
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#f0f0f0',
                            type: 'dashed'
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#e8e8e8',
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#666',
                        },
                        formatter: ''
                    },
                },
                 {
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#e8e8e8',
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#67c23a',
                        },
                        formatter: ''
                    },
                    
                },
                {
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#e8e8e8',
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#f56c6c',
                        },
                        formatter: ''
                    },
                    
                }
                ],
                series: [
                    {
                    name:'总计费量',
                    type: "line",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(64,158,255,0.3)',
                            shadowBlur: 10,
                            shadowOffsetY: 5
                        }
                    },
                    itemStyle: {
                        color: '#409eff'
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            color: '#409eff',
                            position:"middle"  //将警示值放在哪个位置，三个值"start","middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                 {
                    name:'匹配量',
                    type: "line",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(103,194,58,0.3)',
                            shadowBlur: 10,
                            shadowOffsetY: 5
                        }
                    },
                    itemStyle: {
                        color: '#67c23a'
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            color: '#67c23a',
                            position:"middle"  //将警示值放在哪个位置，三个值"start","middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                {
                    name:'不匹配量',
                    type: "line",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(245,108,108,0.3)',
                            shadowBlur: 10,
                            shadowOffsetY: 5
                        }
                    },
                    itemStyle: {
                        color: '#f56c6c'
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            color: '#f56c6c',
                            position:"middle"  //将警示值放在哪个位置，三个值"start","middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                ]
            },
        }
    },
    methods: {
        // 刷新数据
        refreshData() {
            this.getTableDtate();
        },
        
        // 重置查询条件
        resetQuery() {
            this.$refs.sensitiveCondition.resetFields();
            this.sensitiveCondition = {
                type: "onepass",
                flag: '1',
                appid: '',
                beginTime: '',
                endTime: ''
            };
            this.getTableDtate();
        },
        
        // 获取总计费量
        getTotalCharge() {
            if (!this.chartData.length) return 0;
            return this.chartData.reduce((sum, item) => sum + (item.chargeNum || 0), 0);
        },
        
        // 获取总匹配量
        getTotalSuccess() {
            if (!this.chartData.length) return 0;
            return this.chartData.reduce((sum, item) => sum + (item.successNum || 0), 0);
        },
        
        // 获取总不匹配量
        getTotalFail() {
            if (!this.chartData.length) return 0;
            return this.chartData.reduce((sum, item) => sum + (item.failNum || 0), 0);
        },
        
        // 获取匹配率
        getMatchRate() {
            const total = this.getTotalCharge();
            const success = this.getTotalSuccess();
            if (total === 0) return 0;
            return ((success / total) * 100).toFixed(1);
        },

        //获取列表数据
        getTableDtate(){
            this.$api.post( this.API.cpus + 'consumerflashhourstatistics/list',this.sensitiveCondition,res=>{
                this.chartData = res.data || [];
                this.option.xAxis.data=[]
                this.option.series[0].data=[]
                this.option.series[1].data=[]
                this.option.series[2].data=[]
                for(let i = 0; i < res.data.length;i++){
                    this.option.xAxis.data.push(res.data[i].statisticsTime); //横坐标
                    this.option.series[0].data.push(res.data[i].chargeNum)//总计费量
                    this.option.series[1].data.push(res.data[i].successNum)//匹配量
                    this.option.series[2].data.push(res.data[i].failNum)//不匹配量
                }
            })
        },
        // //获取查询时间的开始时间和结束时间
        hande1: function (val) {
            if(val){
                this.sensitiveCondition.beginTime = this.moment(val[0]).format("YYYY-MM-DD")+' 00:00:00';
                this.sensitiveCondition.endTime = this.moment(val[1]).format("YYYY-MM-DD")+' 23:59:59';
            }else{
                this.sensitiveCondition.beginTime = '';
                this.sensitiveCondition.endTime = '';
            }
        },
    },
    created(){
        this.$api.get( this.API.cpus + 'consumeronelogin/app/all?type=onepass',{},res=>{
            this.AppAll=res.data
        })
        this.getTableDtate()
    },
    // activated(){
    //     this.$api.get( this.API.cpus + 'consumeronelogin/app/all?type=onepass',{},res=>{
    //         this.AppAll=res.data
    //     })
    // },
    watch:{
        sensitiveCondition:{ 
            handler(val){
                this.getTableDtate()
            },
            deep:true,
            immediate:true
        }
    }
}
</script>

<style lang="less" scoped>
// 引入公用样式
@import '~@/styles/template-common.less';

// 页面特有样式
.simple-template-page {
  .page-header {
    .header-content {
      .header-left {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          margin: 0 0 8px 0;
        }
        
        .page-subtitle {
          font-size: 14px;
          color: #8c8c8c;
          margin: 0;
        }
      }
    }
  }
}

// 查询条件区域
.query-section {
  margin-bottom: 24px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;

  .section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #409eff;
        font-size: 18px;
      }
    }
  }

  .section-content {
    padding: 20px;
  }
}

// 内联表单样式
.inline-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;

  /deep/ .el-form-item {
    margin-bottom: 0;
    margin-right: 0;
  }

  /deep/ .el-form-item__label {
    font-weight: 500;
    color: #262626;
  }
}

// 紧凑选择器
.compact-select,
.compact-date-picker {
  width: 200px;

  /deep/ .el-input__inner {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.2s ease;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

// 数据概览卡片
.overview-section {
  margin-bottom: 24px;

  .overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .overview-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 14px;
        color: #8c8c8c;
        margin-bottom: 4px;
      }

      .card-value {
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 4px;
      }

      .card-trend {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;

        i {
          font-size: 12px;
        }
      }
    }

    &.total {
      .card-icon {
        background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      }
      .card-trend {
        color: #409eff;
      }
    }

    &.success {
      .card-icon {
        background: linear-gradient(135deg, #67c23a 0%, #52c41a 100%);
      }
      .card-trend {
        color: #67c23a;
      }
    }

    &.fail {
      .card-icon {
        background: linear-gradient(135deg, #f56c6c 0%, #ff4d4f 100%);
      }
      .card-trend {
        color: #f56c6c;
      }
    }

    &.rate {
      .card-icon {
        background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
      }
      .card-trend {
        color: #722ed1;
      }
    }
  }
}

// 图表区域
.chart-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #409eff;
        font-size: 18px;
      }
    }
  }

  .section-content {
    padding: 20px;
  }
}

// 图表容器
.chart-container {
  .enhanced-chart {
    height: 400px;
    width: 100%;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .inline-form {
    flex-direction: column;
    align-items: stretch;

    /deep/ .el-form-item {
      width: 100%;
    }

    .compact-select,
    .compact-date-picker {
      width: 100%;
    }
  }


}
</style>

<style>
/* 全局样式覆盖 */
.el-table--small th {
  background: #fafafa;
}

.el-tag {
  border-radius: 4px;
  font-weight: 500;
}
</style>


