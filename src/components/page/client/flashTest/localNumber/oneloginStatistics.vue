
<template>
  <div class="simple-template-page">
    <!-- 简约页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">数据统计</h1>
          <p class="page-subtitle">查看APPID使用情况和计费数据统计</p>
        </div>
        <div class="header-right">
          <el-tag type="info" size="medium">
            <i class="el-icon-data-analysis"></i>
            实时数据
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 查询条件区域 -->
        <div class="search-section">
          <div class="search-header">
            <h3 class="search-title">
              <i class="el-icon-search"></i>
              查询条件
            </h3>
          </div>
          <div class="search-content">
            <el-form 
              :inline="true" 
              :model="sensitiveCondition" 
              class="advanced-search-form" 
              label-width="100px" 
              ref="sensitiveCondition"
            >
              <div class="search-row">
                <el-form-item label="APP名称" class="search-item">
                  <el-select 
                    v-model="sensitiveCondition.appid" 
                    placeholder="请选择APP" 
                    clearable 
                    class="search-select"
                  >
                    <el-option 
                      v-for="(item,index) in AppAll" 
                      :label="item.appName" 
                      :value="item.appid" 
                      :key="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
                
                <el-form-item label="时间范围" class="search-item">
                  <el-select 
                    v-model="sensitiveCondition.flag" 
                    placeholder="请选择时间" 
                    clearable 
                    class="search-select"
                  >
                    <el-option label="今天" value="1"></el-option>
                    <el-option label="昨天" value="2"></el-option>
                    <el-option label="7天" value="3"></el-option>
                    <el-option label="30天" value="4"></el-option>
                  </el-select>
                </el-form-item>
                
                <el-form-item label="具体时间" class="search-item">
                  <el-date-picker 
                    v-model="sensitiveCondition.time1"
                    value-format="yyyy-MM-dd"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="hande1"
                    class="search-date-picker"
                  >
                  </el-date-picker>
                </el-form-item>
                
                <el-form-item class="search-buttons">
                  <el-button 
                    type="primary" 
                    class="search-btn primary"
                    @click="getTableDtate"
                  >
                    <i class="el-icon-refresh"></i>
                    查询
                  </el-button>
                  <el-button 
                    class="search-btn"
                    @click="resetForm"
                  >
                    <i class="el-icon-refresh-left"></i>
                    重置
                  </el-button>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>

        <!-- 图表展示区域 -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="chart-title">
              <i class="el-icon-s-data"></i>
              计费数据统计
            </div>
            <div class="chart-info">
              <el-tag type="success" size="small">
                <i class="el-icon-timer"></i>
                实时更新
              </el-tag>
            </div>
          </div>
          
          <div class="chart-container">
            <chart 
              id="DataSChert" 
              :option='option' 
              class="statistics-chart"
            ></chart>
          </div>
          
          <!-- 图表说明 -->
          <div class="chart-legend">
            <div class="legend-item">
              <div class="legend-color" style="background: #0099FF;"></div>
              <span class="legend-text">总计费量</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #67c23a;"></div>
              <span class="legend-text">匹配量</span>
            </div>
            <div class="legend-item">
              <div class="legend-color" style="background: #d14a61;"></div>
              <span class="legend-text">不匹配量</span>
            </div>
          </div>
        </div>

        <!-- 数据概览卡片 -->
        <!-- <div class="stats-overview">
          <div class="stats-card">
            <div class="stats-icon">
              <i class="el-icon-s-finance"></i>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ getTotalCharge() }}</div>
              <div class="stats-label">总计费量</div>
            </div>
          </div>
          
          <div class="stats-card">
            <div class="stats-icon success">
              <i class="el-icon-circle-check"></i>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ getTotalSuccess() }}</div>
              <div class="stats-label">总匹配量</div>
            </div>
          </div>
          
          <div class="stats-card">
            <div class="stats-icon warning">
              <i class="el-icon-circle-close"></i>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ getTotalFail() }}</div>
              <div class="stats-label">总不匹配量</div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
// 引入时间戳转换
import {formatDate} from '@/assets/js/date.js' 
import Chart from '@/components/publicComponents/Chart'
export default {
    name:'oneloginStatistics',
    components: {
        Chart,
    },
     data(){
        return {
            AddAPPID:false,
            //查询条件的值
            sensitiveCondition: {
                type:"onelogin",
                flag:'1',
                appid:'',
                beginTime:'',
                endTime:''
            },
            AppAll:[],
            option:{
                color: ['#0099FF','#d14a61','#67c23a'],
                grid: {
                    left: '2%',
                    right: '3%',
                    bottom: '1%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                },
                legend: {
                    data:['总计费量','匹配量','不匹配量']
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#0099FF',//坐标值得具体的颜色
                        }
                    }
                },
                yAxis:[ {
                    type: 'value',
                    name:'计费数',
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#0099FF',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                },
                 {
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#67c23a',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                    
                },
                {
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#d14a61',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                    
                }
                ],
                series: [
                    {
                    name:'总计费量',
                    type: "line",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(0,0,0,0.4)',
                            shadowBlur: 6,
                            shadowOffsetY: 10
                        }
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            color: '#0099FF',
                            position:"middle"  //将警示值放在哪个位置，三个值"start","middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                //  {
                //     name:'匹配量',
                //     type: "line",
                //     barWidth:50,
                //     lineStyle: {
                //         normal: {
                //             width: 3,
                //             shadowColor: 'rgba(0,0,0,0.4)',
                //             shadowBlur: 6,
                //             shadowOffsetY: 10
                //         }
                //     },
                //     markPoint: {//最大值 最小值
                //         data: [
                //             {type: 'max', name: '最大值'},
                //             {type: 'min', name: '最小值'}
                //         ]
                //     },
                //     markLine: { //平均值
                //         data: [
                //             {type: 'average', name: '平均值'}
                //         ],
                //         label:{
                //             color: '#67c23a',
                //             position:"middle"  //将警示值放在哪个位置，三个值"start","middle","end"  开始  中点 结束
                //         }
                //     },
                //     data:[],
                //     // smooth: true //折线折脚
                // },
                // {
                //     name:'不匹配量',
                //     type: "line",
                //     barWidth:50,
                //     lineStyle: {
                //         normal: {
                //             width: 3,
                //             shadowColor: 'rgba(0,0,0,0.4)',
                //             shadowBlur: 6,
                //             shadowOffsetY: 10
                //         }
                //     },
                //     markPoint: {//最大值 最小值
                //         data: [
                //             {type: 'max', name: '最大值'},
                //             {type: 'min', name: '最小值'}
                //         ]
                //     },
                //     markLine: { //平均值
                //         data: [
                //             {type: 'average', name: '平均值'}
                //         ],
                //         label:{
                //             color: '#d14a61',
                //             position:"middle"  //将警示值放在哪个位置，三个值"start","middle","end"  开始  中点 结束
                //         }
                //     },
                //     data:[],
                //     // smooth: true //折线折脚
                // },
                ]
            },
        }
    },
    methods: {
        // 重置表单
        resetForm() {
            this.$refs.sensitiveCondition.resetFields();
            this.sensitiveCondition = {
                type: "onelogin",
                flag: '1',
                appid: '',
                beginTime: '',
                endTime: ''
            };
            this.getTableDtate();
        },

        // 获取总计费量
        getTotalCharge() {
            if (this.option.series[0].data.length > 0) {
                return this.option.series[0].data.reduce((sum, val) => sum + val, 0);
            }
            return 0;
        },

        // 获取总匹配量
        getTotalSuccess() {
            return 0; // 暂时返回0，因为匹配量数据被注释了
        },

        // 获取总不匹配量
        getTotalFail() {
            return 0; // 暂时返回0，因为不匹配量数据被注释了
        },

        //获取列表数据
        getTableDtate(){
            this.$api.post( this.API.cpus + 'consumerflashhourstatistics/list',this.sensitiveCondition,res=>{
                this.option.xAxis.data=[]
                this.option.series[0].data=[]
                // this.option.series[1].data=[]
                // this.option.series[2].data=[]
                for(let i = 0; i < res.data.length;i++){
                    this.option.xAxis.data.push(res.data[i].statisticsTime); //横坐标
                    this.option.series[0].data.push(res.data[i].chargeNum)//总计费量
                    // this.option.series[1].data.push(res.data[i].successNum)//匹配量
                    // this.option.series[2].data.push(res.data[i].failNum)//不匹配量
                }
            })
        },
        // //获取查询时间的开始时间和结束时间
        hande1: function (val) {
            if(val){
                this.sensitiveCondition.beginTime = this.moment(val[0]).format("YYYY-MM-DD")+' 00:00:00';
                this.sensitiveCondition.endTime = this.moment(val[1]).format("YYYY-MM-DD")+' 23:59:59';
            }else{
                this.sensitiveCondition.beginTime = '';
                this.sensitiveCondition.endTime = '';
            }
        },
    },
    created(){
        this.$api.get( this.API.cpus + 'consumeronelogin/app/all?type=onelogin',{},res=>{
            this.AppAll=res.data
        })
    },
    activated(){
        this.$api.get( this.API.cpus + 'consumeronelogin/app/all?type=onelogin',{},res=>{
            this.AppAll=res.data
        })
        this.getTableDtate()
    },
    watch:{
        sensitiveCondition:{ 
            handler(val){
                this.getTableDtate()
            },
            deep:true,
            immediate:true
        },
    }
}
</script>

<style lang="less" scoped>
// 引入公用样式
@import '~@/styles/template-common.less';

// 页面特有样式
.simple-template-page {
  .page-header {
    .header-content {
      .header-left {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          margin: 0 0 8px 0;
        }
        
        .page-subtitle {
          font-size: 14px;
          color: #8c8c8c;
          margin: 0;
        }
      }
    }
  }
}

// 查询区域样式
.search-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
  margin-bottom: 20px;

  .search-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    .search-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #409eff;
        font-size: 18px;
      }
    }
  }

  .search-content {
    padding: 20px;
  }
}

// 图表区域样式
.chart-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
  margin-bottom: 20px;

  .chart-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #409eff;
        font-size: 18px;
      }
    }

    .chart-info {
      .el-tag {
        font-weight: 500;
      }
    }
  }

  .chart-container {
    padding: 20px;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;

    .statistics-chart {
      width: 100%;
      height: 320px;
    }
  }

  .chart-legend {
    padding: 16px 20px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    gap: 24px;
    flex-wrap: wrap;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
      }

      .legend-text {
        font-size: 14px;
        color: #666;
      }
    }
  }
}

// 数据概览卡片
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;

  .stats-card {
    background: white;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .stats-icon {
      width: 48px;
      height: 48px;
      border-radius: 8px;
      background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;

      &.success {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
      }

      &.warning {
        background: linear-gradient(135deg, #e6a23c 0%, #f7ba2a 100%);
      }
    }

    .stats-content {
      flex: 1;

      .stats-value {
        font-size: 28px;
        font-weight: 700;
        color: #262626;
        margin-bottom: 4px;
      }

      .stats-label {
        font-size: 14px;
        color: #8c8c8c;
      }
    }
  }
}

// 搜索表单样式
.advanced-search-form {
  .search-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;

    .search-item {
      margin-bottom: 0;

      /deep/ .el-form-item__label {
        color: #333;
        font-weight: 500;
        font-size: 14px;
      }
    }

    .search-buttons {
      margin-bottom: 0;
      margin-left: auto;
      display: flex;
      gap: 12px;
    }
  }
}

.search-select {
  width: 180px;
}

.search-date-picker {
  width: 240px;
}

.search-btn {
  border-radius: 6px;
  font-size: 14px;
  padding: 10px 20px;
  border: 1px solid #d9d9d9;
  background: white;
  color: #333;
  transition: all 0.2s ease;
  font-weight: 500;

  &:hover {
    border-color: #409eff;
    color: #409eff;
  }

  &.primary {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border-color: #409eff;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #66b1ff 0%, #85c1ff 100%);
      border-color: #66b1ff;
      color: white;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-section {
    .search-content {
      padding: 16px;
    }
  }

  .advanced-search-form {
    .search-row {
      flex-direction: column;
      gap: 12px;
    }

    .search-item {
      width: 100%;

      /deep/ .el-form-item__content {
        width: 100%;
      }
    }

    .search-buttons {
      margin-left: 0;
      width: 100%;

      .search-btn {
        flex: 1;
      }
    }
  }

  .search-select,
  .search-date-picker {
    width: 100%;
  }

  .stats-overview {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-section {
    .chart-container {
      padding: 16px;

      .statistics-chart {
        height: 280px;
      }
    }

    .chart-legend {
      padding: 12px 16px;
      gap: 16px;
    }
  }
}

@media (max-width: 480px) {
  .chart-section {
    .chart-container {
      .statistics-chart {
        height: 240px;
      }
    }
  }
}
</style>

<style>
/* 全局样式覆盖 */
.el-table--small th {
  background: #fafafa;
}

.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 图表样式优化 */
#DataSChert {
  border-radius: 8px;
  overflow: hidden;
}
</style>


