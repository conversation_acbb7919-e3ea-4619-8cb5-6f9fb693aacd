
<template>
  <div class="simple-template-page">
    <!-- 简约页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">应用配置</h1>
          <p class="page-subtitle">管理您的APPID和密钥配置</p>
        </div>
        <div class="header-right">
          <el-button v-permission type="primary" class="action-btn primary" @click="AddID()">
            <i class="el-icon-plus"></i>
            新增APPID
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 简约表格区域 -->
        <div class="table-section">
          <div class="table-header">
            <div class="table-title">
              <i class="el-icon-s-grid"></i>
              APPID列表
            </div>
            <div class="table-count">
              共 {{ tableDataObj.total }} 条记录
            </div>
          </div>
          
          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.6)"
              ref="multipleTable"
              border
              :stripe="true"
              :data="tableDataObj.tableData"
              class="enhanced-table"
            >
              <el-table-column label="APP名称" min-width="150">
                <template slot-scope="scope">
                  <span class="content-text">{{ scope.row.appName }}</span>
                </template>
              </el-table-column>
              
              <el-table-column label="APP ID" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="content-text">{{ scope.row.appid }}</span>
                    <el-button 
                      type="text" 
                      class="action-btn-small info"
                      @click="handleCopy(scope.row.appid, $event)"
                      title="复制APP ID"
                    >
                      <i class="el-icon-document-copy"></i>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="KEY" min-width="150">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="content-text">{{ scope.row.keys }}</span>
                    <el-button 
                      type="text" 
                      class="action-btn-small info"
                      @click="ViewKey(scope.row, scope.$index)"
                      title="查看完整KEY"
                    >
                      <i class="el-icon-view"></i>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="审核状态" width="120">
                <template slot-scope="scope">
                  <el-tag 
                    :type="getStatusType(scope.row.status)" 
                    size="small"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="150" fixed="right">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-button
                      type="text"
                      class="action-btn-small edit"
                      @click="SYedit(scope.row)"
                      title="编辑"
                      v-permission
                    >
                      <i class="el-icon-edit"></i>
                      编辑
                    </el-button>
                    <el-button
                      type="text"
                      class="action-btn-small delete"
                      @click="SYdelete(scope.row)"
                      title="删除"
                      v-permission
                    >
                      <i class="el-icon-delete"></i>
                      删除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          
          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination
              class="simple-pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="sensitiveConObj.currentPage"
              :page-size="sensitiveConObj.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑APPID弹窗 -->
    <el-dialog 
      :title="title" 
      :visible.sync="AddAPPID" 
      width="60%"
      class="modern-dialog"
      :close-on-click-modal="false"
    >
      <!-- 产品能力选择 -->
      <div class="config-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="el-icon-s-operation"></i>
            选择产品能力
          </h3>
        </div>
        <div class="section-content">
          <el-radio-group 
            v-model="AddAPPIDform.type" 
            @change="hendRadio"
            class="product-type-group"
          >
            <el-radio label="onelogin" class="product-radio">
              <div class="radio-content">
                <i class="el-icon-mobile-phone"></i>
                <div class="radio-text">
                  <div class="radio-title">APP一键登录</div>
                  <div class="radio-desc">适用于原生APP应用</div>
                </div>
              </div>
            </el-radio>
            <el-radio label="onelogin_web" class="product-radio">
              <div class="radio-content">
                <i class="el-icon-monitor"></i>
                <div class="radio-text">
                  <div class="radio-title">H5一键登录</div>
                  <div class="radio-desc">适用于H5网页应用</div>
                </div>
              </div>
            </el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 应用信息配置 -->
      <div class="config-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="el-icon-edit-outline"></i>
            填写应用信息
          </h3>
        </div>
        <div class="section-content">
          <el-form
            :model="AddAPPIDform"
            :rules="rules"
            ref="AddAPPIDformRef"
            label-width="140px"
            class="modern-form"
          >
            <!-- APP一键登录配置 -->
            <div v-if="AddAPPIDform.type == 'onelogin'" class="config-form">
              <el-form-item label="APP名称" prop="appName">
                <el-input
                  v-model="AddAPPIDform.appName"
                  placeholder="请输入要使用的本机号码校验服务的APP名称"
                  class="compact-input"
                ></el-input>
              </el-form-item>
              
              <el-form-item label="操作系统" class="form-item-with-tip">
                <div class="tip-content">
                  <el-tooltip
                    content="请至少提供一个客户端平台信息用以审核"
                    placement="top"
                  >
                    <i class="el-icon-warning tip-icon"></i>
                  </el-tooltip>
                </div>
              </el-form-item>
              
              <el-form-item label="iOS APP包名" prop="bundle">
                <el-input
                  v-model="AddAPPIDform.bundle"
                  maxlength="100"
                  placeholder="以字母或下划线开头,数字、字母、下划线或点的组合,小于100个字符"
                  class="compact-input"
                ></el-input>
              </el-form-item>
              
              <el-form-item label="Android APP包名" prop="packageName" class="form-item-with-tip">
                <el-input
                  v-model="AddAPPIDform.packageName"
                  maxlength="100"
                  placeholder="以字母或下划线开头,数字、字母、下划线或点的组合,小于100个字符"
                  class="compact-input"
                ></el-input>
                <div class="tip-content">
                  <el-tooltip placement="top">
                    <div slot="content" class="tooltip-content">
                      <p><strong>获取签名的步骤：</strong></p>
                      <p>1、下载签名获取工具APP(Gen_Signature_Android.apk)并在Android手机上安装</p>
                      <p>2、在手机上安装开发者的应用</p>
                      <p>3、启动签名获取工具APP，然后输入开发者的应用APP包名(如：com.cmic.sso.myapplication)，点击"Get Signature"获取签名按钮，在下面会显示获取到的签名字符串。(注意：包签名区分大小写)</p>
                      <p>4、运营商会对提交的APP信息进行审核，只有审核通过的APP才可正常使用本机号码校验服务</p>
                    </div>
                    <i class="el-icon-question tip-icon"></i>
                  </el-tooltip>
                </div>
              </el-form-item>
              
              <el-form-item label="APP包签名" prop="sign">
                <el-input
                  v-model="AddAPPIDform.sign"
                  maxlength="32"
                  placeholder="数字与字母组合,32个字符"
                  class="compact-input"
                ></el-input>
              </el-form-item>
            </div>

            <!-- H5一键登录配置 -->
            <div v-else class="config-form">
              <el-form-item label="APP名称" prop="appName">
                <el-input
                  v-model="AddAPPIDform.appName"
                  placeholder="请输入要使用的本机号码校验服务的APP名称"
                  class="compact-input"
                ></el-input>
              </el-form-item>
              
              <el-form-item label="应用平台" prop="appType">
                <el-radio-group v-model="AddAPPIDform.appType" class="platform-group">
                  <el-radio label="1" class="platform-radio">
                    <i class="el-icon-monitor"></i>
                    H5
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="请求来源" prop="origin">
                <el-input
                  type="textarea"
                  v-model="AddAPPIDform.origin"
                  placeholder="例如：http://oneloginh5.gtapp.xyz"
                  class="compact-textarea"
                  :rows="3"
                ></el-input>
                <div class="form-tip">
                  请求来源origin指发起请求的业务来源即使用H5一键登录的页面的域名。一般为protocal+host，不包含路径等信息。多个origin请使用英文逗号分割。
                </div>
              </el-form-item>
              
              <el-form-item label="集成网页地址" prop="referer">
                <el-input
                  type="textarea"
                  v-model="AddAPPIDform.referer"
                  placeholder="例如：http://oneloginh5.gtapp.xyz/onepass_new"
                  class="compact-textarea"
                  :rows="3"
                ></el-input>
                <div class="form-tip">
                  Referer集成网页地址是指集成了取号js的页面请求头的Referer，若需要在多个页面集成则每个页面都需要进行报备。未报备的页面无法正常进行校验，多个Referer地址使用英文逗号分割。总长度不能超过3000个字符
                </div>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="AddAPPID = false" class="action-btn">取 消</el-button>
        <el-button 
          v-permission
          type="primary" 
          @click="determine('AddAPPIDformRef')"
          class="action-btn primary"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 引入时间戳转换
import { formatDate } from "@/assets/js/date.js";
import clip from '../../../utils/clipboard'
export default {
  name: "onelogin",
  data() {
    return {
      AddAPPID: false,
      title: "添加APPID",
      appid: "",
      //赋值查询条件的值
      sensitiveConObj: {
        type: "onelogin",
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      // 新增修改数据
      AddAPPIDform: {
        type: "onelogin",
        appName: "",
        bundle: "",
        packageName: "",
        sign: "",
        appType: "1",
        origin: "",
        referer: "",
      },
      rules: {
        origin: [
          { required: true, message: "请填写请求来源Orign", trigger: "change" },
        ],
        referer: [
          {
            required: true,
            message: "请填写集成网页地址Referer",
            trigger: "change",
          },
        ],
      },
    };
  },
  methods: {
    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case 'pass':
          return 'success';
        case 'illegal':
          return 'danger';
        default:
          return 'info';
      }
    },
    
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'pass':
          return '审核通过';
        case 'illegal':
          return '审核不通过';
        default:
          return '未审核';
      }
    },

    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "consumeronelogin/page",
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.loading2 = false;
          res.data.records.forEach((e, i) => {
            e.keys = e.key.substr(0, 3) + "****" + e.key.substr(-3);
          });
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
          
        }
      );
    },
    //查询
    Query() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.getTableDtate();
    },
    hendRadio() {
      this.AddAPPIDform.appName = "";
      //   console.log(this.AddAPPIDform.type);
      //   this.$refs['AddAPPIDformRef'].resetFields();
    },
    handleCopy(name,event){
        clip(name, event)
      },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size;
      this.getTableDtate();
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage;
      this.getTableDtate();
    },
    // key展示
    ViewKey(row, index) {
      if (row.keys.length > 10) {
        this.tableDataObj.tableData[index].keys =
          row.key.substr(0, 3) + "****" + row.key.substr(-3);
      } else {
        this.tableDataObj.tableData[index].keys = row.key;
      }
    },
    // 新增
    AddID() {
      this.title = "添加APPID";
      this.appid = "";
      this.AddAPPID = true;
    },
    determine(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.title == "添加APPID") {
            this.$confirms.confirmation(
              "post",
              "确定新增?",
              this.API.cpus + "consumeronelogin",
              this.AddAPPIDform,
              (res) => {
                if (res.code == 200) {
                  this.AddAPPID = false;
                  this.getTableDtate();
                }
              }
            );
          } else {
            this.AddAPPIDform.appid = this.appid;
            this.$confirms.confirmation(
              "put",
              "确定编辑?",
              this.API.cpus + "consumeronelogin",
              this.AddAPPIDform,
              (res) => {
                if (res.code == 200) {
                  this.AddAPPID = false;
                  this.getTableDtate();
                }
              }
            );
          }
        }
      });
      // this.AddAPPIDform.type="onelogin"
    },
    //编辑
    SYedit(val) {
      this.title = "编辑APPID";
      this.appid = val.appid;
      this.AddAPPID = true;
      if(val.type && val.type == "onelogin"){
        this.AddAPPIDform.type = val.type;
        this.AddAPPIDform.appName = val.appName;
        this.AddAPPIDform.bundle = val.bundle;
        this.AddAPPIDform.packageName = val.packageName;
        this.AddAPPIDform.sign = val.sign;
      }else{
          this.AddAPPIDform.type = val.type;
          if(val.appType && val.appType == 1){
            this.AddAPPIDform.appType = val.appType + "";
            this.AddAPPIDform.appName = val.appName;
            this.AddAPPIDform.origin = val.origin;
            this.AddAPPIDform.referer = val.referer;
          }
      }
      
    },
    // 删除
    SYdelete(val) {
      this.$confirms.confirmation(
        "delete",
        "此操作将发送检测数据, 是否继续?",
        this.API.cpus + "consumeronelogin/" + val.id,
        {},
        (res) => {
          this.getTableDtate();
        }
      );
    },
  },
  mounted() {
    this.getTableDtate();
  },
  // activated(){
  //     this.getTableDtate()
  // },
  watch: {
    AddAPPID(val) {
      if (val == false) {
        this.$refs["AddAPPIDformRef"].resetFields();
        this.AddAPPIDform.appName = "";
        this.AddAPPIDform.bundle = "";
        this.AddAPPIDform.packageName = "";
        this.AddAPPIDform.sign = "";
        this.AddAPPIDform.type = "onelogin";
        this.AddAPPIDform.appType = "1";
      }
    },
  },
};
</script>

<style lang="less" scoped>
// 引入公用样式
@import '~@/styles/template-common.less';

// 页面特有样式
.simple-template-page {
  .page-header {
    .header-content {
      .header-left {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          margin: 0 0 8px 0;
        }
        
        .page-subtitle {
          font-size: 14px;
          color: #8c8c8c;
          margin: 0;
        }
      }
    }
  }
}

// 配置区域样式
.config-section {
  margin-bottom: 24px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;

  .section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #409eff;
        font-size: 18px;
      }
    }
  }

  .section-content {
    padding: 20px;
  }
}

// 产品类型选择
.product-type-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;

  .product-radio {
    flex: 1;
    min-width: 200px;
    margin: 0;

    /deep/ .el-radio__input {
      display: none;
    }

    /deep/ .el-radio__label {
      padding: 0;
      width: 100%;
    }

    .radio-content {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      border: 2px solid #e8e8e8;
      border-radius: 8px;
      background: white;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }

      i {
        font-size: 24px;
        color: #409eff;
      }

      .radio-text {
        flex: 1;

        .radio-title {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 4px;
        }

        .radio-desc {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }

    &.is-checked .radio-content {
      border-color: #409eff;
      background: #e6f7ff;
    }
  }
}

// 平台选择
.platform-group {
  .platform-radio {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: white;
    transition: all 0.2s ease;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    &.is-checked {
      border-color: #409eff;
      background: #e6f7ff;
      color: #409eff;
    }

    i {
      font-size: 16px;
    }
  }
}

// 表单样式
.modern-form {
  .config-form {
    .form-item-with-tip {
      position: relative;

      .tip-content {
        position: absolute;
        right: -30px;
        top: 50%;
        transform: translateY(-50%);

        .tip-icon {
          color: #faad14;
          font-size: 16px;
          cursor: pointer;
        }
      }
    }

    .form-tip {
      margin-top: 8px;
      padding: 8px 12px;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 4px;
      font-size: 12px;
      color: #52c41a;
      line-height: 1.5;
    }
  }
}

// 紧凑输入组件
.compact-input,
.compact-textarea {
  /deep/ .el-input__inner,
  /deep/ .el-textarea__inner {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.2s ease;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

// 弹窗样式
.modern-dialog {
  /deep/ .el-dialog__header {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 20px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  /deep/ .el-dialog__body {
    padding: 24px;
  }

  /deep/ .el-dialog__footer {
    background: #fafafa;
    border-top: 1px solid #e8e8e8;
    padding: 16px 20px;
    text-align: right;

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }
}

// 工具提示样式
.tooltip-content {
  max-width: 300px;
  font-size: 12px;
  line-height: 1.5;

  p {
    margin: 4px 0;
  }

  strong {
    color: #262626;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .product-type-group {
    flex-direction: column;

    .product-radio {
      min-width: auto;
    }
  }

  .config-section {
    .section-content {
      padding: 16px;
    }
  }

  .modern-dialog {
    /deep/ .el-dialog {
      width: 95% !important;
      margin: 5vh auto;
    }
  }
}
</style>

<style>
/* 全局样式覆盖 */
.el-table--small th {
  background: #fafafa;
}

.el-tag {
  border-radius: 4px;
  font-weight: 500;
}
</style>


