<template>
  <div class="data-screening-container">
    <!-- 数据概览卡片 -->
    <div class="overview-cards">
      <!-- 今日数据卡片 -->
      <div class="overview-card" v-if="this.$store.state.isDateState == 1">
        <div class="card-header">
          <h3 class="card-title">今日数据</h3>
          <span class="update-time">更新于：{{ updateTime }}</span>
        </div>
        <div class="card-content">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ sendAmounts }}<span class="unit">条</span></div>
              <div class="stat-label">计费条数</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品余额卡片 -->
      <div class="overview-card balance-card">
        <div class="card-header">
          <h3 class="card-title">产品余额</h3>
        </div>
        <div class="card-content">
          <div class="balance-info">
            <div class="balance-item">
              <div class="balance-label">
                <span class="vertical-line"></span>
                产品名称
              </div>
              <div class="balance-value">闪验</div>
            </div>
            <div class="balance-item">
              <div class="balance-label">
                <span class="vertical-line"></span>
                余额（条）
              </div>
              <div class="balance-value highlight">{{ restNumSum }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 趋势图表区域 -->
    <div class="chart-section">
      <div class="section-header">
        <h3 class="section-title">发送趋势图</h3>
        <div class="chart-controls">
          <span class="control-label">时间：</span>
          <el-radio-group
            v-model="getObject.specificTime"
            size="small"
            @change="handleChangeTimeOptions"
            class="time-radio-group"
          >
            <el-radio-button label="1">更新最近十天</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="chart-container">
        <chart id="DataSChert" :option='option' class="trend-chart"></chart>
      </div>
    </div>

    <!-- 统计数据表格区域 -->
    <div class="statistics-section">
      <div class="section-header">
        <h3 class="section-title">统计数据</h3>
        <div class="statistics-controls">
          <span class="control-label">发送时间：</span>
          <el-radio-group
            v-model="getObjects.specificTime"
            size="small"
            @change="handleChangeTimeOptions1"
            class="time-radio-group"
          >
            <el-radio-button label="1">统计月</el-radio-button>
            <el-radio-button label="2">统计年</el-radio-button>
          </el-radio-group>

          <date-plugin
            v-if="getObjects.specificTime == '1'"
            class="date-picker"
            :datePluginValueList="datePluginValueList1"
            @handledatepluginVal="handledatepluginVals"
          />

          <el-date-picker
            v-if="getObjects.specificTime == '2'"
            v-model="year"
            type="monthrange"
            :clearable='false'
            value-format="yyyy-MM"
            @change="handelMonth"
            :picker-options="monthPickerOptions"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            class="month-picker"
          />

          <el-button
            type="primary"
            @click="download"
            class="export-btn"
            icon="el-icon-download"
          >
            导出数据
          </el-button>
        </div>
      </div>

      <div class="table-container">
        <table-tem :tableDataObj="tableDataObjs1" @handelOptionButton="handelOptionButton"></table-tem>
        <div class="pagination-wrapper">
          <el-pagination
            @size-change="handleSizeChange1s"
            @current-change="handleCurrentChange1s"
            :current-page="getObjects.currentPage"
            :page-size="getObjects.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObjs1.totalRow"
            class="table-pagination"
          />
        </div>
      </div>
    </div>

    <!-- 任务报告弹出框 -->
    <el-dialog 
      title="任务报告查看" 
      :visible.sync="sendMsgDialogVisible" 
      width="860px" 
      class="modern-dialog"
    >
      <template v-if="this.$store.state.isDateState == 1">
        <div class="report-charts">
          <div class="chart-item">
            <PieChart id="pie2" width="360px" height="300px" :basicOption="basicOption1"></PieChart>
          </div>
          <div class="chart-item">
            <PieChart id="pie1" width="420px" height="300px" :basicOption="basicOption2"></PieChart>
          </div>
        </div>
        <div class="report-section">
          <h4 class="report-title">发送明细列表</h4>
          <table-tem :tableDataObj="tableDataObj1"></table-tem>
        </div>
      </template>
      <template v-if="this.$store.state.isDateState == 2">
        <div class="report-charts single">
          <PieChart id="pie1" height="300px" :basicOption="basicOption2"></PieChart>
        </div>
      </template>
      <div class="report-section">
        <h4 class="report-title">回执失败代码分析列表</h4>
        <table-tem :tableDataObj="tableDataObj2"></table-tem>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendMsgDialogVisible = false">知道了</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PieChart from '@/components/publicComponents/PieChart' //饼图
import TableTem from '@/components/publicComponents/TableTem'
import Chart from '@/components/publicComponents/Chart'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import moment from 'moment'
export default {
    name: "totalStatistics",
    components: {
        TableTem,
        Chart,
        DatePlugin,
        PieChart
    },
    data () {
        return {
            updateTime:'',
            restNumSum:'',
            sendAmounts:'',
            sendMsgDialogVisible: false, //报告弹出框
            value: '1',
            Time10:'',//储存近10天时间
            getObject:{
                specificTime: '1', //选择那一天
                value:'',
                beginTime:'',
                endTime:'', 
                currentPage:1,
                pageSize:10,
                isDownload:2,
                productId:5,
            },
            getObject1:{
                specificTime: '1', //选择那一天
                beginTime:'',
                endTime:'', 
                currentPage:1,
                pageSize:10,
                isDownload:2,
                productId:5,
                flag:''
            },
            //统计日 统计月
            year: [moment().subtract(5, "Months").format("YYYY-MM"), moment().format('YYYY-MM')],
            getObjects:{
                specificTime: '1', //选择那一天
                beginMonth: moment().subtract(5, "Months").format("YYYY-MM"),
                endMonth: moment().format('YYYY-MM'),
                beginTime:'',
                endTime:'',  
                currentPage:1,
                pageSize:10,
                isDownload:2,
                productId:5,
            },
            getObjects1:{
                specificTime: '1', //选择那一天
                beginMonth: moment().subtract(5, "Months").format("YYYY-MM"),
                endMonth: moment().format('YYYY-MM'),
                beginTime:'',
                endTime:'',  
                currentPage:1,
                pageSize:10,
                isDownload:2,
                productId:5,
                flag:''
            },
            monthPickerOptions: {
                disabledDate: (time) => {
                    const date = new Date();
                    const year = date.getFullYear();
                    let month = date.getMonth() + 1;
                    if (month >= 1 && month <= 9) {
                        month = '0' + month;
                    }
                    const currentdate = year.toString() + month.toString();  // 当前年⽉
                    const page_year = time.getFullYear();
                    let page_month = time.getMonth() + 1;
                    if (page_month >= 1 && page_month <= 9) {
                        page_month = '0' + page_month;
                    }
                    const pageDate = page_year.toString() + page_month.toString();  //页⾯中的年⽉
                    let min_year = date.getFullYear();
                    let min_month = date.getMonth() + 1 - 5;
                    if (min_month <= 0) {
                        min_year = min_year - 1;
                        min_month = 12 + min_month;
                    }
                    if (min_month >= 1 && min_month <= 9) {
                        min_month = '0' + min_month;
                    }
                    const minDate = min_year.toString() + min_month.toString(); // 最⼩年⽉
                    return currentdate < pageDate || pageDate < minDate;
                },
            },
            datePluginValueList: { //日期选择器
                type:"daterange",
                start:"",
                end:'',
                range:'-',
                clearable:false, //是否开启关闭按钮
                pickerOptions:{
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: (time) => {
                        if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
                            return false
                        }
                        if (this.pickerMinDate !=='') {
                            const day30 = 30 * 24 * 3600 * 1000
                            let maxTime = this.pickerMinDate + day30
                            if (maxTime >  Date.now() - 86400000) {
                                maxTime =  Date.now() - 86400000
                            }
                            const minTime = this.pickerMinDate - day30
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                        this.getObject.specificTime='1'
                        return time.getTime() > Date.now() - 86400000;
                    }
                },
                datePluginValue: '',
            },
            datePluginValueList1: { //日期选择器
                type:"daterange",
                start:"",
                end:'',
                range:'-',
                clearable:false, //是否开启关闭按钮
                pickerOptions:{
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate(time) {
                        let curDate = (new Date()).getTime();
                        let three = 180 * 24 * 3600 * 1000;
                        let threeMonths = curDate - three;
                        return time.getTime() > Date.now() || time.getTime() < threeMonths;;
                    }
                },
                datePluginValue: [moment().subtract(10, "days").format("YYYY-MM-DD"), moment().format('YYYY-MM-DD')],
            },
            tableDataObj1: { 
                //列表数据
                tableData: [],
                tableLabel:[//列表表头
                    {prop:"sendAmount",showName:'提交号码计费数',fixed:false},
                    {prop:"successAmount",showName:'成功号计费数',fixed:false},
                    {prop:"failNumber",showName:'失败号计费数',width:120,fixed:false},
                    {prop:"waitNumber",showName:'待返回号码计费数',width:120,fixed:false}
                ],
                tableStyle:{ //列表配置项
                    isSelection:false,//是否复选框
                    isExpand:false,//是否是折叠的
                    isDefaultExpand:false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'120',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            },
            tableDataObj2: { 
                tableData: [],
                tableLabel:[{ //列表表头
                        prop:"failureCodeNoteName",
                        showName:'失败原因',
                        fixed:false
                    },{
                        prop:"codeNoteNum",
                        showName:'数量',
                        fixed:false
                    },{
                        prop:"codeNoteProportion",
                        showName:'占比',
                        fixed:false,
                        formatData: function(val) {
                            if(val != ''){
                                return val+'%';
                            }else{
                                return val;
                            }
                        }
                    }
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    height:200,//是否固定表头
                    isExpand:false,//是否是折叠的
                    isDefaultExpand:false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'120',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            },
            basicOption1:{//发送明细图表
                data:[
                    {value:'',name: '成功'},
                    {value:'',name: '失败'}, 
                    {value:'',name: '待返回'}
                ],
                ledate:['成功','失败','待返回'],
                bgColor: ['#8996E6', '#98D87D','#FFD86E'],
                radius:'62%',
                title:{
                    textStyle:{
                        color:"#999",
                        fontSize:14
                    },
                    text:'发送明细图表',
                    x:'right'
                },

            },
            basicOption2:{//回执失败代码分析图表
                data:[],
                ledate:[],
                bgColor: ['#8996E6', '#49A9EE', '#98D87D','#FFD86E','#F3857C'],
                radius:'62%',
                title: {
                    text:'回执失败代码分析图表',
                    textStyle:{
                        color:"#999",
                        fontSize:14
                    },
                    x:'right'
                },

            },
            option:{
                color: ['#409eff','#67c23a'],
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                    backgroundColor: 'rgba(255,255,255,0.95)',
                    borderColor: '#e8e8e8',
                    borderWidth: 1,
                    textStyle: {
                        color: '#333'
                    }
                },
                legend: {
                    data:['计费数'],
                    top: 10,
                    textStyle: {
                        color: '#666'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#e8e8e8',
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#666',
                        }
                    }
                },
                yAxis:[ {
                    type: 'value',
                    name:'计费数',
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#f0f0f0',
                            type: 'dashed'
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#e8e8e8',
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#666',
                        },
                        formatter: ''
                    },
                },
                 {
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#e8e8e8',
                        }
                    },
                
                    axisLabel: {
                        textStyle: {
                            color: '#67c23a',
                        },
                        formatter: ''
                    },
                    
                }
                ],
                series: [
                    {
                    name:'',
                    type: "bar",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(64,158,255,0.3)',
                            shadowBlur: 10,
                            shadowOffsetY: 5
                        }
                    },
                    itemStyle: {
                        color: '#409eff'
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            position:"middle"  //将警示值放在哪个位置，三个值"start","middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                ]
            },
            sendAmount:[],
            successAmount:[],
            successRate:[],
            tableDataObj:{ //表格数据
                totalRow:0,
                tableData: [],
                tableLabel:[  //列表表头
                    {prop:"createTime",showName:'统计时间',fixed:false},
                    {prop:"sendAmount",showName:'发送量',fixed:false},
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'160',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
                tableOptions:[
                    {
                        optionName:'任务报告',
                        type:'',
                        size:'mini',
                        optionMethod:'missionReport',
                        icon:'el-icon-success'
                    }
                ]
            },
            //月，日统计表
            tableDataObjs1:{ //表格数据
                totalRow:0,
                loading2:false,
                tableData: [],
                tableLabel:[  //列表表头
                    {prop:"sendTime",showName:'时间',fixed:false},
                    {prop:"sendAmount",showName:'计费数',fixed:false},
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'160',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
            }
        }
    },
    created(){
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth()+1;
        var day = date.getDate();
        var nowDate = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day);
        var lastDate = new Date(date - 1000 * 60 * 60 * 24 * 10);//最后30天可以更改，意义：是获取多少天前的时间
        var lastY = lastDate.getFullYear();
        var lastM = lastDate.getMonth()+1;
        var lastD = lastDate.getDate();
        var LDate = lastY + "-" + (lastM < 10 ? "0" + lastM : lastM) + "-"+(lastD < 10 ? "0" + lastD : lastD)
        this.getObjects.beginTime=LDate
        this.getObjects.endTime=nowDate
        this.Time10=[LDate,nowDate]
    },
    methods: {
        getAlldatasMD(){
            Object.assign(this.getObjects1, this.getObjects);
            this.getObjects1.flag = this.getObjects.specificTime;
            this.tableDataObjs1.loading2 = true;
            this.$api.post( this.API.cpus + 'statistics/page',this.getObjects1,res=>{
                console.log(res)
                this.tableDataObjs1.loading2 = false;
                this.tableDataObjs1.tableData = res.data.records;
                this.tableDataObjs1.totalRow = res.data.total;
            })
        },
        getDatas(){
            this.$api.get(this.API.cpus+'consumerdataoverviewday/businessOverview?productId=5',{},res=>{
                // this.restNumSum = res.data.restNumSum;
                this.sendAmounts = res.data.sendAmount;
                this.updateTime = res.data.updateTime;
            })
            this.$api.get(this.API.recharge+'client/balance/5',{},res=>{
                this.restNumSum = res.data.num;
            })
        },
        handleSizeChange1(size){
            this.getObject.pageSize = size;
            // this.getdataPage();
        },
        handleCurrentChange1(currentPage){
            this.getObject.currentPage = currentPage;
            // this.getdataPage();
        },
        handleSizeChange1s(size){
            this.getObjects.pageSize = size;
            this.getAlldatasMD();
        },
        handleCurrentChange1s(currentPage){
            this.getObjects.currentPage = currentPage;
            this.getAlldatasMD();
        },
        //获取统计图表的数据
        getdataPage(){
            Object.assign(this.getObject1, this.getObject);
            this.getObject1.flag = this.getObject.specificTime;
            this.$api.get( this.API.cpus + 'statistics/sendCharts?productId=5',{},res=>{
                //重置统计图表的数据
                this.sendAmount = [];
                this.successAmount = [];
                this.successRate = [];
                this.option.xAxis.data = []; //横坐标
                
                for(let i = 0; i < res.data.length;i++){
                    this.option.xAxis.data.push(res.data[i].statisticsTime); //横坐标
                    this.sendAmount.push(res.data[i].sendAmount); //发送量
                    this.successAmount.push(res.data[i].successAmount);//成功
                    this.successRate.push(res.data[i].successRate);//成功率
                }
                //获取今天的列表数据
                this.tableDataObj.tableData = res[1];//列表赋值
                this.getObject.value = ''
                this.setOptionItem();
            })
        },
        //设置统计图的展示数据
        setOptionItem(){
            if(this.value == '1'){
                this.option.series[0].data = this.sendAmount;
                this.option.series[0].name = '发送量';
                // this.option.yAxis[0].axisLabel.formatter = '{value}'
                // this.option.tooltip.formatter =' ';
                if(this.$store.state.isDateState == 1){
                    // this.option.series[1].data = this.successRate;
                    // this.option.series[1].name = '发送成功率';
                    // this.option.yAxis[1].axisLabel.formatter = '{value}%'
                    // this.option.tooltip.formatter =' {b}  <br/>发送量 ：{c}  <br/>发送成功率 ：{c1} %';
                }
            }
        },
        handelOptionButton: function(val){
            //任务报告
            if(val.methods=='missionReport'){
                //发送明细（图表）
                this.basicOption1.data[0].value = val.row.successAmount;
                this.basicOption1.data[1].value = val.row.failNumber;
                this.basicOption1.data[2].value = val.row.waitNumber;
                //发送明细（列表）
                this.tableDataObj1.tableData = [val.row];
               
                //获取回执失败代码数据
                this.$api.get(this.API.cpus + 'statistics/statusReport?flag='+this.getObject.specificTime+'&day='+val.row.createTime+'&productId=5',{},res=>{
                    this.basicOption2.ledate = [];
                    this.basicOption2.data = [];
                    if(res){
                        this.tableDataObj2.tableData = res.data;
                        for(let i =0 ; i< res.data.length ; i++){
                            this.basicOption2.ledate.push(res.data[i].failureCodeNoteName);
                            this.basicOption2.data.push({name:res.data[i].failureCodeNoteName,value : res.data[i].codeNoteNum});
                        }
                    }
                })
                this.sendMsgDialogVisible = true
            }
        },
        //时间范围选择
        handledatepluginVal: function(val1,val2){
            if(val1){
                this.getObject.specificTime='5'
                this.getObject.beginTime = val1;
                this.getObject.endTime = val2;
            }else{
                this.getObject.specificTime = '1'
                this.getObject.beginTime = '';
                this.getObject.endTime = '';
            }
        },
        handelMonth(val) {
            if (val) {
                this.getObjects.beginMonth = val[0]
                this.getObjects.endMonth = val[1]
            } else {
                this.getObjects.beginMonth = ''
                this.getObjects.endMonth = ''
            }
        },
        //时间范围选择(月，天)
        handledatepluginVals: function(val1,val2){
            if(val1){
                this.getObjects.specificTime='1'
                this.getObjects.beginTime = val1;
                this.getObjects.endTime = val2;
            }else{
                this.getObjects.specificTime = '2'
                this.getObjects.beginTime = '';
                this.getObjects.endTime = '';
            }
        },
        handleChangeTimeOptions: function(){
            this.datePluginValueList.datePluginValue = ''  
        },
        handleChangeTimeOptions1: function(){
            this.datePluginValueList1.datePluginValue= [moment().subtract(10, "days").format("YYYY-MM-DD"), moment().format('YYYY-MM-DD')]     
        },
         // 导出数据
         download() {
            if (this.tableDataObjs1.tableData.length == 0) {
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            } else {
                this.$File.export(this.API.cpus + 'statistics/exportData', this.getObjects1, '统计总览数据导出.xls');
            }
        },
    },
    mounted(){
        this.getDatas();
        this.getdataPage();
        // this.getnumList();
        this.getAlldatasMD();
    },
    // activated(){
    //     this.getDatas();
    //     this.getdataPage();
    //     // this.getnumList();
    //     this.getAlldatasMD();
    // },
    watch:{
        //监听选择时间的变化
        getObject:{
            handler(val){
                window.Vue.cancel();
                // this.getdataPage();
            },
            deep:true,
        },
        //监听 统计天，统计月 选择时间的变化
        getObjects:{
            handler(val){
                window.Vue.cancel();
                this.getAlldatasMD();
            },
            deep:true,
        },
        //监听是否选择月
        'getObjects.specificTime'(val){
            if(val == 2){
               this.getObjects.beginMonth = moment().subtract(5, "Months").format("YYYY-MM")
                this.getObjects.endMonth = moment().format('YYYY-MM')
               this.getObjects.beginTime = '';
                this.getObjects.endTime = '';
            }else{
                this.getObjects.beginMonth = ''
                this.getObjects.endMonth = ''
                this.getObjects.beginTime = this.Time10[0];
                this.getObjects.endTime = this.Time10[1];
            }
        },
        //发送量，成功量的变换
        value:{
            handler(val){
                this.setOptionItem();
            },
            deep:true,
            immediate:true
        }
    }
}
</script>

<style lang="less" scoped>
// 引入公用样式
@import '~@/styles/template-common.less';

// 数据筛选容器
.data-screening-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

// 数据概览卡片
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }

    .update-time {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .card-content {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      &.simple {
        grid-template-columns: 1fr;
      }

      .stat-item {
        text-align: center;
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #f0f0f0;

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 8px;

          .unit {
            font-size: 14px;
            color: #8c8c8c;
            margin-left: 4px;
          }
        }

        .stat-label {
          font-size: 14px;
          color: #8c8c8c;
        }
      }
    }

    .balance-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .balance-item {
        flex: 1;
        text-align: center;

        .balance-label {
          font-size: 14px;
          color: #8c8c8c;
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .vertical-line {
            width: 2px;
            height: 16px;
            background: #409eff;
            border-radius: 1px;
          }
        }

        .balance-value {
          font-size: 20px;
          font-weight: 600;
          color: #262626;

          &.highlight {
            color: #409eff;
          }
        }
      }
    }
  }
}

// 图表区域
.chart-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }

    .chart-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .control-label {
        font-size: 14px;
        color: #666;
      }

      .time-radio-group {
        /deep/ .el-radio-button__inner {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          background: white;
          color: #666;
          transition: all 0.2s ease;

          &:hover {
            border-color: #409eff;
            color: #409eff;
          }
        }

        /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
          background: #409eff;
          border-color: #409eff;
          color: white;
        }
      }
    }
  }

  .chart-container {
    .trend-chart {
      height: 400px;
      width: 100%;
    }
  }
}

// 统计数据区域
.statistics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }

    .statistics-controls {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .control-label {
        font-size: 14px;
        color: #666;
      }

      .time-radio-group {
        /deep/ .el-radio-button__inner {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          background: white;
          color: #666;
          transition: all 0.2s ease;

          &:hover {
            border-color: #409eff;
            color: #409eff;
          }
        }

        /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
          background: #409eff;
          border-color: #409eff;
          color: white;
        }
      }

      .date-picker,
      .month-picker {
        /deep/ .el-input__inner {
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          transition: all 0.2s ease;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }

      .export-btn {
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  .table-container {
    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;

      .table-pagination {
        /deep/ .el-pagination__total,
        /deep/ .el-pagination__jump {
          color: #666;
        }

        /deep/ .el-pager li {
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            color: #409eff;
          }

          &.active {
            background: #409eff;
            color: white;
          }
        }
      }
    }
  }
}

// 弹窗样式
.modern-dialog {
  /deep/ .el-dialog__header {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 20px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }
  }

  /deep/ .el-dialog__body {
    padding: 24px;
  }

  /deep/ .el-dialog__footer {
    background: #fafafa;
    border-top: 1px solid #e8e8e8;
    padding: 16px 20px;
    text-align: right;

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }
}

// 报告图表
.report-charts {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;

  &.single {
    justify-content: center;
  }

  .chart-item {
    flex: 1;
    text-align: center;
  }
}

.report-section {
  .report-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8e8e8;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-screening-container {
    padding: 16px;
  }

  .overview-cards {
    grid-template-columns: 1fr;
  }

  .chart-section,
  .statistics-section {
    .section-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
    }

    .statistics-controls {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;

      .date-picker,
      .month-picker {
        width: 100%;
      }
    }
  }

  .report-charts {
    flex-direction: column;
  }
}
</style>

<style>
/* 全局样式覆盖 */
.el-table--small th {
  background: #fafafa;
}

.el-tag {
  border-radius: 4px;
  font-weight: 500;
}
</style>
