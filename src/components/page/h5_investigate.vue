<template>
  <div class="h5_from">
    <!-- <div class="h5_header">
                <img src="./images/nav.png" alt="" class="image">
            </div> -->
    <div class="h5_content">
      <div class="banner">
        <img src="../../assets/images//image 124.png" alt="" class="image" />
      </div>
      <div v-if="flag">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="10px"
          style="margin: 10px 0"
        >
          <el-form-item
            label=""
            style="width: 100%; height: 32px; margin-bottom: 50px"
            prop="age"
          >
            <div class="text">1、您的年龄？</div>
            <el-radio-group
              v-model="form.age"
              style="width: 100%; height: 32px"
            >
              <el-radio :label="1">18以下</el-radio>
              <el-radio :label="6">18-28</el-radio>
              <el-radio :label="9">28-38</el-radio>
              <el-radio :label="5">38-48</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label=""
            style="width: 100%; height: 32px; margin-bottom: 50px"
            prop="pleased"
          >
            <div class="text">2、您对本次会展是否满意？</div>
            <el-radio-group
              v-model="form.pleased"
              style="width: 100%; height: 32px"
            >
              <el-radio :label="1">满意</el-radio>
              <el-radio :label="6">一般</el-radio>
              <el-radio :label="9">不满意</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label=""
            style="width: 100%; height: 32px; margin-bottom: 50px"
            prop="acquaintance"
          >
            <div class="text">3、您一般通过哪些途径了解会展？</div>
            <el-radio-group
              v-model="form.acquaintance"
              style="width: 100%; height: 32px"
            >
              <el-radio :label="1">网络</el-radio>
              <el-radio :label="6">电视</el-radio>
              <el-radio :label="9">海报</el-radio>
              <el-radio :label="5">短信</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label=""
            style="width: 100%; height: 32px; margin-bottom: 50px"
            prop="attract"
          >
            <div class="text">4、以下哪种会展信息最能吸引您？</div>
            <el-radio-group
              v-model="form.attract"
              style="width: 100%; height: 32px"
            >
              <el-radio :label="1">展会主题</el-radio>
              <el-radio :label="6">展会规模</el-radio>
              <el-radio :label="9">交通便利</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label=""
            style="width: 100%; height: 32px; margin-bottom: 50px"
            prop="purpose"
          >
            <div class="text">5、您参加会展的目的是什么？</div>
            <el-radio-group
              v-model="form.purpose"
              style="width: 100%; height: 32px"
            >
              <el-radio :label="1">单纯参观</el-radio>
              <el-radio :label="6">学习专业知识</el-radio>
              <el-radio :label="9">其他</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item style="width: 95%; height: 67px" prop="propose">
            <div class="text">6、您对本次会展的看法及建议？</div>
            <el-input
              type="textarea"
              style="width: 100%; height: 100%"
              v-model="form.propose"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div v-else class="success">
         <div class="item">
            <i class="el-icon-success" style="color:#67C23A"></i>
            <span style="margin-left:5px">提交成功</span>
         </div>
      </div>
    </div>
    <div v-if="flag" class="h5_footer">
      <div class="submit" @click="onSubmit('form')">提交</div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        age: "", //年龄
        pleased: "", //满意
        acquaintance: "", //了解
        attract: "",
        purpose: "", //目的
        propose: "", //建议
      },
      flag:true,
      rules: {
        age: [{ required: true, message: "请选择您的年龄", trigger: "change" }],
        pleased: [
          {
            required: true,
            message: "请选择您是否满意这次会展",
            trigger: "change",
          },
        ],
        acquaintance: [
          { required: true, message: "请选择您的了解途径", trigger: "change" },
        ],
        attract: [
          { required: true, message: "请选择吸引您的内容", trigger: "change" },
        ],
        purpose: [
          {
            required: true,
            message: "请选择您参加会展的目的",
            trigger: "change",
          },
        ],
        propose: [
          { required: true, message: "请输入您的建议", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    onSubmit(formName) {
      console.log(this.$refs[formName], "formName");
      this.$refs[formName].validate((valid) => {
        if (valid) {
        //   this.$notify({ type: "success", message: "提交成功" });
        this.flag = !this.flag
          this.$refs[formName].resetFields();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>
<style scoped>
.h5_from {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.h5_header {
  width: 100%;
  height: 44px;
}

.h5_content {
  flex: 1;
  background: #f6f6f6;
  overflow: auto;
}

.h5_footer {
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  /* margin-top: 37px; */
  /* color: #fff;
    background: #4e95ff; */
  display: flex;
  justify-content: space-between;
}
.submit {
  flex: 1;
  color: #fff;
  background: #4e95ff;
}
.image {
  width: 100%;
  height: 100%;
}

.banner {
  width: 100%;
  height: 206px;
}
.text{
    color: #000;
}
.success{
    width: 100%;
    height: 550px;
    display: flex;
    align-items: center;
}
.item{
    width: 100px;
    margin: 0 auto;
    font-size: 18px;
}
</style>
<style>
.el-form-item__label {
  font-size: 12px;
  line-height: 32px;
}

.el-input__inner {
  height: 100%;
}
.el-textarea__inner {
    min-height: 70px !important;
  /* height: 100%; */
}
</style>