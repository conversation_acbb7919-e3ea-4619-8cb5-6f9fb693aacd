<template>
  <div id="login" v-if="isShowBar != 0">
    <div
      v-if="hostname != 'partner-test.zthysms.com'"
      style="
        padding-left: 18%;
        border-bottom: 1px solid #eeeeee52;
        height: 130px;
      "
    >
      <div style="width: 200px; float: left; margin-top: 33px; height: 60px">
        <img
          v-if="logoData"
          style="width: 100%; height: 100%"
          :src="logoData.logo"
          alt=""
        />
        <img
          v-else
          style="width: 100%; height: 100%"
          src="../../../assets/images/logob.png"
          alt=""
        />
      </div>
      <div
        style="
          float: left;
          height: 30px;
          border-right: 1px solid #fff;
          margin: 51px 20px 0 20px;
        "
      ></div>
      <div
        style="float: left; height: 63px; line-height: 63px; margin-top: 33px"
      >
        <span
          v-if="logoData"
          style="font-size: 22px; font-weight: 500; color: #fff"
          >{{ logoData.reminder }}</span
        >
        <span v-else style="font-size: 22px; font-weight: 500; color: #fff"
          >助通融合云通信管理系统</span
        >
      </div>
    </div>
    <div style="position: absolute; left: 18%; top: 26%; width: 550px">
      <img style="width: 100%" src="../../../assets/images/zxts.png" alt="" />
    </div>
    <div class="loginStyle">
      <div class="loginRight">
        <div class="logo_hy">
          <span>欢迎注册</span>
          <span style="font-size: 14px; margin-left: 100px">
            已有账号,
            <span style="color: #16a589; cursor: pointer" @click="goback()"
              >去登录</span
            >
          </span>
        </div>
        <el-form
          sjk_form_tag="132a64c2c322332e82ad0fda37815238"
          :model="registerForms"
          ref="registerForms"
          :rules="rules"
          style="width: 75%; margin: 12px auto 0"
          class="iptStyle"
          v-show="activeName == 'second'"
        >
          <el-form-item prop="username">
            <i
              class="icon iconfont icon-yonghuming"
              style="
                color: #666;
                font-size: 20px;
                position: absolute;
                top: 3px;
                z-index: 99;
                left: 8px;
              "
            ></i>
            <el-input
              sjk_form_ctag="935ebb80014a7209512f04e58bd795df"
              v-model="registerForms.username"
              placeholder="请输入账户名称"
            ></el-input>
          </el-form-item>
          <el-form-item prop="compName">
            <i
              class="icon iconfont icon-yonghuming"
              style="
                color: #666;
                font-size: 20px;
                position: absolute;
                top: 3px;
                z-index: 99;
                left: 8px;
              "
            ></i>
            <el-input
              sjk_form_ctag="cc391060d3b02028060ab22a369dd4e0"
              v-model="registerForms.compName"
              placeholder="请输入公司名"
            ></el-input>
          </el-form-item>
          <el-form-item
            prop="phone"
            :rules="
              filter_rules({
                required: true,
                type: 'mobile',
                message: '请输入正确手机号',
              })
            "
          >
            <i
              class="el-icon-phone"
              style="
                color: #666;
                font-size: 20px;
                position: absolute;
                top: 10px;
                z-index: 99;
                left: 8px;
              "
            ></i>
            <el-input
              sjk_form_ctag="299bcd779c37f5c56aad5c0173e33518"
              v-model="registerForms.phone"
              placeholder="输入手机号码"
              @input="ncrj"
            ></el-input>
          </el-form-item>
          <el-form-item :class="!ncflag ? 'ncrj' : ''" prop="nc">
            <div style="height: 30px">
              <div id="nc"></div>
            </div>
          </el-form-item>
          <el-form-item
            v-if="verify"
            prop="validCode"
            style="position: relative"
          >
            <i
              class="icon iconfont icon-yanzhengma"
              style="
                color: #666;
                font-size: 18px;
                position: absolute;
                top: 3px;
                z-index: 99;
                left: 8px;
              "
            ></i>
            <el-input
              sjk_form_ctag="f87bf802ff44f5a972ec3d3a6fb96364"
              v-model="registerForms.validCode"
              style="width: 220px"
              placeholder="输入验证码"
            ></el-input>
            <el-button
              type="primary"
              @click="getPhoneCode(registerForms.codePhone)"
              style="
                font-size: 14px;
                width: 110px;
                position: absolute;
                top: 1px;
              "
              v-if="nmb == 120"
              >获取验证码</el-button
            >
            <el-button
              type="primary"
              style="font-size: 14px; width: 110px"
              disabled
              v-else
              >重新获取({{ nmb }})</el-button
            >
          </el-form-item>

          <el-form-item prop="checkoutBox">
            <el-checkbox v-model="checked">我已阅读同意</el-checkbox>
            <span @click="contract" style="color: #16a589"
              >《助通云通信用户协议》</span
            >
          </el-form-item>
          <el-form-item style="margin-top: 26px">
            <el-button
              sjk_form_stag="132a64c2c322332e82ad0fda37815238"
              type="primary"
              @click.native.prevent="codeLogin('registerForms')"
              @keyup.enter.native="codeLogin('registerForms')"
              style="font-size: 14px"
              >立即注册</el-button
            >
          </el-form-item>
        </el-form>
        <div
          v-if="IEflag"
          style="font-size: 12px; text-align: center; color: red"
        >
          <p>检测您现在使用的浏览器是IE内核,暂无法正常登陆</p>
          <p>
            请移至chrome,firefox,360(极速版),搜狗,QQ(极速版),Edge,UC,猎豹等浏览器
          </p>
        </div>
      </div>
    </div>
  </div>
  <div
    v-else
    class="login"
    :style="`width: 100%;
    height: 100%;
    background: url(${url}) no-repeat;
    background-size:100% 100%;
    overflow: auto;`"
  >
    <div class="h5_1" style="margin: 10px">
      <img src="../../../assets/images/h5_1.png" width="120px" alt="" />
    </div>
    <div class="h5_2" style="margin: 0 10px">
      <img src="../../../assets/images/h5_t.png" width="100%" alt="" />
    </div>
    <!-- <div class="h5_2" style="margin: 0 10px">
      <img src="../../../assets/images/h5_3.png" width="100%" alt="" />
    </div>
    <div class="h5_2" style="margin: 0 10px">
      <img src="../../../assets/images/h5_4.png" width="100%" alt="" />
    </div> -->
    <div class="h5_register" style="background: #fff; margin: 0 10px">
      <div
        class="wlcome"
        style="
          width: 100%;
          text-align: center;
          font-size: 16px;
          height: 45px;
          line-height: 45px;
        "
      >
        欢迎注册
      </div>
      <el-form
        sjk_form_tag="132a64c2c322332e82ad0fda37815238"
        :model="registerForms"
        ref="registerForms"
        :rules="rules"
        style="width: 75%; margin: 12px auto 0"
        class="iptStyle"
        v-show="activeName == 'second'"
      >
        <el-form-item prop="username">
          <i
            class="icon iconfont icon-yonghuming"
            style="
              color: #666;
              font-size: 20px;
              position: absolute;
              top: 3px;
              z-index: 99;
              left: 8px;
            "
          ></i>
          <el-input
            sjk_form_ctag="935ebb80014a7209512f04e58bd795df"
            v-model="registerForms.username"
            placeholder="请输入账户名称"
          ></el-input>
        </el-form-item>
        <el-form-item prop="compName">
          <i
            class="icon iconfont icon-yonghuming"
            style="
              color: #666;
              font-size: 20px;
              position: absolute;
              top: 3px;
              z-index: 99;
              left: 8px;
            "
          ></i>
          <el-input
            sjk_form_ctag="cc391060d3b02028060ab22a369dd4e0"
            v-model="registerForms.compName"
            placeholder="请输入公司名"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="phone"
          :rules="
            filter_rules({
              required: true,
              type: 'mobile',
              message: '请输入正确手机号',
            })
          "
        >
          <i
            class="el-icon-phone"
            style="
              color: #666;
              font-size: 20px;
              position: absolute;
              top: 10px;
              z-index: 99;
              left: 8px;
            "
          ></i>
          <el-input
            sjk_form_ctag="299bcd779c37f5c56aad5c0173e33518"
            v-model="registerForms.phone"
            placeholder="输入手机号码"
            @input="ncrj"
          ></el-input>
        </el-form-item>
        <el-form-item :class="!ncflag ? 'ncrj' : ''" prop="nc">
          <div style="height: 30px">
            <div id="nc"></div>
          </div>
        </el-form-item>
        <el-form-item v-if="verify" prop="validCode" style="position: relative">
          <i
            class="icon iconfont icon-yanzhengma"
            style="
              color: #666;
              font-size: 18px;
              position: absolute;
              top: 3px;
              z-index: 99;
              left: 8px;
            "
          ></i>
          <el-input
            sjk_form_ctag="f87bf802ff44f5a972ec3d3a6fb96364"
            v-model="registerForms.validCode"
            style="width: 135px"
            placeholder="输入验证码"
          ></el-input>
          <el-button
            type="primary"
            @click="getPhoneCode(registerForms.codePhone)"
            style="font-size: 14px; width: 100px; position: absolute; top: 1px"
            v-if="nmb == 120"
            >获取验证码</el-button
          >
          <el-button
            type="primary"
            style="font-size: 14px; width: 110px"
            disabled
            v-else
            >重新获取({{ nmb }})</el-button
          >
        </el-form-item>

        <el-form-item prop="checkoutBox">
          <el-checkbox v-model="checked">我已阅读同意</el-checkbox>
          <span style="color: #16a589; font-size: 12px" @click="contract"
            >《助通云通信用户协议》</span
          >
        </el-form-item>
        <el-form-item>
          <el-button
            sjk_form_stag="132a64c2c322332e82ad0fda37815238"
            type="primary"
            @click.native.prevent="codeLogin('registerForms')"
            @keyup.enter.native="codeLogin('registerForms')"
            style="font-size: 14px; width: 100%; margin-bottom: 10px"
            >立即注册</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!-- <div>11</div> -->
  </div>
</template>
<script>
var axios = require("axios");
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  data() {
    var username = (rule, value, callback) => {
      if (value != "") {
        // console.log(value.length, "ll");
        if (value.length < 6) {
          callback(new Error("6-50英文字母数字组合"));
        } else {
          let reg = /^[A-Za-z][A-Za-z0-9]{5,50}$/;
          if (reg.test(value)) {
            this.$api.get(
              this.API.upms + "online/existUser/" + value,
              {},
              (res) => {
                if (res.data == 0) {
                  callback();
                } else {
                  callback(new Error("用户名已经存在"));
                }
              }
            );
          } else {
            callback(new Error("由大小写字母开头与数字组合"));
          }
        }
      } else {
        callback(new Error("用户名不能为空"));
      }
    };
    // var username = (rule, value, callback)=>{
    //   if(!/^[a-zA-Z0-9]{6,50}$/.test(value)){
    //     return callback(new Error("用户名格式不正确"));
    //   }else if(value==''){
    //     return callback(new Error("请输入用户名"));
    //   }else{
    //     callback()
    //   }
    // }
    return {
      hostname: window.location.hostname,
      name: "",
      IEflag: false, //检测是否IE
      ncflag: false,
      isShowBar: "0",
      nmb: 120,
      ClickTrue: true,
      verify: false,
      url: require("../../../assets/images/zch5.png"),
      activeName: "second",
      loginForm: {
        password: "",
        code: "",
        imgSrc: "",
        randomStr: "",
      },
      rules: {
        // username: [
        //   { required: true, message: "请输入姓名", trigger: "blur" },
        //   { max: 20, message: "用户名最多至20位", trigger: "blur" },
        // ],
        compName: [
          { required: true, message: "请输入公司名称", trigger: "blur" },
          { max: 20, message: "用户名最多至20位", trigger: "blur" },
        ],
        code: [
          { required: true, message: "请填写验证码", trigger: "blur" },
          { min: 4, max: 4, message: "图形验证需4位字符", trigger: "blur" },
        ],
        // nc: [
        //   { required: true, message: "请滑动人机验证", trigger: "blur" },
        // ],
        password: [
          { required: true, message: "请填写密码", trigger: "blur" },
          { max: 16, message: "密码最多至16位", trigger: "blur" },
        ],
        username: [
          { required: true, validator: username, trigger: "blur" },
          { max: 20, message: "用户名最多至20位", trigger: "blur" },
        ],
        validCode: [
          { required: true, message: "请填写手机验证码", trigger: "blur" },
          { min: 6, max: 6, message: "短信验证码需6位字符", trigger: "blur" },
        ],
      },
      checked: false,
      getVerifications: false,
      tableData: [
        {
          date: "1",
          name: "12365485415",
        },
        {
          date: "2",
          name: "18755896587",
        },
        {
          date: "3",
          name: "15698569878",
        },
      ],
      loginRadio: "",
      registerForms: {
        compName: "",
        phone: "",
        validCode: "",
        username: "",
        source: "web", //来源
      },
      registerData: {
        appKey: "",
        phone: "",
        scene: "",
        sessionId: "",
        sig: "",
        token: "",
        name: "",
      },
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      // portraitUrl:state=>state.portraitUrl,
      logoData: (state) => state.logoData,
    }),
  },
  methods: {
    // 获取项目绝对路径
    ...mapActions([
      //比如'movies/getHotMovies
      "resetStates",
      "savelogoData",
      "saveUrl",
    ]),
    contract() {
      // console.log(111);
      let { href } = this.$router.resolve({ path: "/protocol" });
      // this.$router.push("/protocol")
      window.open(href, "_blank");
    },
    getDomain() {
      var hostname = window.location.hostname;
      var name = hostname.split(".");
      // console.log(name);
      this.name = name[name.length - 2] + "." + name[name.length - 1];
      // console.log(this.name);
      // console.log(hostname,'name');
      // hostname.substring(hostname.indexof('.'))
      // console.log(hostname,'ll');
    },
    ncrj() {
      // console.log(this.registerForms.phone,'lll');
      this.registerData.phone = this.registerForms.phone;
      let phone = this.registerForms.phone;
      this.ncflag = true;
      // console.log(a.length);
      // if(phone.length == 11){

      // }else{
      //   this.ncflag = false
      //   this.verify = false
      // }
      // if(/^[1-9][\d]*$/.test(this.registerForms.phone)){
      //   clearTimeout(this.timer);
      //   this.timer = setTimeout(() => {
      //       this.ncflag = true
      //   }, 200);
      // }else {
      //   this.$message({
      //     message: "请输入正确手机号",
      //     type: "warning",
      //   });
      // }
      // if(){

      // }
      // this.ncflag = true
    },
    //人机验证
    initDrag() {
      var that = this;
      // 实例化nc
      AWSC.use("nc", function (state, module) {
        // 初始化
        window.nc = module.init({
          // 应用类型标识。它和使用场景标识（scene字段）一起决定了滑动验证的业务场景与后端对应使用的策略模型。您可以在人机验证控制台的配置管理页签找到对应的appkey字段值，请务必正确填写。
          appkey: "FFFF0N00000000009C2B",
          //使用场景标识。它和应用类型标识（appkey字段）一起决定了滑动验证的业务场景与后端对应使用的策略模型。您可以在人机验证控制台的配置管理页签找到对应的scene值，请务必正确填写。
          scene: "nc_register",
          // 声明滑动验证需要渲染的目标ID。
          renderTo: "nc",
          //前端滑动验证通过时会触发该回调参数。您可以在该回调参数中将会话ID（sessionId）、签名串（sig）、请求唯一标识（token）字段记录下来，随业务请求一同发送至您的服务端调用验签。
          success: function (data) {
            // window.console && console.log(data.sessionId);
            // window.console && console.log(data.sig);
            // window.console && console.log(data.token);
            that.registerData.appKey = this.appkey;
            that.registerData.phone = that.registerForms.phone;
            that.registerData.scene = this.scene;
            that.registerData.sessionId = data.sessionId;
            that.registerData.sig = data.sig;
            that.registerData.token = data.token;
            that.verify = true; //  拖动状态，判断滑块是否拖动完成
            // window.nc.reset();
            // that.ncflag = false
            // console.log(that.registerData);
          },
          // 滑动验证失败时触发该回调参数。
          fail: function (failCode) {
            window.console && console.log(failCode, "code");
          },
          // 验证码加载出现异常时触发该回调参数。
          error: function (errorCode) {
            window.console && console.log(errorCode);
          },
        });
      });
    },
    // 登录
    //获取图形验证码
    getcode() {
      let times = new Date().getTime();
      this.loginForm.imgSrc = this.API.api + "code?randomStr=" + times; //线上
      this.loginForm.randomStr = times.toString();
    },
    getDomain() {
      var hostname = window.location.hostname;
      var name = hostname.split(".");
      this.name = name[name.length - 2] + "." + name[name.length - 1];
      // console.log(hostname,'name');
      // hostname.substring(hostname.indexof('.'))
      // console.log(hostname,'ll');
    },
    // 获取手机验证码
    getPhoneCode(val) {
      // console.log(this.registerData.phone);
      if (this.ClickTrue) {
        // console.log(11111);
        if (/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(this.registerForms.phone)) {
          // console.log(1111);
          this.ClickTrue = false;
          axios({
            method: "post",
            url: this.API.upms + "code/register/smsCode",
            headers: {
              // 'Authorization':'Basic cGlnOnBpZw=='
            },
            data: this.registerData,
            withCredentials: false,
          }).then((res) => {
            if (res.data.code == 200) {
              --this.nmb;
              const timer = setInterval((res) => {
                --this.nmb;
                if (this.nmb < 1) {
                  this.nmb = 120;
                  this.ClickTrue = true;
                  clearInterval(timer);
                }
              }, 1000);
              this.$message({
                type: "success",
                duration: "2000",
                message: "验证码已发送至手机!",
              });
            } else {
              this.ClickTrue = true;
              this.ncflag = true;
              window.nc.reset();
              if (res.data.msg == "验证码未失效，请失效后再次申请") {
                --this.nmb;
                const timer = setInterval((res) => {
                  --this.nmb;
                  if (this.nmb < 1) {
                    this.nmb = 120;
                    clearInterval(timer);
                  }
                }, 1000);
              }
              this.$message({
                type: "warning",
                duration: "2000",
                message: res.data.msg,
              });
            }
          });
        } else {
          // this.getcode();
          this.$message({
            message: "请输入正确用户名和手机号",
            type: "warning",
          });
        }
        // }).catch(err=>{
        //     this.getcode()
        // })
      } else {
        // console.log(this.ClickTrue, "oo");
        this.$message({
          message: "请先填写正确用户名与手机号",
          type: "warning",
        });
      }
      // }
    },
    // 手机登录
    codeLogin(formName) {
      // if(this.){}
      if (this.checked) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            axios({
              method: "post",
              url: this.API.upms + "online/register",
              headers: {},
              data: this.registerForms,
              withCredentials: false,
            })
              .then((res) => {
                // console.log(res, "res");
                if (res.data.code == 400) {
                  this.$message({
                    message: res.data.msg,
                    type: "error",
                  });
                } else {
                  window.nc.reset();
                  // console.log(res.data.data.access_token,'data');
                  let d = new Date();
                  d.setTime(d.getTime() + 1000 * 60 * 240);
                  let expires = "expires=" + d.toUTCString();
                  // this.token=res.data.access_token
                  this.token = res.data.data.access_token;
                  document.cookie =
                    "ZTGlS_TOKEN=" +
                    res.data.data.access_token +
                    ";path=/;domain=." +
                    this.name +
                    ";expires=" +
                    expires;+"secure";
                  axios({
                    method: "get",
                    url: this.API.upms + "user/info",
                    headers: {
                      Authorization: "Bearer " + res.data.data.access_token,
                    },
                    withCredentials: false,
                  }).then((ress) => {
                    if (
                      ress.data.data.roles[0] == "ROLE_MC" || //管理商
                      ress.data.data.roles[0] == "ROLE_SU" || //子用户
                      ress.data.data.roles[0] == "ROLE_EU" || //终端用户
                      ress.data.data.roles[0] == "ROLE_EUW" //线上终端
                    ) {
                      // window._dgt.push(['trackEvent', 'register',['d_company','d_mobile','d_account'], ['参数1的值','参数2的值','参数3的值']]);
                      // this.$router.push("/");
                      if (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i)) {
                        this.$router.push("/h5Index");
                      } else {
                        this.$router.push("/");
                      }
                    } else {
                      this.$message({
                        message: "用户名输入有误！",
                        type: "warning",
                      });
                    }
                  });
                  this.$message({
                    type: "success",
                    duration: "2000",
                    message: "注册成功！",
                  });
                }
              })
              .catch((err) => {
                // console.log(err,'err');
                this.$message({
                  message: err.msg,
                  type: "error",
                });
              });
            //   axios({
            //     method: "post",
            //     url: this.API.api + "auth/mobile/token", //线上
            //     headers: {
            //       Authorization: "Basic cGlnOnBpZw==",
            //     },
            //     params: {
            //       mobile:
            //         this.registerForms.codePhone +
            //         "@" +
            //         this.registerForms.validCode +
            //         "@" +
            //         this.registerForms.username,
            //     },
            //     withCredentials: false,
            //   }).then((res) => {
            //     this.registerForms.validCode = "";
            //     let d = new Date();
            //     d.setTime(d.getTime() + 1000 * 60 * 240);
            //     let expires = "expires=" + d.toUTCString();
            //     document.cookie =
            //       "ZTGlS_TOKEN=" +
            //       res.data.access_token +
            //       ";path=/;domain=." +
            //       window.localStorage.getItem("domainName") +
            //       ";expires=" +
            //       expires;
            //     axios({
            //       method: "get",
            //       url: this.API.upms + "user/info",
            //       headers: {
            //         Authorization: "Bearer " + res.data.access_token,
            //       },
            //       withCredentials: false,
            //     }).then((ress) => {
            //       if (
            //         ress.data.data.roles[0] == "ROLE_MC" || //管理商
            //         ress.data.data.roles[0] == "ROLE_SU" || //子用户
            //         ress.data.data.roles[0] == "ROLE_EU" //终端用户
            //       ) {
            //         this.$router.push({ path: "/" });
            //       } else {
            //         window.location.href =
            //           "http://yy.mixcloud.com:8081/operation/home"; //线上
            //       }
            //     });
            //   });
          } else {
            // console.log("error submit!!");
            return false;
          }
        });
      } else {
        this.$message({
          message: "请勾选我已阅读同意",
          type: "warning",
        });
      }
    },
    // 验证码登录
    // getVerification(formName) {
    //     console.log(11);
    //   this.$refs[formName].validate((valid) => {
    //     var grant_type = "password";
    //     var scope = "server";
    //     if (valid) {
    //       axios({
    //         method: "post",
    //         url: this.API.upms + "code/checkIp",
    //         data: {
    //           username: this.loginForm.username,
    //         },
    //         withCredentials: false,
    //       })
    //         .then((res) => {
    //           if (res.data.code == 200) {
    //             axios({
    //               method: "post",
    //               url: this.API.api + "auth/oauth/token", //线上
    //               headers: {
    //                 Authorization: "Basic cGlnOnBpZw==",
    //               },
    //               params: {
    //                 username: this.loginForm.username,
    //                 code: this.loginForm.code,
    //                 password: this.loginForm.password,
    //                 // password:Utils.Encrypt(this.loginForm.password),
    //                 randomStr: this.loginForm.randomStr,
    //                 grant_type: grant_type,
    //                 scope: scope,
    //               },
    //               withCredentials: false,
    //             })
    //               .then((res) => {
    //                 this.loginForm.code = "";
    //                 let d = new Date();
    //                 d.setTime(d.getTime() + 1000 * 60 * 240);
    //                 let expires = "expires=" + d.toUTCString();
    //                 document.cookie =
    //                   "ZTGlS_TOKEN=" +
    //                   res.data.access_token +
    //                   ";path=/;domain=." +
    //                   window.localStorage.getItem("domainName") +
    //                   ";expires=" +
    //                   expires;
    //                 axios({
    //                   method: "get",
    //                   url: this.API.upms + "user/info",
    //                   headers: {
    //                     Authorization: "Bearer " + res.data.access_token,
    //                   },
    //                   withCredentials: false,
    //                 }).then((ress) => {
    //                   if (
    //                     ress.data.data.roles[0] == "ROLE_MC" || //管理商
    //                     ress.data.data.roles[0] == "ROLE_SU" || //子用户
    //                     ress.data.data.roles[0] == "ROLE_EU" //终端用户
    //                   ) {
    //                     this.$router.push({ path: "/" });
    //                   } else {
    //                     window.location.href =
    //                       "http://yy.mixcloud.com:8081/operation/home"; //线上
    //                   }
    //                 });
    //               })
    //               .catch((err) => {
    //                 this.getcode();
    //               });
    //           } else {
    //             this.getcode();
    //             this.$message({
    //               message: res.data.msg,
    //               type: "warning",
    //             });
    //           }
    //         })
    //         .catch((err) => {
    //           this.getcode();
    //         });
    //     } else {
    //       console.log("error submit!!");
    //       return false;
    //     }
    //   });
    // },
    Radiochange(val) {
      // console.log(this.tableData[val].name);
      this.radio = this.tableData[val].name;
    },
    // 获取验证码倒计时
    CountdownCode() {
      --this.nmb;
      const timer = setInterval((res) => {
        --this.nmb;
        if (this.nmb < 0) {
          this.nmb = 120;
          clearInterval(timer);
        }
      }, 1000);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    h5() {
      // console.log(111);
      var _this = this;
      window.onresize = function () {
        //须要注意做用域的问题 方法内this是window
        let windowWidth = document.documentElement.clientWidth;
        // console.log(windowWidth,'jjj');
        if (windowWidth < 950) {
          // _this.isShowBar = false
          localStorage.setItem("h5", "0");

          // console.log(_this.isShowBar);
        } else {
          localStorage.setItem("h5", "1");
          // _this.isShowBar = true
        }
      };
    },
    goback() {
      this.$router.push({ path: "/login" });
    },
    versions() {
      //     var u = navigator.userAgent, app = navigator.appVersion;
      //     console.log(u,'u');
      //    var mobile = !!u.match(/AppleWebKit.*Mobile.*/)||!!u.match(/AppleWebKit/)
      //    console.log(mobile,'mobile');
      if (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i)) {
        // localStorage.setItem('h5Lg',"0");
        this.isShowBar = 0;
        // window.location.href = "mobile.html";
      } else {
        this.isShowBar = 1;
        //    localStorage.setItem('h5Lg',"1");
        // window.location.href = "pc.html";
      }
    },
  },
  created() {
    const { source } = this.$route.query;
    // console.log(source,'ll');
    // this.source = source
    if (source) {
      this.registerForms.source = source;
    } else {
      this.registerForms.source = "web";
    }
    // this.registerForms.source = source
    // console.log(this.$route.query);
    this.getDomain();
    // this.h5()
    this.versions();
    // this.isShowBar = localStorage.getItem('h5');
    // console.log(this.isShowBar,'kkk');
    let userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    if (
      userAgent.indexOf("Trident") !== -1 ||
      userAgent.indexOf("MSIE") !== -1
    ) {
      //表示使用的是IE的内核
      this.IEflag = true;
    }
    window.sessionStorage.setItem("logUrl", "home");
    this.saveUrl("home");
    // 注册回车事件
    var _self = this;
    document.onkeydown = function (e) {
      // console.log(e, "e");
      if (window.event == undefined) {
        var key = e.keyCode;
      } else {
        var key = window.event.keyCode;
      }
      if (key == 13) {
        _self.codeLogin("registerForms");
        // _self.getVerification("loginForm");
      }
    };
    this.getcode();
    this.resetStates();
    window.sessionStorage.removeItem("store");
    // var hostname = window.location.hostname;
    // console.log(hostname,'host');
  },
  mounted() {
    this.initDrag();
    // this.h5()
  },

  watch: {
    activeName: function (val) {
      if (val == "first") {
        this.$refs.loginForm.resetFields(); //清空表单
        this.getcode();
      } else if (val == "second") {
        this.$refs.registerForms.resetFields(); //清空表单
      }
    },
    "registerData.phone"(val, vall) {
      // console.log(val,'val');
      // console.log(vall,'vall');
    },
  },
};
</script>
<style scoped>
@media screen and (min-width: 375px) and (max-width: 900px) {
  /* .login{
    width: 100%;
    height: 100%;
    background: url(../../../assets/images/zch5.png) no-repeat;
    background-size:100% 100%;
    overflow: auto;
  } */
  /* .h5_1{
    margin: 10px;
  }
  .h5_2{
    margin: 0 10px;
  }
  .h5_register{
    background: #fff;
    margin: 0 10px;
  }
  .wlcome{
    width: 100%;
    text-align: center;
    font-size: 16px;height: 45px;
    line-height: 45px;
    
  } */
}
.logo_tb {
  text-align: center;
  font-size: 16px;
  height: 30px;
  padding: 10px;
  color: #969696;
}
.logo_tb > div > div {
  float: left;
}
.logo_tbz {
  height: 30px;
  line-height: 30px;
}
.logo_tbi {
  margin-right: 8px;
  margin-left: 20px;
}
.logo_tb i {
  font-size: 30px;
}
.logo_hy {
  /* text-align: center; */
  height: 70px;
  line-height: 70px;
  font-size: 22px;
  margin-left: 60px;
}
.logo_rx {
  text-align: center;
  height: 40px;
  line-height: 40px;
  color: #aaa;
}
#login {
  overflow: hidden;
  width: 100%;
  height: 100%;
  background: url(../../../assets/images/bg.png) no-repeat;
}
.loginStyle {
  height: 330px;
  position: fixed;
  left: 50%;
  top: 45%;
  transform: translate(-50%, -50%);
}
.loginleft {
  position: absolute;
  border-radius: 8px 0px 0px 8px;
  overflow: hidden;
}
.loginleft > img {
  width: 440px;
  height: 420px;
  border-radius: 8px 0px 0px 8px;
}
.loginleft > div {
  position: absolute;
  width: 360px;
  padding: 0 40px;
  top: 70px;
}
.Left_top {
  height: 50px;
  line-height: 50px;
  font-size: 18px;
  color: #fff;
}
.loginRight {
  width: 450px;
  height: 478px;
  /* margin-left: 240px; */
  border: 1px solid #c7c7c7;
  /* background: -webkit-linear-gradient(transparent,  #ffffffc2); /* Safari 5.1 - 6.0 */
  /* background: -o-linear-gradient(transparent,  #ffffffc2); /* Opera 11.1 - 12.0 */
  /* background: -moz-linear-gradient(transparent,  #ffffffc2); /* Firefox 3.6 - 15 */
  /* background: linear-gradient(transparent,  #ffffffc2);  */
  background: #fff;
  border-radius: 5px 5px 5px 5px;
}
.loginRight img {
  width: 40%;
  margin: 30px 0 15px;
  margin-left: 30%;
}
.ncrj {
  display: none;
}
</style>
<style>
.iptStyle .el-input--small .el-input__inner {
  height: 38px !important;
  line-height: 38px !important;
  text-indent: 18px;
}
.iptStyle .el-button--small,
.el-button--small.is-round {
  display: inline-block;
  width: 375px;
  padding: 10px 0px !important;
}
.rememberPas .el-checkbox__label {
  font-size: 12px;
}
#login td,
#login th {
  border: none !important;
}
.loginRight .el-tabs__header {
  margin: 0 0 7px;
}

#nc_2_wrapper {
  width: 100% !important;
}
</style>

