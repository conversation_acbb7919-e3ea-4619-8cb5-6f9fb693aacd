<template>
  <div v-if="isShowBar == 0" class="login" :style="`width: 100%;
    height: 100%;
    background: url(${url}) no-repeat;
    background-size:100% 100%;
    overflow-y:scroll;`">
    <div v-if="hostflag" class="h5_1" style="margin: 10px">
      <img src="../../assets/images/h5_1.png" width="120px" alt="" />
    </div>
    <div v-if="hostflag" class="h5_2" style="margin: 0 10px">
      <img src="../../assets/images/h5_t.png" width="100%" alt="" />
    </div>
    <!-- <div v-if="hostflag" class="h5_2" style="margin: 0 10px">
      <img src="../../assets/images/h5_3.png" width="100%" alt="" />
    </div>
    <div v-if="hostflag" class="h5_2" style="margin: 0 10px">
      <img src="../../assets/images/h5_4.png" width="100%" alt="" />
    </div> -->
    <div class="h5_register" style="background: #fff; margin: 10px 10px">
      <div class="wlcome" style="
          width: 100%;
          text-align: center;
          font-size: 16px;
          height: 45px;
          line-height: 45px;
        ">
        欢迎登录
      </div>
      <!-- <el-tabs
          v-model="activeName"
          @tab-click="handleClick"
          style="width: 329px; margin: 0 auto"
        >
          <el-tab-pane label="验证码登录" name="second"></el-tab-pane>
          <el-tab-pane label="密码登录" name="first"></el-tab-pane>
        </el-tabs> -->
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="0px" class="ms-content">
        <el-form-item prop="codeUsername">
          <el-input v-model="ruleForm.codeUsername" placeholder="用户名" @input="handelShowPwd">
            <i slot="prepend" class="el-icon-lx-people"></i>
            <!-- <el-button slot="prepend" icon="el-icon-lx-people"></el-button> -->
          </el-input>
        </el-form-item>
        <el-form-item v-if="showPwd" prop="password">
          <el-input show-password v-model="ruleForm.password" placeholder="密码">
            <i slot="prepend" class="iconfont icon-mima"></i>
          </el-input>
        </el-form-item>
        <el-form-item prop="codePhone" :rules="filter_rules({
          required: true,
          type: 'mobile',
          message: '请输入正确手机号',
        })
          ">
          <el-input placeholder="手机号" v-model="ruleForm.codePhone" @input="ncrj">
            <i slot="prepend" class="el-icon-phone"></i>
            <!-- <el-button slot="prepend" icon="el-icon-phone"></el-button> -->
          </el-input>
        </el-form-item>
        <el-form-item :class="!ncflag ? 'ncrj' : ''" prop="nc">
          <div style="height: 30px">
            <div id="nc"></div>
          </div>
        </el-form-item>

        <el-form-item v-if="verify" prop="codeObtain" style="position: relative">
          <el-input style="width: 235px" placeholder="验证码" @keyup.enter.native="submitForm('ruleForm')"
            v-model="ruleForm.codeObtain">
            <!-- <el-button slot="prepend" icon="el-icon-info"></el-button> -->
            <i slot="prepend" class="el-icon-info"></i>
            <el-button style="
                background: #409eff;
                color: #fff;
                height: 32px;
                position: absolute;
                top: 9px;
                left: 20px;
              " slot="append" v-if="nmb == 120" @click="getPhoneCode(ruleForm.codePhone)">获取验证码</el-button>
            <el-button disabled style="
                background: #eee;
                color: #555;
                height: 32px;
                position: absolute;
                top: 9px;
                left: 20px;
              " slot="append" v-else>重新获取{{ nmb }}</el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="password_ji">记住用户名</el-checkbox>
        </el-form-item>
        <div class="login-btn">
          <el-button type="primary" @click="submitForm('ruleForm')">登录</el-button>
        </div>
      </el-form>
      <div v-if="hostflag" style="text-align: center; color: #aaa; font-size: 16px">
        <span>服务热线：************</span>
        <!-- <a href="/register">免费注册</a> -->
        <span style="margin-left: 20px" @click="register()">免费注册</span>
      </div>
      <div style="
          text-align: center;
          font-size: 16px;
          height: 30px;
          padding: 10px;
          color: #969696;
        ">
        <div style="width: 340px; height: 30px; display: flex">
          <div style="margin-right: 8px; margin-left: 20px; margin-top: 5px">
            <i class="icon iconfont icon-huojian"></i>
          </div>
          <div style="height: 30px; line-height: 30px">
            <span>高效</span>
          </div>
          <div style="margin-right: 8px; margin-left: 20px; margin-top: 5px">
            <i class="icon iconfont icon-yunduan"></i>
          </div>
          <div style="height: 30px; line-height: 30px">
            <span>全面</span>
          </div>
          <div style="margin-right: 8px; margin-left: 20px; margin-top: 5px">
            <i class="icon iconfont icon-biaoqian"></i>
          </div>
          <div style="height: 30px; line-height: 30px">
            <span>精准</span>
          </div>
          <div style="margin-right: 8px; margin-left: 20px; margin-top: 5px">
            <i class="icon iconfont icon-dunpai"></i>
          </div>
          <div style="height: 30px; line-height: 30px">
            <span>安全</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="login-wrap">
    <div v-if="hostflag" class="LOGO_zt">
      <!-- <img
            class="zt_img"
               
                src="https://images.zthysms.com/static/images/logo100.gif"
                alt=""
            /> -->
      <img class="zt_img" src="../../assets/images/logob.png" alt="" />
      <!-- <span class="zt_lg" style="font-size: 22px; font-weight: 500; color: #fff"
          >助通融合云通信管理系统</span
        > -->
      <span class="zt_lg" style="font-size: 28px; font-weight: 500; color: #fff">助通融合云通信管理系统</span>
    </div>
    <Login1 v-show="loginFlag" />
    <div v-show="!loginFlag" class="ms-login">
      <div v-if="hostflag" class="ms-title">助通融合通信</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="0px" class="ms-content">
        <el-form-item prop="codeUsername">
          <el-input v-model.trim="ruleForm.codeUsername" placeholder="用户名" @input="handelShowPwd">
            <i slot="prepend" class="el-icon-lx-people"></i>
            <!-- <el-button slot="prepend" icon="el-icon-lx-people"></el-button> -->
          </el-input>
        </el-form-item>
        <el-form-item v-if="showPwd" prop="password">
          <el-input show-password v-model="ruleForm.password" placeholder="密码">
            <i style="font-size: 12px" slot="prepend" class="iconfont icon-mima"></i>
          </el-input>
        </el-form-item>
        <el-form-item prop="codePhone" :rules="filter_rules({
          required: true,
          type: 'mobile',
          message: '请输入正确手机号',
        })
          ">
          <el-input placeholder="手机号" v-model="ruleForm.codePhone" @input="ncrj">
            <i slot="prepend" class="el-icon-phone"></i>
            <!-- <el-button slot="prepend" icon="el-icon-phone"></el-button> -->
          </el-input>
        </el-form-item>
        <el-form-item :class="!ncflag ? 'ncrj' : ''" prop="nc">
          <div style="height: 30px">
            <div id="nc"></div>
          </div>
        </el-form-item>
        <el-form-item v-if="verify" prop="codeObtain" style="position: relative">
          <el-input style="width: 235px" placeholder="验证码" @keyup.enter.native="submitForm('ruleForm')"
            v-model="ruleForm.codeObtain">
            <!-- <el-button slot="prepend" icon="el-icon-info"></el-button> -->
            <i slot="prepend" class="el-icon-info"></i>
            <el-button style="
                background: #409eff;
                color: #fff;
                height: 32px;
                position: absolute;
                top: 9px;
                left: 20px;
              " slot="append" v-if="nmb == 120" @click="getPhoneCode(ruleForm.codePhone)">获取验证码</el-button>
            <el-button disabled style="
                background: #eee;
                color: #555;
                height: 32px;
                position: absolute;
                top: 9px;
                left: 20px;
              " slot="append" v-else>重新获取{{ nmb }}</el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="password_ji" @change="handelremberName">记住用户名</el-checkbox>
        </el-form-item>
        <div class="login-btn">
          <el-button type="primary" @click="submitForm('ruleForm')">登录</el-button>
        </div>
        <div v-if="hostflag" style="text-align: center; font-size: 12px">
          <span>服务热线：************</span>
          <span style="margin-left: 20px; cursor: pointer" @click="register()">立即注册</span>
        </div>
        <!-- <p class="login-tips">Tips : 请填写正确的用户名和手机号。<span style="margin-left:20px" @click="passwordLogin()">密码登录</span></p> -->
        <!-- <p class="login-tips">Tips : 请填写正确的用户名和手机号。</p> -->
      </el-form>
    </div>
    <div class="bah">
      <span v-if="hostflag" style="color: rgba(238, 238, 238, 0.32)">
        Copyright Copyright ©2011-{{ year }} 上海助通信息科技有限公司
        地址：上海市闵行区七莘路999号正峰广场3号楼11层
      </span>
      <!-- <a v-else style="color: rgb(238, 238, 238, 0.32);" href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">
                Copyright Copyright ©2011-2020 沪ICP备09065403号-3
            </a> -->
    </div>
    <div class="ban_h">
      <a v-if="hostflag" style="color: rgb(238, 238, 238, 0.32)" href="https://beian.miit.gov.cn" target="_blank"
        rel="noopener noreferrer">
        沪ICP备09065403号-3
      </a>
      <a v-if="hostflag" style="
          display: flex;
          align-items: center;
          margin-left: 10px;
          color: rgb(238, 238, 238, 0.32);
        " href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011202020330" target="_blank">
        <img src="../../assets/images/logo01.png" alt="" srcset="" style="width: 20px" />
        <span style="margin-left: 5px">沪公网安备 31011202020330</span>
      </a>
      <!-- <a v-else style="color: rgb(238, 238, 238, 0.32);" href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer">
                Copyright Copyright ©2011-2020 沪ICP备09065403号-3
            </a> -->
    </div>
    <div class="version">
      <span style="color: rgb(238, 238, 238, 0.32)">{{ version }}</span>
      <a href="http://" target="_blank" rel="noopener noreferrer"></a>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Login1 from "./login1";
import bus from "../common/bus";
import { version } from "../../utils/version";
export default {
  components: {
    Login1,
  },
  data() {
    var validateUsername = (rule, value, callback) => {
      var myregex = /^[0-9A-Za-z_]+$/;
      if (value) {
        if (myregex.test(value)) {
          callback();
        } else {
          callback("用户名格式仅支持 大小写英文、数字、_");
        }
      } else {
        callback("请输入用户名");
      }
    };
    var ruleCode = (rule, value, callback) => {
      var myregex = /^[0-9]*$/;
      if (value) {
        if (myregex.test(value)) {
          callback();
        } else {
          callback("请输入正确验证码");
        }
      } else {
        callback("验证码不能为空");
      }
    };
    return {
      hostname: window.location.hostname,
      nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
      hostflag: true,
      ncflag: false,
      name: "",
      year: "",
      version: version,
      url: require("../../assets/images/zch5.png"),
      nmb: 120,
      ClickTrue: true,
      verify: false,
      isShowBar: "0",
      password_ji: true,
      token: "",
      loginFlag: true,
      username: "",
      password: "",
      ruleForm: {
        codeUsername: "",
        codePhone: "",
        codeObtain: "",
        password: "",
      },
      showPwd: false,
      pwdIndex: 0,
      userNameList: [
        //特定账号密码
        {
          name: "huangjingqing",
          password: "4@Oc3#lW",
        },
        {
          name: "shztkf",
          password: "3ljF@HJr",
        },
        {
          name: "ZTgwdj",
          password: "iDQ#r3YJ",
        },
        {
          name: "guanlishang",
          password: "Q6#8vEfM",
        },
      ],
      rules: {
        codeUsername: [
          {
            required: true,
            validator: validateUsername,
            trigger: ["change", "blur"],
          },
        ],
        codeObtain: [
          { required: true, validator: ruleCode, trigger: ["change", "blur"] },
        ],
      },
      status: "",
      registerData: {
        appKey: "",
        phone: "",
        scene: "",
        sessionId: "",
        sig: "",
        token: "",
        username: "",
      },
    };
  },
  created() {
    // console.log(this.$route.query);
    this.status = this.$route.query.logout;
    this.year = this.moment().format("YYYY");

    bus.$on("loginPh", (target) => {
      // console.log(target,'000');
      this.loginFlag = target;
    });
    // console.log(this.hostname);
    this.nameTile.forEach((item) => {
      // console.log(item);
      if (this.hostname == item) {
        // console.log(1);
        this.hostflag = false;
      }
    });
    // console.log(this.getCookie());
    // this.ruleForm.codeUsername = this.getCookie("userName")
    // this.ruleForm.codePhone = this.getCookie("userPwd")
    // this.getCookie();
    this.getCodeUsername();
    this.versions();
    // console.log(this.username,'ll');
    // this.ruleForm.codeUsername = this.username
    // this.ruleForm.codePhone = this.password
    this.getDomain();
    // console.log(this.isShowBar);
    // console.log(window.location.hostname);
    // console.log(this.getCookie("userName"));
  },

  methods: {
    passwordLogin() {
      this.loginFlag = true;
    },
    handelShowPwd(e) {
      for (let i = 0; i < this.userNameList.length; i++) {
        if (this.userNameList[i].name === e) {
          this.showPwd = true;
          this.pwdIndex = i;
          // this.ruleForm.password = this.userNameList[i].password
          break;
        } else {
          this.showPwd = false;
          // this.ruleForm.password = ""
        }
      }
    },
    //获取主机域名
    getDomain() {
      var hostname = window.location.hostname;
      var name = hostname.split(".");
      this.name = name[name.length - 2] + "." + name[name.length - 1];
      // console.log(hostname,'name');
      // hostname.substring(hostname.indexof('.'))
      // console.log(hostname,'ll');
    },
    versions() {
      //     var u = navigator.userAgent, app = navigator.appVersion;
      //     console.log(u,'u');
      //    var mobile = !!u.match(/AppleWebKit.*Mobile.*/)||!!u.match(/AppleWebKit/)
      //    console.log(mobile,'mobile');
      if (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i)) {
        // localStorage.setItem('h5Lg',"0");
        this.isShowBar = 0;
        // window.location.href = "mobile.html";
      } else {
        this.isShowBar = 1;
        //    localStorage.setItem('h5Lg',"1");
        // window.location.href = "pc.html";
      }
    },
    ncrj() {
      // console.log(this.registerForms.phone,'lll');
      this.registerData.phone = this.ruleForm.codePhone;
      this.ncflag = true;
      // console.log(a.length);
      // if(phone.length == 11){

      // }else{
      //   this.ncflag = false
      //   this.verify = false
      // }
      // if(/^[1-9][\d]*$/.test(this.registerForms.phone)){
      //   clearTimeout(this.timer);
      //   this.timer = setTimeout(() => {
      //       this.ncflag = true
      //   }, 200);
      // }else {
      //   this.$message({
      //     message: "请输入正确手机号",
      //     type: "warning",
      //   });
      // }
      // if(){

      // }
      // this.ncflag = true
    },
    //人机验证
    init() {
      // 实例化nc
      var that = this;
      // 实例化nc
      AWSC.use("nc", function (state, module) {
        // 初始化
        window.nc = module.init({
          // 应用类型标识。它和使用场景标识（scene字段）一起决定了滑动验证的业务场景与后端对应使用的策略模型。您可以在人机验证控制台的配置管理页签找到对应的appkey字段值，请务必正确填写。
          appkey: "FFFF0N00000000009C2B",
          //使用场景标识。它和应用类型标识（appkey字段）一起决定了滑动验证的业务场景与后端对应使用的策略模型。您可以在人机验证控制台的配置管理页签找到对应的scene值，请务必正确填写。
          scene: "nc_register",
          // 声明滑动验证需要渲染的目标ID。
          renderTo: "nc",
          // 添加 passive 选项
          passive: true,
          //前端滑动验证通过时会触发该回调参数。您可以在该回调参数中将会话ID（sessionId）、签名串（sig）、请求唯一标识（token）字段记录下来，随业务请求一同发送至您的服务端调用验签。
          success: function (data) {
            // window.console && console.log(data.sessionId);
            // window.console && console.log(data.sig);
            // window.console && console.log(data.token);
            that.registerData.appKey = this.appkey;
            that.registerData.phone = that.ruleForm.codePhone;
            that.registerData.scene = this.scene;
            that.registerData.sessionId = data.sessionId;
            that.registerData.sig = data.sig;
            that.registerData.token = data.token;
            that.registerData.username = that.ruleForm.codeUsername;
            that.verify = true; //  拖动状态，判断滑块是否拖动完成
            // window.nc.reset();
            // that.ncflag = false
            // console.log(that.registerData);
          },
          // 滑动验证失败时触发该回调参数。
          fail: function (failCode) {
            window.console && console.log(failCode, "code");
          },
          // 验证码加载出现异常时触发该回调参数。
          error: function (errorCode) {
            window.console && console.log(errorCode);
          },
        });
      });
    },
    getPhoneCode(val) {
      if (this.ClickTrue) {
        if (val && /^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(val)) {
          axios({
            method: "get",
            url:
              this.API.upms +
              "user/state?username=" +
              this.ruleForm.codeUsername,
            // headers: {
            //   Authorization: "Basic cGlnOnBpZw==",
            // },
          }).then((response) => {
            console.log(response, "response");
            if (response.data.code == 200) {
              if (response.data.data) {
                if (response.data.data.delFlag == 1) {
                  this.ClickTrue = true;
                  this.$message({
                    message: `尊敬的用户,您的账号在${response.data.data.statusUpdateTime}已被停用，若需恢复使用，请联系客服小伙伴 4000087058`,
                    type: "warning",
                    duration: 10000,
                  });
                } else if (response.data.data.delFlag == 2) {
                  this.ClickTrue = true;
                  this.$message({
                    message: `尊敬的用户，依据平台规则，您连续三月未使用的账户在${response.data.data.statusUpdateTime}已被安全管控。若需恢复使用，请联系客服小伙伴 4000087058`,
                    type: "warning",
                    duration: 10000,
                  });
                } else {
                  this.ClickTrue = false;
                  axios({
                    method: "post",
                    url: this.API.upms + "code/smsCode",
                    headers: {},
                    // data: {
                    //   username: this.ruleForm.codeUsername,
                    //   phone: this.ruleForm.codePhone,
                    // },
                    data: this.registerData,
                    withCredentials: false,
                  }).then((res) => {
                    if (res.data.code == 200) {
                      --this.nmb;
                      const timer = setInterval((res) => {
                        --this.nmb;
                        if (this.nmb < 1) {
                          this.nmb = 120;
                          this.ClickTrue = true;
                          clearInterval(timer);
                        }
                      }, 1000);
                      this.$message({
                        type: "success",
                        duration: "2000",
                        message: "验证码已发送至手机!",
                      });
                    } else {
                      this.ClickTrue = true;
                      this.ncflag = true;
                      window.nc.reset();
                      if (res.data.msg == "验证码未失效，请失效后再次申请") {
                        --this.nmb;
                        const timer = setInterval((res) => {
                          --this.nmb;
                          if (this.nmb < 1) {
                            this.nmb = 120;
                            clearInterval(timer);
                          }
                        }, 1000);
                      }
                      this.$message({
                        type: "error",
                        duration: "2000",
                        message: res.data.msg,
                      });
                    }
                  });
                }
              } else {
                this.$message({
                  type: "error",
                  duration: "3000",
                  message: '用户不存在，请检查您的用户名是否正确！',
                });
              }
            } else {
              this.$message({
                type: "error",
                duration: "2000",
                message: response.data.msg,
              });
            }
          });
        } else {
          this.$message({
            message: "请输入正确用户名和手机号",
            type: "warning",
          });
        }
      } else {
        this.$message({
          message: "请先填写正确用户名与手机号",
          type: "warning",
        });
      }
    },
    //获取cookie
    getCookie: function () {
      if (document.cookie.length > 0) {
        var arr = document.cookie.split("; "); //这里显示的格式需要切割一下自己可输出看下 // console.log(arr)
        // console.log(arr,'arr');
        for (var i = 0; i < arr.length; i++) {
          var arr2 = arr[i].split("="); //再次切割 //判断查找相对应的值
          if (arr2[0] == "userName") {
            this.ruleForm.codeUsername = arr2[1]; //保存到保存数据的地方
            // console.log(this.username);
            // console.log(this.username );
          } else if (arr2[0] == "userPwd") {
            this.ruleForm.codePhone = arr2[1];
          }
        }
        if (this.ruleForm.codePhone != "") {
          this.ncflag = true;
        }
        // this.init();
      }
    },
    setCookie(c_name, c_pwd, exdays) {
      var exdate = new Date();
      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays); //保存的天数
      document.cookie =
        "userName=" + c_name + ";path=/;expires=" + exdate.toLocaleString();
      document.cookie =
        "userPwd=" + c_pwd + ";path=/;expires=" + exdate.toLocaleString();
    },
    //删除cookie
    clearCookie: function () {
      this.setCookie("", "", -1); //修改2值都为空，天数为负1天就好了
    },
    register() {
      this.$router.push({
        path: "/register",
      });
    },
    handelremberName(val) {
      localStorage.setItem("password_rember", val);
      if (val) {
        let formName = {
          name: this.ruleForm.codeUsername,
          phone: this.ruleForm.codePhone,
        };
        localStorage.setItem("formName", JSON.stringify(formName));
      } else {
        localStorage.removeItem("formName");
      }
    },
    getCodeUsername() {
      let objName = JSON.parse(localStorage.getItem("formName"));
      if (objName) {
        this.ruleForm.codeUsername = objName.name;
        this.ruleForm.codePhone = objName.phone;
        this.ncflag = true;
        for (let i = 0; i < this.userNameList.length; i++) {
          if (this.userNameList[i].name === this.ruleForm.codeUsername) {
            this.showPwd = true;
            this.pwdIndex = i;
            this.ruleForm.password = this.userNameList[i].password;
            break;
          } else {
            this.showPwd = false;
            this.ruleForm.password = "";
          }
        }
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.showPwd) {
            if (
              this.ruleForm.password !=
              this.userNameList[this.pwdIndex].password
            ) {
              this.$message({
                message: "用户名或密码输入有误！",
                type: "warning",
              });
              return false;
            }
          }
          if (this.password_ji) {
            localStorage.setItem("password_rember", this.password_ji);
            let formName = {
              name: this.ruleForm.codeUsername,
              phone: this.ruleForm.codePhone,
            };
            localStorage.setItem("formName", JSON.stringify(formName));
          } else {
            localStorage.setItem("password_rember", this.password_ji);
            localStorage.removeItem("formName");
          }
          // localStorage.setItem("ms_username", this.ruleForm.username);
          // if (this.password_ji == true) {
          //   localStorage.setItem("password_rember", this.password_ji);
          //   this.setCookie(
          //     this.ruleForm.codeUsername,
          //     this.ruleForm.codePhone,
          //     7
          //   );
          // } else {
          //   this.clearCookie();
          // }
          // localStorage.setItem("ms_username", this.ruleForm.username);

          axios({
            method: "post",
            url: this.API.api + "auth/mobile/token",
            headers: {
              Authorization: "Basic cGlnOnBpZw==",
            },
            params: {
              mobile:
                this.ruleForm.codePhone +
                "@" +
                this.ruleForm.codeObtain +
                "@" +
                this.ruleForm.codeUsername,
            },
            withCredentials: false,
          }).then((res) => {
            localStorage.removeItem("nonce_list");
            //  localStorage.setItem('ms_username',this.ruleForm.username);
            window.nc.reset();
            this.ruleForm.codeObtain = "";
            let d = new Date();
            d.setTime(d.getTime() + 1000 * 60 * 240);
            let expires = "expires=" + d.toUTCString();
            this.token = res.data.access_token;
            document.cookie =
              "ZTGlS_TOKEN=" +
              res.data.access_token +
              ";path=/;domain=." +
              this.name +
              ";expires=" +
              expires;
            +"secure";
            axios({
              method: "get",
              url: this.API.upms + "user/info",
              headers: {
                Authorization: "Bearer " + res.data.access_token,
              },
              withCredentials: false,
            }).then((ress) => {
              if (this.isShowBar == 0) {
                this.$router.push("/h5Index");
                // if (ress.data.data.roles[0] == "ROLE_MC") {
                //   this.$router.push("/glsHome");
                // }
              } else {
                if (
                  ress.data.data.roles[0] == "ROLE_MC" || //管理商
                  ress.data.data.roles[0] == "ROLE_SU" || //子用户
                  ress.data.data.roles[0] == "ROLE_EU" || //终端用户
                  ress.data.data.roles[0] == "ROLE_EUW" //线上终端
                ) {
                  let path = sessionStorage.getItem("path");
                  if (path) {
                    this.$router.push(path);
                  } else {
                    this.$router.push("/");
                  }
                  //   if(this.status == "contact"){
                  //     var tempwindow = window.open("_blank");
                  //     tempwindow.location ="http://contact.zthysms.cn/";
                  //   }else{
                  //     this.$router.push('/');
                  //   }
                } else {
                  this.$message({
                    message: "用户名输入有误！",
                    type: "warning",
                  });
                }
              }
            });
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
  mounted() {
    let pwdFlag = localStorage.getItem("password_rember");
    this.password_ji = JSON.parse(pwdFlag);

    // console.log(this.password_ji ,'this.password_ji ');
    this.init();
    // this.h5()
  },
};
</script>

<style scoped>
.login-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  /* background: rgb(28, 165, 219); */
  /* background: #1F60C4; */
  background-image: url(../../assets/images/zxts.png);
  /* background-image: url(https://images.zthysms.com/static/images/100zn.png) ; */
  background-size: 40%;
  background-repeat: no-repeat;
  background-position: 250px 150px;
  /* background-position: 190px 240px; */
}

.ms-title {
  width: 100%;
  line-height: 50px;
  text-align: center;
  font-size: 20px;
  /* color: #fff; */
  border-bottom: 1px solid #ddd;
}

.ms-login {
  position: absolute;
  /* left:50%; */
  right: 20%;
  top: 50%;
  width: 350px;
  margin: -180px 0 0 -175px;
  border-radius: 5px;
  background: #fff;
  /* background: rgb(32, 156, 209); */
  overflow: hidden;
}

.ms-content {
  padding: 30px 30px;
}

.login-btn {
  text-align: center;
}

.login-btn button {
  width: 100%;
  height: 36px;
  margin-bottom: 10px;
}

.login-tips {
  font-size: 12px;
  line-height: 30px;
  color: #fff;
}

.LOGO_zt {
  width: 100%;
  height: 100px;
  border-bottom: 1px #aaa solid;
  /* background: #fff; */
}

.zt_img {
  position: fixed;
  top: 30px;
  margin-top: -10px;
  left: 15%;
  margin-left: -90px;
  /* width: 100%; */
  /* height: 100px; */
}

.zt_lg {
  position: fixed;
  top: 5%;
  margin-top: -10px;
  left: 35%;
  margin-left: -150px;
}

.bah {
  width: 100%;
  text-align: center;
  position: absolute;
  bottom: 4%;
  /* left: 50%;
  margin-left: -450px; */
}

.ban_h {
  width: 100%;
  position: absolute;
  bottom: 2%;
  /* text-align: center; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.version {
  position: absolute;
  bottom: 1%;
  left: 10%;
  margin-left: -150px;
}

.ncrj {
  display: none;
}

/* .ms-login{
        position: absolute;
        top: 30%;
        right: -20%;
    } */
</style>