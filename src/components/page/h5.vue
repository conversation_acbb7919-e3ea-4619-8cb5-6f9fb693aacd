<template>
    <div class="h5_from">
            <!-- <div class="h5_header">
                <img src="./images/nav.png" alt="" class="image">
            </div> -->
            <div class="h5_content">
                <div class="banner">
                    <img src="../../assets/images//image 123.png" alt="" class="image">
                </div>
                <div v-if="flag" class="h5_tab">
                    <div :class="count == index ? 'active' : 'attend_expo'" v-for="(item, index) in list" :key='index' @click="tapItem(item, index)">参会人{{index+1}}</div>
                    <div v-if="list.length<4" @click="addList" class="add_attend_expo">
                        <i class="el-icon-plus"></i>
                        新增参会人
                    </div>
                </div>
                <div v-if="flag">
                    <el-form ref="form" :model="form" :rules="rules" label-width="80px" style="margin: 10px 0;">
                        <el-form-item label="姓名" style="width: 90%;height: 32px;" prop="name">
                          <el-input style="width:100%;height: 100%;" v-model="form.name"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话" style="width: 90%;height: 32px;" prop="phone">
                            <el-input style="width:100%;height: 100%;" v-model="form.phone"></el-input>
                        </el-form-item>
                        <el-form-item label="验证码" style="width: 90%;height: 32px;position: relative;" prop="codeObtain">
                            <el-input style="width:100%;height: 100%;" v-model="form.codeObtain"></el-input>
                            <div v-if="nmb==30"  @click="getPhoneCode" class="codeObtain">获取验证码</div>
                            <div  v-else  disabled class="codeObtains">重新取验证码{{nmb}}</div>
                        </el-form-item>
                        <el-form-item label="公司名称" style="width: 90%;height: 32px;" prop="corporateName">
                            <el-input style="width:100%;height: 100%;" v-model="form.corporateName"></el-input>
                        </el-form-item>
                        <el-form-item label="职位" style="width: 90%;height: 32px;" prop="position">
                            <el-input style="width:100%;height: 100%;" v-model="form.position"></el-input>
                        </el-form-item>
                        <el-form-item label="证件类型" style="width: 90%;height: 32px;" prop="cardType">
                            <el-select style="width:100%;height: 100%;"  v-model="form.cardType" placeholder="请选择">
                                <el-option
                                  label="身份证"
                                  value="1">
                                </el-option>
                                <el-option
                                  label="护照"
                                  value="2">
                                </el-option>
                                <el-option
                                  label="港澳通行证"
                                  value="3">
                                </el-option>
                                <el-option
                                  label="台胞证"
                                  value="4">
                                </el-option>
                              </el-select>
                        </el-form-item>
                        <el-form-item label="证件号码" style="width: 90%;height: 32px;" prop="idCard">
                            <el-input style="width:100%;height: 100%;" v-model="form.idCard"></el-input>
                        </el-form-item>
                      </el-form>
                </div>
                <div v-if="flag" class="text">
                    大会报名日期截止至2028年7月31日，请各位嘉宾及时完成参会信息的填写。如您在注册报名中有任何问题，请发送问题至**************************，我们将在24小时内回复您。
                </div>
                <div v-if="!flag" class="success">
                <div class="item">
                    <i class="el-icon-success" style="color:#67C23A"></i>
                    <span style="margin-left:5px">提交成功</span>
                </div>
      </div>
            </div>
            <div v-if="flag" class="h5_footer">
                <div class="submit" @click="onSubmit('form')">
                    提交
                </div>
                <div v-if="list.length>1" class="delete" @click="onDel">
                    删除当前参会人
                </div>
            </div>

            
        </div>
</template>

<script>
export default {
    data() {
        return {
            form: {
                name: '',//姓名
                phone: '',//手机号
                codeObtain: '',//验证码
                corporateName: '',//公司名称
                position: '',//职位
                cardType: '1',//证件类型
                idCard: '',//证件号码
            },
            flag:true,
            rules: {
                name: [
                    { required: true, message: '请输入姓名', trigger: 'change' }
                ],
                phone: [
                    { required: true, message: '请输入手机号', trigger: 'change' }
                ],
                codeObtain: [
                    { required: true, message: '验证码不能为空', trigger: 'change' }
                ],
                position: [
                    { required: true, message: '请输入职位', trigger: 'change' }
                ],
                corporateName: [
                    { required: true, message: '请输入公司名称', trigger: 'change' }
                ],
                cardType: [
                    { required: true, message: '请输入选择证件类型', trigger: 'change' }
                ],
                idCard: [
                    { required: true, message: '请输入身份证号', trigger: 'change' }
                ],
            },
            nmb: 30,
            list: [
                {
                    name: '',//姓名
                    phone: '',//手机号
                    codeObtain: '',//验证码
                    corporateName: '',//公司名称
                    position: '',//职位
                    cardType: '',//证件类型
                    idCard: '',//证件号码
                },
            ],
            count: 0
        }
    },
    methods: {
        // 请求头数据
        //   setHeaderData(){
        //        this.header = 3
        //   }

        getPhoneCode() {
            --this.nmb;
            const timer = setInterval((res) => {
                --this.nmb;
                if (this.nmb < 1) {
                    this.nmb = 30;
                    // this.ClickTrue = true;
                    clearInterval(timer);
                }
            }, 1000);
        },
        addList() {
            let obj = {
                name: '',//姓名
                phone: '',//手机号
                codeObtain: '',//验证码
                corporateName: '',//公司名称
                position: '',//职位
                cardType: '1',//证件类型
                idCard: '',//证件号码
            }
            if (this.list.length == 1) {
                this.list[0] = this.form
                this.list.push(obj)
            } else {
                this.list.push(obj)
            }
            this.count = this.list.length - 1;
            this.form = this.list[this.count];
        },
        tapItem(item, index) {
            this.count = index
            this.form = item;
        },
        onDel(){
            this.list.splice(this.count, 1);
            if(this.count == 0){
                this.count = 0;
                this.form = this.list[this.count];
            }else{
                this.count = this.list.length-1;
                this.form = this.list[this.list.length-1];
            }
            
        },
        onSubmit(formName) {
            console.log(this.$refs[formName],'formName');
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.flag = !this.flag
                    // this.$notify({ type: 'success', message: '提交成功' });
                    this.list = [
                        {
                            name: '',//姓名
                            phone: '',//手机号
                            codeObtain: '',//验证码
                            corporateName: '',//公司名称
                            position: '',//职位
                            cardType: '1',//证件类型
                            idCard: '',//证件号码
                        }
                    ]
                    this.form = this.list[0];
                    this.count = 0
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });

        },
    }
}
</script>

<style scoped>
    .h5_from {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.h5_header {
    width: 100%;
    height: 44px;
}

.h5_content {
    flex: 1;
    background: #f6f6f6;
    overflow: auto;
}

.h5_footer {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    /* margin-top: 37px; */
    /* color: #fff;
    background: #4e95ff; */
    display: flex;
    justify-content: space-between;
}
.submit{
    flex:1 ;
    color: #fff;
    background: #4e95ff;
}
.delete{
    flex:1 ;
    color: #fff;
    background: #F56C6C;
}
.image {
    width: 100%;
    height: 100%;
}

.banner {
    width: 100%;
    height: 206px;
}

.h5_tab {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    overflow-x: scroll;
}

.attend_expo {
    color: #999;
    width: 90px;
    text-align: center;
    /* margin: 0 24px; */
    font-size: 16px;
}

.add_attend_expo {
    color: #999;
    font-size: 16px;
    /* width: 100px; */
}

.active {
    width: 90px;
    text-align: center;
    color: #4e95ff;
    text-decoration: underline;
    /* margin: 0 24px; */
    font-size: 16px;
}

.text {
    width: 341px;
    height: 55px;
    margin: 0 auto;
    color: #999;
    font-size: 12px;
    line-height: 16px;
}

.codeObtain {
    /* width: 75px; */
    height: 25px;
    border: 1px solid #4e95ff;
    color: #4e95ff;
    text-align: center;
    line-height: 25px;
    font-size: 12px;
    position: absolute;
    right: 0;
    margin: 3px 3px;
    padding: 0 5px;
    border-radius: 5px;
    top: 0;
}
.codeObtains{
    height: 25px;
    border: 1px solid #ccc;
    color: #ccc;
    text-align: center;
    line-height: 25px;
    font-size: 12px;
    position: absolute;
    right: 0;
    margin: 3px 3px;
    padding: 0 5px;
    border-radius: 5px;
    top: 0;
}
.success{
    width: 100%;
    height: 550px;
    display: flex;
    align-items: center;
}
.item{
    width: 100px;
    margin: 0 auto;
    font-size: 18px;
}
</style>
<style>
.el-form-item__label {
    font-size: 12px;
    line-height: 32px;
}

.el-input__inner {
    height: 100%;
}
</style>