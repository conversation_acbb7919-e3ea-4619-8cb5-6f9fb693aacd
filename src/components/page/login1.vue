<template>
  <div>
    <div class="ms-login">
      <div class="ms-title">助通融合通信</div>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="0px"
        class="ms-content"
      >
        <el-form-item prop="codeUsername">
          <el-input v-model="ruleForm.codeUsername" placeholder="用户名">
            <!-- <el-button slot="prepend" icon="el-icon-lx-people"></el-button> -->
            <i slot="prepend" class="el-icon-lx-people"></i>
          </el-input>
        </el-form-item>
        <el-form-item prop="codePhone">
          <el-input
            type="password"
            placeholder="密码"
            v-model="ruleForm.password"
          >
            <!-- <el-button slot="prepend" icon="el-icon-phone"></el-button> -->
            <i slot="prepend" class="el-icon-phone"></i>
          </el-input>
        </el-form-item>
        <el-form-item prop="codePhone" style="position: relative">
          <el-input
            style="width: 210px"
            placeholder="验证码"
            @keyup.enter.native="submitForm('ruleForm')"
            v-model="ruleForm.code"
          >
          </el-input>
          <img
            @click="getcode()"
            :src="ruleForm.imgSrc"
            alt=""
            style="height: 32px; position: absolute"
          />
        </el-form-item>
        <div class="login-btn">
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
            @keyup.enter.native="submitForm('ruleForm')"
            >登录</el-button
          >
        </div>
        <p class="login-tips">
          Tips : 请输入正确的用户名和密码。<span
            style="margin-left: 20px"
            @click="phone()"
            >手机号登录</span
          >
        </p>
      </el-form>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import bus from "../common/bus";
export default {
  data() {
    var validateUsername = (rule, value, callback) => {
      if (value) {
        var myregex = /^[0-9A-Za-z\_]+$/;
        if (myregex.test(value)) {
          callback();
        } else {
          callback("用户名格式仅支持 大小写英文、数字、_");
        }
      } else {
        callback("请输入用户名");
      }
    };
    return {
      nmb: 120,
      ClickTrue: true,
      token: "",
      name: "",
      ruleForm: {
        codeUsername: "",
        codePhone: "",
        codeObtain: "",
        password: "",
        imgSrc: "",
        code: "",
        randomStr: "",
      },
      rules: {
        codeUsername: [
          {
            required: true,
            validator: validateUsername,
            trigger: ["change", "blur"],
          },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
    };
  },
  methods: {
    getcode() {
      let times = new Date().getTime();
      this.ruleForm.imgSrc = this.API.api + "code?randomStr=" + times; //线上
      // console.log(this.ruleForm.imgSrc);
      this.ruleForm.randomStr = times.toString();
    },
    getDomain() {
      var hostname = window.location.hostname;
      var name = hostname.split(".");
      this.name = name[name.length - 2] + "." + name[name.length - 1];
      // console.log(hostname,'name');
      // hostname.substring(hostname.indexof('.'))
      // console.log(hostname,'ll');
    },
    phone() {
      bus.$emit("loginPh", false);
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        var grant_type = "password";
        var scope = "server";
        if (valid) {
          axios({
            method: "post",
            url: this.API.api + "auth/oauth/token",
            headers: {
              Authorization: "Basic cGlnOnBpZw==",
            },
            params: {
              username: this.ruleForm.codeUsername,
              code: this.ruleForm.code,
              password: this.ruleForm.password,
              randomStr: this.ruleForm.randomStr,
              grant_type: grant_type,
              scope: scope,
            },
            withCredentials: false,
          })
            .then((res) => {
              localStorage.removeItem("nonce_list");
              this.ruleForm.code = "";
              let d = new Date();
              d.setTime(d.getTime() + 100 * 60 * 240);
              let expires = "expires=" + d.toUTCString();
              document.cookie =
                "ZTGlS_TOKEN=" +
                res.data.access_token +
                ";path=/;domain=." +
                this.name +
                ";expires=" +
                expires;
              axios({
                method: "get",
                url: this.API.upms + "user/info",
                headers: {
                  Authorization: "Bearer " + res.data.access_token,
                },
                withCredentials: false,
              }).then((ress) => {
                if (
                  ress.data.data.roles[0] == "ROLE_MC" ||
                  ress.data.data.roles[0] == "ROLE_SU" || //子用户
                  ress.data.data.roles[0] == "ROLE_EU" || //终端用户
                  ress.data.data.roles[0] == "ROLE_EUW" //线上终端
                ) {
                  let path = sessionStorage.getItem("path");
                  if (path) {
                    this.$router.push(path);
                  } else {
                    this.$router.push("/");
                  }

                  // if(this.$route.query.logout == "contact"){
                  //     var tempwindow = window.open("_blank");
                  //     tempwindow.location ="http://contact.zthysms.cn";
                  // }else{
                  //     this.$router.push('/');
                  // }
                } else {
                  this.$message({
                    message: "用户名输入有误！",
                    type: "warning",
                  });
                }
              });
            })
            .catch((err) => {
              if (
                err.error_description ==
                "UserDetailsService returned null, which is an interface contract violation"
              ) {
                this.$message({
                  message: "账号暂无权限！",
                  type: "error",
                });
              }
              this.getcode();
            });

          // axios({
          //     method: "post",
          //     url: this.API.upms + "code/checkIp",
          //     data: {
          //     username: this.ruleForm.codeUsername,
          //     },
          //     withCredentials: false,
          // }).then(res=>{
          //     if(res.data.code == 200){
          //         axios({
          //             method:'post',
          //             url:this.API.api + 'auth/oauth/token',
          //             headers:{
          //                 Authorization: "Basic cGlnOnBpZw==",
          //             },
          //             params:{
          //                 username:this.ruleForm.codeUsername,
          //                 code:this.ruleForm.code,
          //                 password:this.ruleForm.password,
          //                 randomStr:this.ruleForm.randomStr,
          //                 grant_type: grant_type,
          //                 scope: scope,
          //             },
          //             withCredentials: false,
          //         }).then(res=>{

          //             this.ruleForm.code = ""
          //             let d = new Date()
          //             d.setTime(d.getTime() + 100 * 60 * 240)
          //             let expires = "expires=" + d.toUTCString()
          //             document.cookie = "ZTGlS_TOKEN=" + res.data.access_token + ";path=/;domain=."+this.name+";expires=" + expires;
          //             axios({
          //                 method : "get",
          //                 url : this.API.upms + "user/info",
          //                 headers :{
          //                     Authorization: "Bearer " + res.data.access_token,
          //                 },
          //                 withCredentials: false,
          //             }).then(ress=>{
          //                 if(ress.data.data.roles[0] == "ROLE_MC" ||
          //                    ress.data.data.roles[0] == "ROLE_SU" || //子用户
          //                    ress.data.data.roles[0] == "ROLE_EU" || //终端用户
          //                    ress.data.data.roles[0] == "ROLE_EUW" //线上终端
          //                 ){
          //                     let path = sessionStorage.getItem("path");
          //                     if (path) {
          //                         this.$router.push(path);
          //                     }else{
          //                         this.$router.push('/');
          //                     }

          //                     // if(this.$route.query.logout == "contact"){
          //                     //     var tempwindow = window.open("_blank");
          //                     //     tempwindow.location ="http://contact.zthysms.cn";
          //                     // }else{
          //                     //     this.$router.push('/');
          //                     // }

          //                 }else{
          //                     this.$message({
          //                         message: "用户名输入有误！",
          //                         type: "warning",
          //                     });
          //                 }
          //             })
          //         })
          //         .catch((err)=>{
          //             // console.log(err,'err');
          //             // this.$message({
          //             //             message: "账号已停用",
          //             //             type: "error",
          //             //         });
          //              this.getcode();
          //         })
          //     }
          // })
        }
      });
    },
  },
  created() {
    this.getcode();
    this.getDomain();
  },
};
</script>

<style scoped>
.login-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  /* background-image: url(../../assets/images/login-bg.jpg); */
  background-size: 100%;
}
.ms-title {
  width: 100%;
  line-height: 50px;
  text-align: center;
  font-size: 20px;
  /* color: #fff; */
  border-bottom: 1px solid #ddd;
}
.ms-login {
  position: absolute;
  /* left:50%; */
  right: 20%;
  top: 50%;
  width: 350px;
  margin: -180px 0 0 -175px;
  border-radius: 5px;
  /* background: rgba(255,255,255, 0.3); */
  background: #fff;
  overflow: hidden;
}
.ms-content {
  padding: 30px 30px;
}
.login-btn {
  text-align: center;
}
.login-btn button {
  width: 100%;
  height: 36px;
  margin-bottom: 10px;
}
.login-tips {
  font-size: 12px;
  line-height: 30px;
  /* color:#fff; */
}
/* .ms-login{
        position: absolute;
        top: 50%;
        right: 0;
        margin-right: -100px;
    } */
</style>