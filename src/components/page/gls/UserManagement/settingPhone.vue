<template>
    <div class="bag">
        <div style="padding: 10px;">
            <el-page-header @back="goBack" content="子用户登录手机号管理">
            </el-page-header>
        </div>
        <div class="fillet PersonalInformation-main">
            <header style="margin: 20px 0"></header>
            <div class="fillet LoginCellPhone-box" style="padding-bottom: 60px">
                <div class="LoginCellPhone-matter">
                    <div>温馨提示</div>
                    <div>1.默认登录手机号为新增时填写的首个手机号。</div>
                    <div>
                        2.该登录手机号至多可添加10个，管理员手机号不可删除！
                    </div>
                </div>
                <div class="LoginCellPhone-creat">
                    <el-button type="primary" @click="addphone()">添加登录手机号</el-button>
                </div>
                <div class="LoginCellPhone-search-fun">
                    <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable" border :stripe="true" :data="tableDataObj.tableData" style="width: 100%">
                        <el-table-column label="用户名">
                            <template slot-scope="scope">
                                <span>{{ scope.row.userName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="手机号码">
                            <template slot-scope="scope">
                                <div class="spanColor">
                                    <span v-if="scope.$index == mobileIndex" style="cursor: pointer;color: #67C23A;">{{
                                        scope.row.maskMobile }}</span>
                                    <span v-else style="cursor: pointer;color: #67C23A;"
                                        @click="handelDecode(scope.row, scope.$index)">{{
                                            scope.row.mobile }}</span>

                                    <el-tooltip effect="dark" content="管理员" placement="top">
                                        <i v-if="scope.row.isAdmin == 1" style="color: #409eff;margin-left: 10px;"
                                            class="iconfont icon-yonghuming"></i>
                                    </el-tooltip>
                                </div>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="是否管理员">
                            <template slot-scope="scope">
                                <span v-if="scope.row.isAdmin == 1">管理员</span>
                                <span v-else>用户</span>
                            </template>
                        </el-table-column> -->
                        <el-table-column label="备注">
                            <template slot-scope="scope">
                                <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="220">
                            <template slot-scope="scope">
                                <el-button style="color: #E6A23C;" icon="el-icon-edit" type="text"
                                    @click="editphone(scope.row)">编辑</el-button>
                                <el-button v-if="scope.row.isAdmin != 1" style="color: #F56C6C;" icon="el-icon-delete"
                                    type="text" @click="deletephone(scope.row)">删除</el-button>
                                <el-button v-if="scope.row.isAdmin != 1" style="color: #409eff;" icon="el-icon-s-unfold"
                                    type="text" @click="transferAdmin(scope.row)">管理员转让</el-button>

                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <el-dialog title="添加登录手机号" :visible.sync="LcpDialogVisible" width="800px" class="LoginCellPhoneDialog"
            :close-on-click-modal="false" :before-close="handelClose">
            <el-form :inline="true" :model="addPhoneForm" :rules="addPhoneRules" ref="ruleForm2" class="demo-ruleForm">
                <div v-if="addPhoneForm.list.length">
                    <template v-for="(row, index) in addPhoneForm.list">
                        <el-form-item label-width="120px" :rules="addPhoneRules.phone" label="登录手机号"
                            :prop="'list.' + index + '.phone'">
                            <el-input v-model="row.phone" class="input-t"></el-input>
                        </el-form-item>
                        <el-form-item :rules="addPhoneRules.remark" label="备注" :prop="'list.' + index + '.remark'">
                            <el-input v-model="row.remark" class="input-t"></el-input>
                        </el-form-item>
                        <i style="font-size: 24px;color: #F56C6C;cursor: pointer;" class="el-icon-remove-outline"
                            @click="addPhoneForm.list.splice(index, 1)"></i>
                    </template>
                    <div class="add-white" @click="addWhiteListAction">
                        添加手机号
                    </div>
                </div>
                <div v-else class="add-white-list" @click="addWhiteListAction">
                    添加手机号
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="LcpDialogVisible = false" style="width: 100px; padding: 9px 0">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm2')"
                    style="width: 100px; padding: 9px 0">提交</el-button>
            </span>
        </el-dialog>
        <el-dialog :title="title" :visible.sync="DeletePhone" width="560px" class="LoginCellPhoneDialog"
            :close-on-click-modal="false" :before-close="handelClose">
            <el-form :model="delphone" :rules="addPhoneRules" ref="ruleForm3" class="demo-ruleForm" label-width="130px">
                <el-form-item label="手机号" prop="mobile">
                    <el-input disabled v-model="delphone.mobile" class="input-t"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="delphone.remark" type="textarea" class="input-t"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="DeletePhone = false" style="width: 100px; padding: 9px 0">取消</el-button>
                    <el-button type="primary" @click="delSubmitForm('ruleForm3')"
                        style="width: 100px; padding: 9px 0">确认编辑</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
        <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
    </div>
</template>

<script>
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../common/bus"
import common from "../../../../assets/js/common";
export default {
    name: "settingPhone",
    components: {ResetNumberVue },
    data() {
        var phone = (rule, value, callback) => {
            if (!/^([，；,;]*1\d{10}[，；,;]*)*$/.test(value)) {
                return callback(new Error("请输入正确手机号"));
            } else if (value == "") {
                return callback(new Error("请输入手机号"));
            } else {
                callback();
            }
        };
        return {
            // loginInfo: {
            //     isAdmin: null,
            //     consumerName: "",
            //     mobile: "",
            //     remark: "",
            //     id: ""
            // },
            title: '',
            LcpDialogVisible: false,//登录手机号弹窗
            DeletePhone: false,//删除手机号弹窗
            delphone: {
                phoneId: "",
                mobile: "",
                remark: "",
            },
            //登录手机号表单
            addPhoneForm: {
                list: [
                    {
                        phone: "",
                        remark: "",
                    }
                ],
            },
            //登录手机号表单验证规则
            addPhoneRules: {
                remark: [
                    { required: true, message: "请输入备注" },
                    {
                        min: 2,
                        max: 10,
                        message: "长度在 2 到 10 个字符",
                        trigger: "blur",
                    },
                ],
                phone: [
                    { required: true, validator: phone, trigger: "blur" },
                    // { min: 6, max: 6, message: '请输入6位数字验证码' }
                ],
            },
            //table数据源
            tableDataObj: {
                loading2: false,
                tableData: []
            },
            userName: "",
            mobileIndex: null,
            resetVideo: false,
            infoData: {},
        }
    },
    created() {
        bus.$on("closeVideo", (msg) => {
            this.resetVideo = msg;
        });
        // this.getLoginInfo()
        this.InquireList()
    },
    methods: {
        //返回上一页
        goBack() {
            this.$router.push({
                path: "/UserManagement",
            });
        },
        // getLoginInfo() {
        //     this.$api.get(
        //         this.API.cpus + "userLoginAdmin/loginPhoneInfo",
        //         {},
        //         (res) => {
        //             if (res.code == 200) {
        //                 this.loginInfo = res.data;
        //             }
        //         }
        //     );
        // },
        //获取列表数据
        InquireList() {
            this.tableDataObj.loading2 = true;
            this.$api.get(
                this.API.cpus + "v3/consumer/manager/user/" + this.$route.query.id,
                {},
                (res) => {
                    if (res.code == 200) {
                        this.tableDataObj.tableData = res.data.mobileList;
                        this.tableDataObj.tableData.forEach(item => {
                            item.userName = res.data.userName
                            item.mobile = item.maskMobile
                        })
                        this.userName = res.data.userName
                        this.tableDataObj.loading2 = false;
                    }
                })
        },
        //添加手机号弹窗
        addphone() {
            if (this.tableDataObj.tableData.length >= 10) {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: "子用户最多添加10个手机号码",
                });
            } else {
                this.LcpDialogVisible = true;
            }
        },
        //添加登录手机号
        addWhiteListAction() {
            let obj = {
                phone: "",
                remark: "",
            }
            this.addPhoneForm.list.push(obj)
        },
        //提交表单
        submitForm(val) {
            this.$refs[val].validate((valid) => {
                if (valid) {
                    let data = {
                        phoneList: this.addPhoneForm.list,
                        userName: this.userName,
                    }
                    this.$api.post(
                        this.API.cpus + "v3/consumer/manager/addLoginPhoneV2",
                        data,
                        (res) => {
                            if (res.code == 200) {
                                this.LcpDialogVisible = false; //关闭弹出框
                                // this.cancel();
                                this.nmb = 120;
                                clearInterval(this.timer);
                                this.InquireList(); //刷新列表
                            } else {
                                this.$message({
                                    type: "error",
                                    duration: "2000",
                                    message: res.msg,
                                });
                            }
                        })
                } else {
                    console.log('error submit!!')
                }

            });
        },
        //编辑
        editphone(row) {
            this.title = '编辑';
            this.delphone.phoneId = row.phoneId;
            this.delphone.mobile = row.maskMobile;
            this.delphone.remark = row.remark || "";
            this.DeletePhone = true;
        },
        //删除手机号
        deletephone(row) {
            this.$confirms.confirmation(
                "post",
                "确认删除该手机号",
                this.API.cpus +
                "v3/consumer/manager/deleteLoginPhoneV2",
                { phoneId: row.phoneId, userName: this.userName, },
                (res) => {
                    if (res.code == 200) {
                        this.InquireList();
                    }
                }
            );
        },
        //管理员转让
        transferAdmin(row) {
            let data = {
                destId: row.phoneId,
                sourceId: '',
                userName: this.userName,
            }
            for (let i = 0; i < this.tableDataObj.tableData.length; i++) {
                if (this.tableDataObj.tableData[i].isAdmin == 1) {
                    data.sourceId = this.tableDataObj.tableData[i].phoneId;
                    break;
                }
            }
            this.$confirms.confirmation(
                "post",
                "是否将该账号设置为管理员",
                this.API.cpus +
                "v3/consumer/manager/transferAdmin",
                data,
                (res) => {
                    if (res.code == 200) {
                        this.InquireList();
                    }
                }
            );
        },
        delSubmitForm(val) {
            this.$refs[val].validate((valid) => {
                if (valid) {
                    let data = {
                        phoneId: this.delphone.phoneId,
                        remark: this.delphone.remark,
                        userName: this.userName,
                    }
                    this.$api.post(
                        this.API.cpus + "v3/consumer/manager/updateLoginPhone",
                        data,
                        (res) => {
                            if (res.code == 200) {
                                this.DeletePhone = false
                                this.InquireList(); //刷新列表
                            } else {
                                this.$message({
                                    type: "error",
                                    duration: "2000",
                                    message: res.msg,
                                });
                            }
                        })
                } else {
                    console.log('error submit!!')
                }
            })
        },
        handelDecode(val, index) {
            this.mobileIndex = index
            this.$api.post(
                this.API.upms + "generatekey/decryptMobile",
                {
                    keyId: val.keyId,
                    smsInfoId: val.smsInfoId,
                    cipherMobile: val.cipherMobile,
                },
                (res) => {
                    if (res.code == 200) {
                        this.tableDataObj.tableData[index].maskMobile = res.data;
                        // this.$nextTick(() => {
                        //     this.$set(this.tableDataObj.tableData[index], "maskMobile", res.data);
                        // });
                        // console.log(this.tableDataObj.tableData, 'this.tableDataObj.tableData');
                    } else if (res.code == 4004002) {
                        common.fetchData().then((res) => {
                            if (res.code == 200) {
                                if (res.data.isAdmin == 1) {
                                    this.resetVideo = true;
                                    this.infoData = res.data;
                                } else {
                                    this.$message({
                                        message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                        type: "warning",
                                    });
                                }
                            } else {
                                this.$message({
                                    message: res.msg,
                                    type: "error",
                                });
                            }

                        })
                    } else {
                        this.$message({
                            message: res.msg,
                            type: "warning",
                        });
                    }
                    // this.tableDataObj.tableData[index].mobile=res.data
                }
            );
        },
        //关闭弹窗
        handelClose() {
            this.LcpDialogVisible = false;
            this.DeletePhone = false;
        },
    },
    watch: {
        LcpDialogVisible: function (val) {
            if (!val) {
                this.$refs.ruleForm2.resetFields();
                this.addPhoneForm.list = [
                    {
                        phone: "",
                        remark: "",
                    }
                ];
                // this.$refs.ruleForm2.resetFields()
            }
        },
        DeletePhone: function (val) {
            if (!val) {
                this.$refs.ruleForm3.resetFields();
            }
        },
        resetVideo(val){
            if(!val){
                this.mobileIndex = null;
            }
        },
    }
}
</script>

<style lang="less" scoped>
.LoginCellPhone-box {
    padding: 20px;
}

.LoginCellPhone-matter {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px;
    font-size: 12px;
}

.LoginCellPhone-matter>div {
    /* height:26px; */
    line-height: 26px;
}

.LoginCellPhone-creat {
    margin: 20px 0px;
}

.add-white-list {
    width: 650px;
    height: 50px;
    line-height: 50px;
    border: 1px dashed #66CCFF;
    text-align: center;
    color: #66CCFF;
    cursor: pointer;
    margin: 0 auto;
}

.add-white {
    width: 640px;
    height: 30px;
    line-height: 30px;
    border: 1px dashed #66CCFF;
    text-align: center;
    color: #66CCFF;
    cursor: pointer;
    margin: 0 auto;
}

.input-t {
    width: 250px;
}
</style>