<template>
    <div class="simple-template-page">
        <!-- 简约页面头部 -->
        <!-- <div class="page-header">
          <div class="header-content">
            <h1 class="page-title">模板管理</h1>
            <span class="page-subtitle">短信模板管理中心</span>
          </div>
        </div> -->

        <!-- 主要内容区域 -->
        <div class="page-content">
            <div class="content-container enhanced-layout">
                <!-- 简约提醒区域 -->
                <div class="notice-section">
                    <h3 class="notice-title">温馨提醒</h3>
                    <div class="notice-list">
                        <div class="notice-item">
                            <span
                                class="notice-label">短信组成：</span>一个完整的短信由短信签名和短信正文内容两部分组成，您可以根据业务需求分别设置不同的短信正文内容模板，然后进行组合形成最终展示。
                        </div>
                        <div class="notice-item">
                            <span
                                class="notice-label">审核时间：</span>模板提交审核，工作日预计2小时内完成，非工作日预计4小时内完成。审核时间：周一至周日9:30-22:00（法定节假日顺延）。
                        </div>
                        <div class="notice-item">
                            <span class="notice-label">通知设置：</span>可设置常用手机和邮箱，用于即时接收该应用短信内容审核通知。
                        </div>
                        <!-- <div class="notice-item">
                            <span class="notice-label">批量导入：</span>支持变量模版批量导入。
                        </div> -->
                    </div>
                </div>

                <!-- 固定操作栏 -->
                <div class="fixed-toolbar">
                    <div class="toolbar-container">
                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <div class="action-buttons">
                                <el-button @click="clickAddTem" class="action-btn primary" icon="el-icon-plus">
                                    创建正文模板
                                </el-button>
                                <!-- <el-button @click="exportNums1" class="action-btn" icon="el-icon-download">
                    导出
                  </el-button>
                  <el-button @click="batchQu" class="action-btn" icon="el-icon-upload2">
                    批量导入
                  </el-button> -->
                            </div>

                            <!-- 统计信息 -->
                            <div class="stats-info">
                                <span class="stats-text">共 {{ pageTotal }} 条记录</span>
                                <el-divider direction="vertical"></el-divider>
                                <span class="stats-text">当前第 {{ tabelAlllist.currentPage }} 页</span>
                            </div>
                        </div>

                        <!-- 搜索区域 -->
                        <div class="search-section">
                            <div class="advanced-search-form">
                                <div class="search-row">
                                    <div class="search-item">
                                        <label class="search-label">搜索内容</label>
                                        <el-input placeholder="模板名称、内容、ID" v-model="param" class="search-input"
                                            clearable prefix-icon="el-icon-search" />
                                    </div>

                                    <div class="search-item">
                                        <label class="search-label">审核状态</label>
                                        <el-select v-model="tabelAlllist.status" placeholder="全部状态"
                                            class="search-select" @change="getTableData" clearable>
                                            <el-option label="编辑中" value="0" />
                                            <el-option label="待审核" value="1" />
                                            <el-option label="已通过" value="2" />
                                            <el-option label="驳回" value="3" />
                                            <el-option label="待复审" value="4" />
                                        </el-select>
                                    </div>

                                    <div class="search-buttons">
                                        <el-button type="primary" @click="getTableData" class="search-btn primary"
                                            icon="el-icon-search">
                                            查询
                                        </el-button>
                                        <el-button @click="resetSearch" class="search-btn" icon="el-icon-refresh">
                                            重置
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 模板列表 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3 class="table-title">模板列表</h3>
                    </div>

                    <div class="table-container">
                        <el-table v-loading="tableDataObj.loading2" element-loading-text="正在加载..."
                            element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(255, 255, 255, 0.8)" ref="multipleTable" border
                            :data="tableDataObj.tableData" class="enhanced-table" stripe
                            :header-cell-style="{ background: '#fafafa', color: '#333' }"
                            :row-style="{ height: '60px' }" empty-text="暂无模板数据">
                            <el-table-column prop="temId" label="模板ID" width="80" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.temId }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="userName" label="用户名称" width="100" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.clientName }}
                                </template>
                            </el-table-column>
                            <el-table-column label="模板类型" width="100" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.temType == '1' ? '验证码' : scope.row.temType == '2' ? '通知' : '营销推广' }}
                                </template>
                            </el-table-column>

                            <el-table-column label="申请时间" width="170" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.createTime }}
                                </template>
                            </el-table-column>

                            <el-table-column label="模板名称" width="170">
                                <template slot-scope="scope">
                                    {{ scope.row.temName }}
                                </template>
                            </el-table-column>

                            <el-table-column label="内容" min-width="400">
                                <template slot-scope="scope">
                                    <div class="content-cell">
                                        <div class="content-text">{{ scope.row.temContent }}</div>
                                        <div v-if="scope.row.temStatus == '3' && scope.row.checkReason"
                                            class="reject-reason">
                                            驳回原因：{{ scope.row.checkReason }}
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>

                            <el-table-column label="模板变量" width="200">
                                <template slot-scope="scope">
                                    <div v-if="scope.row.text == '{}' || !scope.row.text" class="no-variables">
                                        无变量
                                    </div>
                                    <div v-else class="variables-list">
                                        <div v-for="(item, index) in scope.row.text.replace(/\{|}/g, '').split(',')"
                                            :key="index" class="variable-item">
                                            <span class="variable-name">{{ item.split(":")[0] }}</span>
                                            <span class="variable-type">{{ item.split(":")[1] }}</span>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>

                            <el-table-column label="字数" width="80" align="center">
                                <template slot-scope="scope">
                                    {{ scope.row.temContent.length }}
                                </template>
                            </el-table-column>

                            <el-table-column label="状态" width="100" align="center">
                                <template slot-scope="scope">
                                    {{ getStatusText(scope.row.temStatus) }}
                                </template>
                            </el-table-column>

                            <el-table-column label="操作" width="200" fixed="right">
                                <template slot-scope="scope">
                                    <div class="table-actions">
                                        <el-tooltip content="编辑模板" placement="top"
                                            v-if="scope.row.temStatus == '0' || scope.row.temStatus == '3'">
                                            <el-button type="text" @click="editTem(scope.$index, scope.row)"
                                                class="action-btn-small edit" icon="el-icon-edit">
                                                编辑
                                            </el-button>
                                        </el-tooltip>

                                        <!-- <el-tooltip content="复制模板" placement="top"
                                            v-if="scope.row.temStatus == '2' && scope.row.version == '2'">
                                            <el-button type="text" @click="copySmsTemp(scope.row)"
                                                class="action-btn-small info" icon="el-icon-copy-document">
                                                复制
                                            </el-button>
                                        </el-tooltip> -->

                                        <el-tooltip content="删除模板" placement="top">
                                            <el-button v-permission type="text" @click="delTem(scope.$index, scope.row)"
                                                class="action-btn-small delete" icon="el-icon-delete">
                                                删除
                                            </el-button>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <!-- 简约分页 -->
                    <div class="pagination-section">
                        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                            :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize"
                            :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                            :total="pageTotal" class="simple-pagination" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
let _ = require("lodash");
export default {
    name: "subTemplateManagement",
    data() {
        return {
            tabelAlllist: {
                //------发送表格请求的对象
                param: "",
                status: "",
                currentPage: 1, //当前页
                pageSize: 10, //每一页条数
            },
            tableDataObj: {
                //列表数据
                tableData: [],
            },
            pageTotal: 0,
            param: '',
        };
    },
    methods: {
        // 获取状态类型
        getStatusType(status) {
            const statusMap = {
                '0': 'info',     // 编辑中
                '1': 'warning',  // 待审核
                '2': 'success',  // 已通过
                '3': 'danger',   // 驳回
                '4': 'primary'   // 待复审
            };
            return statusMap[status] || 'info';
        },

        // 获取状态图标
        getStatusIcon(status) {
            const iconMap = {
                '0': 'el-icon-edit',
                '1': 'el-icon-time',
                '2': 'el-icon-check',
                '3': 'el-icon-close',
                '4': 'el-icon-refresh'
            };
            return iconMap[status] || 'el-icon-info';
        },

        // 获取状态文本
        getStatusText(status) {
            const textMap = {
                '0': '编辑中',
                '1': '待审核',
                '2': '已通过',
                '3': '驳回',
                '4': '待复审'
            };
            return textMap[status] || '未知';
        },
        clickAddTem() {
            this.$router.push({ path: "/subCreateTemplate", query: { i: "1" } });
        },

        // 重置搜索
        resetSearch() {
            this.param = '';
            this.tabelAlllist.status = '';
            this.tabelAlllist.currentPage = 1;
            this.getTableData();
        },
        /* --------------- 列表展示 ------------------*/
        getTableData() {
            //获取列表数据
            this.tableDataObj.loading2 = true;
            let formDatas = this.tabelAlllist;
            this.$api.post(
                this.API.cpus + "v3/consumersmstemplate/manager/page",
                formDatas,
                (res) => {
                    this.tableDataObj.tableData = res.records;
                    this.tableDataObj.loading2 = false;
                    this.pageTotal = res.total;
                }
            );
        },
        //   handleSuccess(res) {
        //     // console.log(res)
        //     if (res.code == 200) {
        //       this.$message({
        //         type: "success",
        //         duration: "2000",
        //         message: "导入成功",
        //       });
        //       this.batchShow = false;
        //       this.fileList = [];
        //       this.getTableData();
        //     } else {
        //       this.$message({
        //         type: "error",
        //         duration: "2000",
        //         message: res.msg,
        //       });
        //     }
        //   },
        //   handleRemove() {
        //     this.fileList = [];
        //   },
        // 批量导入
        //   batchQu() {
        //     this.batchShow = true;
        //   },
        //导出
        //   exportNums1() {
        //     let obj = {
        //       param: this.param,
        //       productType: 12,
        //       status: this.tabelAlllist.status,
        //     };
        //     this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        //       if (res.code == 200) {
        //         this.$message({
        //           type: "success",
        //           duration: "2000",
        //           message: "已加入到文件下载中心!",
        //         });
        //       } else {
        //         this.$message({
        //           type: "error",
        //           duration: "2000",
        //           message: res.msg,
        //         });
        //       }
        //     });
        //   },
        handleSizeChange(size) {
            //分页每一页的有几条
            this.tabelAlllist.pageSize = size;
            this.getTableData();
        },
        handleCurrentChange: function (currentPage) {
            //分页的第几页
            this.tabelAlllist.currentPage = currentPage;
            this.getTableData();
        },
        //编辑模板
        editTem(index, val) {
            let rouer = {
                path: "/subCreateTemplate",
                query: { param: val.temId },
            };
            this.$router.push(
                rouer,
                () => { },
                (e) => {
                    console.log(e, "e");
                }
            );
        },
        //   copySmsTemp(val) {
        //     if (val) {
        //       this.$confirms.confirmation(
        //         "post",
        //         "确定复制当前模板？",
        //         this.API.cpus + `v3/consumersmstemplate/template/copy`,
        //         {
        //           temId: val.temId
        //         },
        //         (res) => {
        //           // this.InquireList()
        //           if (res.code == 200) {
        //             this.$router.push({
        //               path: "/subCreateTemplate",
        //               query: { param: res.data },
        //             });
        //           }
        //         }
        //       );
        //     }
        //   },
        // 删除
        delTem(index, val) {
            let temId = val.temId;
            this.$confirms.confirmation(
                "delete",
                "此操作将永久删除该数据, 是否继续？",
                this.API.cpus + "v3/consumersmstemplate/manager/delete/" + temId,
                {},
                (res) => {
                    this.getTableData();
                }
            );
        },
    },
    mounted() {
        this.header = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
        this.getTableData();
        this.getCompany = _.debounce(this.getTableData, 500);
    },
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* tempv1 特有样式 */
.tem-font {
    font-size: 12px;
}

.tem-font span {
    color: red;
}

.f-basic {
    color: #16a589;
    font-weight: bold;
}

.short-title {
    font-weight: bolder;
    padding-bottom: 5px;
}

.font-sizes {
    padding-top: 2px;
    font-size: 12px;
    color: rgb(163, 163, 163);
}

.font-sizes1 {
    margin-top: 10px;
}
</style>
<style>
.el-table .cell.el-tooltip {
    white-space: pre-wrap !important;
}

.myTems .el-dialog__body {
    padding-bottom: 50px !important;
}

.text-adaNum {
    color: #16a589;
    cursor: pointer;
}

.TemDialog .el-tabs__item {
    height: 32px;
    line-height: 32px;
}

.TemDialog .el-tabs__header {
    margin: 0px;
}

.TemDialog .el-tabs__content {
    overflow: inherit;
}

.el-table--small th {
    background: #f5f5f5;
}
</style>