<template>
  <div class="bag" style="padding: 10px;">
    <div class="Top_title">
      <span
        style="
          display: inline-block;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        "
        @click="goBack()"
        ><i class="el-icon-arrow-left"></i> 返回</span
      >|
      <span>用户详情</span>
    </div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <el-tabs v-model="activeName" type="card" @tab-click="handleClickTab">
        <el-tab-pane label="账户信息" name="accountInfo">
          <div class="account-Info-box">
            <div>
              <span
                class="Signature-list-header"
                style="
                  display: inline-block;
                  width: 100%;
                  height: 30px;
                  line-height: 30px;
                "
                >基本信息</span
              >
            </div>
            <!-- 基本信息 -->

            <table-tem
              class="basicInfo"
              :tableDataObj="basicInfoTable"
            ></table-tem>
            <p style="margin-top: 30px; font-weight: bold">通知与告警</p>
            <div style="padding-top: 15px">
              <el-button
                v-permission
                @click="addAlarmContact"
                type="primary"
                style="margin: 10px 5px"
                >添加子用户联系人</el-button
              >
              <span style="font-weight: normal">最多可添加5个指定联系人</span>
            </div>
            <!-- 表格组件 -->
            <table-tem
              :tableDataObj="tableDataObj"
              @handelOptionButton="handelOptionButton"
            ></table-tem>
            <div style="position: relative; padding-top: 60px">
              <span class="Signature-list-header">近期充值信息</span>
              <!-- <span style="font-weight:normal;font-size:12px;padding-left:18px">当前账户余额：{{accountBalance}}</span> -->
              <!-- <el-date-picker     
                    class="accountinfo-w dateStyle"
                    v-model="searchInput.time"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="handeTime">
            </el-date-picker> -->
            </div>
            <!-- 表格组件 -->
            <table-tem :tableDataObj="recentTableDataObj"></table-tem>
            <!--分页-->
            <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0">
            <el-pagination class="page_bottom" style="float:right" @size-change="handleSizeChange" @current-change="handleCurrentChange"  :current-page="searchInput.currentPage"  :page-size='searchInput.pageSize' :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="totalSize">
            </el-pagination>
        </el-col> -->
            <!-- 余额提醒通知     -->
            <div>
              <div class="fillet bas-block" style="height: 181px">
                <!-- <div style="padding-top:60px">
                    <span class="Signature-list-header">余额提醒通知</span>
                </div>
                <el-button type="primary"  class="set-balance-tips" @click="handleRebalanceReminder" v-if="rebalanceReminder == false">启用</el-button>
                <div v-if="rebalanceReminder == true">
                    <transition name="fade">  
                        <div class="tips-box" v-show="balanceReminder">
                            <div style="padding-bottom:15px;">当前账号余额条数在不足 <span class="bac-color-red"> {{balanceReminders.SingleFrequencysub }} </span> 条时提醒</div>
                            <p style="color:red;font-size:12px;">注：请去添加告警联系人，以便正常接收余额不足通知！</p>
                            <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                            <el-row>
                                <el-button type="primary"  class="set-balance-tips" @click="setSendBalance">设置</el-button>
                                <el-button type="primary"  class="set-balance-tips" @click="handleChangeBla()">关闭设置</el-button>
                            </el-row>
                        </div>
                    </transition>
                    <transition name="fade">  
                        <div class="tips-box" v-show="!balanceReminder" >
                            <div style="padding-bottom:15px;">当前账号余额条数在不足 <el-input  style="width:120px;" v-model="balanceReminders.successRate" oninput = "value=value.replace(/[^\d]/g,'')"></el-input> 条时提醒</div>
                            <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                            <el-row>
                                <el-button type="primary"  class="sure-balance-tips" @click="sureSendbalace">确定</el-button>
                                <el-button type="primary" plain  class="cancel-balance-tips" @click="cancelSendbalace">取消</el-button>
                            </el-row>
                        </div>
                    </transition>
                </div> -->
                <div style="padding-top: 60px">
                  <span class="Signature-list-header"
                    >防轰炸设置<span style="color: red; font-size: 12px"
                      >（设置当前账号，一天内向同一手机号，发送短信数量限制）</span
                    ></span
                  >
                </div>
                <el-form
                  :model="SendSettings"
                  label-width="124px"
                  :inline="true"
                >
                  <el-form-item label="验证码发送：">
                    <el-input-number
                      v-if="copySettings.parentOverrunCode != 0"
                      v-model="SendSettings.overrunCode"
                      :min="1"
                      :max="copySettings.parentOverrunCode"
                      label=""
                    ></el-input-number>
                    <el-input-number
                      v-else
                      v-model="SendSettings.overrunCode"
                      :min="1"
                      :max="999"
                      label=""
                    ></el-input-number>
                    <!-- <el-input  v-model="SendSettings.overrunCode"></el-input> -->
                  </el-form-item>
                  <el-form-item label="行业通知发送：">
                    <el-input-number
                      v-if="copySettings.parentOverrunIndustry != 0"
                      v-model="SendSettings.overrunIndustry"
                      :min="1"
                      :max="copySettings.parentOverrunIndustry"
                      label=""
                    ></el-input-number>
                    <el-input-number
                      v-else
                      v-model="SendSettings.overrunIndustry"
                      :min="1"
                      :max="999"
                      label=""
                    ></el-input-number>
                    <!-- <el-input  v-model="SendSettings.overrunIndustry"></el-input> -->
                  </el-form-item>
                  <el-form-item label="营销短信发送：">
                    <el-input-number
                      v-if="copySettings.parentOverrunMarket != 0"
                      v-model="SendSettings.overrunMarket"
                      :min="1"
                      :max="copySettings.parentOverrunMarket"
                      label=""
                    ></el-input-number>
                    <el-input-number
                      v-else
                      v-model="SendSettings.overrunMarket"
                      :min="1"
                      :max="999"
                      label=""
                    ></el-input-number>
                    <!-- <el-input  v-model="SendSettings.overrunMarket"></el-input> -->
                  </el-form-item>
                  <el-button
                    v-permission
                    type="primary"
                    class="sure-balance-tips"
                    @click="save"
                    >保存</el-button
                  >
                </el-form>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="子用户签名管理" name="signature">
          <div v-if="activeName == 'signature'">
            <signature></signature>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 编辑紧急联系人弹窗 -->
    <el-dialog
      :title="addAlertTitle"
      :visible.sync="addAlertContactDialog"
      :close-on-click-modal="false"
      width="520px"
    >
      <el-form
        :model="addAlertContactForm.formData"
        :rules="addAlertContactForm.formRule"
        label-position="left"
        ref="addAlertContactForm"
        label-width="135px"
        style="padding: 0 30px 10px"
      >
        <el-form-item label="姓名" prop="linkmanName">
          <el-input
            v-model="addAlertContactForm.formData.linkmanName"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机" prop="linkmanPhone">
          <el-input
            v-model="addAlertContactForm.formData.linkmanPhone"
          ></el-input>
        </el-form-item>
        <el-form-item label="接收人工审核通知" prop="auditRemind">
          <el-radio v-model="addAlertContactForm.formData.auditRemind" label="2"
            >是</el-radio
          >
          <el-radio v-model="addAlertContactForm.formData.auditRemind" label="1"
            >否</el-radio
          >
        </el-form-item>
        <!-- <el-form-item label="余额变更&不足通知" prop="balanceRemind" v-if="this.$route.query.ids == '14' && this.isOpenBalace == true">
                <el-radio v-model="addAlertContactForm.formData.balanceRemind" label="2">是</el-radio>
                <el-radio v-model="addAlertContactForm.formData.balanceRemind" label="1">否</el-radio>
            </el-form-item> -->
        <el-form-item label="余额变更&不足通知" prop="balanceRemind">
          <el-radio
            v-model="addAlertContactForm.formData.balanceRemind"
            label="2"
            >是</el-radio
          >
          <el-radio
            v-model="addAlertContactForm.formData.balanceRemind"
            label="1"
            >否</el-radio
          >
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button
          @click="cancelForm('addAlertContactForm')"
          style="width: 100px; padding: 9px 0"
          >取消</el-button
        >
        <el-button
          v-permission
          type="primary"
          @click="submitForm('addAlertContactForm')"
          style="width: 100px; padding: 9px 0"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
var axios = require("axios");
import TableTem from "@/components/publicComponents/TableTem";
import signature from "./signature.vue";
// 引入时间戳转换
import { formatDate } from "@/assets/js/date.js";

export default {
  name: "UserDetails",
  components: {
    TableTem,
    signature,
  },
  data() {
    var optionWidths = () => {
      window.onresize = function () {
        //须要注意做用域的问题 方法内this是window
        let windowWidth = document.documentElement.clientWidth;
        if (windowWidth > 950) {
          return 50;
        } else {
          return 132;
        }
      };
    };
    //扩展号验证
    // var checkSignature=(rule,value,callback)=>{
    //     if(value==""){
    //         callback()
    //     }
    //     else{
    //         if(!/^\d{1,12}$/.test(value)){
    //             callback(new Error('指定扩展号由1-12位数字组成'))
    //         }
    //         else{
    //             this.$api.get(this.API.omcs+'operatingSignature/checkClientExt',{ext:value,userId:this.clientId},
    //             res=>{
    //                 if(res.data!="0"){
    //                 console.log(value)
    //                     callback(new Error("此扩展号已存在"))
    //                 }
    //                 else{
    //                     callback()
    //                 }
    //             })
    //         }
    //     }
    // };
    var checkPhone = (rule, value, callback) => {
      let reg =
        /^((((13[0-9])|199|166|198|165|191|167|146|(15[0-9])|(18[0-9])|(14[1,3,5,7,8,9])|(17[1,3,5,6,7,8]))\d{8})|((170[0,1,2,3,4,5,6,7,8,9])\d{7}))$/;
      if (!value) {
        // return callback(new Error('请输入手机号'));
        callback();
      } else {
        var a = false;
        for (var i = 0; i < this.tableDataObj.tableData.length; i++) {
          if (
            this.addAlertContactForm.formData.linkmanPhone ==
              this.tableDataObj.tableData[i].linkmanPhone &&
            this.editPhone != value
          ) {
            a = true;
            return callback(new Error("手机号已存在"));
          }
        }
        if (!a) {
          if (reg.test(value)) {
            callback();
          }
          return callback(new Error("请输入正确的手机号"));
        }
      }
    };
    var checkBlackWords = (rule, value, callback) => {
      if (value == "") {
        return callback(new Error("加黑关键字不能为空"));
      } else {
        var reg =
          /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,9}$/gi;
        if (!reg.test(value)) {
          return callback(
            new Error("加黑关键词包括数字、汉字、字母、逗号,最多10组")
          );
        } else {
          var arr = value.split(",");
          var arr2 = [];
          for (var i = 0; i < arr.length; i++) {
            if (arr2.indexOf(arr[i]) < 0) {
              arr2.push(arr[i]);
            }
          }
          if (arr2.length != arr.length) {
            return callback(new Error("不允许重复"));
          } else {
            callback();
          }
        }
      }
    };
    return {
      isOpenBalace: false,
      balanceReminder: true,
      rebalanceReminder: false,
      addAlertContactDialog: false,
      clientId: "",
      activeName: "accountInfo",
      addAlertStatus: 1, //添加联系人的弹窗 --状态1为新增，0为编辑
      //余额预警通知
      balanceReminders: {
        SingleFrequencysub: "",
        successRate: "",
      },
      userId: "",
      isWar: false,
      // ————————————————————————————————————————————————————————————————
      SendSettings: {
        overrunCode: "",
        overrunIndustry: "",
        overrunMarket: "",
      },
      copySettings: {
        overrunCode: "",
        overrunIndustry: "",
        overrunMarket: "",
      },
      SendSettingsArry: ["1", "2", "3", "4", "5", "6"],
      replyGrabbing: [],
      receiptGrabbing: [],
      replyPush: [],
      receiptPush: [],
      corporateName: "", //公司名称
      accountBalance: 2,
      jsonData: "", //基本信息=》获取行业类别
      reportStatusNum: "1",
      channel: [], //通道列表
      hasSelectBox: "", //复选框
      roleId: "", //角色ID
      smsId: "", //sms的id
      parentId: "", //传给子用户列表的id
      //基本信息-列表
      basicInfoTable: {
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: "consumerName",
            showName: "用户名",
            fixed: false,
            showColorTag: {
              color: "#16A589",
            },
          },
          {
            prop: "phone",
            showName: "手机号",
            fixed: false,
          },
          {
            prop: "compName",
            showName: "公司名称",
            fixed: false,
          },
          {
            prop: "consumerStatus",
            showName: "用户状态",
            fixed: false,
            formatData: () => {
              return this.jsonData.consumerStatus == 1 ? "停用" : "启用";
            },
          },
          {
            prop: "roleId",
            showName: "用户类型",
            fixed: false,
            formatData: function (val) {
              let type = "";
              if (val == 12) {
                return (type = "管理商");
              } else if (val == 14) {
                return (type = "终端用户");
              } else {
                return (type = "子用户");
              }
              return type;
            },
          },
          { prop: "industryId", showName: "行业", fixed: false },
          // {prop:'salesName',showName:'所属销售',fixed:false},
          // {prop:'serviceName',showName:'所属客服',fixed:false},
          { prop: "createName", showName: "创建人", fixed: false },
          {
            prop: "createTime",
            showName: "创建时间",
            fixed: false,
            width: "145",
          },
          { prop: "email", showName: "邮箱", fixed: false },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: optionWidths, //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      addAlertContactForm: {
        formData: {
          userId: "",
          conLinkmanId: "",
          linkmanName: "",
          linkmanPhone: "",
          auditRemind: "2",
          warningRemind: "1",
          balanceRemind: "1",
        },
        formRule: {
          linkmanName: [
            { required: true, message: "请输入姓名", trigger: "blur" },
            // {pattern:/^[\u4e00-\u9fa5]{2,4}$/gi,message:'姓名为2-4个中文字符!'}
          ],
          linkmanEmail: [
            { required: false, message: "请输入邮箱地址", trigger: "blur" },
            {
              type: "email",
              message: "请输入正确的邮箱地址",
              trigger: ["blur", "change"],
            },
          ],
          auditRemind: [
            {
              required: true,
              message: "请选择是否人工审核通知",
              trigger: "change",
            },
          ],
          warningRemind: [
            {
              required: true,
              message: "请选择是否接收预警通知",
              trigger: "change",
            },
          ],
          balanceRemind: [
            {
              required: true,
              message: "请选择是否接收余额预警通知",
              trigger: "change",
            },
          ],
          linkmanPhone: [
            { required: true, validator: checkPhone, trigger: "change" },
          ],
        },
      },
      //表格数据列表
      tableDataObj: {
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: "linkmanName",
            showName: "姓名",
            width: "140",
            fixed: false,
          },
          {
            prop: "linkmanPhone",
            showName: "手机",
            fixed: false,
          },
          {
            prop: "auditRemind",
            showName: "审核通知",
            fixed: false,
            formatData: function (val) {
              return val == "2" ? "Y" : "N";
            },
          },
          {
            prop: "balanceRemind",
            showName: "余额变更&不足通知",
            fixed: false,
            formatData: function (val) {
              return val == "2" ? "Y" : "N";
            },
          },
          {
            prop: "createTime",
            showName: "创建时间",
            fixed: false,
            width: "160",
          },
          {
            prop: "updateTime",
            showName: "更新时间",
            fixed: false,
            width: "160",
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: optionWidths, //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "编辑",
            type: "",
            size: "mini",
            optionMethod: "editContact",
            icon: "el-icon-edit", //按钮图标
            color: "#16A589", //按钮颜色
          },
          {
            optionName: "删除",
            type: "",
            size: "mini",
            optionMethod: "delContact",
            icon: "el-icon-delete", //按钮图标
            color: "#f56c6c", //按钮颜色
          },
        ],
      },
      //近期充值信息-列表
      recentTableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
        tableLabel: [
          {
            prop: "id",
            showName: "充值ID",
            fixed: false,
          },
          {
            prop: "productId",
            showName: "产品名称",
            fixed: false,
            formatData: (val) => {
              return val == 1 ? "短信" : "彩信";
            },
          },
          {
            prop: "rechargeNum",
            showName: "充值条数",
            fixed: false,
          },
          {
            prop: "rechargeTime",
            showName: "充值时间",
            fixed: false,
            formatData: (val) => {
              return this.$common.formatDate(val);
            },
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: optionWidths, //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
    };
  },
  watch: {
    addAlertContactDialog(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.addAlertContactForm.resetFields(); //表单验证清空
        this.isWar = false;
        for (let key in this.addAlertContactForm.formData) {
          this.addAlertContactForm.formData[key] = "";
          this.addAlertContactForm.formData.auditRemind = "2";
          this.addAlertContactForm.formData.warningRemind = "1";
        }
      }
    },
    moreInfoDialog(val) {
      if (val == false) {
        for (let k in this.moreInfoForm.formData) {
          this.moreInfoForm.formData[k] = "";
        }
      }
    },
  },
  methods: {
    goBack() {
      this.$router.push({ path: "/UserManagement" });
    },
    handleClickTab(tab) {
      this.currentTabComponent = tab.name;
    },
    //余额提醒通知启用
    handleRebalanceReminder() {
      this.rebalanceReminder = true;
      this.balanceReminder = false;
    },
    //余额提醒设置
    setSendBalance() {
      this.balanceReminders.successRate =
        this.balanceReminders.SingleFrequencysub;
      this.balanceReminder = !this.balanceReminder;
    },
    /* --------------- 列表展示 ------------------*/
    getTableData() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      this.$api.get(
        this.API.cpus + "consumerpartnerlinkman/page/" + this.userId,
        {},
        (res) => {
          this.tableDataObj.tableData = res.data;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (val == "addAlertContactForm") {
          if (valid) {
            // console.log();
            this.addAlertContactForm.formData.userId = this.userId;
            if (this.addAlertStatus == 1) {
              //新增
              if (
                this.addAlertContactForm.formData.linkmanPhone == "" &&
                this.addAlertContactForm.formData.linkmanEmail == ""
              ) {
                this.$message({
                  message: "手机号或邮箱必须填一个",
                  type: "warning",
                });
              } else {
                this.$confirms.confirmation(
                  "post",
                  "确定新增子用户联系人",
                  this.API.cpus + "consumerpartnerlinkman/add",
                  this.addAlertContactForm.formData,
                  (res) => {
                    this.getTableData();
                    this.addAlertContactDialog = false; //关闭弹窗
                  }
                );
              }
            } else if (this.addAlertStatus == 0) {
              //编辑conLinkmanId
              if (
                this.addAlertContactForm.formData.linkmanPhone == "" &&
                this.addAlertContactForm.formData.linkmanEmail == ""
              ) {
                this.$message({
                  message: "手机号或邮箱必须填一个",
                  type: "warning",
                });
              } else {
                this.$confirms.confirmation(
                  "put",
                  "确定编辑子用户联系人？",
                  this.API.cpus + "consumerpartnerlinkman/update",
                  this.addAlertContactForm.formData,
                  (res) => {
                    this.getTableData();
                    this.addAlertContactDialog = false; //关闭弹窗
                  }
                );
              }
            }
          } else {
            return false;
          }
        }
      });
    },
    handelOptionButton: function (val) {
      if (val.methods == "editContact") {
        // this.$api.get(this.API.omcs+'operatingBalanceNotice/smsNum/'+this.clientId,{},res=>{
        //     if(res == '' || res == 'undefined' || res == null || res.smsIsOpen == 2){
        //         this.isOpenBalace = false;
        //     }else{
        //         this.isOpenBalace = true;
        //     }
        // })
        this.editPhone = val.row.linkmanPhone;
        this.addAlertStatus = 0;
        this.addAlertContactDialog = true;
        this.addAlertContactForm.formData.conLinkmanId = val.row.conLinkmanId;
        for (let k in this.addAlertContactForm.formData) {
          this.addAlertContactForm.formData[k] = val.row[k];
        }
      } else if (val.methods == "delContact") {
        let conLinkmanId = val.row.conLinkmanId;
        const mount = this.tableDataObj.tableData.length;
        if (mount >= 2) {
          this.$confirms.confirmation(
            "delete",
            "确定删除子用户联系人？",
            this.API.cpus +
              "consumerpartnerlinkman/" +
              this.userId +
              "/" +
              conLinkmanId,
            {},
            (res) => {
              this.getTableData();
            }
          );
        } else {
          this.$message({
            message: "至少保留一条信息",
            type: "warning",
          });
        }
      }
    },
    //表格数据转换
    formatData: function (row, column) {
      if (column.property == "status") {
        return row[column.property] == 1 ? "停止" : "启用";
      } else if (column.property == "userStatus") {
        return row[column.property] == 1 ? "停止" : "启用";
      } else if (column.property == "date") {
        return formatDate(
          new Date(row[column.property] * 1000),
          "yyyy-MM-dd hh:mm:ss"
        );
      }
    },
    //添加告警联系人
    addAlarmContact() {
      if (this.tableDataObj.tableData.length <= 4) {
        this.addAlertContactDialog = true;
        this.addAlertStatus = 1;
        // this.$api.get(this.API.omcs+'operatingBalanceNotice/smsNum/'+this.clientId,{},res=>{
        //     if(res == '' || res == 'undefined' || res == null || res.smsIsOpen == 2){
        //         this.isOpenBalace = false;
        //     }else{
        //         this.isOpenBalace = true;
        //     }
        // })
      } else {
        this.$message({
          type: "warning",
          message: "最多添加5个告警联系人",
        });
      }
    },
    cancelForm(val) {
      //取消
      if (val == "addAlertContactForm") {
        this.addAlertContactDialog = false;
      }
    },
    //关闭余额预警
    // handleChangeBla(){
    //     this.$confirms.confirmation('get','关闭余额提醒通知后，您将收不到此类提醒，您确认关闭吗？',this.API.omcs+'serviceBalanceNotice/close/'+this.userId,{
    //     },res =>{
    //         this.rebalanceReminder = false;
    //         this.balanceReminders.SingleFrequencysub = '';
    //         this.balanceReminders.successRate = '';
    //         this.getsmsClient();
    //         this.isOpenBalace = false;
    //     });
    // },
    //余额预警确定
    // sureSendbalace(){
    //     if(this.balanceReminders.successRate != ''){
    //         let reg = /^([1-9][0-9]{0,6}|10000000)$/;
    //         if(reg.test(this.balanceReminders.successRate)){
    //             this.$api.post(this.API.omcs+'serviceBalanceNotice/save',{
    //                 userId:this.userId,
    //                 smsNum:this.balanceReminders.successRate
    //             },res=>{
    //                 this.balanceReminder = !this.balanceReminder
    //                 this.balanceReminders.SingleFrequencysub =  this.balanceReminders.successRate;
    //                 this.getsmsClient();
    //             })
    //         }else{
    //             this.$message({
    //                 message: '余额提醒条数设置范围在1-1000万之间！',
    //                 type: 'warning'
    //             });
    //         }

    //     }else{
    //         this.$message({
    //             message: '余额提醒条数不能为空！',
    //             type: 'warning'
    //         });
    //     }
    // },
    //余额提醒通知取消
    cancelSendbalace() {
      if (
        this.balanceReminders.SingleFrequencysub == "" ||
        this.balanceReminders.SingleFrequencysub == "undefined" ||
        this.balanceReminders.SingleFrequencysub == null
      ) {
        this.rebalanceReminder = false;
      } else {
        this.balanceReminder = !this.balanceReminder;
        this.balanceReminders.successRate =
          this.balanceReminders.SingleFrequencysub;
      }
    },
    /**---------数据表格（基本信息）------ */
    getBasicData() {
      this.$api.get(
        this.API.cpus + "v3/consumer/manager/user/info/" + this.userId,
        {},
        (res) => {
          console.log("-----------基本信息---------", res);
          if (res.code == 200) {
            let a = [res.data];
            this.basicInfoTable.tableData = a;
            this.SendSettings.overrunCode = res.data.overrunCode;
            this.SendSettings.overrunIndustry = res.data.overrunIndustry;
            this.SendSettings.overrunMarket = res.data.overrunMarket;
            this.copySettings.parentOverrunCode = res.data.parentOverrunCode;
            this.copySettings.parentOverrunIndustry =
              res.data.parentOverrunIndustry;
            this.copySettings.parentOverrunMarket =
              res.data.parentOverrunMarket;

            this.$api.get(
              this.API.recharge + "partner/recharge/page",
              { username: res.data.consumerName, pageSize: 5 },
              (res) => {
                this.recentTableDataObj.tableData = res.data.records;
              }
            );
          } else {
            this.basicInfoTable.tableData = [];
            this.recentTableDataObj.tableData = [];
          }
        }
      );
    },
    // handeTime: function (val) { //获取查询时间框的值
    //     if(val){
    //         this.searchInput.beginTime = this.moment(val[0]).format("YYYY-MM-DD ");
    //         this.searchInput.endTime = this.moment(val[1]).format("YYYY-MM-DD ");
    //     } else{
    //         this.searchInput.beginTime = '';
    //         this.searchInput.endTime = '';
    //     }
    //     this.searchInput.pageSize=10;
    //     this.searchInput.currentPage=1;
    //     this.getRecentData();
    // },
    // handleSizeChange(size) {
    //  this.searchInput.pageSize=size;
    //  this.getRecentData();
    // },
    // handleCurrentChange: function(currentPage){
    //     this.searchInput.currentPage=currentPage;
    //     this.getRecentData();
    // },
    //防轰炸设置
    save() {
      if (
        !/^[+]{0,1}(\d+)$/.test(this.SendSettings.overrunIndustry) ||
        !/^[+]{0,1}(\d+)$/.test(this.SendSettings.overrunCode) ||
        !/^[+]{0,1}(\d+)$/.test(this.SendSettings.overrunMarket)
      ) {
        this.$message({
          message: "防轰炸请填写整数",
          type: "warning",
        });
      } else {
        this.$confirms.confirmation(
          "post",
          "保存修改防轰炸设置？",
          this.API.cpus + "v3/consumer/manager/overrun",
          {
            overrunIndustry: this.SendSettings.overrunIndustry,
            overrunCode: this.SendSettings.overrunCode,
            overrunMarket: this.SendSettings.overrunMarket,
            userId: this.$route.query.id,
          },
          (res) => {}
        );
      }
    },
  },
  created() {
    this.userId = this.$route.query.id;
    if(this.$route.query.type&&this.$route.query.type=='1'){
      this.activeName ='signature'
    }
    this.getBasicData();
    this.getTableData();
  },
  // activated(){
  //     this.getBasicData();
  //     this.getTableData();
  // },
  computed: {
    addAlertTitle() {
      return this.addAlertStatus == 1 ? "添加子用户联系人" : "编辑子用户联系人";
    },
  },
};
</script>
<style scoped>
.red {
  color: red;
}
.OuterFrame {
  padding: 20px 0;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-search-fun {
  display: block;
  padding: 30px 0 20px 0;
  font-weight: bold;
}
.Signature-list-header {
  margin-right: 20px;
  margin-top: 20px;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.mr-3 {
  margin-right: 3px;
}
.addUserDialog .el-steps--simple {
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0;
  padding: 13px 7%;
}
.backIcon {
  cursor: pointer;
}
.dateStyle {
  position: absolute;
  right: 0;
  bottom: 10px;
}
</style>
<style>
.accountinfo-w {
  width: 300px !important;
}
.productInfoTable1 td {
  padding: 2px 0 !important;
}
.el-input-number__decrease,
.el-input-number__increase {
  top: 2px !important;
}
.account-Info-box .el-radio + .el-radio {
  margin-left: 0px !important;
}
/* .account-Info-box {
  background: #fff;
  padding: 0 10px;
  height: 90%;
} */
</style>


