<template>
    <div>
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 月统计分析</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet Statistics-box" style="padding:6px 18px 18px 18px">
            <div class="passageWay-title">
                <!-- 查询框开始 -->
                <el-form :inline="true" :model="passageformInline"  label-width="80px" class="demo-form-inline" ref="passageform">
                    <el-form-item label="时间：" >
                        <el-date-picker
                            v-model="passageformInline.monthTime"
                            type="month"
                            value-format="yyyy-MM"
                            placeholder="选择月">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="用户名称" prop="userName">
                        <el-input v-model="passageformInline.userName" placeholder="请输入用户名" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="公司名称" prop="compName">
                        <el-input v-model="passageformInline.compName" placeholder="请输入用户名" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item  label-width="80px">
                        <el-button type="primary" plain @click="Query()">查询</el-button>
                        <el-button type="primary" plain @click="reSet()">重置</el-button>
                        <el-button type="primary" plain @click="export1()">导出</el-button>
                    </el-form-item>
                </el-form>
                <!-- 查询框结束 -->
                <div class="passage-table">
                    <!-- 表格和分页开始 -->
                    <table-tem :tableDataObj="tableDataObj" ></table-tem>
                        <!--分页-->
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="passageformInline1.currentPage" :page-size="passageformInline1.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
                        </el-pagination>
                    </el-col>
                    <!-- 表格和分页结束 -->
                </div>
            </div>
        </div>    
    </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin'  //时间
import TableTem from '@/components/publicComponents/TableTem' //列表
export default {
    name:'MMSmonthRecord',
    components:{DatePlugin,TableTem},
    data (){
        return{
            name:'MMSmonthRecord',
            flag: '', //选择那一天
            
            passageformInline: {
                userName: '',
                compName:'',
                productId:'2',
                currentPage:1,
                pageSize:10,
                monthTime:new Date().getFullYear() + "-" + (new Date().getMonth()+1 < 10 ? "0" + (new Date().getMonth()+1) : new Date().getMonth()+1),
                startTime:'',
                endTime:'',
            },
            passageformInline1: {
                userName: '',
                compName:'',
                productId:'2',
                currentPage:1,
                pageSize:10,
                monthTime:new Date().getFullYear() + "-" + (new Date().getMonth()+1 < 10 ? "0" + (new Date().getMonth()+1) : new Date().getMonth()+1),
                startTime:'',
                endTime:'',
            },
            tableDataObj: { //列表数据
                total:0,
                loading2:false,
                tableData: [],
                tableLabel:[
                    {prop:"userName",showName:'用户名称',fixed:false},
                    {prop:"compName",showName:'公司名称',fixed:false},
                    {prop:"smsBalance",showName:'彩信剩余条数',fixed:false},
                    {prop:"sendAmount",showName:'发送号码数',fixed:false},
                    {prop:"chargeNum",showName:'发送计费数',fixed:false}
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
           }
       
        }
    },
    methods:{
        //获取列表数据
        getSendReportDate(){
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'v3/consumer/statistics/month/statistics',this.passageformInline1,res=>{
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tableData = res.data.records;
                this.tableDataObj.total = res.data.total;
            })
        },
        //导出
        export1(){
            let aa ={};
            Object.assign(aa,this.passageformInline1);
            aa.isDownload = 1;
            aa.productId='2';
            if(this.tableDataObj.tableData.length == 0){
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            }else{
                this.$File.export(this.API.cpus + 'v3/consumer/statistics/month/statistics/export',aa,'月发送统计.xlsx');
            }
        },
        //选择日趋
        handledatepluginVal: function(val1){
            console.log(val1);
            if(val1){
                this.passageformInline.monthTime = val1;
            }else{
                this.passageformInline.monthTime = '';
            }
        },
        //重置
        reSet(){
            this.$refs.passageform.resetFields();
            this.passageformInline.monthTime = new Date().getFullYear() + "-" + (new Date().getMonth()+1 < 10 ? "0" + (new Date().getMonth()+1) : new Date().getMonth()+1);
            Object.assign(this.passageformInline1,this.passageformInline);
        },
        //查询
        Query(){
            Object.assign(this.passageformInline1,this.passageformInline);
            this.getSendReportDate();
        },
        //改变分页的数量
        handleSizeChange(size) {
            this.passageformInline1.pageSize = size;
        },
        //改变分页的页数
        handleCurrentChange: function(currentPage){
            this.passageformInline1.currentPage = currentPage;
        }
    },
    created(){
        if(this.$store.state.isDateState == 1){
            this.tableDataObj.tableLabel.push(
                
                    {prop:"chargeSuccessNum",showName:'成功计费数',fixed:false},
                    {prop:"chargeFailNum",showName:'失败计费数',fixed:false},
                    {prop:"chargeWaitNum",showName:'待返回计费数',fixed:false},
                    {prop:"successRate",showName:'成功率',fixed:false,
                    formatData:function(val) { return val ? val +' %' :  val;},
                        showColorTag: {
                            color: "red"
                        }
                    },
                    // {prop:"failRate",showName:'失败率',fixed:false,
                    // formatData:function(val) { return val ? val +' %' :  val;}},
                    {prop:"waitRate",showName:'待返回率',fixed:false,
                    formatData:function(val) { return val ? val +' %' :  val;}}
            )
        }
    },
    mounted(){
        this.getSendReportDate();
    },
    // activated(){
    //     this.getSendReportDate();
    // },
    watch:{
        passageformInline1:{
            handler(val){
                this.getSendReportDate();
            },
            deep:true
        }
    }
}
</script>
<style scoped>
.search-date{
    position: relative;
    top:2px;
    margin-bottom: 6px;
    margin-top:10px;
}
.demo-form-inline{
    margin-top:12px;
}
.passage-table{
    margin-bottom: 40px;
}
</style>