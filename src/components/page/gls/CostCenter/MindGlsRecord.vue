<template>
    <div  class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 我的充值</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet Statistics-box">
            <div class="OuterFrame fillet" style="height: 100%;">
                <div>
                    <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline"
                        label-width="100px">
                        <!-- <el-form-item label="用户名："  prop="userName">
                            <el-input v-model="formInline.userName" placeholder="" class="input-w"></el-input>
                        </el-form-item>
                        <el-form-item label="公司名称："  prop="compName">
                            <el-input v-model="formInline.compName" placeholder="" class="input-w"></el-input>
                        </el-form-item> -->
                        <!-- <el-form-item label="充值时间："  prop="time">
                            <el-date-picker class="input-w"
                            v-model="formInline.time"
                            value-format="yyyy-MM-dd"
                            type="daterange"
                            range-separator="-"
                            :clearable = false
                            @change="getTimeOperating"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item> -->
                        <el-form-item label-width="82px" label="开始时间" prop="beginTime">
                            <el-date-picker v-model="formInline.beginTime" :picker-options="pickerOptions"
                                value-format="yyyy-MM-dd 00:00:00" type="date" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label-width="82px" label="结束时间" prop="endTime">
                            <el-date-picker v-model="formInline.endTime" :picker-options="pickerOptions"
                                value-format="yyyy-MM-dd 23:59:59" type="date" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="充值类型" label-width="80px" prop="productId">
                            <el-select v-model="formInline.productId" placeholder="请选择" class="input-w">
                                <el-option v-for="item in balanceData" :key="item.productId" :label="item.name"
                                    :value="item.productId"></el-option>
                                <!-- <el-option label="请选择" value=""></el-option>
                                <el-option label="短信" value="1"></el-option>
                                <el-option label="彩信" value="2"></el-option>
                                <el-option label="视频短信" value="3"></el-option>
                                <el-option label="国际短信" value="4"></el-option>
                                <el-option label="闪验" value="5"></el-option>
                                <el-option label="语音验证码" value="6"></el-option>
                                <el-option label="语音通知" value="7"></el-option> -->
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="boderbottom">
                    <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
                    <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
                </div>
                <div class="sensitive-fun">
                    <!-- <span class="sensitive-list-header">我的充值列表</span> -->
                </div>
                <div class="Mail-table" style="padding-bottom:40px;">
                    <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
                        style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="formInlines.currentPage"
                            :page-size="formInlines.pageSize" :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
                        </el-pagination>
                    </el-col>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
export default {
    name: 'MindGlsRecord',
    components: {
        DatePlugin,
        TableTem
    },
    data() {
        return {
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            balanceData: [],
            // 搜索数据
            formInline: {
                userName: '',
                compName: '',
                beginTime: '',
                endTime: '',
                productId: '',
                time: [],
                pageSize: 10,
                currentPage: 1,
            },
            // 存储搜索数据
            formInlines: {
                userName: '',
                compName: '',
                beginTime: '',
                endTime: '',
                productId: '',
                time: [],
                pageSize: 10,
                currentPage: 1,
            },
            //用户列表数据
            tableDataObj: {
                loading2: false,
                tablecurrent: { //分页参数
                    total: 0,
                },
                tableData: [],
                tableLabel: [
                    {
                        prop: "id",
                        showName: '充值ID',
                        fixed: false
                    },
                    {
                        prop: "orderNumber",
                        showName: '订单号',
                        fixed: false
                    },
                    {
                        prop: "rechargeNum",
                        showName: '充值条数',
                        fixed: false
                    },
                    {
                        prop: "productName",
                        showName: '充值类型',
                        fixed: false
                    },
                    {
                        prop: "rechargeTime",
                        showName: '充值时间',
                        fixed: false,
                        showCondition: {
                            condition: '1'
                        },
                    },
                    {
                        prop: "rechargeNote",
                        showName: '备注',
                        fixed: false
                    }
                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    isExpand: false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '160',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                },
                // tableOptions:[
                //     // {
                //     //     optionName:'查看详情',
                //     //     type:'',
                //     //     size:'mini',
                //     //     optionMethod:'querys',
                //     //     icon:'el-icon-setting'
                //     // },
                //     {
                //         optionName:'导出',
                //         type:'',
                //         size:'mini',
                //         optionMethod:'exportNums',
                //         icon:'el-icon-download'
                //     }
                // ],
            }
        };
    },
    methods: {
        // 发送请求方法
        InquireList() {
            this.tableDataObj.loading2 = true;
            // this.formInlines.userId =this.$store.state.userId
            this.$api.get(this.API.recharge + 'client/recharge/page', this.formInlines, res => {
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tableData = res.data.records
                this.tableDataObj.tablecurrent.total = res.data.total
            })
        },
        handelOptionButton(val) {
            //查看详情跳转
            if (val.methods == 'querys') {
                this.$router.push({ path: '/EmptyNumberDetails', query: { 'num': val.row.batchNo } })
            } else if (val.methods == 'exportNums') {
                //导出 
                if (val.row.fileGroup != '' && val.row.reportStatus == 3) {
                    // 时间过滤
                    Date.prototype.format = function (format) {
                        var args = {
                            "M+": this.getMonth() + 1,
                            "d+": this.getDate(),
                            "h+": this.getHours(),
                            "m+": this.getMinutes(),
                            "s+": this.getSeconds(),
                            "q+": Math.floor((this.getMonth() + 3) / 3),  //quarter
                            "S": this.getMilliseconds()
                        };
                        if (/(y+)/.test(format))
                            format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
                        for (var i in args) {
                            var n = args[i];
                            if (new RegExp("(" + i + ")").test(format))
                                format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length));
                        }
                        return format;
                    };
                    var that = this
                    filedownload()
                    function filedownload() {
                        fetch(that.API.wmcs + 'phonecheck/emptyphonedownload/' + val.row.batchNo, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': "Bearer " + window.Vue.$common.getCookie('ZTGlS_TOKEN')
                            },
                            // body: JSON.stringify({
                            //     batchNo: val.row.batchNo,
                            // })
                        })
                            .then(res => res.blob())
                            .then(data => {
                                let blobUrl = window.URL.createObjectURL(data);
                                download(blobUrl);
                            });
                    }
                    function download(blobUrl) {
                        var a = document.createElement('a');
                        a.style.display = 'none';
                        a.download = "(" + new Date().format("yyyy-MM-dd hh:mm:ss") + ") " + '空号检测.zip'
                        a.href = blobUrl;
                        a.click();
                    }

                    // this.$File.export(this.API.cpus +'mobileCheck/download',{batchNo:val.row.batchNo},`空号检测报表.zip`);
                } else if (val.row.fileGroup == '' && val.row.reportStatus == 3) {
                    this.$message.warning('接口提交的数据不可导出！');
                } else if (val.row.fileGroup != '' && val.row.reportStatus != 3) {
                    this.$message.warning('报表状态未完成不可导出！');
                } else {
                    this.$message.warning('此数据不可导出！');
                }
            }
        },
        // 查询
        ListSearch() {
            Object.assign(this.formInlines, this.formInline)
            this.InquireList();
        },
        // 重置
        Reset(formName) {
            this.$refs[formName].resetFields();
            this.formInline.beginTime = '';
            this.formInline.endTime = '';
            Object.assign(this.formInlines, this.formInline)
        },
        // 操作时间
        getTimeOperating(val) {
            if (val) {
                this.formInline.beginTime = val[0] + " 00:00:00"
                this.formInline.endTime = val[1] + " 23:59:59"
            } else {
                this.formInline.beginTime = ""
                this.formInline.endTime = ""
            }
        },
        // 分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
        },
        handleCurrentChange: function (currentPage) {
            this.formInlines.currentPage = currentPage;
        },
    },
    created() {
        if (JSON.parse(localStorage.getItem('balanceList'))) {
            this.balanceData = JSON.parse(localStorage.getItem('balanceList'))
        }
    },
    // activated(){
    //      this.InquireList()
    // },
    watch: {
        // 监听搜索/分页数据
        formInlines: {
            handler() {
                this.InquireList()
            },
            deep: true,
            immediate: true,
        },
    },
}
</script>
<style scoped>
.Statistics-box {
    padding: 20px;
}

.addC .el-select {
    width: 100%;
}

.addC .el-cascader {
    width: 100%;
}
</style>