<template>
  <div class="bag">
    <div class="Top_title" style="padding: 10px;">
        <span>充值记录</span>
    </div>
     <div class="rechargerecord fillet">
   
        
        <el-form ref="chaxunObj" :model="chaxunObj" label-width="80px">
           <el-form-item label="充值日期" style="display:inline-block" prop="userName">
              <date-plugin class="search-date"  :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal" ></date-plugin>
          </el-form-item>
          <el-form-item label="充值类型" style="display:inline-block">
            <el-select v-model="chaxunObj.productId" placeholder="请选择类型">
              <el-option label="全部" value=""></el-option>
              <el-option label="短信" value="1"></el-option>
              <el-option label="彩信" value="2"></el-option>
              <el-option label="视频短信" value="3"></el-option>
              <el-option label="国际短信" value="4"></el-option>
              <el-option label="闪验" value="5"></el-option>
              <el-option label="语音验证码" value="6"></el-option>
              <el-option label="语音通知" value="7"></el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" plain @click="query()">查 询</el-button>
          <el-button type="primary" plain style="" @click="reset()">重 置</el-button>
        </el-form>
      <div style="padding-bottom:40px;">
        <!-- <table-tem :tableDataObj="tableDataObj" ></table-tem> -->
          <el-table
            v-loading="tableDataObj.loading2 "
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="tableDataObj.tableData"
            style="width: 100%"
           >
            <el-table-column label="日期" width="140">
                <template slot-scope="scope">
                    <span >{{ scope.row.rechargeTime }}</span>
                </template>
            </el-table-column>
            <el-table-column label="类型">
                <template slot-scope="scope">
                    <span v-if="scope.row.productId==1">短信</span>
                    <span v-else-if="scope.row.productId==2">彩信</span>
                    <span v-else-if="scope.row.productId==3">视频短信</span>
                    <span v-else-if="scope.row.productId==4">国际短信</span>
                    <span v-else-if="scope.row.productId==5">闪验</span>
                    <span v-else-if="scope.row.productId==6">语音验证码</span>
                    <span v-else-if="scope.row.productId==7">语音通知</span>
                </template>
            </el-table-column>
            <el-table-column label="充值条数">
                <template slot-scope="scope">
                    <span >{{ scope.row.rechargeNum+" ("+scope.row.unit+")"}}</span>
                </template>
            </el-table-column>
            <el-table-column label="订单号">
                <template slot-scope="scope">
                    <span >{{ scope.row.orderNumber }}</span>
                </template>
            </el-table-column>
            <el-table-column label="备注" >
                <template slot-scope="scope">
                    <span >{{ scope.row.rechargeNote }}</span>
                </template>
            </el-table-column>
        </el-table>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page pageStyle" slot="pagination" >
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="chaxunObj.currentPage"  :page-size='chaxunObj.pageSize' :page-sizes="[10, 20, 50, 100,300]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
            </el-pagination>
        </el-col>
      </div>
    </div>
  </div>
 
</template>

<script>
import TableTem from '../../publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import { mapState, mapMutations,mapActions } from "vuex";
export default {
  name: "RechargeRecord",
  components: {TableTem,DatePlugin},
  computed:{
      ...mapState({  //比如'movies/hotMovies
          roleId:state=>state.userId,
        })
  },
  data() {
    return {
      datePluginValueList: { //日期选择器
          type:"daterange",
          start:"",
          end:'',
          range:'-',
          clearable:true,
          pickerOptions:{
                disabledDate: (time) => {
                    return time.getTime() > Date.now() ;
                }
              },
          datePluginValue: ''
      },
      formInline:{
        currentPage:1,
        pageSize:10,
        beginTime:'',
        endTime:'',
        productId:''
      },
      chaxunObj:{
        currentPage:1,
        pageSize:10,
        beginTime:'',
        endTime:'',
        productId:''
      },
      tableDataObj:{
        total:0,
        tableData: [],
      },
    };
  },
  methods: {
    reset(){//重置   
      this.datePluginValueList.datePluginValue ='';
      this.formInline.beginTime='';
      this.formInline.endTime='';
      this.formInline.productId='';
      this.formInline.pageSize=10;
      this.formInline.currentPage=1;
      Object.assign(this.chaxunObj,this.formInline);
      this.getDate();
    },
    // 查询
    query(){
      Object.assign(this.formInline,this.chaxunObj);
      this.getDate();
    },
    getDate(){
      this.tableDataObj.loading2 = true;
        this.$api.get(this.API.recharge+'client/recharge/page?'+'currentPage='+this.chaxunObj.currentPage+'&pageSize='+this.chaxunObj.pageSize+'&beginTime='+this.chaxunObj.beginTime+'&endTime='+this.chaxunObj.endTime+'&productId='+this.chaxunObj.productId,{},res=>{
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
        })
    },
    handleSizeChange(size) { //分页一页的size
      this.chaxunObj.pageSize = size
      this.getDate();
    },
    handleCurrentChange: function(currentPage){//分页第几页
      this.chaxunObj.currentPage = currentPage;
      this.getDate();
    },
    handledatepluginVal: function(val1,val2){
      if(val1){
          this.chaxunObj.beginTime = val1;
          this.chaxunObj.endTime = val2;
      }else{
        this.chaxunObj.beginTime = '';
          this.chaxunObj.endTime = '';
      }
    },
  },
  created(){
    this.getDate();
  },
  // watch:{
  //   chaxunObj:{
  //     handler(){
  //         this.getDate();
  //     },
  //     deep:true,
  //     immediate:true
  //   }
  // }
}
</script>

<style scoped>
.rechargerecord{
  padding:20px;
}
.statistical-title{
  font-weight: bold;
  padding-bottom: 10px;
}
.detailsList{
  height:42px;
  line-height: 26px;
  padding-left:30px;
  font-size:12px;
}
.detailsList-title{
  display: inline-block;
  width:80px;
}
.detailsList-content{
  display: inline-block;
  width:340px;
  color:#848484;
  padding-left:10px;
  border-bottom: 1px solid #eee;
}
 .look-at-more{
    color:#16a589;
    padding-top: 12px;
}
</style>
<style>
.rechargerecord .el-dialog__footer{
  text-align:center;
}
.el-dialog__title{
  font-size:16px;
}
</style>


