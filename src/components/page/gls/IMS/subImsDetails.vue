<template>
    <div class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 子用户国际短信发送明细</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet Statistics-box" style="padding:6px 18px 18px 18px">
            <div class="passageWay-title">
                <!-- 查询框开始 -->
                <el-form :inline="true" :model="passageformInline" label-width="80px" class="demo-form-inline"
                    ref="passageform">
                    <!-- <el-date-picker
                        v-model="datePluginValue"
                        type="datetimerange"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator="至"
                        @change="timeClick"
                        :picker-options="pickerOptions"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker> -->
                    <el-form-item label="用户名称" prop="clientName">
                        <el-input v-model="passageformInline.clientName" placeholder="请输入用户名"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号" prop="mobile">
                        <el-input v-model="passageformInline.mobile" placeholder="请输入手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="消息ID" prop="msgid">
                        <el-input v-model="passageformInline.msgid" placeholder="请输入ID"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="标题" prop="title">
                        <el-input v-model="passageformInline.title" placeholder="请输入标题"></el-input>
                    </el-form-item> -->
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="passageformInline.smsStatus" placeholder="请选择状态">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="成功" value="1"></el-option>
                            <el-option label="失败" value="2"></el-option>
                            <el-option label="待返回" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开始时间" prop="sendBeginTime">
                        <el-date-picker v-model="passageformInline.sendBeginTime" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss" default-time="00:00:00" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束时间" prop="sendEndTime">
                        <el-date-picker v-model="passageformInline.sendEndTime" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label-width="80px">
                        <el-button type="primary" plain @click="Query()">查询</el-button>
                        <el-button type="primary" plain @click="reSet()">重置</el-button>
                        <!-- <el-button type="primary" plain @click="export1()">导出</el-button> -->
                    </el-form-item>
                </el-form>
                <!-- 查询框结束 -->
                <div class="passage-table">
                    <!-- 表格和分页开始 -->
                    <!-- 表格和分页开始 -->
                    <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable" border :data="tableDataObj.tableData" style="width: 100%">
                        <el-table-column type="selection">
                        </el-table-column>
                        <el-table-column prop="username" label="用户名称" width="120px">
                        </el-table-column>
                        <el-table-column prop="msgid" label="msgId" width="180px">
                        </el-table-column>
                        <el-table-column label="手机号码" width="100px">
                            <template slot-scope="scope">
                                <div style="color: #16A589;cursor: pointer;" @click="tableContent(scope.row, scope.$index)">
                                    <span>{{ scope.row.mobile }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="content" label="短信内容" width="500px">
                        </el-table-column>
                        <el-table-column prop="chargeNum" label="计费数" width="80px">
                        </el-table-column>
                        <el-table-column prop="sendTime" label="发送时间" width="140px">
                        </el-table-column>
                        <el-table-column prop="reportTime" label="回执时间" width="140px">
                        </el-table-column>
                        <el-table-column label="发送状态" width="90px">
                            <template slot-scope="scope">
                                <span v-if="scope.row.status == 1">成功</span>
                                <span v-else-if="scope.row.status == 2">失败</span>
                                <span v-else-if="scope.row.status == 3">待返回</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="originalCode" label="备注">
                        </el-table-column>
                        <!-- <el-table-column
                            prop="ext"
                            label="扩展号"
                            width="90px"
                        >
                        </el-table-column>
                        <el-table-column
                            label="发送通道号"
                            width="85px"
                        >
                        <template slot-scope="scope">
                            <span @click="save(scope.row.channelId)" style="color:rgb(22, 165, 137);cursor: pointer;">{{scope.row.channelId }}</span>  
                        </template>
                        </el-table-column> -->
                        <el-table-column prop="area" label="国家">
                        </el-table-column>
                        <el-table-column prop="price" label="单价" width="90px">
                        </el-table-column>
                        <!-- <el-table-column
                            prop="ip"
                            label="提交Ip"
                            width="110px"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="source"
                            label="来源"
                            width="100px"
                        >
                        </el-table-column> -->
                    </el-table>
                    <!--分页-->
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
                        style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="passageformInline1.currentPage"
                            :page-size="passageformInline1.pageSize" :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
                        </el-pagination>
                    </el-col>
                    <!-- 表格和分页结束 -->
                </div>
            </div>
        </div>
        <!-- 预览手机弹框   -->
        <el-dialog title="预览" :visible.sync="dialogVisible" width="40%">
            <div>
                <div class="send-mobel-box">
                    <img src="../../../../assets/images/phone.png" alt="">
                    <div class="mms-content-exhibition">
                        <el-scrollbar class="sms-content-exhibition">
                            <div style="width: 253px;">
                                <span
                                    style="display: inline-block;padding: 5px;border-radius: 5px;background: #e2e2e2;margin-top: 5px;">{{
                                        title }}</span>
                            </div>
                            <div style="overflow-wrap: break-word;width: 234px;background: #e2e2e2;padding: 10px;border-radius: 5px;margin-top: 5px;"
                                v-for="(item, index) in viewData" :key=index>
                                <img v-if="item.media == 'jpg' || item.media == 'gif'"
                                    :src="API.imgU + item.mediaGroup + '/' + item.mediaPath" style="width: 235px;"
                                    class="avatar video-avatar" ref="avatar">
                                <video v-if="item.type == 'video'" style="width: 235px;"
                                    v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                                    class="avatar video-avatar" controls="controls">
                                </video>
                                <audio v-if="item.type == 'audio'" style="width: 235px;" autoplay="autoplay"
                                    controls="controls" preload="auto"
                                    v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath">
                                </audio>
                                <div style="white-space: pre-line;">
                                    {{ item.txt }}
                                </div>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
                <div style="padding: 10px 60px;">
                    <div v-for="(item, index) in viewData" :key=index style="display: inline-block;margin: 0 15px;">
                        <span v-if="item.media" style="cursor: pointer;color: #16A589;"
                            @click="downloadView(item.mediaPath)">{{ item.media }}素材下载</span>
                    </div>
                </div>
            </div>
        </el-dialog>
        <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
    </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin'  //时间
import TableTem from '@/components/publicComponents/TableTem' //列表
import moment from 'moment'
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../common/bus"
import common from "../../../../assets/js/common";
export default {
    name: 'subImsDetails',
    components: { DatePlugin, TableTem, ResetNumberVue },
    data() {
        return {
            name: 'subImsDetails',
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            // 预览
            viewData: '',
            indexVeew: 1,
            title: '',
            dialogVisible: false,
            passageformInline: {
                clientName: '',
                mobile: '',
                smsStatus: '',
                msgid: '',
                title: '',
                currentPage: 1,
                pageSize: 10,
                sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                sendEndTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            },
            passageformInline1: {
                clientName: '',
                mobile: '',
                smsStatus: '',
                msgid: '',
                title: '',
                currentPage: 1,
                pageSize: 10,
                sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                sendEndTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            },
            tableDataObj: { //列表数据
                total: 0,
                loading2: false,
                tableData: [],
                // tableLabel:[
                //     {prop:"username",showName:'用户名',fixed:false},
                //     {prop:"mobile",showName:'手机号',width:"100",fixed:false},
                //     {prop:"title",showName:'标题',fixed:false,},
                //     {prop:"signature",showName:'签名',fixed:false},
                //     {prop:"msgid",showName:'msgId',fixed:false,width:"180"},
                //     {prop:"status",showName:'状态',width:"70",formatData: function(val) { 
                //         if(val == '1'){
                //             return val='成功'
                //         }else if(val == '2'){
                //             return val='失败'
                //         }else if(val == '3'){
                //             return val='待返回'
                //         }
                //     },fixed:false},
                //     {prop:"sendTime",showName:'发送时间',fixed:false,width:"140"},
                //     {prop:"reportTime",showName:'回执时间',fixed:false,width:"140"},
                //     {prop:"originalCode",showName:'备注',fixed:false},
                // ],
                // tableStyle:{
                //     isSelection:false,//是否复选框
                //     isExpand:false,//是否是折叠的
                //     style: {//表格样式,表格宽度
                //         width:"100%"
                //         },
                //     border:true,//是否边框
                //     stripe:false,//是否有条纹
                // },
            },
            resetVideo: false,
            infoData: {},
        }
    },
    methods: {
        //获取列表数据
        getSendReportDate() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'v3/consumer/manager/ims/sub-accounts/message', this.passageformInline1, res => {
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tableData = res.data.records;
                this.tableDataObj.total = res.data.total;
            })
        },
        // 素材下载------------------------------
        downloadView(val) {
            this.downloadAjax(this.API.imgU + 'group1/' + val)
        },
        ajax(url, callback, options) {
            window.URL = window.URL || window.webkitURL;
            var xhr = new XMLHttpRequest();
            xhr.open('get', url, true);
            if (options.responseType) {
                xhr.responseType = options.responseType;
            }
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    callback(xhr);
                }
            };
            xhr.send();
        },
        downloadAjax(urls) {
            let innerurl = urls;// 文件地址
            var name = urls.replace(/(.*\/)*([^.]+).*/ig, '$2');
            this.ajax(innerurl, function (xhr) {
                let filename = name + '.' + innerurl.replace(/(.*\.)/, '');
                let content = xhr.response;
                let a = document.createElement('a');
                let blob = new Blob([xhr.response]);
                let url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = filename;
                a.click();
                window.URL.revokeObjectURL(url);
            }, {
                responseType: 'blob'
            });
        },
        // 素材下载------------------------------
        // 预览
        View(val) {
            this.indexVeew = 1
            this.viewData = val.contents
            this.title = val.title
            this.dialogVisible = true
        },
        //选择日趋
        handledatepluginVal: function (val1, val2) {
            this.passageformInline.sendBeginTime = val1
            this.passageformInline.sendEndTime = val2
        },
        //重置
        reSet() {
            this.$refs.passageform.resetFields();
            this.passageformInline.sendBeginTime = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss');
            this.passageformInline.sendEndTime = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss');
            this.passageformInline.smsStatus = ""
            this.datePluginValue = '';
            Object.assign(this.passageformInline1, this.passageformInline);
        },
        //查询
        Query() {
            Object.assign(this.passageformInline1, this.passageformInline);
            this.getSendReportDate();
        },
        //改变分页的数量
        handleSizeChange(size) {
            this.passageformInline1.pageSize = size;
        },
        //改变分页的页数
        handleCurrentChange: function (currentPage) {
            this.passageformInline1.currentPage = currentPage;
        },
        // 点击手机号
        tableContent(row, index) {
            this.$api.post(this.API.upms + '/generatekey/decryptMobile', {
                keyId: row.keyId,
                smsInfoId: row.decryptMobile,
                cipherMobile: row.cipherMobile
            }, res => {
                if (res.code == 200) {
                    this.tableDataObj.tableData[index].mobile = res.data;
                } else if (res.code == 4004002) {
                    common.fetchData().then((res) => {
                        if (res.code == 200) {
                            if (res.data.isAdmin == 1) {
                                this.resetVideo = true;
                                this.infoData = res.data;
                            } else {
                                this.$message({
                                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                    type: "warning",
                                });
                            }
                        } else {
                            this.$message({
                                message: res.msg,
                                type: "error",
                            });
                        }

                    })
                } else {
                    this.$message({
                        message: res.msg,
                        type: 'warning'
                    });
                }
                // this.tableDataObj.tableData[index].mobile=res.data
                // this.tableDataObj1.tableData[index].mobile=res.data
            })
        },
        timeClick(val) {
            if (val) {
                this.passageformInline.sendBeginTime = this.moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
                this.passageformInline.sendEndTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
            } else {
                this.passageformInline.sendBeginTime = ''
                this.passageformInline.sendEndTime = ''
            }
        },
    },
    created() {
        bus.$on("closeVideo", (msg) => {
            this.resetVideo = msg;
        });
    },
    mounted() {
        this.getSendReportDate();
    },
    // activated(){
    //     this.getSendReportDate();
    // },
    watch: {
        passageformInline1: {
            handler(val) {
                this.getSendReportDate();
            },
            deep: true
        }
    }
}
</script>
<style scoped>
.search-date {
    position: relative;
    top: 2px;
    margin-bottom: 6px;
    margin-top: 10px;
}

.demo-form-inline {
    margin-top: 12px;
}

.passage-table {
    margin-bottom: 40px;
}

.send-mobel-box {
    width: 300px;
    overflow: hidden;
    position: relative;
    margin-bottom: 35px;
    left: 28%;
}

.send-mobel-box img {
    width: 300px;
}

.mms-content-exhibition {
    position: absolute;
    top: 0;
    width: 300px;
    height: 375px;
    margin: 135px 25px 0px 25px;
    overflow: auto;
    overflow-y: auto;
}
</style>