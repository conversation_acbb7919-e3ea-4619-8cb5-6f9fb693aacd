<template>
    <div class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 子用户短信回复 明细</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <!-- <div class="Top_title">子用户短信发送明细</div> -->
        <div class="fillet Statistics-box" style="padding:6px 18px 18px 18px">
            <div class="passageWay-title">
                <!-- 查询框开始 -->
                <el-form :inline="true" :model="passageformInline" label-width="80px" class="demo-form-inline"
                    ref="passageform">
                    <el-form-item label="用户名称" prop="clientName">
                        <el-input v-model="passageformInline.clientName" placeholder="请输入用户名"></el-input>
                    </el-form-item>
                    <el-form-item label="签名" prop="signature">
                        <el-input v-model="passageformInline.signature" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="开始时间" prop="beginTime">
                        <el-date-picker v-model="passageformInline.beginTime" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss" default-time="00:00:00" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束时间" prop="endTime">
                        <el-date-picker v-model="passageformInline.endTime" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label-width="80px">
                        <el-button type="primary" plain @click="Query()">查询</el-button>
                        <el-button type="primary" plain @click="reSet()">重置</el-button>
                        <!-- <el-button type="primary" plain @click="export1()">导出</el-button> -->
                    </el-form-item>
                </el-form>
                <!-- 查询框结束 -->
                <div class="passage-table">
                    <!-- 表格和分页开始 -->
                    <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable" border :stripe="true" :data="tableDataObj.tableData" style="width: 100%">
                        <!-- <el-table-column type="selection" width="46"></el-table-column> -->
                        <!-- <el-table-column label="签名ID" width="80">
                        <template slot-scope="scope">{{ scope.row.signatureId }}</template>
                    </el-table-column> -->
                        <el-table-column label="用户名称" width='120'>
                            <template slot-scope="scope">
                                <span>{{ scope.row.userName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="手机号码" width='130'>
                            <template slot-scope="scope">
                                <!-- <div class="spanColor" @click="trueFlogShow(scope.row.content)" v-html="scope.row.content"></div> -->
                                <div class="spanColor" v-html="scope.row.mobile"></div>
                            </template>
                        </el-table-column>
                        <el-table-column label="回复内容" width="150">
                            <template slot-scope="scope">
                                <span>{{ scope.row.content }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="回复时间" width="180">
                            <template slot-scope="scope">
                                <span>{{ scope.row.createTime }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="发送内容">
                            <template slot-scope="scope">
                                <!-- <el-tooltip placement="top">
                                   <div class="tooltip" slot="content">11</div>
                                  <span class="span">{{scope.row.smsContent }}</span>
                            </el-tooltip> -->
                                <el-tooltip class="item" effect="dark" placement="top-start">
                                    <div class="tooltip" slot="content">{{ scope.row.smsContent }}</div>
                                    <span class="span">{{ scope.row.smsContent }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="消息ID" width='300'>
                            <template slot-scope="scope">
                                <el-tooltip class="item" effect="dark" placement="top-start">
                                    <div class="tooltip" slot="content">{{ scope.row.msgid }}</div>
                                    <span class="span">{{ scope.row.msgid }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页-->
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
                        style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="passageformInline.currentPage"
                            :page-size="passageformInline.pageSize" :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
                        </el-pagination>
                    </el-col>
                    <!-- 表格和分页结束 -->
                </div>
            </div>
        </div>
        <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
    </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin'  //时间
import TableTem from '@/components/publicComponents/TableTem' //列表
import moment from 'moment'
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../../common/bus"
import common from "../../../../../assets/js/common";
export default {
    name: 'subSmsRecord',
    components: { DatePlugin, TableTem,ResetNumberVue },
    data() {
        return {
            name: 'subSmsRecord',
            flag: '', //选择那一天
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            passageformInline: {
                clientName: '',
                signature: "",
                currentPage: 1,
                pageSize: 10,
                beginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            },
            tableDataObj: { //列表数据
                total: 0,
                loading2: false,
                tableData: [],
            },
            resetVideo: false,
            infoData: {},

        }
    },
    methods: {
        //获取列表数据
        getSendReportDate() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumerpartner/reply/page', this.passageformInline, res => {
                console.log(res, 'res');
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tableData = res.records;
                this.tableDataObj.total = res.total;
            })
        },
        //手机号解密
        handPhone(val, index) {
            console.log(val);
            this.$api.post(this.API.upms + '/generatekey/decryptMobile', {
                keyId: val.keyId,
                smsInfoId: val.smsInfoId,
                cipherMobile: val.cipherMobile
            }, res => {
                if (res.code == 200) {
                    this.tableDataObj.tableData[index].mobile = res.data;
                } else if (res.code == 4004002) {
                    common.fetchData().then((res) => {
                        if (res.code == 200) {
                            if (res.data.isAdmin == 1) {
                                this.resetVideo = true;
                                this.infoData = res.data;
                            } else {
                                this.$message({
                                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                    type: "warning",
                                });
                            }
                        } else {
                            this.$message({
                                message: res.msg,
                                type: "error",
                            });
                        }

                    })
                } else {
                    this.$message({
                        message: res.msg,
                        type: 'warning'
                    });
                }
                // this.tableDataObj.tableData[index].mobile=res.data

            })
        },
        //选择日趋
        handledatepluginVal: function (val1, val2) {
            this.passageformInline.beginTime = val1
            this.passageformInline.endTime = val2
        },
        //重置
        reSet() {
            this.$refs.passageform.resetFields();
            this.passageformInline.beginTime = '';
            this.passageformInline.endTime = '';
            this.datePluginValue = '';

        },
        //查询
        Query() {

            this.getSendReportDate();
        },
        //改变分页的数量
        handleSizeChange(size) {
            this.passageformInline.pageSize = size;
            this.getSendReportDate();
        },
        //改变分页的页数
        handleCurrentChange: function (currentPage) {
            this.passageformInline.currentPage = currentPage;
            this.getSendReportDate();
        },
        timeClick(val) {
            if (val) {
                this.passageformInline.beginTime = this.moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
                this.passageformInline.endTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
            } else {
                this.passageformInline.beginTime = ''
                this.passageformInline.endTime = ''
            }
        },
    },
    created() {
        bus.$on("closeVideo", (msg) => {
            this.resetVideo = msg;
        });
        this.getSendReportDate();
    },
    // activated(){
    //     this.getSendReportDate();
    // },
    // watch:{
    //     passageformInline1:{
    //         handler(val){
    //             this.getSendReportDate();
    //         },
    //         deep:true
    //     }
    // }
}
</script>
<style scoped>
.search-date {
    position: relative;
    top: 2px;
    margin-bottom: 6px;
    margin-top: 10px;
}

.demo-form-inline {
    margin-top: 12px;
}

.passage-table {
    margin-bottom: 40px;
}
</style>