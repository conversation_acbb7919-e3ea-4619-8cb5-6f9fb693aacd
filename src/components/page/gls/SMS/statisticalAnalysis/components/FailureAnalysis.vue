<template>
    <div id="FailureAnalysis">
        <div class="fillet failure-analysis-chart-box">
            <div class="failure-analysis-chart-title">
                <slot name="sele"></slot>
                <el-radio-group v-model="specificTime" size="small" text-color="#fff" fill="#16a589" @change="handleChangeTimeOptions">
                    <el-radio-button label="今天" ></el-radio-button>
                    <el-radio-button label="昨天"></el-radio-button>
                    <el-radio-button label="近7天"></el-radio-button>
                    <el-radio-button label="近30天" class="threeDay"></el-radio-button>
                </el-radio-group>
                <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal" @IsThereValue="IsThereValue"></date-plugin>
            </div>
            <!-- <div class="failure-analysis-title">
                <span>趋势图</span>
                <div class="failure-analysis-select">
                     <el-select v-model="value" placeholder="请选择" style="margin-right:10px;">
                        <el-option v-for="item in options" :key="item.value"  :label="item.label"  :value="item.value">
                        </el-option>
                    </el-select>
                    <span style="font-weight:normal;">总发送量：234345 条</span>
                </div>
            </div> -->
            <bar-graph :bargraphobj='bargraphobj'></bar-graph>
        </div>
        <div class="fillet failure-analysis-chart-box">
            <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem>
        </div>
    </div>
</template>

<script>

import TableTem from '@/components/publicComponents/TableTem'
import BarGraph from '@/components/publicComponents/BarGraph'
import DatePlugin from '@/components/publicComponents/DatePlugin'
export default {
    name: "FailureAnalysis",
    components: {
        TableTem,
        BarGraph,
        DatePlugin
    },
    data () {
        return {
             opts: [{
                value: '选项1',
                label: '黄金糕'
                }, {
                value: '选项2',
                label: '双皮奶'
            }],
            options: [{
                value: '1',
                label: '发送量'
                }, {
                value: '2',
                label: '成功量'
            }],
            value: '',
            va1: '',
            specificTime: '今天', //选择那一天
            datePluginValueList: { //日期选择器
                type:"datetimerange",
                start:"",
                end:'',
                range:'-',
                clearable:false,
                pickerOptions:{
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    }
                },
                defaultTime:['00:00:00', '23:59:59'], //默认起始时刻
                datePluginValue: ''
            },
            bargraphobj:{ //统计图数据
                abscissa:['hh','asdf','dd','cc','ff'],
                ordinate:['12','23','34','56','45'],
                color:['#438074'],
                title:'发送（条）'
            },
            tableDataObj:{ //表格数据
                tableData: [{
                    date: '王二',
                    chongzhi: '18701456123',
                    chongzhij: '<EMAIL>',
                    danjian: 'Y',
                    leix: 'Y',
                    sh:'N'
                    }],
                    tableLabel:[{
                    prop:"date",
                    showName:'姓名',
                    width:'120',
                    fixed:false
                    },{
                    prop:"chongzhi",
                    showName:'手机',
                    fixed:false
                    },{
                    prop:"chongzhij",
                    showName:'邮箱',
                    fixed:false
                    },{
                    prop:"danjian",
                    showName:'余额不足提醒',
                    fixed:false
                    },{
                    prop:"leix",
                    showName:'发送超量提醒',
                    fixed:false
                    },{
                    prop:"sh",
                    showName:'审核通知',
                    fixed:false
                }],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'180',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            }
        }
    },
    methods: {
        handelOptionButton: function(val){
            if(val.methods=='dele'){
                // this.deleRow()
            }
        },
        handledatepluginVal: function(val1,val2){
            // this.datePluginValue = [val1,val2];
            if(val1){
                this.specificTime=''
            }
        },
        IsThereValue: function (val){
            if(!val){
                if(this.specificTime == ''){
                    this.specificTime = '今天'
                }
            }
        },
        handleChangeTimeOptions: function(){
            this.datePluginValueList.datePluginValue = ''
        }
    }
}
</script>

<style scoped>
   .failure-analysis-chart-box{
       margin:10px 0;
       padding:20px 20px 20px 20px;
   }
   .failure-analysis-chart-title{
        display: flex;
   }
   .failure-analysis-chart{
       height:360px;
   }
   .failure-analysis-title{
       padding-top:40px;
       font-weight: bold;
   }
   .look-at-more{
       color:#16a589;
   }
   .failure-analysis-select{
       position: relative;
       margin-top:10px;
   }
   
</style>
<style>
#FailureAnalysis .threeDay .el-radio-button__inner{
    border-right: 0 ;
}
.failure-analysis-chart-box .el-radio-button:last-child .el-radio-button__inner{
    border-radius: 0;
}
.failure-analysis-chart-box .el-range-editor.el-input__inner{
    border-radius: 0px 4px 4px 0;
}
</style>
