<template>
    <div>
        <div class="fillet Statistics-box">
            <div class="OuterFrame fillet" style="height: 100%;">
                <div>
                    <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
                        <el-form-item label="提交时间" label-width="80px" prop="time">
                            <el-date-picker class="input-w"
                            v-model="formInline.time"
                            value-format="yyyy-MM-dd"
                            type="daterange"
                            range-separator="-"
                            :clearable = false
                            @change="getTimeOperating"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="boderbottom">
                    <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
                    <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
                </div>
                <div class="sensitive-fun">
                    <span class="sensitive-list-header">短链统计报表</span>
                </div>
                <div class="Mail-table"  style="padding-bottom:40px;">
                    <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="formInlines.currentPage" :page-size="formInlines.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
                        </el-pagination>
                    </el-col>
                </div>  
            </div>
        </div>
    </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
export default {
    name:'reportForm',
    components:{
        DatePlugin,
        TableTem
    },
    data() {
        return {
            name:'reportForm',
            // 搜索数据
            formInline: {
                beginTime:new Date().toLocaleDateString().split('/').join('-')+' 00:00:00',
                endTime:new Date().toLocaleDateString().split('/').join('-')+" 23:59:59",
                time: [new Date().toLocaleDateString().split('/').join('-'),new Date().toLocaleDateString().split('/').join('-')],
                pageSize:10,
                currentPage:1,
            },
            // 存储搜索数据
            formInlines:{
                beginTime:new Date().toLocaleDateString().split('/').join('-')+' 00:00:00',
                endTime:new Date().toLocaleDateString().split('/').join('-')+" 23:59:59",
                time: [new Date().toLocaleDateString().split('/').join('-'),new Date().toLocaleDateString().split('/').join('-')],
                pageSize:10,
                currentPage:1,
            },
            //用户列表数据
            tableDataObj: {
                loading2:false,
                tablecurrent:{ //分页参数
                    total:0,
                },
                tableData: [],
                tableLabel:[{
                    prop:"shortLinkTime",
                    showName:'发送时间',
                    width:'140',
                    fixed:false
                    },
                    {
                    prop:"shortUrl",
                    showName:'短链接',
                    fixed:false
                    },{
                    prop:"generateCount",
                    showName:'发送号码数',
                    fixed:false
                    },{
                    prop:"receiveCount",
                    showName:'成功号码数',
                    fixed:false
                    },{
                    prop:"openCount",
                    showName:'链接打开次数',
                    fixed:false
                    }
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'120',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
                tableOptions:[
                    {
                        optionName:'查看详情',
                        type:'',
                        size:'mini',
                        optionMethod:'exportNums',
                        icon:''
                    }
                ],
            }
        };
    },
    methods: {
        // 发送请求方法
        InquireList(){
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'shortlink/getdatalist',this.formInlines,res=>{
                this.tableDataObj.loading2=false;
                this.tableDataObj.tableData=res.records
                this.tableDataObj.tablecurrent.total=res.total
            })
        },
        handelOptionButton(val){
            if(val.methods=='exportNums'){
                //查看详情
                this.$router.push({path:'/detailShort?code='+val.row.shortCode})
            }
        },
        // 查询
        ListSearch(){
            Object.assign(this.formInlines,this.formInline)
        },
        // 重置
        Reset(formName){
            this.$refs[formName].resetFields();
            this.formInline.time = [new Date().toLocaleDateString().split('/').join('-'),new Date().toLocaleDateString().split('/').join('-')],
            this.formInline.beginTime = new Date().toLocaleDateString().split('/').join('-')+' 00:00:00';
            this.formInline.endTime = new Date().toLocaleDateString().split('/').join('-')+" 23:59:59";
            Object.assign(this.formInlines,this.formInline)
        },
        // 操作时间
        getTimeOperating(val){
            if(val){
                this.formInline.beginTime=val[0]+" 00:00:00"
                this.formInline.endTime=val[1]+" 23:59:59"
            }else{
                this.formInline.beginTime=""
                this.formInline.endTime=""
            }
        },
        // 分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
        },
        handleCurrentChange: function(currentPage){
            this.formInlines.currentPage = currentPage;
        },
    },
    watch:{
        // 监听搜索/分页数据
        formInlines:{
            handler() {
                this.InquireList()
            },
            deep: true,
            immediate: true,
        },
    },
}
</script>
<style scoped>
.Statistics-box{
    padding:20px;
}
.addC .el-select {
    width: 100%;
}
.addC .el-cascader{
    width: 100%;
}
</style>
