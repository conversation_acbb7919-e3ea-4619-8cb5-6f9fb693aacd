<template>
    <div id="TemplateQuery">
        <div class="fillet  TemplateQuery-chart-box">
            <div class=" TemplateQuery-chart-title">
                <slot name="sele"></slot>
                <el-radio-group v-model="eidtDate.specificTime" size="small" text-color="#fff" fill="#16a589" @change="handleChangeTimeOptions">
                    <el-radio-button label="1">今天</el-radio-button>
                    <el-radio-button label="2">近7天</el-radio-button>
                    <el-radio-button label="3" >近30天</el-radio-button>
                    <el-radio-button label="4" class="threeDay">近一年</el-radio-button>
                </el-radio-group>
                <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal"></date-plugin>
            </div>
            <div class="template-query-tab-type">
                <el-form ref="form" :model="form"  class="query-frame" >
                    <el-form-item label="模板名称" label-width="76px">
                        <el-select v-model="form.templateName" placeholder="请选择">
                            <el-option label="全部" value=""></el-option>
                            <el-option
                            v-for="item in options"
                            :key="item.temId"
                            :label="item.temName"
                            :value="item.temId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item style="margin-left:20px;"> 
                        <el-button type="primary" plain @click="temPlateQu()">查 询</el-button>
                        <el-button type="primary"  @click="temExport()">导出数据</el-button>
                    </el-form-item>  
                </el-form>
                <div style="padding-bottom:40px;">
                    <table-tem :tableDataObj="tableDataObj" ></table-tem>
                    <el-col v-if="paginationShow" :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="1" :page-size="tableDataObj.curPageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow">
                        </el-pagination>
                    </el-col>
                </div>
            </div>
        </div> 
    </div>
</template>
<script>
import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
export default {
    name: "TemplateQuery",
    components: {
        TableTem,
        DatePlugin
    },
    data () {
        return {
            name: "TemplateQuery",
            form:{
                templateName:''
            },
            paginationShow:false,
            datePluginValueList: { //日期选择器
                type:"daterange",
                start:"",
                end:'',
                range:'-',
                clearable:false,
                pickerOptions:{
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: (time) => {
                        if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
                            return false
                        }
                        if (this.pickerMinDate !=='') {
                            const day30 = 30 * 24 * 3600 * 1000
                            let maxTime = this.pickerMinDate + day30
                            if (maxTime >  Date.now() - 86400000) {
                                maxTime =  Date.now() - 86400000
                            }
                            const minTime = this.pickerMinDate - day30
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                        return time.getTime() > Date.now() - 86400000;
                    }
                },
                datePluginValue: ''
            },
            eidtDate:{
                beginTime:'',
                endTime:'',
                specificTime: '1', //选择那一天
                temId:''
            },
            options: [],//查询下拉框的选项列表
            tableDataObj:{ //表格数据
                loading2:false,
                tableData: [],
                currentPage:1,
                curPageSize:10,
                totalRow:0,
                tableLabel:[{
                    prop:"createTime",
                        showName:'统计时间',
                        width:'160',
                        fixed:false
                    },{
                        prop:"temName",
                        showName:'模板名称',
                        fixed:false
                    },{
                        prop:"sendAmount",
                        showName:'发送量',
                        fixed:false
                    }],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'180',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            }
        }
    },
    created(){
        console.log(this.$store.state.isDateState)
        if(this.$store.state.isDateState == 1){
            this.tableDataObj.tableLabel.push({
                prop:"failureAmount",
                showName:'失败',
                fixed:false
            },{
                prop:"successAmount",
                showName:'成功条数',
                fixed:false
            },{
                prop:"successRate",
                showName:'发送成功率',
                fixed:false,
                formatData:function(val) {
                    return val ? val+'%' :  val;
                }
            });
        }
       
    },
    methods: {
        //获取模板名称
        gettmplate () {
            this.$api.get( this.API.cpus + 'statistics/selectTemNameByClient',{},res=>{
                this.options = res.data;
            })
        },
        //发送请求
        sendReport(val){
            let datas = {
                flag:this.eidtDate.specificTime,
                temId:this.form.templateName,
                beginTime:this.eidtDate.beginTime,
                endTime:this.eidtDate.endTime,
                isDownload : 2,
                currentPage:this.tableDataObj.currentPage,
                curPageSize:this.tableDataObj.curPageSize
            }
            this.$api.post( this.API.cpus + 'statistics/templatePage',datas,res=>{
                // if(val === 1){
                    this.tableDataObj.tableData = res.records;
                    this.tableDataObj.totalRow = res.total; 
                    this.tableDataObj.loading2 = false;
                // }else{
                //     this.tableDataObj.tableData = res;
                //     this.tableDataObj.loading2 = false;
                // }
               
            });
        },
        //获取列表数据
        getdate () {
            this.tableDataObj.loading2 = true;
            this.paginationShow = true;
            if(this.eidtDate.specificTime === '1'){//选择时间范围
                this.sendReport()
            }else {//选择其他
                this.sendReport(1);
            }
        },
        //日期
        handledatepluginVal (val1,val2) {
            if(val1){
                this.eidtDate.specificTime='5';
                this.eidtDate.beginTime = val1;
                this.eidtDate.endTime = val2;
            }else{
                this.eidtDate.specificTime='1';
                this.eidtDate.beginTime = '';
                this.eidtDate.endTime = '';
            }
        },
        //查询
        temPlateQu(){
            this.eidtDate.temId = this.form.templateName;
            this.tableDataObj.currentPage = 1;
            this.tableDataObj.curPageSize = 10;
        },
        //导出
        temExport(){
            if(this.tableDataObj.tableData.length == 0){
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            }else{
                let datas = {
                    flag:this.eidtDate.specificTime,
                    temId:this.form.templateName,
                    beginTime:this.eidtDate.beginTime,
                    endTime:this.eidtDate.endTime,
                    isDownload : 1,
                    currentPage:this.tableDataObj.currentPage,
                    curPageSize:this.tableDataObj.curPageSize
                }
                this.$File.export(this.API.cpus +'statistics/templatePage', datas,`模板查询.zip`)
            } 
        },
        //时间段选择（今天，昨天，近一年）
        handleChangeTimeOptions () {
            this.datePluginValueList.datePluginValue = ''
        },
        //获取分页的每页数量
        handleSizeChange (size) {
            this.tableDataObj.curPageSize = size;
            this.getdate();
        },
        //获取分页的第几页
        handleCurrentChange (currentPage) {
            this.tableDataObj.currentPage = currentPage;
            this.getdate();
        },
    },
    mounted(){
        this.gettmplate();
        this.getdate();
    },
    watch:{
        eidtDate:{
            handler(val){
                window.Vue.cancel();
                this.getdate();
                if(val.specificTime == '1' || val.specificTime == '2'){
                    // this.paginationShow = false;
                    this.paginationShow = true;
                }else{
                    this.paginationShow = true;
                }
            },
            deep:true
        },
        //监听时间的变化
        'datePluginValueList.datePluginValue':{
            handler(val){
                if(val != ''){
                     this.getdate();
                }
            }
        },
        templateName:{
            handler(){
                this.getdate();
            },
            deep:true
        }
    }
}
</script>

<style scoped>
    .TemplateQuery-chart-box{
        margin:10px 0;
        padding:20px 20px 20px 20px;
    }
    .TemplateQuery-chart-title{
            display: flex;
    }
    .TemplateQuery-chart{
        height:360px;
    }
    .TemplateQuery-title{
        padding-top:40px;
        font-weight: bold;
    }
    .look-at-more{
        color:#16a589;
    }
    .TemplateQuery-select{
        position: relative;
        margin-top:10px;
    }
    .template-query-tab-type{
        margin-top:20px;
    }
    .query-frame{
       margin-top:35px; 
    }
    .query-frame .el-form-item{
        display: inline-block;
        margin-bottom:12px; 
    }
</style>
<style>
#TemplateQuery .threeDay .el-radio-button__inner{
    border-right: 0 ;
}
.TemplateQuery-chart-box .el-radio-button:last-child .el-radio-button__inner{
    border-radius: 0;
}
.TemplateQuery-chart-box .el-range-editor.el-input__inner{
    border-radius: 0px 4px 4px 0;
}
.TemplateQuery-chart-box .el-table--small td,.TemplateQuery-chart-box .el-table--small th{
    padding:8px 0;
}
</style>
