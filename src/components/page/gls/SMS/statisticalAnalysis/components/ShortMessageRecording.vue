<template>
    <div id="ShortMessageRecording">
        <div class="Top_title">
            <span>发送详情</span>
        </div>
        <div class="fillet  ShortMessageRecording-chart-box">
            <div class=" ShortMessageRecording-chart-title">
                <slot name="sele"></slot>
                <el-radio-group v-model="specificTime" size="small" text-color="#fff" fill="#16a589" @change="handleChangeTimeOptions">
                    <el-radio-button label="1" >今天</el-radio-button>
                    <el-radio-button label="2" class="threeDay">近4天</el-radio-button>
                </el-radio-group>
                <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal"></date-plugin>
            </div>
            <div class="short-message-recording-type">
                <!-- <el-tabs v-model="activeName2" type="card" @tab-click="handleClick">
                    <el-tab-pane label="发送记录" name="first"> -->
                        <el-form ref="SHformList" :model="form" label-width="86px" class="query-frame">
                            <el-form-item label="手机号码" prop="mobile">
                                <el-input v-model="form.mobile" placeholder="请输入手机号" class="input-w"></el-input>
                            </el-form-item>
                            <el-form-item label="发送状态" prop="smsStatus" v-if="this.$store.state.isDateState == 1">
                                <el-select v-model="form.smsStatus" placeholder="发送状态" class="input-w">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="成功" value="1"></el-option>
                                    <el-option label="失败" value="2"></el-option>
                                    <el-option label="待返回" value="3"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="短信签名" prop="signature">
                                <el-input v-model="form.signature"  class="input-w"></el-input>
                            </el-form-item>
                            <el-form-item label="短信内容" prop="content">
                                <el-input v-model="form.content"  class="input-w"></el-input>
                            </el-form-item>
                             <el-form-item label="模板名称" prop="temId">
                                <el-select v-model="form.temId" placeholder="请选择" class="input-w">
                                    <el-option label="全部" value=''></el-option>
                                    <el-option label="自定义内容" value='0'  v-if="this.$store.state.custom == 1"></el-option>
                                    <el-option
                                    v-for="item in templateName"
                                    :key="item.temId"
                                    :label="item.temName"
                                    :value="item.temId">
                                    </el-option>
                                </el-select>
                            </el-form-item>  
                            <el-form-item > 
                                <el-button type="primary" plain @click="querySending()">查 询</el-button>
                                <el-button type="primary" plain @click="resetSending('SHformList')">重 置</el-button>
                                <el-button type="primary"  @click="exportNums()">导出数据</el-button>
                            </el-form-item>
                        </el-form>
                        <div style="padding-bottom:40px;">
                            <table-tem :tableDataObj="tableDataObj"></table-tem>
                            <el-col  :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                                <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="1" :page-size="formData.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow">
                                </el-pagination>
                            </el-col>
                        </div>
                    <!-- </el-tab-pane>
                </el-tabs> -->
            </div>
        </div>
        <el-dialog
            title="批量加入黑名单"
            :visible.sync="AddBlacks"
            width="500px"
            top="30vh"
        >  
            <el-form :model="AddBlackform" ref="AddBlackformRef"  label-width="100px" style="padding: 0px 25px 0px 0px;">
                <el-form-item label="备注" prop="remark" >
                    <el-input type="textarea" maxlength="70" placeholder="请输入备注内容，不超过70个字" v-model="AddBlackform.remark"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right;">
                <span slot="footer" class="dialog-footer">
                    <el-button  type="primary" @click="batchAddblack()">确定</el-button>
                    <el-button @click="AddBlacks = false">取 消</el-button>
                </span>
            </div>
        </el-dialog>
    </div>
</template>

<script>

import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
export default {
    name: "ShortMessageRecording",
    components: {
        TableTem,
        DatePlugin
    },
    data () {
        return {
             name: "ShortMessageRecording",
            //模板名称
            templateName:[],
            //复选框的值
            selectId:'',
            // 单条存值
            AddBlackVal:'',
            // 备注
            AddBlackform:{
                remark:''
            },
            AddBlacks:false,
            AddBlacksStatus:false,
            //发送查询的值
            form:{
                mobile:'',
                smsStatus:'',
                signature:'',
                content:'',
                temId:'',
                sendBeginTime:'',
                sendEndTime:'',
                currentPage:1,
                pageSize:10,
                isDownload : 2
            },
            //复制发送查询的值
            formData:{
                mobile:'',
                smsStatus:'',
                signature:'',
                content:'',
                temId:'',
                sendBeginTime:'',
                sendEndTime:'',
                currentPage:1,
                pageSize:10,
                isDownload : 2
            },
            //回复查询的值
            form1:{
                mobile:'',
                currentPage:1,
                sendBeginTime:'',
                sendEndTime:'',
                pageSize:10,
                isDownload : 2
            },
            //复制回复查询的值
            formData1:{
                mobile:'',
                currentPage:1,
                sendBeginTime:'',
                sendEndTime:'',
                pageSize:10,
                isDownload : 2
            },
            activeName2: 'first',
            tabsactive:0, //获取tabs选项卡的index
            specificTime: '1', //选择那一天
            datePluginValueList: { //日期选择器
                type:"daterange",
                start:"",
                end:'',
                range:'-',
                clearable:false,
                pickerOptions:{
                     onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: (time) => {
                        if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
                            return false
                        }
                        if (this.pickerMinDate !=='') {
                            const day30 = 30 * 24 * 3600 * 1000
                            let maxTime = this.pickerMinDate + day30
                            if (maxTime >  Date.now() - 345600000) {
                                maxTime =  Date.now() - 345600000
                            }
                            const minTime = this.pickerMinDate - day30
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                        return time.getTime() > Date.now() - 345600000;
                    }
                },
                datePluginValue: ''
            },
            tableDataObj:{ //表格数据
                loading2:false,
                totalRow:0,
                tableData: [],
                // tableLabelExpand:[//折叠的列表表头
                //     { prop:"content",showName:'短信内容:'}
                // ],
                tableLabel:[  //列表表头
                    {prop:"mobile",showName:'手机号码',width:'120',fixed:false},
                    { prop:"content",showName:'短信内容'},
                    // {prop:"wordCount",showName:'字数',width:'60',fixed:false},
                    {prop:"chargeNum",showName:'计费条数',width:'80',fixed:false},
                    // {prop:"smsStatus",showName:'短信类型',formatData: function(val) { return val == '2' ? '成功' : '失败' },fixed:false},
                    {prop:"sendTime",showName:'发送时间',width:'150',fixed:false},
                    {prop:"reportTime",showName:'状态上报时间',width:'150',fixed:false},
                    
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'180',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            },
            tableDataObj1:{ //表格数据
                loading2:false,
                totalRow:0,
                tableData: [],
                tableLabel:[  //列表表头
                    {prop:"mobile",showName:'手机号码',width:'120',fixed:false},
                    {prop:"content",showName:'回复内容',fixed:false},
                    {prop:"createTime",showName:'回复时间',width:'150',fixed:false},
                    {prop:"ext",showName:'扩展码',width:'150',fixed:false}
                ],
                tableStyle:{
                    isSelection:true,//是否复选框
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'120',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
                conditionOption:[
                    {
                        contactCondition:'isBlacknumber',//关联的表格属性
                        contactData:'2',//关联的表格属性-值
                        optionName:'加入黑名单',//按钮的显示文字
                        optionMethod:'Addblack',//按钮的方法
                        icon:'el-icon-edit',//按钮图标
                        optionButtonColor:'',//按钮颜色
                    }
                ]
            }
        }
    },
    created(){
        //判断是否开启数据展示
        if(this.$store.state.isDateState == 1){
            this.tableDataObj.tableLabel.push({prop:"smsStatus",showName:'发送状态',formatData: function(val) { 
                        if(val == '1'){
                            return val='成功'
                        }else if(val == '2'){
                            return val='失败'
                        }else{
                            return val='待返回'
                        }
                    },width:'100',fixed:false},
                    {prop:"originalCode",showName:'备注',fixed:false});
        }
    },
    methods: {
        //获取已通过的模板名称
        getTemplateName(){
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate',{
                currentPage:1,
                pageSize:200
            },res=>{
                this.templateName = res.records;
            })
        },
        //发送请求
        sendReport(flag){
            let data = Object.assign({},this.formData);
            data.flag = flag;
            this.$api.post(this.API.cpus + 'statistics/smsPage',data,res=>{
                this.tableDataObj.tableData = res.records;
                this.tableDataObj.totalRow = res.total; 
                this.tableDataObj.loading2 = false;
            })
        },
         //发送请求
        sendReport1(flag){
            let data = Object.assign({},this.formData1);
            data.flag = flag;
            this.$api.post(this.API.cpus + 'statistics/replyPage',data,res=>{
                this.tableDataObj1.tableData = res.records;
                this.tableDataObj1.totalRow = res.total; 
                this.tableDataObj1.loading2 = false;
            })
        },
        //获取发送列表数据
        getdate () {
            this.tableDataObj.loading2 = true;
            if(this.specificTime === ''){//选择时间范围
                this.sendReport('5');
            }else {//选择其他
                this.sendReport(this.specificTime);
            }
        },
        //获取回复列表数据
        getdate1 () {
            this.tableDataObj1.loading2 = true;
            if(this.specificTime === ''){//选择时间范围
                this.sendReport1('5');
            }else {//选择其他
                this.sendReport1(this.specificTime);
            }
        },
        //查询 （发送）
        querySending(){
            Object.assign(this.formData,this.form);
        },
        //重置 （发送）
        resetSending(formName){
            this.$refs[formName].resetFields();
             Object.assign(this.formData,this.form);
        },
        //重置 （回复）
        resetReply(formName){
            this.$refs[formName].resetFields();
             Object.assign(this.formData1,this.form1);
        },
        //查询 （回复）
        queryReply(){
            Object.assign(this.formData1,this.form1);
        },
        //导出（发送）
        exportNums(){
            if(this.tableDataObj.tableData.length == 0){
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            }else{
                let aa = Object.assign({},this.formData);
                if(this.specificTime === ''){//选择时间范围
                    aa.flag = '5';
                }else {//选择其他
                    aa.flag = this.specificTime;
                }
                aa.isDownload = '1';
                this.$File.export(this.API.cpus +'statistics/smsPage', aa,`发送记录.zip`)
            }         
        },
        //导出（回复）
        exportNums1(){
            if(this.tableDataObj1.tableData.length == 0){
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            }else{
                let aa = Object.assign({},this.formData1);
                aa.isDownload = '1';
                if(this.specificTime === ''){//选择时间范围
                    aa.flag = '5';
                }else {//选择其他
                    aa.flag = this.specificTime;
                }
                this.$File.export(this.API.cpus +'statistics/replyPage', aa,`回复记录.zip`)
            } 
        },
        //时间范围选择器
        handledatepluginVal: function(val1,val2){
            if(val1){
                this.formData.sendBeginTime=val1;
                this.formData.sendEndTime=val2;
                this.formData1.sendBeginTime=val1;
                this.formData1.sendEndTime=val2;
                this.form.sendBeginTime=val1;
                this.form.sendEndTime=val2;
                this.form1.sendBeginTime=val1;
                this.form1.sendEndTime=val2;
                this.specificTime='';
            }else{
                this.specificTime = '1';
                this.formData.sendBeginTime='';
                this.formData.sendEndTime='';
                this.formData1.sendBeginTime='';
                this.formData1.sendEndTime='';
                this.form.sendBeginTime='';
                this.form.sendEndTime='';
                this.form1.sendBeginTime='';
                this.form1.sendEndTime='';
            }
        },
        //选择（今日，昨天，近一年时 置空时间范围框的值）
        handleChangeTimeOptions: function(){
            this.datePluginValueList.datePluginValue = '';
            this.formData.sendBeginTime='';
            this.formData.sendEndTime='';
            this.formData1.sendBeginTime='';
            this.formData1.sendEndTime='';
            this.form.sendBeginTime='';
            this.form.sendEndTime='';
            this.form1.sendBeginTime='';
            this.form1.sendEndTime='';
        },
        //获取tabs 选项卡的index
        handleClick(tab) {
            this.tabsactive = tab.index;
        },
        //获取分页的每页数量
        handleSizeChange (size) {
            this.formData.pageSize = size;
        },
        //获取分页的第几页
        handleCurrentChange (currentPage) {
            this.formData.currentPage = currentPage;
        },
         //获取分页的每页数量
        handleSizeChange1 (size) {
            this.formData1.pageSize = size;
            this.getdate1();
        },
        //获取分页的第几页
        handleCurrentChange1 (currentPage) {
            this.formData1.currentPage = currentPage;
            this.getdate1();
        },
        // 点击批量加入
        batchAdd(){
            if(this.selectId){
                this.AddBlacks=true
            }else{
                this.$message({
                    type: 'error',
                    duration:'2000',
                    message:"请勾选需要批量的内容"
                });
            }
        },
        //列表复选框的值
        handelSelection(val){
            let selectId = [];
            for(let i=0;i<val.length;i++){
                selectId.push(val[i].smsReplyId)
            }
            this.selectId = selectId.join(); //批量操作选中id
        },
        // 单条加入黑名单
        handelOptionButton: function(val){
            if(val.methods=='Addblack'){
                this.AddBlacks=true
                this.AddBlacksStatus=true
                this.AddBlackVal=val.row.smsReplyId
            }
        },
        //批量加入黑名单
        batchAddblack(){
            this.$confirms.confirmation('post','确定加入黑名单?',this.API.cpus+'consumerclientblacknumber/blacklistBatch',{idStr:this.AddBlacksStatus==false?this.selectId:this.AddBlackVal,remark:this.AddBlackform.remark},res =>{
            this.AddBlacks=false    
            this.getdate1(); 
            })
        }
    },
    mounted(){
        this.getTemplateName();
    },
    watch:{
        //监听查询框的值是否改变
        formData:{
            handler(val){
                if(this.tabsactive == 0){
                    this.getdate();
                }
            },
            deep:true
        },
        formData1:{
            handler(){
                if(this.tabsactive == 1){
                    this.getdate1();
                }
            },
            deep:true
        },
        //监听是否切换了发送记录和回复记录
        tabsactive:{
            handler(val){
                if(val == 0){ //发送记录
                    this.getdate();
                }else{ //回复记录
                    this.getdate1(); 
                }
            },
            deep:true,
            immediate:true
        },
        //监听(今天，昨天，近一年)的改变
        specificTime:{
            handler(val){
                window.Vue.cancel();
                if(this.tabsactive == 0){
                    this.getdate();
                }else{
                    this.getdate1();
                }
            },
            deep:true,
            immediate:true
        },
        AddBlacks(val){
            if(val==false){
                this.AddBlacksStatus=false
                this.$refs.AddBlackformRef.resetFields() 
            }
        }
    }
}
</script>

<style scoped>
    .ShortMessageRecording-chart-box{
        margin:10px 0;
        padding:20px 20px 20px 20px;
    }
    .ShortMessageRecording-chart-title{
        display: flex;
    }
    .ShortMessageRecording-chart{
        height:360px;
    }
    .ShortMessageRecording-title{
        padding-top:40px;
        font-weight: bold;
    }
    .look-at-more{
        color:#16a589;
    }
    .ShortMessageRecording-select{
        position: relative;
        margin-top:10px;
    }
    .short-message-recording-type{
        margin-top:40px;
    }
    .query-frame{
       margin-top:20px; 
    }
    .query-frame .el-form-item{
        display: inline-block;
        margin-bottom:12px; 
    }
</style>
<style>
#ShortMessageRecording .threeDay .el-radio-button__inner{
    border-right: 0 ;
}
.ShortMessageRecording-chart-box .el-radio-button:last-child .el-radio-button__inner{
    border-radius: 0;
}
.ShortMessageRecording-chart-box .el-range-editor.el-input__inner{
    border-radius: 0px 4px 4px 0;
}
.ShortMessageRecording-chart-box .el-table--small td,.ShortMessageRecording-chart-box .el-table--small th{
    padding-top:8px;
    padding-bottom:8px;
}
</style>
