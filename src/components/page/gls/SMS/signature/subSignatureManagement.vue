<template>
    <div class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 子用户签名管理</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet Signature-box">
            <div>
                <el-form :model="tableDataObj.tablecurrent" :inline="true" ref="tablecurrent" class="demo-tablecurrent">
                    <el-form-item label="用户名称" prop="consumerName">
                        <el-input v-model="tableDataObj.tablecurrent.consumerName"></el-input>
                    </el-form-item>
                    <el-form-item label="签名" prop="signature">
                        <el-input v-model="tableDataObj.tablecurrent.signature"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="创建人" prop="createName">
                        <el-input v-model="tableDataObj.tablecurrent.createName"></el-input>
                    </el-form-item> -->
                    <el-form-item label="审核状态" prop="auditStatus">
                        <el-select v-model="tableDataObj.tablecurrent.auditStatus" placeholder="请选择">
                            <el-option label="编辑中" value="0"></el-option>
                            <el-option label="待审核" value="1"></el-option>
                            <el-option label="审核通过" value="2"></el-option>
                            <el-option label="审核不通过" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否已完善实名信息" prop="isComplete">
                        <el-select v-model="tableDataObj.tablecurrent.isComplete" placeholder="请选择">
                            <el-option label="已完善" :value="true"></el-option>
                            <el-option label="未完善" :value="false"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="submitForm('tablecurrent')">查询</el-button>
                        <el-button type="primary" @click="resetForm('tablecurrent')">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!-- <div class="Signature-search-fun">
                <el-button type="primary" style="margin-bottom: 10px" @click="establishSig()">创建签名</el-button>
            </div> -->
            <div class="Mail-table">
                <!-- 表格和分页开始 -->
                <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
                    element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
                    ref="multipleTable" border :data="tableDataObj.tableData" style="width: 100%">
                    <el-table-column prop="signatureId" label="ID" width="90"></el-table-column>
                    <el-table-column label="用户名称">
                        <template slot-scope="scope">
                            <span>{{ scope.row.consumerName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="签名">
                        <template slot-scope="scope">
                            <span>{{ scope.row.signature }}</span>
                            <span v-if="scope.row.auditReason == null"></span>
                            <span v-else-if="scope.row.auditReason == ''"></span>
                            <span v-else-if="scope.row.auditReason == '审核通过'"></span>
                            <span v-else-if="scope.row.auditStatus == '3'" style="color: #f56c6c">( 驳回原因：{{
                                scope.row.auditReason }}
                                )</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="短信示例">
                        <template slot-scope="scope">
                            <Tooltip v-if="scope.row.contentExample" :content="scope.row.contentExample"
                                className="wrapper-text" effect="light">
                            </Tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column label="审核状态" width="120">
                        <template slot-scope="scope">
                            <el-tag effect="light" type="info" v-if="scope.row.auditStatus == '0'">编辑中</el-tag>
                            <el-tag effect="light" type="warning" v-else-if="scope.row.auditStatus == '1'">待审核</el-tag>
                            <el-tag effect="light" type="success" v-else-if="scope.row.auditStatus == '2'">审核通过</el-tag>
                            <el-tag effect="light" type="danger" v-else-if="scope.row.auditStatus == '3'">审核不通过</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否已完善实名信息" width="140">
                        <template slot-scope="scope">
                            <el-tag effect="light" type="success" v-if="scope.row.reportId">已完善</el-tag>
                            <el-tag effect="light" type="warning" v-else>未完善</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="移动" width="140">
                        <template slot="header" slot-scope="scope">
                            <i class="iconfont icon-yidong" style="color: #409eff; font-size: 20px; margin-right: 5px"></i>
                            移动
                        </template>
                        <template slot-scope="scope">
                            <!-- 根据 ydAvalibleType 判断是否有实名类型 -->
                            <div v-if="scope.row.ydAvalibleType && scope.row.ydAvalibleType.trim()" class="status-tags-container">
                                <el-tag 
                                    v-for="(tag, index) in scope.row.ydAvalibleType.split(',')" 
                                    :key="index"
                                    size="small" 
                                    type="success"
                                    class="status-tag success fixed-width">
                                    {{ tag.trim() }}
                                </el-tag>
                            </div>
                            <!-- 无 AvalibleType 情况下根据 reportId 判断 -->
                            <div v-else class="single-operator-status">
                                <el-tooltip :content="scope.row.reportId ? '报备中' : '未实名'" placement="top" effect="dark">
                                    <el-tag size="small" :type="scope.row.reportId ? 'warning' : 'info'" class="status-tag">
                                        {{ scope.row.reportId ? '报备中' : '未实名' }}
                                    </el-tag>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="联通" width="140">
                        <template slot="header" slot-scope="scope">
                            <i class="iconfont icon-liantong" style="color: #f56c6c; font-size: 20px; margin-right: 5px"></i>
                            联通
                        </template>
                        <template slot-scope="scope">
                            <!-- 根据 ltAvalibleType 判断是否有实名类型 -->
                            <div v-if="scope.row.ltAvalibleType && scope.row.ltAvalibleType.trim()" class="status-tags-container">
                                <el-tag 
                                    v-for="(tag, index) in scope.row.ltAvalibleType.split(',')" 
                                    :key="index"
                                    size="small" 
                                    type="success"
                                    class="status-tag success fixed-width">
                                    {{ tag.trim() }}
                                </el-tag>
                            </div>
                            <!-- 无 AvalibleType 情况下根据 reportId 判断 -->
                            <div v-else class="single-operator-status">
                                <el-tooltip :content="scope.row.reportId ? '报备中' : '未实名'" placement="top" effect="dark">
                                    <el-tag size="small" :type="scope.row.reportId ? 'warning' : 'info'" class="status-tag">
                                        {{ scope.row.reportId ? '报备中' : '未实名' }}
                                    </el-tag>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="电信" width="140">
                        <template slot="header" slot-scope="scope">
                            <i class="iconfont icon-dianxin" style="color: #409eff; font-size: 20px; margin-right: 5px"></i>
                            电信
                        </template>
                        <template slot-scope="scope">
                            <!-- 根据 dxAvalibleType 判断是否有实名类型 -->
                            <div v-if="scope.row.dxAvalibleType && scope.row.dxAvalibleType.trim()" class="status-tags-container">
                                <el-tag 
                                    v-for="(tag, index) in scope.row.dxAvalibleType.split(',')" 
                                    :key="index"
                                    size="small" 
                                    type="success"
                                    class="status-tag success fixed-width">
                                    {{ tag.trim() }}
                                </el-tag>
                            </div>
                            <!-- 无 AvalibleType 情况下根据 reportId 判断 -->
                            <div v-else class="single-operator-status">
                                <el-tooltip :content="scope.row.reportId ? '报备中' : '未实名'" placement="top" effect="dark">
                                    <el-tag size="small" :type="scope.row.reportId ? 'warning' : 'info'" class="status-tag">
                                        {{ scope.row.reportId ? '报备中' : '未实名' }}
                                    </el-tag>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="实名状态原因" width="140">
                        <template slot-scope="scope">
                            <div
                                v-if="scope.row.ydReason && (scope.row.auditStatus == '2' || scope.row.auditStatus == '3')">
                                {{ scope.row.ydReason }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="申请时间" width="180">
                        <template slot-scope="scope">{{
                            scope.row.createTime | fmtDate
                        }}</template>
                    </el-table-column>
                    <el-table-column label="首次通过时间" width="180">
                        <template slot-scope="scope">
                            <div v-if="scope.row.firstAuditTime">
                                {{
                                    scope.row.firstAuditTime | fmtDate
                                }}
                            </div>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="创建人" width="180">
                        <template slot-scope="scope">
                            {{ scope.row.createName }}
                        </template>
                    </el-table-column> -->
                    <el-table-column label="操作" width="180">
                        <template slot-scope="scope">
                            <el-button v-permission type="text" style="color: #E6A23C"
                             v-if="scope.row.auditStatus == '0' || scope.row.auditStatus == '3'"
                              @click="details(scope.$index, scope.row)">
                              <i class="el-icon-edit"></i>
                              &nbsp;编辑
                            </el-button>
                            <el-button v-permission v-if="scope.row.auditStatus == '2'" type="text"
                                @click="getRealNameInfo(scope.row)">
                                <i class="el-icon-success"></i>
                                &nbsp;实名信息更改
                            </el-button>
                            <!-- <el-button type="text" @click="editContentExample(scope.row)"><i class="el-icon-s-tools"></i>&nbsp;短信示例管理</el-button> -->
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页-->
                <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page pageStyle" slot="pagination">
                    <el-pagination class="page_bottom" @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" :current-page="tableDataObj.tablecurrent.currentPage"
                        :page-size="tableDataObj.tablecurrent.pageSize" :page-sizes="[10, 20, 50, 100, 300]"
                        layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
                    </el-pagination>
                </el-col>
                <!-- 表格和分页结束 -->
            </div>
            <!-- 实名信息弹框 -->
            <RealNameDialog :visible.sync="realNameDialogVisible" :initial-form-data="realNameInfo"
                :show-signature-types="true" :show-file-upload="true" :show-content-example="true"
                :upload-url="this.API.cpus + 'v3/file/upload'" :headers="header" @submit="submitFormRealNameInfo"
                @cancel="handleCancel" @close="handelClose" @upload-success="handleSuccess"
                @file-remove="handleRemove1" />
        </div>
    </div>
</template>

<script>
import TableTem from "@/components/publicComponents/TableTem";
import Tooltip from "@/components/publicComponents/tooltip";
import { formatDate } from "@/assets/js/date.js";
import RealNameDialog from '@/components/common/RealNameDialog.vue';
export default {
    name: "subSignatureManagement",
    components: { TableTem, Tooltip, RealNameDialog },
    data() {
        return {
            name: "subSignatureManagement",
            header: {},
            userId: null,
            fileList: [],
            flieULR: [],
            copyUrl: "",
            copyUrlList: [],
            dialogVisible: false,
            dialogImageUrl: "",
            tableDataObj: {
                //列表数据
                loading2: false,
                tablecurrent: {
                    //分页参数
                    consumerName: "",
                    signature: "",
                    auditStatus: "",
                    createName: "",
                    isComplete: null,
                    currentPage: 1,
                    pageSize: 10,
                    // userId: ""
                },
                total: 0,
                tableData: [],
            },
            realNameDialogVisible: false, //实名信息弹框显示隐藏
            realNameInfo: {
                companyName: "",//公司名称
                creditCode: "",//统一社会信用代码
                legalPerson: "",//法人姓名
                principalIdCard: "",//负责人身份证号
                principalName: "",//负责人姓名
                principalMobile: "",//负责人手机号
                signatureId: "",//签名id
                imgUrl: "",
                signatureType: "",
                signatureSubType: 0,
                userId: ""
            }, //实名信息
            formRules: {
                companyName: [
                    { required: true, message: '请输入企业名称', trigger: 'change' },
                ],
                creditCode: [
                    { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
                ],
                legalPerson: [
                    { required: true, message: '请输入法人姓名', trigger: 'change' },
                ],
                principalIdCard: [
                    { required: true, message: '请输入负责人身份证号', trigger: 'change' },
                ],
                principalName: [
                    { required: true, message: '请输入负责人姓名', trigger: 'change' },
                ],
                principalMobile: [
                    { required: true, message: '请输入负责人手机号', trigger: 'change' },
                ],
                imgUrl: [
                    { required: true, message: '请上传文件', trigger: 'change' },
                ],
                signatureType: [
                    { required: true, message: '请选择签名来源', trigger: 'change' },
                ],
                signatureSubType: [
                    { required: true, message: '请选择签名类型', trigger: 'change' },
                ],
            },
        };
    },
    filters: {
        fmtDate(val) {
            if (val) {
                return formatDate(new Date(val), "yyyy-MM-dd hh:mm:ss");
            }
        },
    },
    methods: {
        // 获取标签类型
        getTagType(content) {
            const trimmed = content.trim();
            if (trimmed === '验证码') {
                return 'success';
            } else if (trimmed === '行业') {
                return 'success';
            } else if (trimmed === '通知') {
                return 'success';
            }
            return 'success'; // 默认蓝色
        },

        handelCheck(realname) {
            if (realname && realname.id) {
                this.$router.push({
                    path: '/realNameDetail',
                    query: { id: realname.id }
                });
            }
        },
        /* --------------- 列表展示 ------------------*/
        gettableData() {
            //获取列表数据
            this.tableDataObj.loading2 = true;
            this.$api.post(
                this.API.cpus + "v3/consumer/manager/user/signaturePage",
                this.tableDataObj.tablecurrent,
                (res) => {
                    this.tableDataObj.tableData = res.records;
                    this.tableDataObj.total = res.total;
                    this.tableDataObj.loading2 = false;
                }
            );
        },
        submitForm() {
            this.tableDataObj.tablecurrent.currentPage = 1;
            this.gettableData();
        },
        //重置
        resetForm() {
            this.$refs.tablecurrent.resetFields();
            this.tableDataObj.tablecurrent.currentPage = 1;
            this.tableDataObj.tablecurrent.pageSize = 10;
            this.tableDataObj.tablecurrent.signature = "";
            this.tableDataObj.tablecurrent.auditStatus = "";
            this.tableDataObj.tablecurrent.isComplete = null;
            this.gettableData();
        },
        handleSizeChange(size) {
            //分页一页的size
            this.tableDataObj.tablecurrent.pageSize = size;
            this.gettableData();
        },
        handleCurrentChange: function (currentPage) {
            //分页第几页
            this.tableDataObj.tablecurrent.currentPage = currentPage;
            this.gettableData();
        },
        /* --------------- 列表展示 -----------------------*/

        /* ---- 列表展示操作功能：编辑、删除 、创建（新增）-------*/
        // establishSig() {
        //     //创建签名功能
        //     this.$router.push({
        //         path: "/userSignature",
        //         query: { id: this.userId }
        //     });
        // },
        //编辑
        details(index, val) {
            this.$router.push({
                path: "/userSignature",
                query: {
                    id: val.userId,
                    d: val.signatureId
                }
            });
        },
        //删除
        // dele(index, val) {
        //     this.$confirms.confirmation(
        //         "delete",
        //         "此操作将永久删除该数据, 是否继续？",
        //         this.API.cpus + "v3/consumer/manager/user/signature/delete?signatureId=" + val.signatureId + "&userId=" + this.userId,
        //         {},
        //         (res) => {
        //             this.gettableData();
        //         }
        //     );
        // },
        editContentExample(row){
            this.$router.push({
                path: '/contentExample',
                query: {
                    signature: row.signature,
                    userId: row.userId
                }
            })
        },
        getRealNameInfo(row) {
            this.realNameDialogVisible = true; //打开实名信息弹出框
            this.realNameInfo.signatureId = row.signatureId;
            this.realNameInfo.userId = row.userId;

            this.$api.get(this.API.cpus + 'signature/findModel/realName?signatureId=' + row.signatureId + '&userId=' + row.userId, {}, res => {
                if (res.code == 200) {
                    this.realNameInfo.companyName = res.data.companyName;
                    this.realNameInfo.creditCode = res.data.creditCode;
                    this.realNameInfo.legalPerson = res.data.legalPerson;
                    this.realNameInfo.principalIdCard = res.data.principalIdCard;
                    this.realNameInfo.principalName = res.data.principalName;
                    this.realNameInfo.principalMobile = res.data.principalMobile;
                    this.realNameInfo.signatureType = res.data.signatureType;
                    this.realNameInfo.signatureSubType = res.data.signatureSubType;
                    if (res.data.imgUrl) {
                        this.realNameInfo.imgUrl = res.data.imgUrl;
                        this.copyUrl = res.data.imgUrl;
                        this.flieULR = res.data.imgUrl.split(",").filter(item => item.trim() !== '');
                        this.fileList = this.flieULR.map((item) => {
                            return {
                                name: item,
                                url: this.API.imgU + item,
                            };
                        });
                        let imgUrlList = JSON.stringify(this.fileList)
                        this.copyUrlList = JSON.parse(imgUrlList)
                    } else {
                        this.fileList = [];
                    }
                }
            })
        },
        submitFormRealNameInfo(formData) {
            this.$confirms.confirmation(
                "post",
                "确认修改实名信息？",
                this.API.cpus + "signature/realName/edit",
                formData,
                res => {
                    if (res.code == 200) {
                        this.realNameDialogVisible = false; //关闭实名信息弹出框
                        this.gettableData();
                    }
                }
            )
        },
        handelClose() {
            // this.realNameDialogVisible = false; //关闭实名信息弹出框
        },
        handleCancel() {
            this.$message({
                message: '已取消操作',
                type: 'info'
            });
        },
        handleSuccess(res) {
            if (res.code == 200) {
                this.flieULR.push(res.data.fullpath);
                this.realNameInfo.imgUrl = this.flieULR.join(",");
            } else {
                this.$message({
                    message: res.msg,
                    type: "error",
                });
            }
        },
        handleRemove1(file, fileList) {
            if (file.response) {
                this.flieULR.splice(this.flieULR.indexOf(file.response.data.fullpath), 1);
                this.realNameInfo.imgUrl = this.flieULR.join(",");
            } else {
                this.flieULR.splice(this.flieULR.indexOf(file.name), 1);
                this.realNameInfo.imgUrl = this.flieULR.join(",");
            }
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url
            this.dialogVisible = true
        }
    },
    watch: {
        realNameDialogVisible: function (val) {
            if (!val) {
                this.realNameInfo.companyName = "";
                this.realNameInfo.creditCode = "";
                this.realNameInfo.legalPerson = "";
                this.realNameInfo.principalIdCard = "";
                this.realNameInfo.principalName = "";
                this.realNameInfo.signatureId = "";
                this.flieULR = [];
                this.fileList = [];
                this.copyUrl = "";
                this.copyUrlList = [];
                this.realNameInfo.imgUrl = "";
                this.realNameInfo.signatureType = "";
                this.realNameInfo.signatureSubType = "";
            }
        },
    },
    created() {
        // 从路由参数中获取userId
        this.gettableData();
    },
    mounted() {
        this.header = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
    }
};
</script>

<style scoped>
.Signature-box {
    padding: 20px;
}

.Signature-matter {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px 14px;
    border-radius: 5px;
    font-size: 12px;
}

.Signature-matter>p {
    padding: 5px 0px;
}

.Signature-set {
    color: #0066cc;
}

.Signature-creat {
    margin-top: 20px;
}

.Signature-contact {
    color: #0066ff;
    font-size: 12px;
    cursor: pointer;
}

.Signature-list-header {
    display: inline-block;
    padding: 30px 0 14px 0;
    font-weight: bold;
}

.Mail-table {
    padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
    margin-left: 0px;
}

.sig-type .el-radio {
    display: block;
    white-space: normal;
    padding-bottom: 16px;
}

.sig-type .el-radio-group {
    padding-top: 8px;
}

.sig-type-title-tips {
    font-weight: bold;
}

.input-w {
    width: 400px;
}

/* 运营商状态样式 */
.single-operator-status {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 4px 2px;
    min-height: 40px;
}

.status-tags-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
    width: 100%;
}

.status-tag {
    font-size: 12px !important;
    padding: 4px 8px !important;
    height: auto !important;
    line-height: 1.3 !important;
    border-radius: 4px !important;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0 !important;
    border: 1px solid transparent;
}

.status-tag.fixed-width {
    width: 64px !important;
    min-width: 64px !important;
    max-width: 64px !important;
    padding: 4px 6px !important;
}

.status-tag.success {
    font-weight: 500;
}

.status-tag:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
<style>
.el-table--small th {
    background: #f5f5f5;
}
</style>
