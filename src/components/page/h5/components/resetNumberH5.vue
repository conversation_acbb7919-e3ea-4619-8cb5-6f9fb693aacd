<template>
    <div class="reset_container">
        <van-sticky>
            <van-nav-bar title="重置解密次数" left-text="返回" left-arrow @click-left="clickBack"></van-nav-bar>
        </van-sticky>
        <!-- <h3 style="margin-bottom: 16px;">重置解密次数</h3> -->
        <!-- <van-cell-group style="margin-top: 30px;">
            <van-field v-model="code" label="验证码" placeholder="请输入验证码" />
        </van-cell-group>
        <div style="text-align: center;">
            <button @click="resetTimes">重置</button>
        </div> -->

        <div class="Login-c-p-getPhone">
            <p>您今日查看明码号码次数已达上限，为保护客户信息安全，</p>
            <p>验证码将会发送至管理员手机号：{{ loginInfo.mobile }}，请注意查收！</p>
            <!-- <van-cell-group style="margin-top: 30px;">
                <van-field v-model="code" label="验证码" placeholder="请输入验证码">
                    <template #button>
                        <van-button size="small" type="primary">发送验证码</van-button>
                    </template>
                </van-field>
            </van-cell-group> -->
        </div>
        <div style="display: flex; padding: 0 20px; box-sizing: border-box; margin-top: 20px;">
            <el-input v-model="code" style="width:240px;"></el-input>
            <el-button type="primary" plain style="flex: 1; padding:9px 0px; margin-left: 10px;" @click.prevent="send" v-if="nmb == 120">获取验证码</el-button>
            <el-button type="primary" plain style="flex: 1; padding:9px 0px; margin-left: 10px;" disabled v-else>重新获取({{ nmb }})</el-button>
        </div>

        <div style="text-align: left; padding: 0 20px; box-sizing: border-box; margin-top: 20px;">
            <el-button style="width: 100px;" type="primary" @click="submitForm" :loading="loading">重置</el-button>
        </div>
        
    </div>
</template>

<script>
export default {
    data() {
        return {
            code: "",
            nmb: 120,
            loginInfo: {
                isAdmin: null,
                consumerName: "",
                mobile: "",
                remark: "",
                id: ""
            },
            timer: null,
            loading: false
        };
    },
    methods: {
        // 返回上一页
        clickBack() {
            this.$router.back();
        },
        send() {
            this.$api.get(this.API.cpus + "userLoginAdmin/sendVerificationCode?flag=1&phoneId=" + this.loginInfo.id, {}, res => {
                if (res.code == 200) {
                    this.$toast('验证码已发送至手机！')
                    --this.nmb;
                    this.timer = setInterval(res => {
                        --this.nmb;
                        if (this.nmb < 1) {
                            this.nmb = 120;
                            clearInterval(this.timer);
                        };
                    }, 1000);
                } else {
                    this.$toast(res.msg)
                }

            })
        },
        // 重置解密次数
        submitForm() {
            this.loading = true
            let reg = /^\d{6}$/
            if (reg.test(this.code)) {
                this.$api.post(
                    this.API.cpus + "userLoginAdmin/phoneDecryptUnfreeze",
                    {
                        flag:"1",
                        verifyCode: this.code
                    },
                    (res) => {
                        this.loading = false
                        if (res.code == 200) {
                            this.$toast('解密次数已重置成功！')
                            this.$router.back()
                        }else{
                            this.$toast(res.msg)
                        }
                    }
                );
            } else {
                this.$toast('请输入有效验证码！')
            }
        },
    },
    created() {
        if (this.$router.currentRoute.query.data) {
            this.loginInfo = this.$router.currentRoute.query.data
        }
    }
};
</script>

<style lang="less" scoped>
.reset_container {
    width: 100%;
    height: 100%;
    // padding: 30px;
    box-sizing: border-box;
    border: none;
}
.Login-c-p-getPhone {
    margin-top: 20px;
    color: #909399;
    font-size: 12px;
    margin-left: 16px;
    margin-bottom: 10px;
    word-break: break-all;
}
// button,
// button:focus {
//     margin-top: 26px;
//     font-size: 17px;
//     padding: 10px 25px;
//     border-radius: 0.7rem;
//     background-image: linear-gradient(#b1b1b1, #e1e1e1);
//     border: 2px solid #b1b1b1;
//     border-bottom: 5px solid #b1b1b1;
//     box-shadow: 0px 1px 6px 0px #b1b1b1;
//     transform: translate(0, -3px);
//     cursor: pointer;
//     transition: 0.2s;
//     transition-timing-function: linear;
// }

// button:active {
//   transform: translate(0, 0);
//   border-bottom: 2px solid #b1b1b1;
// }

</style>