<template>
    <div>
        <van-nav-bar title="充值" left-text="返回" left-arrow @click-left="onClickLeft" />
        <div style="padding: 10px;">
            <van-form validate-first @failed="onFailed" @submit="onSubmit">
                <!-- 通过 pattern 进行正则校验 -->
                <van-field label="用户名" readonly v-model="formData.username" clearable name="用户名" placeholder="用户名" />

                <van-field label="公司名" readonly v-model="formData.compName" clearable name="公司名" placeholder="公司名" />

                <div style="display: flex;align-items: center;border-bottom: 1px solid #F2F6FC;">
                    <van-field label="充值类别" readonly clickable name="充值类别" :value="productId" placeholder="充值类别"
                        @click="showPicker = true">
                    </van-field>
                </div>

                <van-popup v-model="showPicker" position="bottom" :style="{ height: '30%' }">
                    <van-picker show-toolbar :columns="balanceData" value-key="name" @confirm="onConfirm"
                        @cancel="showPicker = false" />
                </van-popup>

                <van-field label="金额/条" v-model="formData.rechargeNum" type="digit" clearable placeholder="金额/条"
                    :rules="[{ required: true, message: '请输入充值金额' },{ pattern:  /^\d{1,8}$/, message: '最大3000万条' }]">
                </van-field>

                <van-field label="备注" v-model="formData.rechargeNote" type="textarea" clearable placeholder="备注"
                    :rules="[{ required: true, message: '请输入备注' }]">
                </van-field>

                <div style="margin: 16px;">
                    <van-button round block type="info" native-type="submit">提交</van-button>
                </div>
            </van-form>

        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            statusOf: "",
            productId: "短信",
            showPicker: false,
            balanceData: [],
            formData: {
                username: '', // 用户名
                compName: '', // 公司名
                productId: 1, // 产品ID
                rechargeNum: '', // 充值金额
                rechargeNote: '', // 充值备注
            },
        }
    },
    created() {
        if (JSON.parse(localStorage.getItem('balanceList'))) {
            this.balanceData = JSON.parse(localStorage.getItem('balanceList'))
        }
        if (this.$route.query.userInfo) {
            let userInfo = JSON.parse(this.$route.query.userInfo)
            this.formData.username = userInfo.consumerName
            this.formData.compName = userInfo.compName
            
        }

    },
    methods: {
        onClickLeft() {
            this.$router.push('/balanceManagment');
        },
        onFailed(msg) {
            console.log(msg, 'failed')
            // this.$toast.fail(msg);
        },
        generatePass() {
            this.$api.get(this.API.cpus + 'consumerclientinfo/generatePassword', {}, res => {
                this.formData.password = res
            })
        },
        onConfirm(selected) {
            this.productId = selected.name;
            this.formData.productId = selected.productId;
            this.showPicker = false;
        },
        onSubmit(value) {
            this.formData.orderNumber = 'gls' + (new Date()).valueOf() + Math.ceil(Math.random() * 10000000);
            this.formData.type = '1'
            if (this.formData.rechargeNum > 30000000) {
                this.$toast.fail('充值金额不能超过3000万条');
            }else{
                this.$api.post(this.API.recharge + 'partner/recharge', this.formData, res => {
                    if (res.code === 200) {
                        this.$toast.success('充值成功');
                        this.$router.push('/balanceManagment');
                    }else{
                        this.$toast.fail(res.msg);
                    }
                })
            }
        }
    }
}
</script>

<style lang="less" scoped></style>