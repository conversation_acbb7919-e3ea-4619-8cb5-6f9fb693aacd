<template>
    <div class="record">
        <van-sticky>
            <van-nav-bar title="充值记录" left-text="返回" left-arrow @click-left="onClickLeft" />
        </van-sticky>
        <van-tabs @click="onClick">
            <van-tab title="子用户充值记录">
                <router-view />
            </van-tab>
            <van-tab title="我的充值">
                <router-view />
            </van-tab>
        </van-tabs>
    </div>
</template>

<script>
export default {
    name: 'Record',
    data() {
        return {

        }
    },
    methods: {
        onClickLeft() {
            this.$router.push({ path: '/glsHome' });
        },
        onClick(index) {
            console.log(index, 'index');
            if (index === 0) {
                this.$router.push({ path: '/record/glRecord' });
            }else if (index === 1) {
                this.$router.push({ path: '/record/MindGlsRecord' });
            }
        }
    }
}
</script>

<style lang="less" scoped>
.record {
    // background: #eee;
}
</style>