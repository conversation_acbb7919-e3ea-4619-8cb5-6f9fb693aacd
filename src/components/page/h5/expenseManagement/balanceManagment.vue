<template>
    <div>
        <van-sticky>
            <van-nav-bar title="余额管理" left-text="返回" left-arrow @click-left="onClickLeft" />
        </van-sticky>

        <div class="user-list">
            <div class="user-input">
                <van-search v-model="tabelAlllist.userName" shape="round" show-action placeholder="用户名" @search="onSearch">
                    <template slot="action">
                        <div @click="onSearch">搜索</div>
                    </template>
                </van-search>
            </div>
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
                <van-list v-model="loading" :finished="finished" :finished-text="`总共${total}条数据,没有更多了`" @load="onLoad">
                    <div v-for="item in list" :key="item.userId">
                        <van-swipe-cell :right-width="220">
                            <div class="item-content">
                                <div class="user-item">
                                    <div class="user-company">{{ item.consumerName }}</div>
                                </div>
                                <div class="user-item">
                                    <div class="user-company">{{ item.compName }}</div>
                                </div>
                                <div class="user-item" @click="detailUser(item)">
                                    <van-tag size="medium" :color="tagColor[0]">{{ item.balanceList[0].name }} : {{
                                        item.balanceList[0].num }}</van-tag>
                                </div>
                                <!-- <div style="color: #67C23A;" @click="detailUser(item)">
                                        余额
                                    </div> -->
                            </div>
                            <van-button square slot="right" type="info" size="normal" text="充值"
                                @click="handleRecharge(item)" />
                            <van-button square slot="right" color="#E6A23C" size="normal" text="扣款"
                                @click="handledeductMoney(item)" />
                            <van-button square slot="right" type="info" size="normal" text="余额提醒"
                                @click="handelAction(item)">余额提醒</van-button>
                        </van-swipe-cell>
                    </div>
                </van-list>
            </van-pull-refresh>
            <van-action-sheet v-model="remindShow" title="余额提醒设置">
                <div class="content">
                    <div class="van-action-sheet__content">
                        <div>
                            当前账号余额条数 在不足
                        </div>
                        <van-field v-model="remindData.NumberBalances" label=" " type="digit" placeholder="" />
                        <div>条时提醒</div>
                    </div>
                    <div class="footer_btn">
                        <van-button text="取消" @click="remindShow = false" />
                        <van-button style="margin-left: 18px;" type="info" text="确定" @click="subimtRemind" />
                    </div>
                </div>

            </van-action-sheet>
            <van-popup v-model="detailShow" closeable position="bottom" :style="{ height: '30%' }" @close="handleClose">
                <h3 class="van-popup__title">余额明细 ：{{ detailList.consumerName }}</h3>
                <h4 class="van-popup__company">公司名称：{{ detailList.compName }}</h4>
                <div class="balanceLists">
                    <van-tag style="margin: 6px;" size="large" v-for="(item, index) in detailList.balanceList"
                        :key="index + 'a'" :color="tagColor[index]">{{ item.name }} : {{ item.num }}</van-tag>
                </div>
            </van-popup>
        </div>
    </div>
</template>

<script>
import { tagColor } from '../../utils/tagColor';
export default {
    data() {
        return {
            list: [],
            delShow: false,
            remindShow: false,
            detailShow: false,
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            loading: false,//是否正在加载
            refreshing: false,//是否正在刷新
            total: 0,//总条数
            userName: "",
            remindData: {//余额提醒设置
                NumberBalances: "1",//余额条数
                productId: "1",//产品id
                username: ""
            },
            tabelAlllist: {//搜索条件
                userName: "",//用户名
                userStatus: "0",//用户状态
                currentPage: 0,
                pageSize: 10,
            },
            detailList: {},//余额明细
            tagColor: [],
        }
    },
    created() {
        this.tagColor = tagColor;
    },
    methods: {
        onClickLeft() {
            this.$router.push('/glsHome');
        },
        onSearch() {
            this.tabelAlllist.currentPage = 1
            this.loading = true;
            this.finished = false
            this.getUserList()
            // if(this.tabelAlllist.userName == ""){
            //     this.onLoad()
            // }else{
            //     this.tabelAlllist.currentPage = 1
            //     this.getUserList()
            // }
        },
        // onClick(name) {
        //     this.tabelAlllist.currentPage = 1
        //     this.loading = true;
        //     this.finished = false
        //     this.getUserList()
        // },
        //获取用户列表
        getUserList() {
            this.$api.post(
                this.API.cpus + "v3/consumer/record/user/manager/list",
                this.tabelAlllist,
                (res) => {
                    if (res.code == 200) {
                        this.total = res.data.total;
                        if (res.data.records.length == 0) { //没有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.finished = true; //加载完成
                            } else { //是第一页，下拉刷新
                                this.list = []; //刷新列表
                                this.finished = false;
                                this.$toast.fail('暂无数据');
                            }
                        } else { //有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.list = [...this.list, ...res.data.records] //数组合并
                            } else { //是第一页，下拉刷新
                                this.list = res.data.records //刷新列表
                                this.refreshing = false; //刷新完成
                            }
                            if (this.list.length == res.data.total) { //没有更多数据
                                this.finished = true; //加载完成
                            }
                            this.loading = false; //加载完成
                        }
                    }
                }
            );
        },
        //上拉加载
        onLoad() {
            this.loading = true;
            this.tabelAlllist.currentPage++
            this.getUserList()
        },
        //下拉刷新
        onRefresh() {
            this.tabelAlllist.currentPage = 1
            this.refreshing = false;
            this.getUserList()
        },
        //删除用户
        handleRecharge(row) {
            this.$router.push({
                path: '/recharge',
                query: {
                    userInfo: JSON.stringify(row)
                }
            });
        },
        //充值
        handledeductMoney(row) {
            this.$router.push({
                path: '/deductMoney',
                query: {
                    userInfo: JSON.stringify(row)
                }
            });
        },
        //余额提醒
        subimtDel() {
            this.$api.delete(
                this.API.cpus + 'v3/consumer/manager/user/del/' + this.userName,
                {},
                (res) => {
                    if (res.code == 200) {
                        this.$toast.success('删除成功');
                        this.delShow = false;
                        this.onSearch();
                    } else {
                        this.$toast.fail('删除失败');
                    }
                })
        },
        handelAction(row) {
            this.remindShow = true;
            this.remindData.username = row.consumerName;

        },
        detailUser(row) {
            // console.log(row, 'row');
            this.detailList = row;
            // console.log(this.detailList , 'this.detailList');
            this.detailShow = true;
        },
        handleClose() {
            this.detailShow = false;
            this.detailList = {};
            // console.log(111)
        },
        //余额提醒设置
        subimtRemind(){
            this.$api.post(
                this.API.recharge + 'partner/balance/notice/open',
                this.remindData,
                (res) => {
                    if (res.code == 200) {
                        this.$toast.success('设置成功');
                        this.remindShow = false;
                        this.onSearch();
                    } else {
                        this.$toast.fail('设置失败');
                    }
                })
        },
    },

}
</script>

<style lang="less" scoped>
.user-list {
    margin: 18px;
}

.item-content {
    display: flex;
    // height: 50px;
    border-bottom: 1px solid #eee;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    margin: 10px 0;
}

.user-item {
    width: 95px;
    height: 40px;
    line-height: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-name {
    font-size: 16px;
    font-weight: bold;
}

.user-company {
    font-size: 12px;
}

.user-btn {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.create-btn {
    width: 80px;
}

.search-status {
    flex: 1;
}

.van-action-sheet__content {
    display: flex;
    justify-content: center;
    align-items: center;
    // padding: 10px 10px;
}
.footer_btn{
    margin: 14px 0;
    float: right;
}
.content {
    padding: 18px 18px;
}

.balanceLists {
    padding: 0 18px;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    overflow: auto;
}

.van-popup__title {
    padding: 18px;
    margin-left: 10px;
}

.van-popup__company {
    padding: 0 18px;
    margin-left: 10px;
}

// .user-input {
//     display: flex;
//     align-items: center;
// }

// .search-btn {
//     width: 100px;
//     margin-left: 10px;
// }</style>