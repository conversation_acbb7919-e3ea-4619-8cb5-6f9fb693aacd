<template>
    <div class="gl-record">
        <div class="user-input">
            <!-- <van-search v-model="tabelAlllist.username" shape="round" show-action placeholder="用户名" @search="onSearch">
                <template slot="action">
                    <div @click="onSearch">搜索</div>
                </template>
            </van-search> -->
            <van-tabs title-active-color="#409eff" color="#409eff" v-model="tabelAlllist.productId" @click="onSearch">
                <van-tab v-for="item in balanceData" :title="item.name" :name="item.productId"
                    :key="item.productId"></van-tab>
            </van-tabs>
        </div>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list v-model="loading" :finished="finished" :finished-text="`总共${total}条数据,没有更多了`" @load="onLoad">
                <div v-if="list.length">
                    <div v-for="item in list" :key="item.id">
                        <div class="item">
                            <div class="item-content">
                                <div class="item-left">
                                    <img class="item-img" v-if="item.productId == 1" :src="icon4" alt="">
                                    <img class="item-img" v-if="item.productId == 2" :src="icon5" alt="">
                                    <img class="item-img" v-if="item.productId == 3" :src="icon6" alt="">
                                    <img class="item-img" v-if="item.productId == 4" :src="icon7" alt="">
                                    <img class="item-img" v-if="item.productId == 5" :src="icon8" alt="">
                                    <img class="item-img" v-if="item.productId == 6" :src="icon9" alt="">
                                    <img class="item-img" v-if="item.productId == 7" :src="icon10" alt="">
                                    <img class="item-img" v-if="item.productId == 8" :src="icon11" alt="">
                                    <img class="item-img" v-if="item.productId == 9" :src="icon12" alt="">
                                    <img class="item-img" v-if="item.productId == 10" :src="icon13" alt="">
                                </div>
                                <div class="item-right">
                                    <!-- <div class="item-title">{{item.id}}</div> -->
                                    <!-- <div class="item-desc">{{item.compName}}</div> -->
                                    <div class="item-time">{{item.productName}}</div>
                                    <div class="item-time">{{item.orderNumber}}</div>
                                    <div class="item-time">{{item.rechargeTime}}</div>
                                    <div class="item-time">备注：{{item.rechargeNote}}</div>
                                </div>
                            </div>
                            <div>
                                <div class="item-price"> 
                                    <div></div>  
                                    <div>充值条数/元：{{item.rechargeNum}}{{item.unit}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <van-empty description="暂无数据" />
                </div>
            </van-list>
        </van-pull-refresh>
    </div>
</template>

<script>
import {icon4,icon5,icon6,icon7,icon8,icon9,icon10,icon11,icon12,icon13} from "@/components/page/h5/imgs.js"

export default {
    name: 'glRecord',
    data() {
        return {
            balanceData: [],
            tabelAlllist: {
                username: '',
                productId: '1',
                currentPage: 0,
                pageSize: 10,
            },
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            loading: false,
            refreshing: false,
            total: 0,
            list: [],
            icon4,icon5,icon6,icon7,icon8,icon9,icon10,icon11,icon12,icon13,
        }
    },
    created() {
        if (JSON.parse(localStorage.getItem('balanceList'))) {
            this.balanceData = JSON.parse(localStorage.getItem('balanceList'))
        }
    },
    methods: {
        onSearch() {
            this.tabelAlllist.currentPage = 1
            this.loading = true;
            this.finished = false
            this.getUserList()
        },
        getUserList() {
            this.$api.get(
                this.API.recharge + "client/recharge/page",
                this.tabelAlllist,
                (res) => {
                    if (res.code == 200) {
                        this.total = res.data.total;
                        if (res.data.records.length == 0) { //没有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.finished = true; //加载完成
                                
                            } else { //是第一页，下拉刷新
                                this.list = []; //刷新列表
                                this.finished = false;
                                this.loading = false;
                                this.$toast.fail('暂无数据');
                            }
                        } else { //有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.list = [...this.list, ...res.data.records] //数组合并
                            } else { //是第一页，下拉刷新
                                this.list = res.data.records //刷新列表
                                this.refreshing = false; //刷新完成
                            }
                            if (this.list.length == res.data.total) { //没有更多数据
                                this.finished = true; //加载完成
                            }
                            this.loading = false; //加载完成
                        }
                    }
                }
            );
        },
        onLoad() {
            this.loading = true;
            this.tabelAlllist.currentPage++
            this.getUserList()
        },
        onRefresh() {
            this.tabelAlllist.currentPage = 1
            this.refreshing = false;
            this.getUserList()
        },
    }
}
</script>

<style lang="less" scoped>
.gl-record {
    margin: 18px;

}

.item {
    // height: 150px;
    border-radius: 10px;
    // box-shadow: 1px 1px 1px 1px rgba(0,0,0,0.1);
    // margin-bottom: 10px;
    background-color: #F2F6FC;
    margin: 14px;
    padding: 10px;
    // background-color: #fff;
}
.item-content{
    display: flex;
    align-items: center;
    // padding: 0 10px;
    // align-content: center;
    // align-items: center;
}
.item-img{
    width: 100px;
}
.item-right{
    margin-left: 10px;
    line-height: 1.5;
    // margin-top: 10px;
}

.item-price{
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}
</style>