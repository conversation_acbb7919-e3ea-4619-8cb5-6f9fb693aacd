<template>
  <div class="h5-home">
    <!-- <div class="h5-home-header">
            <van-icon name="search" class="h5-home-header-icon" />
        </div> -->
    <div class="h5-home-content">
      <router-view />
    </div>
    <div>
      <van-tabbar v-model="active" route>
        <van-tabbar-item
          v-if="roleId === '12'"
          replace
          to="/glsHome"
          icon="home-o"
          >首页</van-tabbar-item
        >
        <van-tabbar-item v-else replace to="/mcHome" icon="home-o"
          >首页</van-tabbar-item
        >
        <van-tabbar-item replace to="/userInfo" icon="friends-o"
          >我的</van-tabbar-item
        >
      </van-tabbar>
    </div>
  </div>
</template>

<script>
export default {
  name: "Home",
  data() {
    return {
      active: 0,
      roleId: "",
    };
  },
  created() {
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        this.$router.push({ path: res.data.roleId === "12" ? "/glsHome" : "/mcHome" });
        localStorage.setItem('userInfo', JSON.stringify(res.data))

      }
    //     if (res.data.balanceList) {
    //       this.balanceList = res.data.balanceList.filter(
    //         (item) => item.productId !== 2
    //       );
    //       // console.log(this.balanceList,'this.balanceList')
    //       localStorage.setItem("balanceList", JSON.stringify(this.balanceList)); // 存储到本地缓存中，下次打开页面可以直接取出('balanceList'，)
    //     }
    //   }
    );
    
  },
};
</script>

<style lang="less" scoped>
.h5-home {
  height: 100%;
  background-color: #f5f5f5;
}

.h5-home-header {
  height: 44px;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
}

.h5-home-content {
  height: calc(100% - 44px);
  background-color: #fff;
}
</style>