<template>
    <div class="contaner">
        <van-sticky>
            <van-nav-bar title="子用户回复明细" left-text="返回" left-arrow @click-left="clickBack"></van-nav-bar>
        </van-sticky>
        <div class="searchArea">
            <van-search v-model="tabelAlllist.clientName" shape="round" background="#4fc08d" placeholder="请输入用户名称" />
            <div class="searchText" @click="onSearch">查询</div>
            <van-icon @click="showBottom = true" class="searchIcon" name="filter-o" />
        </div>
        <!-- <div class="user-btn">
                <van-button class="create-btn" type="primary" size="small" @click="CreateUser">创建用户</van-button>
                <div class="search-status">
                    <van-tabs v-model="tabelAlllist.userStatus" @click="onSearch">
                        <van-tab title="全部" name=""></van-tab>
                        <van-tab title="启用" name="0"></van-tab>
                        <van-tab title="停用" name="1"></van-tab>
                    </van-tabs>
                </div>

            </div> -->
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" style="min-height: calc( -90px + 100vh); padding: 0 16px;">
            <van-list v-model="loading" :finished="finished" :finished-text="`总共${total}条数据,没有更多了`" @load="onLoad" v-loading="tabelLoading">

                <div v-for="(item, index) in list" :key="index">
                    <div class="item-content">
                        <div class="item-content-userInfo">
                            <div class="item-content-userInfo-userName">{{ item.userName }}</div>
                            <div class="item-content-userInfo-compName">
                                {{ item.mobile }}
                            </div>
                        </div>
                        <div class="item-content-data">

                            <div style="display: flex;">
                                <div>
                                    <div class="data-item">
                                        <div class="data-title">消息ID</div>
                                        <div class="data-value">{{ item.msgid }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">发送内容</div>
                                        <div class="data-value">{{ item.smsContent }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">回复内容</div>
                                        <div class="data-value">{{ item.content }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">回复时间</div>
                                        <div class="data-value">{{ item.createTime }}</div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </van-list>
        </van-pull-refresh>
        <van-popup v-model="showBottom" closeable round position="bottom" :style="{ height: '30%', background: '#f6f6f6' }">
            <div class="van-popup-title">
                <van-field style="width: 95%;" v-model="tabelAlllist.signature" placeholder="签名" />
                <div style="display: flex;margin-top: 18px;">
                    <van-field readonly :value="tabelAlllist.beginTime" placeholder="开始日期"
                        @click="hidePickerShowPicker('start')" />
                    <van-field readonly style="margin-left: 10px;" :value="tabelAlllist.endTime" placeholder="结束日期"
                        @click="hidePickerShowPicker('end')" />
                </div>
                <div style="margin-top: 18px;">
                    <van-button style="width: 100px;" round type="info" plain @click="handelReset">重置</van-button>
                    <van-button style="width: 100px;margin-left: 10px;" round type="info" @click="onSearch">查询</van-button>
                </div>
            </div>
        </van-popup>
        <van-popup v-model="showPicker" position="bottom">
            <van-datetime-picker type="datetime" v-model="currentDate" @confirm="onConfirm" @cancel="showPicker = false" />
        </van-popup>

    </div>
</template>

<script>
import { tagColor } from '@/components/page/utils/tagColor';
import { icon4 } from "@/components/page/h5/imgs.js"
import moment from 'moment'
export default {
    name: 'TextMessage',
    data() {
        return {
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            refreshing: false,//是否正在刷新
            loading: false,//是否正在加载
            list: [],
            total: 0,//总条数
            tabelAlllist: {//搜索条件
                clientName: "",//用户名
                signature: "",//公司名
                beginTime: moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
                endTime: moment().endOf('day').format('YYYY-MM-DD 23:59:59'),
                currentPage: 0,
                pageSize: 10,
            },
            timeTpey: "",
            currentDate: new Date(),
            tagColor: [],
            // step: 0,
            isDateState: 1,
            showBottom: false,
            showPicker: false,
            searchValue: "",
            icon4,
            tabelLoading: false
        }
    },
    created() {
        this.isDateState = JSON.parse(localStorage.getItem("userInfo")).isDateState
        this.tagColor = tagColor;
    },
    methods: {
        onSearch() {
            this.tabelAlllist.currentPage = 1
            this.loading = true;
            this.finished = false
            this.showBottom = false
            this.getUserList()
        },
        //上拉加载
        onLoad() {
            this.loading = true;
            this.tabelAlllist.currentPage++
            this.getUserList()
        },
        //下拉刷新
        onRefresh() {
            this.list = []
            this.tabelAlllist.currentPage = 1
            this.refreshing = false;
            this.finished = false
            // this.step++
            this.getUserList()
        },
        //获取用户列表
        getUserList() {
            console.log(2313);
            this.tabelLoading = true
            this.$api.post(
                this.API.cpus + "consumerpartner/reply/page",
                this.tabelAlllist,
                (res) => {
                    this.tabelLoading = false
                    this.total = res.total;
                    if (res.records.length == 0) { //没有数据
                        console.log(2);
                        if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                            this.finished = true; //加载完成
                        } else { //是第一页，下拉刷新
                            console.log(22);
                            this.list = []; //刷新列表
                            this.finished = true;
                            this.$toast.fail('暂无数据');
                        }
                    } else { //有数据
                        console.log(1111);
                        if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                            this.list = [...this.list, ...res.records] //数组合并
                        } else { //是第一页，下拉刷新
                            this.list = res.records //刷新列表
                            this.refreshing = false; //刷新完成
                        }
                        if (this.list.length == res.total) { //没有更多数据
                            this.finished = true; //加载完成
                        }
                        this.loading = false; //加载完成
                    }
                }

            );
        },
        // 返回
        clickBack() {
            this.$router.push('/glsSmsStatistics');
            // this.showBottom = true
        },
        hidePickerShowPicker(type) {
            this.showPicker = true
            this.timeTpey = type
        },
        onConfirm(date) {
            if (this.timeTpey == "start") {
                this.tabelAlllist.beginTime = moment(date).format('YYYY-MM-DD 00:00:00')
            } else {
                this.tabelAlllist.endTime = moment(date).format('YYYY-MM-DD 23:59:59')
            }
            this.showPicker = false
        },
        handelReset() {
            this.tabelAlllist.signature = ""
            this.tabelAlllist.beginTime = moment().startOf('day').format('YYYY-MM-DD 00:00:00')
            this.tabelAlllist.endTime = moment().endOf('day').format('YYYY-MM-DD 23:59:59')
        },
        // scrollFunction() {
        //     if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
        //         document.getElementsByClassName("backToTop")[0].style.display = "block";
        //     } else {
        //         document.getElementById("backToTop")[0].style.display = "none";
        //     }
        // },
    }
}
</script>

<style lang="less">
.van-search {
    background: none !important;
    padding: 0 0 0 16px !important;
    overflow: hidden;
    width: calc(100% - 80px);
    box-sizing: border-box;

    .van-search__content {
        background: #fff !important;
        // border-radius: 16rpx;
        // height: 80rpx;
        // line-height: 80rpx;
    }

}
</style>

<style lang="less" scoped>
.contaner {
    padding: 0;
    background-color: #f6f6f6;

    .searchArea {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .searchText {
            margin: 0 12px;
            font-size: 14px;
            color: #1989fa;
        }

        .searchIcon {
            color: #1989fa;
        }
    }

    .item-content {
        // height: 150px;
        // display: flex;
        // height: 50px;
        border-bottom: 1px solid #eee;
        // align-items: center;
        // justify-content: space-between;
        // padding: 10px;
        margin: 10px 0;
        background-color: #fff;
        // box-shadow: -2px -2px 8px rgba(0, 0, 0, .2);
        border-radius: 6px;
        color: #171A1D;

        .item-content-userInfo {
            // display: flex;
            align-items: center;
            padding: 10px;
            // background-image: repeating-linear-gradient(45deg, #d6f4fa, #d5faf2);
            border-bottom: 1px solid #eee;

            .item-content-userInfo-userName {
                width: 48%;
                margin-right: 2%;
                margin-bottom: 5px;
                font-size: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .item-content-userInfo-compName {
            font-size: 14px;
            // width: 50%;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 3px;
        }

        .item-content-data {
            padding: 8px;

            .data-item {
                display: flex;
                margin: 4px;

                .data-title {
                    width: 100px;
                    // font-weight: bold;
                    color: #666;
                }
            }
        }
    }


    .backToTop {
        position: fixed;
        bottom: 20px;
        right: 20px;
    }
}

.van-popup-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 54px;
}

/deep/ .van-cell {
    border-radius: 64px;
    width: 180px;
}
</style>