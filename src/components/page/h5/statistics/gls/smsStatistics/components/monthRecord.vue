<template>
    <div class="contaner">
        <van-sticky>
            <van-nav-bar title="月发送统计" left-text="返回" left-arrow @click-left="clickBack"></van-nav-bar>
        </van-sticky>
        <div class="searchArea">
            <van-search v-model="tabelAlllist.userName" shape="round" background="#4fc08d" placeholder="请输入用户名称" />
            <div class="searchText" @click="onSearch">查询</div>
            <van-icon @click="showBottom = true" class="searchIcon" name="filter-o" />
        </div>
        <!-- <div class="user-btn">
                <van-button class="create-btn" type="primary" size="small" @click="CreateUser">创建用户</van-button>
                <div class="search-status">
                    <van-tabs v-model="tabelAlllist.userStatus" @click="onSearch">
                        <van-tab title="全部" name=""></van-tab>
                        <van-tab title="启用" name="0"></van-tab>
                        <van-tab title="停用" name="1"></van-tab>
                    </van-tabs>
                </div>

            </div> -->
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" style="min-height: calc(100vh + 1px); padding: 0 16px;">
            <van-list v-model="loading" :finished="finished" :finished-text="`总共${total}条数据,没有更多了`" @load="onLoad" v-loading="tabelLoading">

                <div v-for="(item, index) in list" :key="index">
                    <div class="item-content">
                        <div class="item-content-userInfo">
                            <div class="item-content-userInfo-userName">{{ item.userName }}</div>
                            <div class="item-content-userInfo-compName">
                                {{ item.compName }}
                            </div>
                        </div>
                        <div class="item-content-data">

                            <div style="display: flex;">
                                <div>
                                    <div class="data-item">
                                        <div class="data-title">短信剩余条数</div>
                                        <div class="data-value">{{ item.smsBalance }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">发送号码数</div>
                                        <div class="data-value">{{ item.sendAmount }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">发送计费数</div>
                                        <div class="data-value">{{ item.chargeNum }}</div>
                                    </div>
                                </div>
                                <div v-if="isDateState === 1" style="margin-left: 10px;">
                                    <div class="data-item">
                                        <div class="data-title">成功计费数</div>
                                        <div class="data-value" v-if="item.chargeSuccessNum === 0">{{ 0 }}</div>
                                        <div class="data-value" v-else-if="item.chargeSuccessNum">{{ item.chargeSuccessNum
                                        }}
                                        </div>
                                        <div class="data-value" v-else>-</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">失败计费数</div>
                                        <div class="data-value">{{ item.chargeFailNum }}</div>
                                        <div class="data-value" v-if="item.chargeFailNum === 0">{{ 0 }}</div>
                                        <div class="data-value" v-else-if="item.chargeFailNum">{{ item.chargeFailNum }}
                                        </div>
                                        <div class="data-value" v-else>-</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">待返回计费数</div>
                                        <div class="data-value">{{ item.chargeWaitNum }}</div>
                                        <div class="data-value" v-if="item.chargeWaitNum === 0">{{ 0 }}</div>
                                        <div class="data-value" v-else-if="item.chargeWaitNum">{{ item.chargeWaitNum }}
                                        </div>
                                        <div class="data-value" v-else>-</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">成功率</div>
                                        <div class="data-value">{{ item.successRate }}</div>
                                        <div class="data-value" v-if="item.successRate === 0">{{ 0 }}%</div>
                                        <div class="data-value" v-else-if="item.successRate">{{ item.successRate }}%</div>
                                        <div class="data-value" v-else>-</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">待返回率</div>
                                        <div class="data-value">{{ item.waitRate }}</div>
                                        <div class="data-value" v-if="item.waitRate === 0">{{ 0 }}%</div>
                                        <div class="data-value" v-else-if="item.waitRate">{{ item.waitRate }}%</div>
                                        <div class="data-value" v-else>-</div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </van-list>
        </van-pull-refresh>
        <van-popup v-model="showBottom" closeable round position="bottom" :style="{ height: '30%', background: '#f6f6f6' }">
            <div class="van-popup-title">
                <van-field style="width: 95%;" v-model="tabelAlllist.compName" placeholder="公司名" />
                <div style="display: flex;margin-top: 18px;">
                    <van-field readonly :value="tabelAlllist.monthTime" placeholder="xxxx-xx"
                        @click="hidePickerShowPicker" />
                </div>
                <div style="margin-top: 18px;">
                    <van-button style="width: 100px;" round type="info" plain @click="handelReset">重置</van-button>
                    <van-button style="width: 100px;margin-left: 10px;" round type="info" @click="onSearch">查询</van-button>
                </div>
            </div>
        </van-popup>
        <van-popup v-model="showPicker" position="bottom">
            <van-datetime-picker type="year-month" v-model="currentDate" @confirm="onConfirm"
                @cancel="showPicker = false" />
        </van-popup>

    </div>
</template>

<script>
import { tagColor } from '@/components/page/utils/tagColor';
import { icon4 } from "@/components/page/h5/imgs.js"
import moment from 'moment'
export default {
    name: 'TextMessage',
    data() {
        return {
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            refreshing: false,//是否正在刷新
            loading: false,//是否正在加载
            list: [],
            total: 0,//总条数
            statisList: [
                {
                    clientName: "用户发送统计",
                    path: "/statistics/userRecord",
                    id: 1
                },
                {
                    clientName: "月发送统计",
                    path: "/statistics/monthRecord",
                    id: 2
                },
                {
                    clientName: "子用户发送明细",
                    path: "/statistics/subUserRecord",
                    id: 3
                },
                {
                    clientName: "子用户回复明细",
                    path: "/statistics/subUserReplyRecord",
                    id: 4
                }
            ],//统计数据
            tabelAlllist: {//搜索条件
                userName: "",//用户名
                compName: "",//公司名
                monthTime: new Date().getFullYear() + "-" + (new Date().getMonth() + 1 < 10 ? "0" + (new Date().getMonth() + 1) : new Date().getMonth() + 1),//月份
                startTime: "",
                endTime: "",
                currentPage: 0,
                pageSize: 10,
            },
            currentDate: new Date(),
            tagColor: [],
            // step: 0,
            isDateState: 1,
            showBottom: false,
            showPicker: false,
            searchValue: "",
            icon4,
            tabelLoading: false
        }
    },
    created() {
        this.isDateState = JSON.parse(localStorage.getItem("userInfo")).isDateState
        this.tagColor = tagColor;
    },
    methods: {
        onSearch() {
            this.tabelAlllist.currentPage = 1
            this.loading = true;
            this.finished = false
            this.showBottom = false
            this.getUserList()
        },
        //上拉加载
        onLoad() {
            this.loading = true;
            this.tabelAlllist.currentPage++
            this.getUserList()
        },
        //下拉刷新
        onRefresh() {
            this.list = []
            this.tabelAlllist.currentPage = 1
            this.refreshing = false;
            this.finished = false
            // this.step++
            this.getUserList()
        },
        //获取用户列表
        getUserList() {
            this.tabelLoading = true
            this.$api.post(
                this.API.cpus + "v3/consumer/statistics/month/statistics",
                this.tabelAlllist,
                (res) => {
                    this.tabelLoading = false
                    if (res.code == 200) {
                        this.total = res.data.total;
                        if (res.data.records.length == 0) { //没有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.finished = true; //加载完成
                            } else { //是第一页，下拉刷新
                                this.list = []; //刷新列表
                                this.finished = false;
                                this.$toast.fail('暂无数据');
                            }
                        } else { //有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.list = [...this.list, ...res.data.records] //数组合并
                            } else { //是第一页，下拉刷新
                                this.list = res.data.records //刷新列表
                                this.refreshing = false; //刷新完成
                            }
                            if (this.list.length == res.data.total) { //没有更多数据
                                this.finished = true; //加载完成
                            }
                            this.loading = false; //加载完成
                        }
                    }
                }
            );
        },
        // 返回
        clickBack() {
            this.$router.push('/glsSmsStatistics');
            // this.showBottom = true
        },
        hidePickerShowPicker() {
            this.showPicker = true
        },
        onConfirm(date) {
            this.tabelAlllist.monthTime = moment(date).format('YYYY-MM')
            this.showPicker = false
        },
        handelReset() {
            this.tabelAlllist.compName = ""
            this.tabelAlllist.monthTime = new Date().getFullYear() + "-" + (new Date().getMonth() + 1 < 10 ? "0" + (new Date().getMonth() + 1) : new Date().getMonth() + 1)//月份
        },
        // scrollFunction() {
        //     if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
        //         document.getElementsByClassName("backToTop")[0].style.display = "block";
        //     } else {
        //         document.getElementById("backToTop")[0].style.display = "none";
        //     }
        // },
    }
}
</script>

<style lang="less">
.van-search {
    background: none !important;
    padding: 0 0 0 16px !important;
    overflow: hidden;
    width: calc(100% - 80px);
    box-sizing: border-box;

    .van-search__content {
        background: #fff !important;
        // border-radius: 16rpx;
        // height: 80rpx;
        // line-height: 80rpx;
    }

}
</style>

<style lang="less" scoped>
.contaner {
    padding: 0;
    background-color: #f6f6f6;

    .searchArea {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .searchText {
            margin: 0 12px;
            font-size: 14px;
            color: #1989fa;
        }

        .searchIcon {
            color: #1989fa;
        }
    }

    .item-content {
        // height: 150px;
        // display: flex;
        // height: 50px;
        border-bottom: 1px solid #eee;
        // align-items: center;
        // justify-content: space-between;
        // padding: 10px;
        margin: 10px 0;
        background-color: #fff;
        // box-shadow: -2px -2px 8px rgba(0, 0, 0, .2);
        border-radius: 6px;
        color: #171A1D;

        .item-content-userInfo {
            // display: flex;
            align-items: center;
            padding: 10px;
            // background-image: repeating-linear-gradient(45deg, #d6f4fa, #d5faf2);
            border-bottom: 1px solid #eee;

            .item-content-userInfo-userName {
                width: 48%;
                margin-right: 2%;
                margin-bottom: 5px;
                font-size: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .item-content-userInfo-compName {
            font-size: 14px;
            // width: 50%;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 3px;
        }

        .item-content-data {
            padding: 8px;

            .data-item {
                display: flex;
                margin: 4px;

                .data-title {
                    width: 100px;
                    // font-weight: bold;
                    color: #666;
                }
            }
        }
    }


    .backToTop {
        position: fixed;
        bottom: 20px;
        right: 20px;
    }
}

.van-popup-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 54px;
}

/deep/ .van-cell {
    border-radius: 64px;
    width: 180px;
}
</style>