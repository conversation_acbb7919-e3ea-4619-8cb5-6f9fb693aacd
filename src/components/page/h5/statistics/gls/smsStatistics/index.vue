<template>
    <div class="contaner">
        <van-sticky>
            <van-nav-bar title="短信明细" left-text="返回" left-arrow @click-left="clickBack"></van-nav-bar>
        </van-sticky>
        <div>
            <div class="sms-container-items">
                <div class="sms-item" v-for="(item, index) in statisList" :key="index">
                    <div>
                        <img class="sms-img" :src="icon4" alt="" @click="$router.push({path:item.path})">
                    </div>
                    <div class="sms-item-text">
                        <div  class="sms-item-text-num">{{item.clientName}}</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="searchArea">
            <van-search
                v-model="searchValue"
                shape="round"
                background="#4fc08d"
                placeholder="请输入用户名称"
            />
            <div class="searchText">查询</div>
            <van-icon class="searchIcon" name="filter-o" />
        </div>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" style="min-height: calc(100vh + 1px); padding: 0 16px;">
            <van-list v-model="loading" :finished="finished" :finished-text="`总共${total}条数据,没有更多了`" @load="onLoad">
                
                <div v-for="(item, index) in list" :key="index">
                    <div class="item-content">
                        <div class="item-content-userInfo">
                            <div class="item-content-userInfo-userName">{{ item.userName }}</div>
                        </div>
                        <div class="item-content-data">
                            <div class="item-content-userInfo-compName">
                                {{ item.compName }}</div>
                            <div class="data-item">
                                <div class="data-title">短信剩余条数</div>
                                <div class="data-value">{{ item.smsBalance }}</div>
                            </div>
                            <div class="data-item">
                                <div class="data-title">发送号码数</div>
                                <div class="data-value">{{ item.sendAmount }}</div>
                            </div>
                            <div class="data-item">
                                <div class="data-title">发送计费数</div>
                                <div class="data-value">{{ item.chargeNum }}</div>
                            </div>
                            <div class="data-item" v-if="isDateState === 1">
                                <div class="data-title">成功计费数</div>
                                <div class="data-value" v-if="item.chargeSuccessNum === 0">{{ 0 }}</div>
                                <div class="data-value" v-else-if="item.chargeSuccessNum">{{ item.chargeSuccessNum }}</div>
                                <div class="data-value" v-else>-</div>
                            </div>
                            <div class="data-item" v-if="isDateState === 1">
                                <div class="data-title">失败计费数</div>
                                <div class="data-value">{{ item.chargeFailNum }}</div>
                                <div class="data-value" v-if="item.chargeFailNum === 0">{{ 0 }}</div>
                                <div class="data-value" v-else-if="item.chargeFailNum">{{ item.chargeFailNum }}</div>
                                <div class="data-value" v-else>-</div>
                            </div>
                            <div class="data-item" v-if="isDateState === 1">
                                <div class="data-title">待返回计费数</div>
                                <div class="data-value">{{ item.chargeWaitNum }}</div>
                                <div class="data-value" v-if="item.chargeWaitNum === 0">{{ 0 }}</div>
                                <div class="data-value" v-else-if="item.chargeWaitNum">{{ item.chargeWaitNum }}</div>
                                <div class="data-value" v-else>-</div>
                            </div>
                            <div class="data-item" v-if="isDateState === 1">
                                <div class="data-title">成功率</div>
                                <div class="data-value">{{ item.successRate }}</div>
                                <div class="data-value" v-if="item.successRate === 0">{{ 0 }}%</div>
                                <div class="data-value" v-else-if="item.successRate">{{ item.successRate }}%</div>
                                <div class="data-value" v-else>-</div>
                            </div>
                            <div class="data-item" v-if="isDateState === 1">
                                <div class="data-title">待返回率</div>
                                <div class="data-value">{{ item.waitRate }}</div>
                                <div class="data-value" v-if="item.waitRate === 0">{{ 0 }}%</div>
                                <div class="data-value" v-else-if="item.waitRate">{{ item.waitRate }}%</div>
                                <div class="data-value" v-else>-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </van-list>
        </van-pull-refresh>
        <van-popup
            v-model="showBottom"
            position="bottom"
            :style="{ height: '30%' }"
        >
        </van-popup> -->
    </div>
</template>

<script>
import { tagColor } from '@/components/page/utils/tagColor';
import {icon4} from "@/components/page/h5/imgs.js"
export default {
    name: 'TextMessage',
    data() {
        return {
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            refreshing: false,//是否正在刷新
            loading: false,//是否正在加载
            list: [],
            total: 0,//总条数
            statisList: [
                {
                    clientName: "用户发送统计",
                    path:"/glsUserRecord",
                    id: 1
                },
                {
                    clientName: "月发送统计",
                    path:"/glsMonthRecord",
                    id: 2
                },
                {
                    clientName: "子用户发送明细",
                    path:"/glsSubUserRecord",
                    id: 3
                },
                {
                    clientName: "子用户回复明细",
                    path:"/glsSubUserReplyRecord",
                    id: 4
                }
            ],//统计数据
            tabelAlllist: {//搜索条件
                userName: "",//用户名
                currentPage: 0,
                pageSize: 10,
            },
            tagColor: [],
            // step: 0,
            isDateState: 1,
            showBottom: false,
            searchValue: "",
            icon4
        }
    },
    created() {
        this.isDateState = JSON.parse(localStorage.getItem("userInfo")).isDateState
        this.tagColor = tagColor;
        // console.log("orderRingTon", orderRingTon)
        window.onscroll = function() {
            this.scrollFunction();
        };
    },
    methods: {
        //上拉加载
        onLoad() {
            this.loading = true;
            this.tabelAlllist.currentPage++
            this.getUserList()
        },
        //下拉刷新
        onRefresh() {
            this.list = []
            this.tabelAlllist.currentPage = 1
            this.refreshing = false;
            this.finished = false
            // this.step++
            this.getUserList()
        },
        //获取用户列表
        getUserList() {
            this.$api.post(
                this.API.cpus + "v3/consumer/statistics/user/statistics",
                this.tabelAlllist,
                (res) => {
                    if (res.code == 200) {
                        this.total = res.data.total;
                        if (res.data.records.length == 0) { //没有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.finished = true; //加载完成
                            } else { //是第一页，下拉刷新
                                this.list = []; //刷新列表
                                this.finished = false;
                                this.$toast.fail('暂无数据');
                            }
                        } else { //有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.list = [...this.list, ...res.data.records] //数组合并
                            } else { //是第一页，下拉刷新
                                this.list = res.data.records //刷新列表
                                this.refreshing = false; //刷新完成
                            }
                            if (this.list.length == res.data.total) { //没有更多数据
                                this.finished = true; //加载完成
                            }
                            this.loading = false; //加载完成
                        }
                    }
                }
            );
        },
        // 返回
        clickBack() {
            this.$router.push('/glsHome');
            // this.showBottom = true
        },
        scrollFunction() {
            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                document.getElementsByClassName("backToTop")[0].style.display = "block";
            } else {
                document.getElementById("backToTop")[0].style.display = "none";
            }
        },
        topFunction() {
            document.body.scrollTop = 0; // 对Safari
            document.documentElement.scrollTop = 0; // 对Chrome, Firefox, IE 和 Opera
        },
    }
}
</script>

<style lang="less">
.van-search {
    background: none !important;
    padding: 0 0 0 16px !important;
    overflow: hidden;
    width: calc(100% - 80px);
    box-sizing: border-box;

    .van-search__content {
        background: #fff !important;
        // border-radius: 16rpx;
        // height: 80rpx;
        // line-height: 80rpx;
    }
    
}
</style>

<style lang="less" scoped>
.contaner {
    padding: 0;
    // background-color: #f6f6f6;
    .sms-container-items {
        display: flex;
        align-items: center;
        justify-content: space-between;
        // flex-shrink: 1;
        flex-wrap: wrap;

        .sms-item {
            width: 32%;
            height: 85px;
            // margin: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin: 20px 0 10px;
            cursor: pointer;

            .sms-item-text {
                text-align: center;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 12px;
                color: #171A1D;
                .sms-item-text-num {
                    margin: 4px 0 6px;
                    font-size: 14px;
                    // font-weight: 500;
                }
            }
        }

        .sms-img {
            width: 40px;
        }
    }
    .searchArea {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .searchText {
            margin: 0 12px;
            font-size: 14px;
            color: #1989fa;
        }

        .searchIcon {
            color: #1989fa;
        }
    }

    .item-content {
        // height: 150px;
        // display: flex;
        // height: 50px;
        border-bottom: 1px solid #eee;
        // align-items: center;
        // justify-content: space-between;
        // padding: 10px;
        margin: 10px 0;
        background-color: #fff;
        // box-shadow: -2px -2px 8px rgba(0, 0, 0, .2);
        border-radius: 6px;
        color: #171A1D;

        .item-content-userInfo {
            // display: flex;
            align-items: center;
            padding: 10px;
            // background-image: repeating-linear-gradient(45deg, #d6f4fa, #d5faf2);
            border-bottom: 1px solid #eee;

            .item-content-userInfo-userName {
                width: 48%;
                margin-right: 2%;
                margin-bottom: 5px;
                font-size: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .item-content-userInfo-compName {
            font-size: 16px;
            // width: 50%;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 3px;
        }

        .item-content-data {
            padding: 8px;

            .data-item {
                display: flex;
                margin: 4px;

                .data-title {
                    width: 100px;
                    // font-weight: bold;
                    color: #666;
                }
            }
        }
    }

    
    .backToTop {
        position: fixed;
        bottom: 20px;
        right: 20px;
    }
}
</style>