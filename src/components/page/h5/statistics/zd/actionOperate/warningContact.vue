<template>
  <div>
    <van-sticky>
      <van-nav-bar
        :title="title"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
      />
    </van-sticky>
    <div>
      <van-form validate-first @failed="onFailed" @submit="submitForm">
        <van-cell-group>
          <van-field
            label="姓名"
            v-model="form.linkmanName"
            clearable
            name="姓名"
            placeholder="姓名"
            :rules="[{ required: true, message: '请填写姓名' }]"
          />
        </van-cell-group>
        <van-cell-group>
          <van-field
            label="手机号码"
            v-model="form.linkmanPhone"
            clearable
            name="手机号码"
            placeholder="手机号码"
            :rules="[{ required: true, message: '请填写手机号码' }]"
          />
        </van-cell-group>
        <van-cell-group v-if="userInfo.roleId == '14' && userInfo.roleId == 2">
          <van-field
            name="radio"
            label="接收预警通知"
            :rules="[{ required: true, message: '请选择是否接收预警通知' }]"
          >
            <template slot="input">
              <van-radio-group
                v-model="form.warningRemind"
                direction="horizontal"
              >
                <van-radio name="2">是</van-radio>
                <van-radio name="1">否</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
        <van-cell-group>
          <van-field
            name="radio"
            label="人工审核通知"
            :rules="[{ required: true, message: '请选择是否人工审核通知' }]"
          >
            <template slot="input">
              <van-radio-group
                v-model="form.auditRemind"
                direction="horizontal"
              >
                <van-radio name="2">是</van-radio>
                <van-radio name="1">否</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
        <van-cell-group>
          <van-field
            name="radio"
            label="余额变更&不足通知"
            :rules="[
              { required: true, message: '请选择是否余额变更&不足通知' },
            ]"
          >
            <template slot="input">
              <van-radio-group
                v-model="form.balanceRemind"
                direction="horizontal"
              >
                <van-radio name="2">是</van-radio>
                <van-radio name="1">否</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
        <div style="margin-top: 18px">
          <van-button
            native-type="submit"
            style="width: 100px; margin-left: 18px"
            type="primary"
            size="small"
            block
            >提交</van-button
          >
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "warningContact",
  data() {
    return {
      title: "添加通知预警设置",
      form: {
        conLinkmanId: "",
        linkmanName: "",
        linkmanPhone: "",
        // linkmanEmail: '',
        warningRemind: "1",
        auditRemind: "2",
        balanceRemind: "1",
        category: "2",
        productType: "1",
      },
      userInfo: {},
    };
  },
  created() {
    let data = JSON.parse(localStorage.getItem("userInfo"));
    if (data) {
      this.userInfo = data;
    }
    if (this.$route.query.row) {
      this.title = "修改通知预警设置";
      let row = JSON.parse(this.$route.query.row);
      this.form.conLinkmanId = row.conLinkmanId;
      this.form.linkmanName = row.linkmanName;
      this.form.warningRemind = row.warningRemind + "";
      this.form.auditRemind = row.auditRemind + "";
      this.form.balanceRemind = row.balanceRemind + "";
      this.form.category = row.category + "";
      this.form.productType = row.productType = "";
      this.$api.get(
        this.API.cpus + "consumerclientlinkman/" + row.conLinkmanId,
        {},
        (res) => {
          if (res.code == 200) {
            this.form.linkmanPhone = res.data.linkmanPhone;
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
        }
      );
    }
  },
  methods: {
    onClickLeft() {
      this.$router.push("/mcNotificationWarning");
    },
    onFailed(msg) {
      console.log(msg);
    },
    submitForm() {
      if (this.$route.query.row) {
        this.$api.get(
          this.API.cpus +
            "consumerclientlinkman/validatePhoneExist/" +
            this.form.linkmanPhone +
            "?conLinkmanId=" +
            this.form.conLinkmanId,
          {},
          (res) => {
            if (res.code == 200) {
              this.$api.put(
                this.API.cpus + "consumerclientlinkman/update",
                this.form,
                (res) => {
                  if (res.code == 200) {
                    this.$toast.success("添加成功");
                    this.$router.push("/mcNotificationWarning");
                  } else {
                    this.$toast.fail(res.msg);
                  }
                }
              );
            } else {
              this.$toast.fail("手机号已重复，重新输入！");
            }
          }
        );
      } else {
        this.$api.get(
          this.API.cpus +
            "consumerclientlinkman/validatePhoneExist/" +
            this.form.linkmanPhone,
          {},
          (res) => {
            if (res.code == 200) {
              this.$api.post(
                this.API.cpus + "consumerclientlinkman/add",
                this.form,
                (res) => {
                  if (res.code == 200) {
                    this.$toast.success("添加成功");
                    this.$router.push("/mcNotificationWarning");
                  } else {
                    this.$toast.fail(res.msg);
                  }
                }
              );
            } else {
              this.$toast.fail("手机号已重复，重新输入！");
            }
          }
        );
      }
      //   this.$router.push("/mcNotificationWarning");
    },
  },
};
</script>

<style lang="less" scoped>
</style>