<template>
    <div class="contaner">
        <van-sticky>
            <van-nav-bar title="预警设置" left-text="返回" left-arrow @click-left="clickBack"></van-nav-bar>
        </van-sticky>

        <div class="sms-container-items">
            <div class="sms-item" v-for="(item, index) in statisList" :key="index" @click="openWarnPopup(item)">
                <div>
                    <img class="sms-img" :src="item.img" alt="">
                </div>
                <div class="sms-item-text">{{ item.clientName }}</div>
                <van-tag style="margin-top: 5px;" v-if="item.open === 1" type="success">开启</van-tag>
                <van-tag style="margin-top: 5px; background: #909399" v-if="item.open === 0">未开启</van-tag>
            </div>
        </div>

        <van-popup v-model="showWarnPopup" closeable round position="bottom" :style="{ height: '40%', background: '#f6f6f6' }">
            <div style="padding: 40px 10px 0 15px; box-sizing: border-box; overflow-y: auto;">
                <div style="margin-bottom: 10px;">{{ warnItem && warnItem.clientName }}</div>
                <transition name="fade">
                    <div class="tips-box">
                        <div>在不足 <el-input style="width:180px;"
                            v-model="NumberBalances"></el-input> 条时提醒</div>
                        <div class="basic-help" style="margin:10px 0 10px 0;"></div>
                        <div style="color: red; font-size: 12px; margin-bottom: 10px;" v-if="warnItem && warnItem.open === 1">注：请去添加告警联系人，以便正常接收余额不足通知！</div>
                        <el-row>
                            <el-button type="primary" @click="determine">确定</el-button>
                            <el-button type="danger" v-if="warnItem && warnItem.open === 1" @click="shutDownSetBalance">关闭设置</el-button>
                            <el-button type="primary" plain
                                @click="showWarnPopup = false">取消</el-button>
                        </el-row>
                    </div>
                </transition>
            </div>
        </van-popup>

        <van-popup v-model="showSetConfirm" position="bottom" close-on-click-overlay>
            <div class="popup-content">
                <h3>确认修改</h3>
                <p>你确定要继续吗？</p>
                <div class="buttons">
                    <el-button size="large" type="primary" round @click="setConfirm">确定</el-button>
                    <el-button size="large" type="primary" round plain @click="showSetConfirm = false">取消</el-button>
                </div>
            </div>
        </van-popup>

        <van-popup v-model="showCloseConfirm" position="bottom" close-on-click-overlay>
            <div class="popup-content">
                <h3>确认关闭</h3>
                <p>你确定要继续吗？</p>
                <div class="buttons">
                    <el-button size="large" type="primary" round @click="closeConfirm">确定</el-button>
                    <el-button size="large" type="primary" round plain @click="showCloseConfirm = false">取消</el-button>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script>
import { icon1, icon4, icon6, icon7, icon8, icon9, icon10 } from "@/components/page/h5/imgs.js"
import { mapState, mapMutations, mapActions } from "vuex";
import axios from "axios";
export default {
    name: 'TextMessage',
    data() {
        return {
            showSetConfirm: false, //设置确认弹窗
            showCloseConfirm: false, //关闭设置弹窗
            NumberBalances: null, //预警设置的余额条数
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            refreshing: false,//是否正在刷新
            loading: false,//是否正在加载
            list: [],
            total: 0,//总条数
            statisList: [
                {
                    clientName: "短信余额提醒",
                    img: icon4,
                    productId: "1",
                    open: 0,
                    num: 0
                },
                {
                    clientName: "彩信余额提醒",
                    img: icon4,
                    productId: "2",
                    open: 0,
                    num: 0
                },
                {
                    clientName: "视频短信余额提醒",
                    img: icon6,
                    productId: "3",
                    open: 0,
                    num: 0
                },
                {
                    clientName: "国际短信余额提醒",
                    img: icon7,
                    productId: "4",
                    open: 0,
                    num: 0
                },
                {
                    clientName: "闪验余额提醒",
                    img: icon8,
                    productId: "5",
                    open: 0,
                    num: 0
                },
                {
                    clientName: "语音验证码余额提醒",
                    img: icon9,
                    productId: "6",
                    open: 0,
                    num: 0
                },
                {
                    clientName: "语音通知余额提醒",
                    img: icon10,
                    productId: "7",
                    open: 0,
                    num: 0
                },
            ],
            icon1,icon4,icon6,icon7,icon8,icon9,icon10,
            showWarnPopup: false,
            promiseArr: [],
            warnItem: null
        }
    },
    created() {
        this.statisList.forEach(item => {
            this.promiseArr.push(
                axios.get(this.API.recharge + 'client/balance/notice/info', { params: {username: this.consumerName, productId: item.productId } })
            )
        })
    },
    computed: {
        ...mapState({  //比如'movies/hotMovies
            compName: state => state.compName,
            consumerName: state => state.userName,
            createTime: state => state.createLocalDateTime,
            userID: state => state.userId,
            roleId: state => state.roleId,
        })
    },
    methods: {
        // 返回
        clickBack() {
            this.$router.push('/mcHome');
        },

        // 打开预警设置弹窗
        openWarnPopup(item) {
            this.showWarnPopup = true
            if (item.open === 1) {
                this.NumberBalances = item.num
            } else {
                this.NumberBalances = null
            }
            this.warnItem = item
        },

        //点击确定
        determine() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances)) {
                this.showSetConfirm = true
            } else {
                this.$toast('请填写有效余额条数');
            }
        },

        // 确认设置弹窗
        setConfirm() {
            this.$api.post(this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances, productId: this.warnItem.productId, username: this.consumerName }, res => {
                if (res.code === 200) {
                    this.showSetConfirm = false
                    this.showWarnPopup = false
                    this.statisList[Number(this.warnItem.productId) - 1].open = res.data.open
                    this.statisList[Number(this.warnItem.productId) - 1].num = res.data.num
                    this.$toast('设置成功')
                } else {
                    this.$toast('设置失败')
                }
            })
        },

        // 关闭设置弹窗
        shutDownSetBalance() {
            this.showCloseConfirm = true
        },

        // 确认关闭设置
        closeConfirm() {
            this.$api.post(this.API.recharge + 'client/balance/notice/close', { productId: this.warnItem.productId, username: this.consumerName }, res => {
                if (res.code === 200) {
                    this.showCloseConfirm = false
                    this.showWarnPopup = false
                    this.statisList[Number(this.warnItem.productId) - 1].open = res.data.open
                    this.statisList[Number(this.warnItem.productId) - 1].num = res.data.num
                    this.$toast('关闭成功')
                } else {
                    this.$toast('关闭失败')
                }
            })
        },
    },
    watch: {
        promiseArr: {
            handler(newV) {
                if (newV.length === 7) {
                    Promise.all(newV).then(res => {
                        res.forEach((item, index) => {
                            this.statisList[index].open = item.data.data.open
                            this.statisList[index].num = item.data.data.num
                        })
                    })
                }
            },
            deep: true
        },

        showWarnPopup: {
            handler(newV) {
                if (!newV) {
                    this.NumberBalances = null
                    this.warnItem = null
                }
            }
        }
    }
}
</script>

<style lang="less">
.van-search {
    background: none !important;
    padding: 0 0 0 16px !important;
    overflow: hidden;
    width: calc(100% - 80px);
    box-sizing: border-box;

    .van-search__content {
        background: #fff !important;
        // border-radius: 16rpx;
        // height: 80rpx;
        // line-height: 80rpx;
    }
    
}
</style>

<style lang="less" scoped>
.contaner {
    padding: 0;
    // background-color: #f6f6f6;
    .bigTitle {
        font-size: 18px;
        color: #000;
        font-weight: bold;
        padding: 10px 30px;
    }
    .sms-container-items {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        // flex-shrink: 1;
        flex-wrap: wrap;
        margin-bottom: 20px;

        .sms-item {
            width: 32%;
            height: 85px;
            // margin: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin: 20px 0 10px;
            cursor: pointer;

            .sms-item-text {
                text-align: center;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 12px;
                color: #171A1D;
                word-wrap: break-word;
                margin-top: 8px;
            }
        }

        .sms-img {
            width: 40px;
        }
    }
    .searchArea {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .searchText {
            margin: 0 12px;
            font-size: 14px;
            color: #1989fa;
        }

        .searchIcon {
            color: #1989fa;
        }
    }

    .item-content {
        // height: 150px;
        // display: flex;
        // height: 50px;
        border-bottom: 1px solid #eee;
        // align-items: center;
        // justify-content: space-between;
        // padding: 10px;
        margin: 10px 0;
        background-color: #fff;
        // box-shadow: -2px -2px 8px rgba(0, 0, 0, .2);
        border-radius: 6px;
        color: #171A1D;

        .item-content-userInfo {
            // display: flex;
            align-items: center;
            padding: 10px;
            // background-image: repeating-linear-gradient(45deg, #d6f4fa, #d5faf2);
            border-bottom: 1px solid #eee;

            .item-content-userInfo-userName {
                width: 48%;
                margin-right: 2%;
                margin-bottom: 5px;
                font-size: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .item-content-userInfo-compName {
            font-size: 16px;
            // width: 50%;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 3px;
        }

        .item-content-data {
            padding: 8px;

            .data-item {
                display: flex;
                margin: 4px;

                .data-title {
                    width: 100px;
                    // font-weight: bold;
                    color: #666;
                }
            }
        }
    }

    
    .backToTop {
        position: fixed;
        bottom: 20px;
        right: 20px;
    }
}

.tips-box {
    position: absolute;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    border: 1px solid #e6e6e6;
}

.popup-content {
  padding: 20px;
  text-align: center;
  background-color: #fff;
}
h3 {
  margin-bottom: 10px;
  font-size: 18px;
  color: #333;
}
p {
  margin-bottom: 20px;
  color: #666;
}
.buttons {
  display: flex;
  justify-content: space-around;
}
</style>