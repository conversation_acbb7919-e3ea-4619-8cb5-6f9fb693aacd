<template>
  <div>
    <van-sticky>
      <van-nav-bar
        title="通知预警设置"
        left-text="返回"
        left-arrow
        @click-left="onClickLeft"
      />
    </van-sticky>

    <div class="user-list">
      <div class="user-btn">
        <van-button class="create-btn" type="info" size="small" @click="addUser"
          >添加告警联系人</van-button
        >
        <span style="color: #f56c6c; font-size: 12px; margin-left: 5px"
          >最多可添加5个指定联系人</span
        >
      </div>
      <div v-if="list.length == 0">
        <van-empty image="error" description="暂无数据" />
      </div>
      <div v-else>
        <div v-for="(item, index) in list" :key="index">
          <van-swipe-cell :right-width="150">
            <div class="item-content">
              <div class="user-item">
                <div class="user-name">{{ item.linkmanName }}</div>
                <div class="user-company">{{ item.linkmanPhone }}</div>
              </div>
              <div class="user-item">
                <div>
                  审核通知：
                  <span style="color: #67c23a" v-if="item.auditRemind == '2'"
                    >Y</span
                  >
                  <span style="color: #f56c6c" v-else>N</span>
                </div>
                <div>
                  余额变更&不足通知：
                  <span style="color: #67c23a" v-if="item.balanceRemind == '2'"
                    >Y</span
                  >
                  <span style="color: #f56c6c" v-else>N</span>
                </div>
              </div>
            </div>
            <van-button
              square
              slot="right"
              type="warning"
              size="normal"
              text="编辑"
              @click="editUser(item)"
            />
            <van-button
              square
              slot="right"
              type="danger"
              size="normal"
              text="删除"
              @click="delAllS(item)"
            />
          </van-swipe-cell>
        </div>
      </div>
    </div>
    <van-action-sheet v-model="delShow" title="此操作将永久删除该数据">
      <div class="van-action-sheet__content">
        <van-button round block type="default" @click="delShow = false"
          >取消</van-button
        >
        <van-button
          style="margin-left: 10px"
          round
          block
          type="danger"
          @click="subimtDel"
          >删除</van-button
        >
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import { Dialog } from "vant";
import { tagColor } from "../../../../utils/tagColor";
export default {
  components: {
    [Dialog.Component.name]: Dialog.Component,
  },

  data() {
    return {
      list: [], //列表数据
      delShow: false, //删除弹窗
      conLinkmanId: "", //联系人id
      //   delShow: false, //删除弹窗
      //   detailShow: false, //详情弹窗
      //   finished: false, //是否已加载完成，加载完成后不再触发load事件
      //   loading: false, //是否正在加载
      //   refreshing: false, //是否正在刷新
      //   isDateState: null, //是否开启数据
      //   total: 0, //总条数
      //   userName: "", //搜索用户名
      //   tabelAlllist: {
      //     userName: "",
      //     userStatus: "0",
      //     currentPage: 0,
      //     pageSize: 10,
      //   },
      //   detailList: {}, //详情弹窗数据
      //   tagColor: [],
    };
  },
  created() {
    this.tagColor = tagColor;
    this.getUserList();
  },
  methods: {
    onClickLeft() {
      this.$router.push("/mcHome");
    },
    //获取列表
    getUserList() {
      this.$api.get(this.API.cpus + "consumerclientlinkman/page", {}, (res) => {
        if (res.code == 200) {
          this.list = res.data;
        }
      });
    },
    addUser() {
      if (this.list.length >= 5) {
        this.$toast("最多可添加5个指定联系人");
        return;
      }
      this.$router.push("/mcWarningContact");
    },
    editUser(row){
        this.$router.push({
          path: "/mcWarningContact",
          query: {
            row: JSON.stringify(row),
          },
        })
    },
    delAllS(row) {
      this.delShow = true;
      this.conLinkmanId = row.conLinkmanId;
    },
    //确认删除
    subimtDel() {
      this.$api.delete(
        this.API.cpus + "consumerclientlinkman/" + this.conLinkmanId,
        {},
        (res) => {
          if (res.code == 200) {
            this.$toast.success("删除成功");
            this.delShow = false;
            this.getUserList();
          } else {
            this.$toast.fail("删除失败");
          }
        }
      );
    },
  },
};
</script>

<style lang="less" scoped>
.user-list {
  margin: 18px;
}

.item-content {
  display: flex;
  // height: 50px;
  border-bottom: 1px solid #eee;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  margin: 10px 0;
}

.user-item {
  //   width: 95px;
  line-height: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.close-btn {
  width: 55px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-name {
  font-size: 16px;
  font-weight: bold;
}

.user-company {
  font-size: 12px;
}

.user-btn {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.create-btn {
  width: 120px;
}

.search-status {
  flex: 1;
}

.van-action-sheet__content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 18px 18px;
}

.balanceLists {
  padding: 0 18px;
  display: flex;
  // justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  overflow: auto;
}

.van-popup__title {
  padding: 18px;
  margin-left: 10px;
}

.van-popup__company {
  padding: 0 18px;
  margin-left: 10px;
}

// .user-input {
//     display: flex;
//     align-items: center;
// }

// .search-btn {
//     width: 100px;
//     margin-left: 10px;
// }
</style>