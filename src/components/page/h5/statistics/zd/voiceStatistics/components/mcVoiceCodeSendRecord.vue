<template>
    <div class="contaner">
        <van-sticky>
            <van-nav-bar title="发送详情" left-text="返回" left-arrow @click-left="clickBack"></van-nav-bar>
        </van-sticky>
        <div class="searchArea">
            <van-search v-model="tabelAlllist.mobile" shape="round" @clear="onSearch" background="#4fc08d" placeholder="请输入手机号码" />
            <div class="searchText" @click="onSearch">查询</div>
            <van-icon @click="showBottom = true" class="searchIcon" name="filter-o" />
        </div>
        <!-- <div class="user-btn">
                <van-button class="create-btn" type="primary" size="small" @click="CreateUser">创建用户</van-button>
                <div class="search-status">
                    <van-tabs v-model="tabelAlllist.userStatus" @click="onSearch">
                        <van-tab title="全部" name=""></van-tab>
                        <van-tab title="启用" name="0"></van-tab>
                        <van-tab title="停用" name="1"></van-tab>
                    </van-tabs>
                </div>

            </div> -->
        <van-tabs style="margin-top: 10px;" v-model="tabelAlllist.status" @change="onSearch">
            <van-tab title="全部" name=""></van-tab>
            <van-tab title="成功" name="1"></van-tab>
            <van-tab title="失败" name="2"></van-tab>
            <van-tab title="待返回" name="3"></van-tab>
        </van-tabs>
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" style="min-height: calc(100vh - 145px); padding: 0 16px;">
            <van-list v-model="loading" :finished="finished" :finished-text="`总共${total}条数据,没有更多了`" @load="onLoad" v-loading="tabelLoading">

                <div v-for="(item, index) in list" :key="index">
                    <div class="item-content" @click="showDetail(item)">
                        <div class="item-content-userInfo">
                            <div class="item-content-userInfo-userName">
                                <div class="item-content-userInfo-userName-text">{{ item.msgid }}</div>
                                <van-tag v-if="item.status === 1" type="success">成功</van-tag>
                                <van-tag v-if="item.status === 2" type="danger">失败</van-tag>
                                <van-tag v-if="item.status === 3" color="#909399">待返回</van-tag>
                            </div>
                            <div class="item-content-userInfo-compName">
                                {{ item.content }}
                            </div>
                        </div>
                        <div class="item-content-data">
                            
                            <div style="display: flex;">
                                <div>
                                    <div class="data-item">
                                        <div class="data-title">手机号码</div>
                                        <div class="data-value" style="color: rgb(22, 165, 137)" @click.stop="handPhone(item, index)">{{ item.mobile }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">发送时间</div>
                                        <div class="data-value">{{ item.sendTime }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">状态上报时间</div>
                                        <div class="data-value">{{ item.reportTime }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">播报次数</div>
                                        <div class="data-value">{{ item.playNum }}</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-title">发送计费数</div>
                                        <div class="data-value">{{ item.chargeNum }}</div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </van-list>
        </van-pull-refresh>
        <van-popup v-model="showBottom" closeable round position="bottom" :style="{ height: '40%', background: '#f6f6f6' }">
            <div class="van-popup-title">
                
                <div style="display: flex;">
                    <van-field :value="tabelAlllist.sendBeginTime" placeholder="开始日期"
                        @click="hidePickerShowPicker('start')" />
                    <van-field style="margin-left: 10px;" :value="tabelAlllist.sendEndTime" placeholder="结束日期"
                        @click="hidePickerShowPicker('end')" />
                </div>
                <div style="margin-top: 10px;">
                    <van-button style="width: 100px;" round type="info" @click="onSearch">查询</van-button>
                    <van-button style="width: 100px; margin-left: 10px;" round type="info" plain @click="handelReset">重置</van-button>
                </div>
            </div>
        </van-popup>
        <van-popup v-model="showPicker" position="bottom">
            <van-datetime-picker type="date" v-model="currentDate" @confirm="onConfirm" @cancel="showPicker = false" />
        </van-popup>
        <van-popup v-model="showMsgDetail" closeable round position="bottom" :style="{ height: '40%', background: '#f6f6f6' }">
            <div style="padding: 40px; box-sizing: border-box; overflow-y: auto;">
                <h4 style="font-size: 16px; margin-bottom: 10px;">发送内容详情：</h4>
                <span style="text-indent: 2em;">{{ msgDetail || "" }}</span>
            </div>
        </van-popup>
    </div>
</template>

<script>
import { tagColor } from '@/components/page/utils/tagColor';
import { icon4 } from "@/components/page/h5/imgs.js"
import moment from 'moment'
import common from "@/assets/js/common";

export default {
    name: 'TextMessage',
    data() {
        return {
            showMsgDetail: false,
            msgDetail: "",
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            refreshing: false,//是否正在刷新
            loading: false,//是否正在加载
            list: [],
            total: 0,//总条数
            //统计数据
            tabelAlllist: {//搜索条件
                mobile: "",
                sendBeginTime: moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
                sendEndTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                currentPage: 0,
                pageSize: 10,
                status: "",
                isDownload: 2,
                type: "1",
            },
            timeTpey: "",
            currentDate: new Date(),
            tagColor: [],
            // step: 0,
            isDateState: 1,
            showBottom: false,
            showPicker: false,
            searchValue: "",
            icon4,
            tabelLoading: false
        }
    },
    created() {
        this.isDateState = JSON.parse(localStorage.getItem("userInfo")).isDateState
        this.tagColor = tagColor;
    },
    methods: {
        // 查看详情
        showDetail(item) {
            console.log("查看详情", item)
            this.msgDetail = item.content
            this.showMsgDetail = true
        },
        // 解密手机号
        handPhone(val, index) {
            // console.log(index);
            // console.log(this.tableDataObj.tableData);
            this.$api.post(
                this.API.upms + "/generatekey/decryptMobile",
                {
                    keyId: val.keyId,
                    smsInfoId: val.smsInfoId,
                    cipherMobile: val.cipherMobile,
                },
                (res) => {
                    if (res.code == 200) {
                        this.list[index].mobile = res.data;
                        // this.resetVideo = true;
                    } else if (res.code == 4004002) {
                        common.fetchData().then((res) => {
                            if (res.code == 200) {
                                if (res.data.isAdmin == 1) {
                                    this.$toast('您今日解密次数已超限，需重置解密次数！');
                                    this.$router.push("/resetNumberH5")
                                } else {
                                    this.$toast('您今日解密次数已超限，如需重置解密次数，请联系管理员！');
                                }
                            } else {
                                this.$toast.fail(res.msg);
                            }

                        })
                    } else {
                        this.$message({
                        message: res.msg,
                        type: "warning",
                        });
                    }
                // this.tableDataObj.tableData[index].mobile=res.data
                }
            );
            // console.log(val);
        },
        onSearch() {
            this.tabelAlllist.currentPage = 1
            this.loading = true;
            this.finished = false
            this.showBottom = false
            this.getUserList()
        },
        //上拉加载
        onLoad() {
            console.log("上拉加载")
            this.loading = true;
            this.tabelAlllist.currentPage++
            this.getUserList()
        },
        //下拉刷新
        onRefresh() {
            console.log("下拉刷新")
            this.list = []
            this.tabelAlllist.currentPage = 0
            this.refreshing = false;
            this.finished = false
            // this.step++
            this.getUserList()
        },
        //获取用户列表
        getUserList() {
            this.tabelLoading = true
            if (this.tabelAlllist.currentPage == 0 || this.list.length === 0)  this.tabelAlllist.currentPage = 1
            this.$api.post(
                this.API.cpus + "consumervoicemessage/messages",
                this.tabelAlllist,
                (res) => {
                    this.tabelLoading = false
                    if (res.code == 200) {
                        this.total = res.data.total;
                        if (res.data.records.length == 0) { //没有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.finished = true; //加载完成
                            } else { //是第一页，下拉刷新
                                this.list = []; //刷新列表
                                this.finished = true;
                                this.$toast.fail('暂无数据');
                            }
                            this.loading = false; //加载完成
                        } else { //有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.list = [...this.list, ...res.data.records] //数组合并
                            } else { //是第一页，下拉刷新
                                this.list = res.data.records //刷新列表
                                this.refreshing = false; //刷新完成
                            }
                            if (this.list.length == res.data.total) { //没有更多数据
                                this.finished = true; //加载完成
                            }
                            this.loading = false; //加载完成
                        }
                    }
                }
            );
        },
        // 返回
        clickBack() {
            this.$router.push('/mcHome');
            // this.showBottom = true
        },
        hidePickerShowPicker(type) {
            this.showPicker = true
            this.timeTpey = type
        },
        onConfirm(date) {
            if (this.timeTpey == "start") {
                this.tabelAlllist.sendBeginTime = moment(date).format('YYYY-MM-DD 00:00:00')
            } else {
                this.tabelAlllist.sendEndTime = moment(date).format('YYYY-MM-DD 23:59:59')
            }
            this.showPicker = false
        },
        handelReset() {
            this.tabelAlllist.mobile = ""
            this.tabelAlllist.sendBeginTime = moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss')
            this.tabelAlllist.sendEndTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
    }
}
</script>

<style lang="less">
.van-search {
    background: none !important;
    padding: 0 0 0 16px !important;
    overflow: hidden;
    width: calc(100% - 80px);
    box-sizing: border-box;

    .van-search__content {
        background: #fff !important;
        // border-radius: 16rpx;
        // height: 80rpx;
        // line-height: 80rpx;
    }

}
</style>

<style lang="less" scoped>
.contaner {
    padding: 0;
    background-color: #f6f6f6;

    .searchArea {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .searchText {
            margin: 0 12px;
            font-size: 14px;
            color: #1989fa;
        }

        .searchIcon {
            color: #1989fa;
        }
    }

    .item-content {
        // height: 150px;
        // display: flex;
        // height: 50px;
        border-bottom: 1px solid #eee;
        // align-items: center;
        // justify-content: space-between;
        // padding: 10px;
        margin: 10px 0;
        background-color: #fff;
        // box-shadow: -2px -2px 8px rgba(0, 0, 0, .2);
        border-radius: 6px;
        color: #171A1D;

        .item-content-userInfo {
            // display: flex;
            align-items: center;
            padding: 10px;
            // background-image: repeating-linear-gradient(45deg, #d6f4fa, #d5faf2);
            border-bottom: 1px solid #eee;

            .item-content-userInfo-userName {
                width: 100%;
                margin-right: 2%;
                margin-bottom: 5px;
                font-size: 22px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .item-content-userInfo-userName-text {
                    max-width: 258px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }

        .item-content-userInfo-compName {
            font-size: 14px;
            // width: 50%;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 3px;
        }

        .item-content-data {
            padding: 8px;

            .data-item {
                display: flex;
                margin: 4px;

                .data-title {
                    width: 100px;
                    // font-weight: bold;
                    color: #666;
                }
            }
        }
    }
}

.van-popup-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40px;
}

/deep/ .van-cell {
    border-radius: 64px;
    width: 180px;
}

.input {
    background-color: #05060f0a;
    border-radius: .5rem;
    padding: 0 1rem;
    border: 2px solid transparent;
    font-size: 1rem;
    transition: border-color .3s cubic-bezier(.25,.01,.25,1) 0s, color .3s cubic-bezier(.25,.01,.25,1) 0s,background .2s cubic-bezier(.25,.01,.25,1) 0s;
    width: 100%;
    box-sizing: border-box;
    height: 35px;
}

.label {
  display: block;
  margin-bottom: .3rem;
  font-size: .9rem;
  font-weight: bold;
  color: #05060f99;
  transition: color .3s cubic-bezier(.25,.01,.25,1) 0s;
}

.input-group {
    width: 95%;
    margin-top: 10px;
}

.input:hover, .input:focus, .input-group:hover .input {
  outline: none;
  border-color: #05060f;
}

.input-group:hover .label, .input:focus {
  color: #05060fc2;
}
</style>