<template>
    <div class="contaner">
        <van-sticky>
            <van-nav-bar title="语音验证码明细" left-text="返回" left-arrow @click-left="clickBack"></van-nav-bar>
        </van-sticky>
        <div>
            <div class="sms-container-items">
                <div class="sms-item" v-for="(item, index) in statisList" :key="index">
                    <div>
                        <img class="sms-img" :src="icon4" alt="" @click="$router.push({path:item.path})">
                    </div>
                    <div class="sms-item-text">
                        <div  class="sms-item-text-num">{{item.clientName}}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {icon4} from "@/components/page/h5/imgs.js"
export default {
    name: 'TextMessage',
    data() {
        return {
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            refreshing: false,//是否正在刷新
            loading: false,//是否正在加载
            list: [],
            total: 0,//总条数
            statisList: [
                {
                    clientName: "发送详情",
                    path:"/mcVoiceSendRecord",
                    id: 1
                }
            ],
            icon4
        }
    },
    created() {
        
    },
    methods: {
        // 返回
        clickBack() {
            this.$router.push('/mcHome');
            // this.showBottom = true
        }
    }
}
</script>

<style lang="less">
.van-search {
    background: none !important;
    padding: 0 0 0 16px !important;
    overflow: hidden;
    width: calc(100% - 80px);
    box-sizing: border-box;

    .van-search__content {
        background: #fff !important;
        // border-radius: 16rpx;
        // height: 80rpx;
        // line-height: 80rpx;
    }
    
}
</style>

<style lang="less" scoped>
.contaner {
    padding: 0;
    // background-color: #f6f6f6;
    .sms-container-items {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        // flex-shrink: 1;
        flex-wrap: wrap;

        .sms-item {
            width: 32%;
            height: 85px;
            // margin: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            margin: 20px 0 10px;
            cursor: pointer;

            .sms-item-text {
                text-align: center;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 12px;
                color: #171A1D;
                .sms-item-text-num {
                    margin: 4px 0 6px;
                    font-size: 14px;
                    // font-weight: 500;
                }
            }
        }

        .sms-img {
            width: 40px;
        }
    }
    .searchArea {
        display: flex;
        align-items: center;
        margin-top: 10px;

        .searchText {
            margin: 0 12px;
            font-size: 14px;
            color: #1989fa;
        }

        .searchIcon {
            color: #1989fa;
        }
    }

    .item-content {
        // height: 150px;
        // display: flex;
        // height: 50px;
        border-bottom: 1px solid #eee;
        // align-items: center;
        // justify-content: space-between;
        // padding: 10px;
        margin: 10px 0;
        background-color: #fff;
        // box-shadow: -2px -2px 8px rgba(0, 0, 0, .2);
        border-radius: 6px;
        color: #171A1D;

        .item-content-userInfo {
            // display: flex;
            align-items: center;
            padding: 10px;
            // background-image: repeating-linear-gradient(45deg, #d6f4fa, #d5faf2);
            border-bottom: 1px solid #eee;

            .item-content-userInfo-userName {
                width: 48%;
                margin-right: 2%;
                margin-bottom: 5px;
                font-size: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .item-content-userInfo-compName {
            font-size: 16px;
            // width: 50%;
            color: #666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-top: 3px;
        }

        .item-content-data {
            padding: 8px;

            .data-item {
                display: flex;
                margin: 4px;

                .data-title {
                    width: 100px;
                    // font-weight: bold;
                    color: #666;
                }
            }
        }
    }

    
    .backToTop {
        position: fixed;
        bottom: 20px;
        right: 20px;
    }
}
</style>