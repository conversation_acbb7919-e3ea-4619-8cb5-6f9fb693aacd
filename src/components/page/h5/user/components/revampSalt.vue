<template>
    <div class="newPhone">
        <van-nav-bar title="修改加密盐" left-text="返回" left-arrow @click-left="onClickLeft" />
        <div class="phone-input">
            <!-- <van-steps :active="active" active-icon="success" active-color="#38f">
                <van-step>手机号验证</van-step>
                <van-step>修改加密盐</van-step>
            </van-steps> -->
            <div>
                <div class="phone-input-content">
                    <!-- <van-radio-group v-model="mobile" @change="onRadioChange">
                        <van-radio v-for="(item, index) in loginPhoneList" :key="index + 'b'" :label="item.mobile"
                            :name="item.mobile">
                            <van-cell :border="false" :title="item.consumerName" :value="item.maskMobile" />
                        </van-radio>
                    </van-radio-group> -->
                    验证码将会发送至您的手机号：{{ loginInfo.mobile }}，请注意查收！
                </div>
                <div>
                    <van-field v-model="smsCode" required center clearable label="短信验证码" placeholder="请输入短信验证码">
                        <template slot="button">
                            <van-button v-if="nmb == 120" size="small" type="primary" @click="getCaptcha">发送验证码</van-button>
                            <van-button v-else disabled size="small" type="primary">重新发送({{ nmb }}s)</van-button>
                        </template>
                    </van-field>
                </div>
            </div>
            <div>
                <van-form validate-first @failed="onFailed" @submit="submitForm">
                    <van-cell-group>
                        <van-field v-model="salt" name="validator" clearable label="加密盐"
                            :rules="[{ required: true, message: '' }]" placeholder="请输入加密盐"></van-field>
                    </van-cell-group>
                    <div style="display: flex;justify-content: center;margin-top: 18px;">
                        <!-- <van-button style="width: 100px;" plain hairline type="info" size="small" block
                            @click="back">上一步</van-button> -->
                        <van-button native-type="submit" style="width: 100px;margin-left: 18px;" type="primary" size="small"
                            block>提交</van-button>
                    </div>
                </van-form>
            </div>

        </div>
    </div>
</template>

<script>
export default {
    name: 'newPhone',
    data() {
        return {
            active: 0,
            mobile: '',// 手机号
            salt: "",// 加密盐
            show: false,
            value: '',
            smsCode: '',// 短信验证码
            loginPhoneList: [],// 登录手机列表
            // pattern: !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g,
            nmb: 120,
            loginInfo: {
                isAdmin: null,
                consumerName: "",
                mobile: "",
                remark: "",
                id: ""
            },
        }
    },
    created() {
        // this.getLoginPhone()
        this.getLoginInfo()
    },
    methods: {
        getLoginInfo() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/loginPhoneInfo",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginInfo = res.data;
                    }
                }
            );
        },
        onClickLeft() {
            this.$router.push({ path: '/userInfo' });
        },
        // 登录手机列表
        // getLoginPhone() {
        //     this.$api.post(
        //         this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        //         {},
        //         (res) => {
        //             if (res.code == 200) {
        //                 this.loginPhoneList = res.data.data
        //             }
        //         })
        // },
        // onRadioChange(val) {
        //     console.log(val, 'val');
        // },
        // 发送验证码
        getCaptcha() {
            this.$api.get(this.API.cpus + "userLoginAdmin/sendVerificationCode?flag=1&phoneId=" + this.loginInfo.id, {}, res => {
                if (res.code == 200) {
                    this.$toast.success('验证码已发送，请注意查收！');
                    --this.nmb;
                    this.timer = setInterval(res => {
                        --this.nmb;
                        if (this.nmb < 1) {
                            this.nmb = 120;
                            clearInterval(this.timer);
                        };
                    }, 1000);
                } else {
                    this.$toast.fail(res.msg);
                }

            })
        },
        // 下一步
        // subNext() {
        //     if (this.smsCode) {
        //         this.$api.get(
        //             this.API.cpus +
        //             "code/checkVerificationCode?code=" +
        //             this.smsCode +
        //             "&flag=1",
        //             {},
        //             (res) => {
        //                 if (res.code == 200) {
        //                     this.active = 1;
        //                 } else {
        //                     this.$toast.fail('验验证码无效！');
        //                 }
        //             })
        //     } else {
        //         this.$toast.fail('请输入短信验证码');
        //     }
        // },
        // back() {
        //     this.active = 0;
        // },
        onFailed(errorInfo) {
            console.log('failed', errorInfo);
        },
        // 提交表单
        submitForm() {
            if(!this.smsCode){
                this.$toast.fail('请输入短信验证码');
                return false;
            }
            let data = {
                flag: 1,
                verifyCode: this.smsCode,
                salt: this.salt,
            }
            this.$api.put(
                this.API.cpus +
                "/consumerclientinfo/saltV2",
                data,
                (res) => {
                    if (res.code == 200) {
                        this.$toast.success('加密盐修改成功！');
                        this.$router.push({ path: '/glsUser' });
                    } else {
                        this.$toast.fail('加密盐修改失败！');
                    }
                })
        },
    }
}
</script>

<style lang="less" scoped>
.phone-input {
    margin: 18px;
}

.phone-input-title {
    display: flex;
    align-items: center;
}

/deep/.van-radio__label {
    width: 100%;
}
</style>