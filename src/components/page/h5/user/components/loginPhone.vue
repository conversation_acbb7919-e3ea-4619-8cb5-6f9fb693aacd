<template>
    <div class="loginPhone-wrap">
        <van-nav-bar title="登录手机号管理" left-text="返回" left-arrow @click-left="onClickLeft" />
        <div class="loginPhone-content">

            <div class="loginPhone">
                <div class="loginPhone-title">
                    <div>温馨提示</div>
                    <div>1.默认登录手机号为您注册账号时的手机号。</div>
                    <div>
                        2.该登录手机号至多可添加10个，至少一个。当只有一个手机号时不允许删除！
                    </div>
                </div>
                <div class="loginPhone-btn">
                    <van-button size="small" v-if="loginInfo.isAdmin == 1" type="info" @click="addPhone">添加手机号</van-button>
                </div>
                <div v-for="(item, index) in loginPhoneList" :key="index + 'a'">
                    <van-swipe-cell :right-width="120" :left-width="90">
                        <!-- <van-cell :border="false" :title="item.consumerName" :value="item.mobile" /> -->
                        <div style="display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 10px 0;
                        border-bottom: 1px solid #e5e5e5;
                        ">
                            <div class="loginPhone-item">
                                <i v-if="item.isAdmin == 1" style="color: #409eff;" class="iconfont icon-yonghuming"></i>
                                {{ item.consumerName }}
                            </div>
                            <div class="loginPhone-item">{{ item.mobile }}

                            </div>
                            <div class="loginPhone-item">{{ item.remark }}</div>
                        </div>
                        <van-button v-if="loginInfo.isAdmin == 1" square slot="right" type="warning" text="编辑"
                            @click="editphone(item)" />
                        <van-button v-if="loginInfo.isAdmin == 1 && item.isAdmin != 1" square slot="right" type="danger"
                            text="删除" @click="deleteLoginPhone(item)" />
                        <van-button v-if="loginInfo.isAdmin == 1 && item.isAdmin != 1" square slot="left" type="info"
                            @click="transferAdmin(item)">管理员转让</van-button>
                    </van-swipe-cell>
                </div>

                <!-- <van-cell-group>
                <van-cell title="手机号" value="1" />
                <van-cell title="验证码" value="2" />
            </van-cell-group> -->

            </div>
        </div>
        <!-- <van-dialog v-model="show" :title="title" show-cancel-button @confirm="handleConfirm">
            
        </van-dialog> -->
        <van-action-sheet v-model="show" :title="title">
            <van-field v-if="title != '编辑'" v-model="delphone.verifyCode" required center clearable label="短信验证码"
                placeholder="请输入短信验证码">
                <template slot="button">
                    <van-button v-if="nmb == 120" size="small" type="primary" @click="getCaptcha">发送验证码</van-button>
                    <van-button v-else disabled size="small" type="primary">重新发送({{ nmb }}s)</van-button>
                </template>
            </van-field>
            <van-field v-else v-model="delphone.remark" required center clearable label="备注" placeholder="备注">
            </van-field>
            <div style="display: flex;justify-content: center;align-items: center;margin: 10px; 0">
                <van-button style="width: 100px;" type="default" @click="show = false">取消</van-button>
                <van-button style="width: 100px;margin-left: 10px;" type="primary" @click="handleConfirm">{{
                    title == '删除手机号' ? '确认删除' : title == '管理员转让' ? '确认转让' : '确认编辑' }}</van-button>
            </div>
        </van-action-sheet>
    </div>
</template>

<script>
import { Dialog } from 'vant';
export default {
    name: 'loginPhone',
    data() {
        return {
            loading: true,
            show: false,
            userInfo: {},
            title: "",
            loginPhoneList: [],
            nmb: 120,
            smsCode: "",
            loginInfo: {
                isAdmin: null,
                consumerName: "",
                mobile: "",
                remark: "",
                id: ""
            },
            codeStatus: null,
            delphone: {
                verifyCode: "",
                flag: "2",
                id: "",
                isAdmin: "",
                mobile: "",
                remark: "",
            },
            adminform: {
                destId: "",
                sourceId: "",
            },
            phoneId: ""
        };
    },
    created() {
        this.getLoginInfo()
        this.getLoginPhone();
    },
    methods: {
        // 点击左侧返回
        onClickLeft() {
            this.$router.push('/userInfo');
        },
        getLoginInfo() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/loginPhoneInfo",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginInfo = res.data;
                    }
                }
            );
        },
        // 获取登录手机号
        getLoginPhone() {
            this.$api.post(
                this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginPhoneList = res.data.data
                    }
                })
        },
        getCaptcha() {
            this.$api.get(
                this.API.cpus +
                "userLoginAdmin/sendVerificationCode?flag=2&phoneId=" + this.loginInfo.id,
                {},
                (res) => {
                    if (res.code == 200) {
                        this.$toast.success('验证码已发送，请注意查收');
                        --this.nmb;
                        const timer = setInterval((res) => {
                            --this.nmb;
                            if (this.nmb < 1) {
                                this.nmb = 120;
                                clearInterval(timer);
                            }
                        }, 1000);
                    } else {
                        this.$toast.fail('验证码未失效，需失效后重新获取!');
                    }
                })
        },
        getCodeStatus(type, obj) {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/verifiedStatus",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.codeStatus = res.data;
                        if (type == 'del') {
                            if (res.data === true) {
                                let data = {
                                    // verifyCode: this.delphone.verifyCode,
                                    flag: obj.flag,
                                    phoneId: obj.phoneId
                                }
                                this.delFun(data)
                            } else {
                                this.delphone.id = obj.phoneId;
                                // this.delphone.isAdmin = obj.isAdmin;
                                this.show = true;
                            }
                        } else if (type == 'admin') {
                            if (res.data === true) {
                                let data = {
                                    flag: this.delphone.flag,
                                    destId: obj.destId,
                                    sourceId: obj.sourceId
                                }
                                this.adminFun(data)
                            } else {
                                this.adminform.destId = obj.destId;
                                this.adminform.sourceId = obj.sourceId;
                                this.show = true;
                            }
                        }
                    }
                })
        },
        delFun(obj) {
            Dialog.confirm({
                title: '手机号删除',
                message: '确认删除该手机号',
            })
                .then(() => {
                    this.$api.post(
                        this.API.cpus + "userLoginAdmin/deleteLoginPhoneV2",
                        obj,
                        (res) => {
                            if (res.code == 200) {
                                this.$toast('删除成功！');
                                this.getLoginPhone();
                                this.show = false;
                            } else {
                                this.$toast(res.msg);
                            }
                        })
                })
                .catch(() => {
                    // on cancel
                });

        },
        adminFun(obj) {
            Dialog.confirm({
                title: '管理员转让',
                message: '确认将该手机号设置为管理员',
            })
                .then(() => {
                    this.$api.post(
                        this.API.cpus + "userLoginAdmin/transferAdmin",
                        obj,
                        (res) => {
                            if (res.code == 200) {
                                this.$toast('转让成功！');
                                this.show = false;
                                this.$nextTick(() => {
                                    this.getLoginInfo();
                                    this.getLoginPhone();
                                });
                            } else {
                                this.$toast(res.msg);
                            }
                        })
                })
                .catch(() => {
                    // on cancel
                });
        },
        handleConfirm() {
            if (this.title == "删除手机号") {
                if (this.delphone.verifyCode || this.codeStatus) {
                    let data = {
                        verifyCode: this.delphone.verifyCode,
                        flag: this.delphone.flag,
                        phoneId: this.delphone.id
                    }
                    this.delFun(data)
                } else {
                    this.$toast('请输入验证码！');
                }
            } else if (this.title == "管理员转让") {
                let data = {
                    verifyCode: this.delphone.verifyCode,
                    destId: this.adminform.destId,
                    sourceId: this.adminform.sourceId,
                    flag: this.delphone.flag,
                }
                this.adminFun(data)
            } else if (this.title == "编辑") {
                let data = {
                    phoneId: this.phoneId,
                    remark: this.delphone.remark,
                }
                this.$api.post(
                    this.API.cpus + "userLoginAdmin/updateLoginPhone",
                    data,
                    (res) => {
                        if (res.code == 200) {
                            this.show = false
                            this.getLoginPhone();
                        } else {
                            this.$toast(res.msg);
                        }
                    })
            }


        },
        // 删除登录手机号
        deleteLoginPhone(row) {
            // if (this.loginPhoneList.length == 1) {
            //     this.$toast('至少保留一个手机号！');
            //     return;
            // }
            this.title = "删除手机号";
            let data = {
                flag: 2,
                phoneId: row.id,
            }
            this.getCodeStatus('del', data)
            // this.show = true;
            // this.$api.get(
            //     this.API.cpus + "consumerclientinfo/deleteLoginPhone/" +
            //     mobile,
            //     {
            //     },
            //     (res) => {
            //         if (res.code == 200) {
            //             this.$toast('删除成功！');
            //             this.getLoginPhone();
            //         }
            //     })
        },
        editphone(row) {
            this.title = '编辑';
            this.phoneId = row.id;
            this.delphone.mobile = row.mobile;
            this.delphone.remark = row.remark || "";
            this.show = true;
        },
        transferAdmin(row) {
            let data = {
                destId: row.id,
                sourceId: ''
            }
            for (let i = 0; i < this.loginPhoneList.length; i++) {
                if (this.loginPhoneList[i].isAdmin == 1) {
                    data.sourceId = this.loginPhoneList[i].id;
                    break;
                }
            }
            this.title = '管理员转让'
            this.getCodeStatus('admin', data)
        },
        addPhone() {
            this.$router.push('/newPhone');
        },
    },

}
</script>

<style lang="less" scoped>
.loginPhone-wrap {}

.loginPhone-content {
    margin: 18px;
}

.loginPhone-title {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px;
    font-size: 12px;
}

.loginPhone-title>div {
    // height: 26px;
    line-height: 26px;
}

.loginPhone-btn {
    margin-top: 10px;
}

.loginPhone-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e5e5e5;
}

.loginPhone-item {
    width: 90px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>