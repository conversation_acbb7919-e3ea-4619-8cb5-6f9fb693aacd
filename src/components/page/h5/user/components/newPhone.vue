<template>
    <div class="newPhone">
        <van-nav-bar title="添加登录手机号" left-text="返回" left-arrow @click-left="onClickLeft" />
        <div class="phone-input">
            <!-- <van-steps :active="active" active-icon="success" active-color="#38f">
                <van-step>手机号验证</van-step>
                <van-step>添加新手机号</van-step>
            </van-steps> -->
            <div>
                <div v-if="!codeStatus" class="phone-input-content">
                    <!-- <van-radio-group v-model="mobile" @change="onRadioChange">
                        <van-radio v-for="(item, index) in loginPhoneList" :key="index + 'b'" :label="item.mobile"
                            :name="item.mobile">
                            <van-cell :border="false" :title="item.consumerName" :value="item.maskMobile" />
                        </van-radio>
                    </van-radio-group> -->
                    验证码将会发送至您的手机号：{{ loginInfo.mobile }}，请注意查收！
                </div>
                <div v-if="!codeStatus">
                    <van-field v-model="smsCode" required center clearable label="短信验证码" placeholder="请输入短信验证码">
                        <template slot="button">
                            <van-button v-if="nmb == 120" size="small" type="primary" @click="getCaptcha">发送验证码</van-button>
                            <van-button v-else disabled size="small" type="primary">重新发送({{ nmb }}s)</van-button>
                        </template>
                    </van-field>
                </div>
            </div>
            <div>
                <van-cell-group>
                    <van-field v-model="newPhone" required center clearable label="新手机号" placeholder="请输入新手机号"></van-field>
                </van-cell-group>
            </div>
            <div>
                <van-cell-group>
                    <van-field v-model="remark" required center clearable label="备注" placeholder="请输入备注"></van-field>
                </van-cell-group>
            </div>
            <div style="margin-top: 18px;">
                <!-- <div style="display: flex;justify-content: center;">
                    <van-button style="width: 100px;" plain hairline type="info" size="small" block
                        @click="back">上一步</van-button>
                    <van-button style="width: 100px;margin-left: 18px;" type="primary" size="small" block
                        @click="submitForm">提交</van-button>
                </div> -->

                <van-button v-if="active == 0" type="primary" block @click="submitForm">提交</van-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'newPhone',
    data() {
        return {
            active: 0,
            codeStatus: null,
            mobile: '',
            newPhone: "",
            remark: "",
            show: false,
            value: '',
            smsCode: '',
            loginPhoneList: [],
            nmb: 120,
            loginInfo: {
                isAdmin: null,
                consumerName: "",
                mobile: "",
                remark: "",
                id: ""
            },
        }
    },
    created() {
        this.getLoginInfo()
        this.getCodeStatus()
        this.getLoginPhone()
    },
    methods: {
        getLoginInfo() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/loginPhoneInfo",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginInfo = res.data;
                    }
                }
            );
        },
        getCodeStatus() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/verifiedStatus",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.codeStatus = res.data;
                    }else{
                        this.$toast.fail(res.msg);
                    }
                 })
        },
        // 点击返回
        onClickLeft() {
            this.$router.push({ path: '/glsLoginPhone' });
        },
        // 获取登录手机号
        getLoginPhone() {
            this.$api.post(
                this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginPhoneList = res.data.data
                    }else{
                        this.$toast.fail(res.msg);
                    }
                })
        },
        // onRadioChange(val) {
        //     console.log(val, 'val');
        // },
        // 获取验证码
        getCaptcha() {
            this.$api.get(
                this.API.cpus +
                "userLoginAdmin/sendVerificationCode?flag=2&phoneId=" + this.loginInfo.id,
                {},
                (res) => {
                    if (res.code == 200) {
                        this.$toast.success('验证码已发送，请注意查收');
                        --this.nmb;
                        const timer = setInterval((res) => {
                            --this.nmb;
                            if (this.nmb < 1) {
                                this.nmb = 120;
                                clearInterval(timer);
                            }
                        }, 1000);
                    } else {
                        this.$toast.fail('验证码未失效，需失效后重新获取!');
                    }
                })
            // if (this.mobile) {
            //     --this.nmb;
            //     const timer = setInterval((res) => {
            //         --this.nmb;
            //         if (this.nmb < 1) {
            //             this.nmb = 120;
            //             clearInterval(timer);
            //         }
            //     }, 1000);
            //     this.$api.get(
            //         this.API.cpus +
            //         "code/sendVerificationCode?phone=" +
            //         this.mobile +
            //         "&flag=2",
            //         {},
            //         (res) => {
            //             if (res.code == 200) {
            //                 this.$toast.success('验证码已发送，请注意查收');
            //             } else {

            //                 this.$toast.fail('验证码未失效，需失效后重新获取!');
            //             }
            //         })
            // } else {
            //     this.$toast.fail('请选择手机号');
            // }
        },
        // 下一步
        subNext() {
            if (this.smsCode) {
                this.$api.get(
                    this.API.cpus +
                    "code/checkVerificationCode?code=" +
                    this.smsCode +
                    "&flag=2",
                    {},
                    (res) => {
                        if (res.code == 200) {
                            this.active = 1;
                        } else {
                            this.$toast.fail('验验证码无效！');
                        }
                    })
            } else {
                this.$toast.fail('请输入短信验证码');
            }
        },
        // 上一步
        back() {
            this.active = 0;
        },
        // 提交表单
        submitForm() {
            let params = {
                flag: 2,
                phoneList: [
                    {
                        phone: this.newPhone,
                        remark: this.remark
                    }
                ],
                verifyCode: this.smsCode,
            }
            // let test = 
            if (this.smsCode||this.codeStatus) {
                if (this.newPhone && /^([，；,;]*1\d{10}[，；,;]*)*$/.test(this.newPhone)) {
                    if (this.remark) {
                        this.$api.post(
                            this.API.cpus + "userLoginAdmin/addLoginPhoneV2",
                            params,
                            (res) => {
                                if (res.code == 200) {
                                    this.$router.push({ path: '/glsLoginPhone' });
                                    // this.cancel();
                                    this.nmb = 120;
                                    clearInterval(this.timer);
                                } else {
                                    this.$toast.fail(res.msg);
                                }
                            })
                    } else {
                        this.$toast.fail('请输入备注');
                    }
                } else {
                    this.$toast.fail('请输入正确手机号');
                }
            } else {
                this.$toast.fail('请输入短信验证码');
                return;
            }


            // if (this.newPhone) {
            //     const found = this.loginPhoneList.find(element => element.mobile === this.newPhone);
            //     if (found) {
            //         this.$toast.fail('该手机号已被绑定！');
            //     } else {
            //         this.$api.get(
            //             this.API.cpus +
            //             "consumerclientinfo/addLoginPhone/" +
            //             this.newPhone,
            //             {},
            //             (res) => {
            //                 if (res.code == 200) {
            //                     this.$router.push({ path: '/glsLoginPhone' });
            //                 } else {
            //                     this.$toast.fail(res.msg);
            //                 }
            //             })
            //     }

            // }
        },
    }
}
</script>

<style lang="less" scoped>
.phone-input-content {
    font-size: 12px;
}

.phone-input {
    margin: 18px;
}

.phone-input-title {
    display: flex;
    align-items: center;
}

/deep/.van-radio__label {
    width: 100%;
}
</style>