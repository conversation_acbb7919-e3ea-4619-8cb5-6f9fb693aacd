<template>
    <div class="h5-container">
        <div class="user-header">
            <div class="user-avatar">
                <h2>{{ userInfo.username }}</h2>
                <div style="cursor: pointer;color: #333;">
                    <a style="color: #333;" href="https://doc.zthysms.com" target="_blank">
                        <van-icon color="#409eff" name="description" />开发文档
                    </a>

                </div>
            </div>
        </div>
        <div class="user-content">
            <div class="user-item">
                <div class="user-item-title">{{ userInfo.compName }}</div>
            </div>
            <div class="user-item" @click="clickLoginPhone">
                <div class="user-item-title">登录手机号管理</div>
                <van-icon name="arrow" />
            </div>
            <div class="user-item" @click="clickChangePwd">
                <div class="user-item-title">修改接口密码</div>
                <van-icon name="arrow" />
            </div>
            <div v-if="userInfo.roleId == '12'" class="user-item" @click="clickChangeCipher">
                <div class="user-item-title">修改秘钥</div>
                <van-icon name="arrow" />
            </div>
            <div v-if="userInfo.roleId == '12' && userInfo.cipherMode == 2" class="user-item" @click="clickChangeSalt">
                <div class="user-item-title">修改加密盐</div>
                <van-icon name="arrow" />
            </div>
            <!-- <div class="user-item">
                <div class="user-item-title">设置</div>
                <van-icon name="arrow" />
            </div> -->
        </div>
        <div class="user-footer">
            <van-button block round type="info" size="normal" @click="logout">退出登录</van-button>
        </div>
        <van-action-sheet v-model="show" title="退出登录">
            <div class="van-action-sheet__content">
                <van-button round block type="default" @click="show=false">取消</van-button>
                <van-button style="margin-left: 10px;" round block type="info" @click="subimtLogout">退出</van-button>
            </div>
        </van-action-sheet>
    </div>
</template>

<script>
export default {
    name: 'User',
    data() {
        return {
            loading: true,
            show:false,
            userInfo: {},
            loginInfo: {
                isAdmin: null,
                consumerName: "",
                mobile: "",
                remark: "",
                id: ""
            },
        };
    },
    created() {
        this.getInfo();
        this.getLoginInfo()
    },
    mounted() {
        this.loading = false;
    },
    methods: {
        getInfo() {
            this.$api.get(
                this.API.cpus + "consumerclientinfo/getClientInfo",
                {},
                (res) => {
                    if (res.code === 200) {
                        this.userInfo = res.data;
                    } else {
                        this.$toast.fail(res.msg);
                    }
                }
            );
        },
        getLoginInfo() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/loginPhoneInfo",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginInfo = res.data;
                    }
                }
            );
        },
        //登录手机号管理
        clickLoginPhone(){
            if(this.loginInfo.isAdmin == 1){
                this.$router.push('/glsLoginPhone');
            }else{
                this.$toast.fail('您不是管理员，无权访问');
            }
        },
        //退出登录
        logout() {
            this.show = true;
        },
        //确认退出登录
        subimtLogout() {
            this.$api.post(this.API.auth + 'loginOut', {}, res => {
                if (res.code == 200) {
                    this.show = false;
                    var oDate = new Date();
                    oDate.setDate(oDate.getDate() - 1)
                    if (this.$common.getCookie('ZTGlS_TOKEN')) {
                        document.cookie = "ZTGlS_TOKEN=" + this.$common.getCookie('ZTGlS_TOKEN') + ";path=/;domain=." + this.hostname + ";expires=" + oDate.toGMTString();
                    }
                    window.sessionStorage.setItem('logUrl', "home")
                    window.sessionStorage.removeItem('path')
                    this.$router.push('/login');
                }
            })
        },
        //修改接口密码
        clickChangePwd(){
            if(this.loginInfo.isAdmin == 1){
                this.$router.push('/revamppwd');
            }else{
                this.$toast.fail('您不是管理员，无权访问');
            }
        },
        //修改秘钥
        clickChangeCipher(){
            if(this.loginInfo.isAdmin == 1){
                this.$router.push('/revampCipher');
            }else{
                this.$toast.fail('您不是管理员，无权访问');
            }
            
        },
        //修改加密盐
        clickChangeSalt(){
            if(this.loginInfo.isAdmin == 1){
                this.$router.push('/revampSalt');
            }else{
                this.$toast.fail('您不是管理员，无权访问');
            }
           
        }
    },


}
</script>

<style lang="less" scoped>
.h5-container {
    height: 100%;
    background-color: #f5f5f5;
    padding: 0 18px;
    position: relative;
}

.user-header {
    height: 100px;
    color: #333;
    display: flex;
}

.user-avatar {
    width: 100px;
    height: 51px;
    padding: 35px 0;
    line-height: 1.5;

}

.user-content {
    // height: 300px;
    // height: 100%;
    background-color: #fff;
    border-radius: 10px;
    margin-top: 18px;
}

.user-item {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
}

.user-item-title {
    font-size: 14px;
    color: #333;
    font-weight: 800;
}

.user-footer {
    width: 90%;
    position: absolute;
    bottom: 0;
    margin-bottom: 26px;
}
.van-action-sheet__content{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 18px 18px;
}
</style>