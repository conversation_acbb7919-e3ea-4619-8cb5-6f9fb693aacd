<template>
    <div class="loginPhone-wrap">
        <van-nav-bar title="子用户登录手机号管理" left-text="返回" left-arrow @click-left="onClickLeft" />
        <div class="loginPhone-content">

            <div class="loginPhone">
                <div class="loginPhone-title">
                    <div>温馨提示</div>
                    <div>1.默认登录手机号为新增时填写的首个手机号。</div>
                    <div>
                        2.该登录手机号至多可添加10个，管理员手机号不可删除！
                    </div>
                </div>
                <div class="loginPhone-btn">
                    <van-button size="small" type="info" @click="addPhone">添加手机号</van-button>
                </div>
                <div v-for="(item, index) in loginPhoneList" :key="index + 'a'">
                    <van-swipe-cell :right-width="120" :left-width="90">
                        <!-- <van-cell :border="false" :title="item.consumerName" :value="item.mobile" /> -->
                        <div style="display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 10px 0;
                        border-bottom: 1px solid #e5e5e5;
                        ">
                            <div class="loginPhone-item">
                                <i v-if="item.isAdmin == 1" style="color: #409eff;" class="iconfont icon-yonghuming"></i>
                                {{ item.userName }}
                            </div>
                            <div v-if="index == mobileIndex" style="cursor: pointer;color: #67C23A;"
                                class="loginPhone-item">{{ item.maskMobile }}
                            </div>
                            <div v-else style="cursor: pointer;color: #67C23A;" class="loginPhone-item"
                                @click="handelDecode(item, index)">{{ item.mobile }}</div>
                            <div class="loginPhone-item">{{ item.remark }}</div>
                        </div>
                        <van-button square slot="right" type="warning" text="编辑" @click="editphone(item)" />
                        <van-button v-if="item.isAdmin != 1" square slot="right" type="danger" text="删除"
                            @click="deleteLoginPhone(item)" />
                        <van-button v-if="item.isAdmin != 1" square slot="left" type="info"
                            @click="transferAdmin(item)">管理员转让</van-button>
                    </van-swipe-cell>
                </div>
            </div>
        </div>
        <van-action-sheet v-model="show" :title="title">
            <van-field v-model="delphone.remark" required center clearable label="备注" placeholder="备注">
            </van-field>
            <div style="display: flex;justify-content: center;align-items: center;margin: 10px 0;">
                <van-button style="width: 100px;" type="default" @click="show = false">取消</van-button>
                <van-button style="width: 100px;margin-left: 10px;" type="primary" @click="handleConfirm">确认编辑</van-button>
            </div>
        </van-action-sheet>
        <van-action-sheet v-model="addPhoneShow" title="添加手机号">
            <van-field v-model="delphone.mobile" required center clearable label="手机号" placeholder="手机号">
            </van-field>
            <van-field v-model="delphone.remark" required center clearable label="备注" placeholder="备注">
            </van-field>
            <div style="display: flex;justify-content: center;align-items: center;margin: 10px; 0">
                <van-button style="width: 100px;" type="default" @click="addPhoneShow = false">取消</van-button>
                <van-button style="width: 100px;margin-left: 10px;" type="primary"
                    @click="handlePhoneConfirm">确认添加</van-button>
            </div>
        </van-action-sheet>
        <van-action-sheet v-model="resetVideo" title="重置解密次数">
            <div class="Login-c-p-getPhone">
                <p>您今日解密次数达到上限，请先输入手机验证码进行重置解密次数 。</p>
                <p>验证码将会发送至管理员绑定的手机号：{{ infoData.mobile }}，请注意查收！</p>
            </div>
            <van-field v-model="formInline.verifyCode" required center clearable label="短信验证码" placeholder="请输入短信验证码">
                <template slot="button">
                    <van-button v-if="nmb == 120" size="small" type="primary" @click="getCaptcha">发送验证码</van-button>
                    <van-button v-else disabled size="small" type="primary">重新发送({{ nmb }}s)</van-button>
                </template>
            </van-field>
            <div style="display: flex;justify-content: center;align-items: center;margin: 10px; 0">
                <van-button style="width: 100px;" type="default" @click="resetVideo = false">取消</van-button>
                <van-button style="width: 100px;margin-left: 10px;" type="primary" @click="submitForm">重置</van-button>
            </div>
        </van-action-sheet>
    </div>
</template>

<script>
import { Dialog } from 'vant';
import common from "../../../../../assets/js/common";
export default {
    name: 'loginPhone',
    data() {
        return {
            loading: true,
            show: false,
            addPhoneShow: false,
            userInfo: {},
            title: "",
            loginPhoneList: [],
            delphone: {
                mobile: "",
                remark: "",
            },
            adminform: {
                destId: "",
                sourceId: "",
            },
            phoneId: "",
            userName: "",
            mobileIndex: null,
            resetVideo: false,
            infoData: {},
            nmb: 120,
            formInline: {
                verifyCode: ""
            },
        };
    },
    created() {
        this.InquireList()
    },
    methods: {
        // 点击左侧返回
        onClickLeft() {
            this.$router.push('/userlist');
        },
        // 获取登录手机号
        InquireList() {
            this.$api.get(
                this.API.cpus + "v3/consumer/manager/user/" + this.$route.query.userId,
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginPhoneList = res.data.mobileList;
                        this.loginPhoneList.forEach(item => {
                            item.userName = res.data.userName
                            item.mobile = item.maskMobile
                        })
                        this.userName = res.data.userName
                    }
                })
        },
        getCaptcha() {
            this.$api.get(this.API.cpus + "userLoginAdmin/sendVerificationCode?flag=1&phoneId=" + this.infoData.id, {}, res => {
                if (res.code == 200) {
                    this.$message({
                        type: 'success',
                        duration: '2000',
                        message: '验证码已发送至手机!'
                    });
                    --this.nmb;
                    this.timer = setInterval(res => {
                        --this.nmb;
                        if (this.nmb < 1) {
                            this.nmb = 120;
                            clearInterval(this.timer);
                        };
                    }, 1000);
                } else {
                    this.$message({
                        type: 'warning',
                        message: res.msg
                    });
                }

            })
        },
        handelDecode(val, index) {
            this.mobileIndex = index
            this.$api.post(
                this.API.upms + "/generatekey/decryptMobile",
                {
                    keyId: val.keyId,
                    smsInfoId: val.smsInfoId,
                    cipherMobile: val.cipherMobile,
                },
                (res) => {
                    if (res.code == 200) {
                        this.loginPhoneList[index].maskMobile = res.data;
                        // this.$nextTick(() => {
                        //     this.$set(this.tableDataObj.tableData[index], "maskMobile", res.data);
                        // });
                        // console.log(this.tableDataObj.tableData, 'this.tableDataObj.tableData');
                    } else if (res.code == 4004002) {
                        common.fetchData().then((res) => {
                            if (res.code == 200) {
                                if (res.data.isAdmin == 1) {
                                    this.resetVideo = true;
                                    this.infoData = res.data;
                                } else {
                                    this.$message({
                                        message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                        type: "warning",
                                    });
                                }
                            } else {
                                this.$message({
                                    message: res.msg,
                                    type: "error",
                                });
                            }

                        })
                    } else {
                        this.$message({
                            message: res.msg,
                            type: "warning",
                        });
                    }
                    // this.tableDataObj.tableData[index].mobile=res.data
                }
            );
        },
        handlePhoneConfirm() {
            if (this.delphone.mobile == "") {
                this.$toast('请输入手机号！');
                return;
            }
            if (!/^([，；,;]*1\d{10}[，；,;]*)*$/.test(this.delphone.mobile)) {
                this.$toast('请输入正确的手机号！');
                return;
            }
            if (this.delphone.remark == "") {
                this.$toast('请输入备注！');
                return;
            }
            let data = {
                phoneList: [
                    {
                        phone: this.delphone.mobile,
                        remark: this.delphone.remark,
                    }
                ],
                userName: this.userName,
            }
            this.$api.post(
                this.API.cpus + "v3/consumer/manager/addLoginPhoneV2",
                data,
                (res) => {
                    if (res.code == 200) {
                        this.addPhoneShow = false
                        this.InquireList(); //刷新列表
                    } else {
                        this.$toast(res.msg);
                    }
                })
        },
        submitForm() {
            if (this.formInline.verifyCode == "") {
                this.$toast('请输入验证码！');
                return;
            }
            this.$api.post(
                this.API.cpus + "userLoginAdmin/phoneDecryptUnfreeze",
                {
                    flag: "1",
                    // phoneId: this.loginInfo.id,
                    verifyCode: this.formInline.verifyCode
                },
                (res) => {
                    if (res.code == 200) {
                        this.$message({
                            type: 'success',
                            message: '解密次数已重置成功！'
                        });
                        this.resetVideo = false;
                    } else {
                        this.$message({
                            type: 'error',
                            message: res.msg
                        });
                    }
                }
            );
        },
        handleConfirm() {
            let data = {
                phoneId: this.phoneId,
                remark: this.delphone.remark,
                userName: this.userName,
            }
            this.$api.post(
                this.API.cpus + "v3/consumer/manager/updateLoginPhone",
                data,
                (res) => {
                    if (res.code == 200) {
                        this.show = false
                        this.InquireList(); //刷新列表
                    } else {
                        this.$toast(res.msg);
                    }
                })
        },
        // 删除登录手机号
        deleteLoginPhone(row) {
            Dialog.confirm({
                title: '手机号删除',
                message: '确认删除该手机号',
            })
                .then(() => {
                    this.$api.post(
                        this.API.cpus + "v3/consumer/manager/deleteLoginPhoneV2",
                        {
                            phoneId: row.phoneId,
                            userName: this.userName,
                        },
                        (res) => {
                            if (res.code == 200) {
                                this.$toast('删除成功！');
                                this.InquireList();
                            } else {
                                this.$toast(res.msg);
                            }
                        })
                })
                .catch(() => {
                    // on cancel
                });
        },
        editphone(row) {
            this.title = '编辑';
            this.phoneId = row.phoneId;
            this.delphone.mobile = row.maskMobile;
            this.delphone.remark = row.remark || "";
            this.show = true;
        },
        transferAdmin(row) {
            let data = {
                destId: row.phoneId,
                sourceId: '',
                userName: this.userName,
            }
            for (let i = 0; i < this.loginPhoneList.length; i++) {
                if (this.loginPhoneList[i].isAdmin == 1) {
                    data.sourceId = this.loginPhoneList[i].phoneId;
                    break;
                }
            }
            Dialog.confirm({
                title: '管理员转让',
                message: '确认将该手机号设置为管理员',
            })
                .then(() => {
                    this.$api.post(
                        this.API.cpus + "v3/consumer/manager/transferAdmin",
                        data,
                        (res) => {
                            if (res.code == 200) {
                                this.$toast('转让成功！');
                                this.InquireList();
                            } else {
                                this.$toast(res.msg);
                            }
                        })
                })
                .catch(() => {
                    // on cancel
                });
        },
        addPhone() {
            if (this.loginPhoneList.length >= 10) {
                this.$toast('最多只能添加10个手机号！');

            } else {
                this.addPhoneShow = true;
            }

            // this.$router.push('/newPhone');
        },
    },
    watch: {
        // visible(val) {
        //     console.log(val,'val');

        //     this.dialogVisible = val
        // },
        resetVideo(val) {
            if (!val) {
                this.nmb = 120;
                this.formInline.verifyCode = "";
                clearInterval(this.timer);
                this.infoData = {};
                this.mobileIndex = null;
            }
        }
    }

}
</script>

<style lang="less" scoped>
.loginPhone-wrap {}

.loginPhone-content {
    margin: 18px;
}

.loginPhone-title {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px;
    font-size: 12px;
}

.loginPhone-title>div {
    // height: 26px;
    line-height: 26px;
}

.loginPhone-btn {
    margin-top: 10px;
}

.loginPhone-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e5e5e5;
}

.loginPhone-item {
    width: 90px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.Login-c-p-getPhone{
    margin-top: 20px;
    color: #909399;
    font-size: 12px;
    margin-left: 16px;
    margin-bottom: 10px;
}
</style>