<template>
    <div>
        <van-nav-bar :title="statusOf == 'edit'? '编辑用户' : '创建用户'" left-text="返回" left-arrow @click-left="onClickLeft" />
        <div style="padding: 10px;">
            <van-form validate-first @failed="onFailed" @submit="onSubmit">
                <!-- 通过 pattern 进行正则校验 -->
                <van-field label="用户名" :readonly="statusOf == 'edit'" v-model="formData.userName" clearable  name="用户名"
                    placeholder="用户名" :rules="[{ validator: userName, message: '用户名必须包含字母、数字，长度4-20位' }]" />

                <van-field label="公司名" v-model="formData.compName" clearable  name="公司名" placeholder="公司名"
                    :rules="[{ required: true, message: '请填写公司名' }]" />

                <van-field label="手机号" v-model="formData.phone" clearable  name="手机号" placeholder="手机号"
                    :rules="[{ validator: phone, message: '请正确填写手机号,多个请用(,)隔开' }]" />

                <van-field label="账户状态"  v-if="statusOf == 'edit'" readonly :value="formData.consumerStatus == 1 ? '停用' : '启用'" clearable
                    name="账户状态" placeholder="账户状态" />

                <van-field label="默认签名" v-model="formData.signature" clearable  name="默认签名" placeholder="默认签名"
                    :rules="[{ validator: signature, message: '不能使用空格和特殊符号,长度2-15位' }]" />

                <van-field label="扩展号" v-if="statusOf == 'edit'" readonly v-model="formData.ext" clearable  name="扩展号"
                    placeholder="扩展号" />

                <van-field label="账户密码" v-if="statusOf == 'create'" v-model="formData.password" clearable  placeholder="账户密码"
                    :rules="[{ required: true, message: '请填账户密码' }, { pattern: /^.{6,20}$/, message: '密码长度为6-20位' }]">
                    <template slot="button">
                        <van-button size="small" type="primary" @click="generatePass">生成密码</van-button>
                    </template>
                </van-field>

                <div style="display: flex;align-items: center;border-bottom: 1px solid #F2F6FC;">
                    <van-field label="行业类别" readonly clickable name="行业类别" :value="industryId"  placeholder="点击行业类别"
                        @click="showPicker = true">
                    </van-field>
                    <van-icon @click="handleClear" class="van-icon-clear" style="margin: 0 10px;" color="#409eff" size="24"
                        v-if="industryId" name="clear" />
                </div>

                <van-popup v-model="showPicker" position="bottom" :style="{ height: '30%' }">
                    <van-picker show-toolbar :columns="industrys" value-key="industryName" @confirm="onConfirm"
                        @cancel="showPicker = false" />
                </van-popup>

                <van-field label="公司地址" v-model="formData.compAddress" clearable  placeholder="公司地址">
                </van-field>

                <van-field label="电子邮箱" v-model="formData.email" clearable name="电子邮箱"  placeholder="电子邮箱"
                    :rules="[{ validator: email, message: '请正确填写邮箱' }]">
                </van-field>

                <van-field name="消息报告" label="消息报告">
                    <template slot="input">
                        <van-radio-group v-model="formData.smsReportLevel" direction="horizontal">
                            <van-radio name="1">不接收报告</van-radio>
                            <van-radio name="2">批量推送</van-radio>
                            <van-radio style="margin-top: 10px;" name="3">主动抓取</van-radio>
                        </van-radio-group>
                    </template>
                </van-field>

                <van-field v-if="formData.smsReportLevel == 2" v-model="formData.replyUrl" clearable label="上行地址"
                    placeholder="回复短信的地址格式  (以http/https开头)"
                    :rules="[{ validator: reportUrl, message: '请正确输入上行地址(以http/https开头)' }]">
                </van-field>

                <van-field v-if="formData.smsReportLevel == 2" v-model="formData.reportUrl" clearable label="下行地址"
                    placeholder="状态报告的地址格式  (以http/https开头)"
                    :rules="[{ validator: reportUrl, message: '请正确输入下行地址(以http/https开头)' }]">
                </van-field>

                <div style="margin: 16px;">
                    <van-button round block type="info" native-type="submit">提交</van-button>
                </div>
            </van-form>

        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            statusOf: "",// 状态，create创建，edit编辑
            industryId: "",// 行业类别id
            showPicker: false,// 是否显示行业类别选择器
            industrys: [],// 行业类别列表
            formData: {
                userName: '',// 用户名
                compAddress: '',// 公司地址
                compName: '',// 公司名
                consumerStatus: '0',// 账户状态
                email: '',// 电子邮箱
                industryId: '',// 行业类别id
                password: '',// 账户密码
                phone: '',// 手机号
                replyUrl: '',// 上行地址
                reportUrl: '',// 下行地址
                smsReportLevel: '1',// 消息报告等级
                signature: '',// 默认签名
                ext: ""// 扩展号
            },
        }
    },
    created() {
        if (this.$route.query.userId) {
            this.statusOf = "edit";
            this.$api.get(this.API.cpus + 'v3/consumer/manager/user/' + this.$route.query.userId, {}, res => {
                res.data.smsReportLevel += ""
                this.formData = res.data
                if (res.data.signature) {
                    this.formData.signature = res.data.signature.replace(/【/g, "").replace(/】/g, "")
                } else {
                    this.formData.signature = ''
                }
            })
        } else {
            this.statusOf = "create";
            this.$api.get(this.API.cpus + 'v3/consumer/manager/user/ext', {}, res => {
                this.formData.ext = res.data
            })
        }
        this.getIndustrys();
    },
    methods: {
        userName(val) {
            let pattern = /^[0-9A-Za-z_]{4,20}$/;
            if (pattern.test(val)) {
                this.$api.get(this.API.upms + 'online/existUser/' + val, {}, res => {
                    if (res.data == 0) {
                        return true;
                    } else {
                        this.$toast.fail('用户名已经存在！');
                        return false;
                    }
                })
            } else {
                return false;
            }
        },
        phone(val) {
            var phoneArr = val.split(",");
            var flag = false;
            let pattern = /^1[3|4|5|6|7|8|9]\d{9}(,1[3|4|5|6|7|8|9]\d{9})*$/;
            if (!pattern.test(val)) {
                return false;
            } else {
                if (phoneArr.length > 1) {
                    for (var i = 0; i < phoneArr.length; i++) {
                        for (var j = i; j < phoneArr.length; j++) {
                            if (phoneArr[i] == phoneArr[j + 1]) {
                                flag = true
                            }
                        }
                    }
                }
                if (flag) {
                    this.$toast.fail('手机号不可重复添加');
                    return true;
                } else {
                    return true;
                }
            }
        },
        signature(val) {
            let pattern = /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,15}$/;
            if (pattern.test(val)) {
                return true;
            } else {
                return false;
            }
        },
        email(val) {
            let pattern = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
            if (pattern.test(val)) {
                return true;
            } else {
                return false;
            }
        },
        reportUrl(val) {
            let pattern = /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/;
            if (pattern.test(val)) {
                return true;
            } else {
                return false;
            }
        },
        onClickLeft() {
            this.$router.push('/userlist');
        },
        onFailed(msg) {
            console.log(msg, 'failed')
            // this.$toast.fail(msg);
        },
        // 生成密码
        generatePass() {
            this.$api.get(this.API.cpus + 'consumerclientinfo/generatePassword', {}, res => {
                this.formData.password = res
            })
        },
        /**获取行业类别列表*/
        getIndustrys() {
            this.$api.get(this.API.cpus + 'v3/consumer/manager/selectAllIndustry', {}, res => {
                this.industrys = res.data;
            })
        },
        // 选择行业类别
        onConfirm(selected) {
            this.industryId = selected.industryName;
            this.formData.industryId = selected.id;
            // this.industryId = selected[0].id;
            this.showPicker = false;
        },
        // 点击清除行业类别
        handleClear() {
            this.industryId = ''
            this.formData.industryId = ''
        },
        // 提交表单
        onSubmit(value) {
            let formData = this.formData;
            if (formData.signature) {
                formData.signature = `【${formData.signature}】`
            }
            if (this.$route.query.userId) {
                this.formData.userId = this.$route.query.userId
                this.$api.post(this.API.cpus + 'v3/consumer/manager/user/info', formData, res => {
                    if (res.code == 200) {
                        this.$toast.success('修改成功');
                        this.$router.push('/userlist');
                    } else {
                        this.$toast.fail(res.msg);
                    }
                })
            } else {
                this.$api.post(this.API.cpus + 'v3/consumer/manager/user/create', formData, res => {
                    if (res.code == 200) {
                        this.$toast.success('创建成功');
                        this.$router.push('/userlist');
                    } else {
                        this.$toast.fail(res.msg);
                    }
                })
            }


        }
    }
}
</script>

<style lang="less" scoped></style>