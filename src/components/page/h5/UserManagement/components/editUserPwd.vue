<template>
    <div class="newPhone">
        <van-nav-bar title="修改密码" left-text="返回" left-arrow @click-left="onClickLeft" />
        <div class="phone-input">
            <!-- <van-steps :active="active" active-icon="success" active-color="#38f">
                <van-step>手机号验证</van-step>
                <van-step>密码修改</van-step>
            </van-steps> -->
            <!-- <div>
                <div class="phone-input-content">
                    <van-radio-group v-model="mobile" @change="onRadioChange">
                        <van-radio v-for="(item, index) in tableD" :key="index + 'b'" :label="item.phone"
                            :name="item.phone">
                            <van-cell :border="false" :title="item.name" :value="item.phone" />
                        </van-radio>
                    </van-radio-group>
                </div>
                <div>
                    <van-field v-model="smsCode" required center clearable label="短信验证码" placeholder="请输入短信验证码">
                        <template slot="button">
                            <van-button v-if="nmb == 120" size="small" type="primary" @click="getCaptcha">发送验证码</van-button>
                            <van-button v-else disabled size="small" type="primary">重新发送({{ nmb }}s)</van-button>
                        </template>
                    </van-field>
                </div>
                <div style="margin-top: 18px;">
                    <van-button type="primary" block @click="subNext">下一步</van-button>
                </div>
            </div> -->
            <div>
                <van-form validate-first @failed="onFailed" @submit="submitForm">
                    <van-cell-group>
                        <van-field v-model="password" name="validator"
                            :rules="[{ validator, message: '密码必须包含字母、数字，长度8-16位' }]" clearable label="新密码"
                            placeholder="请输入新密码"></van-field>
                    </van-cell-group>
                    <van-cell-group>
                        <van-field v-model="subPassword" name="validator"
                            :rules="[{ validator, message: '密码必须包含字母、数字，长度8-16位' }]" clearable label="确认密码"
                            placeholder="请输入确认密码"></van-field>
                    </van-cell-group>
                    <div style="display: flex;justify-content: center;margin-top: 18px;">
                        <!-- <van-button style="width: 100px;" plain hairline type="info" size="small" block
                            @click="back">上一步</van-button> -->
                        <van-button native-type="submit" style="width: 100px;margin-left: 18px;" type="primary" size="small"
                            block>提交</van-button>
                    </div>
                </van-form>
            </div>

        </div>
    </div>
</template>

<script>
export default {
    name: 'newPhone',
    data() {
        return {
            active: 0,
            mobile: '',
            password: "",
            subPassword:"",
            show: false,
            value: '',
            smsCode: '',// 短信验证码
            loginPhoneList: [],// 登录手机号列表
            // pattern: !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g,
            nmb: 120,// 验证码倒计时
            phoneData: '',// 手机号
            phoneOriginal: [],// 原始手机号列表
            tableD: [],// 登录手机号列表
        }
    },
    created() {
        if (this.$route.query.userId) {
            this.getLoginPhone(this.$route.query.userId)
        }
    },
    methods: {
        onClickLeft() {
            this.$router.push({ path: '/userInfo' });
        },
        validator(val) {
            console.log(val, 'val')
            let pattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]*$/;
            if (pattern.test(val)) {
                return true;
            } else {
                // this.$toast.fail('密码必须包含字母、数字，长度8-16位');
                return false;
            }
            // return  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]*$/g.test(val)
        },
        // 获取登录手机号列表
        getLoginPhone(userId) {
            this.$api.get(
                this.API.cpus + "v3/consumer/manager/user/phone/" + userId,
                {},
                (res) => {
                    if (res.code == 200) {
                        let resdata = [];
                        let tabledata = [];
                        let phonelength = res.data;
                        this.phoneData = phonelength[0].phone;
                        this.phoneOriginal = [];
                        for (var i = 0; i < phonelength.length; i++) {
                            // 列表数据
                            let a = {};
                            a.Numbering = i + 1;
                            a.username = phonelength[0].consumerName;
                            a.phone = phonelength[i].phone;
                            resdata[resdata.length] = a;
                            this.phoneOriginal.push(phonelength[i].phone);
                            // 登录手机号列表
                            let b = {};
                            b.index = i;
                            b.name = i + 1;
                            b.phone = phonelength[i].phone;
                            tabledata[tabledata.length] = b;
                        }
                        if (phonelength[0].phone == "") {
                            this.tableD = [];
                        } else {
                            this.tableD = tabledata;
                        }
                        console.log(this.tableD, 'this.tableD');
                    }
                })
        },
        onRadioChange(val) {
            console.log(val, 'val');
        },
        // 获取验证码
        getCaptcha() {
            if (this.mobile) {
                --this.nmb;
                const timer = setInterval((res) => {
                    --this.nmb;
                    if (this.nmb < 1) {
                        this.nmb = 120;
                        clearInterval(timer);
                    }
                }, 1000);
                this.$api.get(
                    this.API.cpus +
                    "code/sendVerificationCode?phone=" +
                    this.mobile +
                    "&flag=2",
                    {},
                    (res) => {
                        if (res.code == 200) {
                            this.$toast.success('验证码已发送，请注意查收');
                        } else {

                            this.$toast.fail('验证码未失效，需失效后重新获取!');
                        }
                    })
            } else {
                this.$toast.fail('请选择手机号');
            }
        },
        // 下一步
        subNext() {
            if (this.smsCode) {
                this.$api.get(
                    this.API.cpus +
                    "code/checkVerificationCode?code=" +
                    this.smsCode +
                    "&flag=2",
                    {},
                    (res) => {
                        if (res.code == 200) {
                            this.active = 1;
                        } else {
                            this.$toast.fail('验验证码无效！');
                        }
                    })
            } else {
                this.$toast.fail('请输入短信验证码');
            }
        },
        back() {
            this.active = 0;
        },
        onFailed(errorInfo) {
            console.log('failed', errorInfo);
        },
        //   提交表单
        submitForm() {
            if (this.password != this.subPassword) {
                this.$toast.fail('两次密码输入不一致！');
                return;
            }
            this.$api.post(
                this.API.cpus + "v3/consumer/manager/user/pwd",
                {userId: this.$route.query.userId, password: this.password },
                (res) => {
                    if (res.code == 200) {
                        this.$toast.success('密码修改成功！');
                        this.$router.push({ path: '/userlist' });
                    } else {
                        this.$toast.fail('密码修改失败！');
                    }
                })
        },
    }
}
</script>

<style lang="less" scoped>
.phone-input {
    margin: 18px;
}

.phone-input-title {
    display: flex;
    align-items: center;
}

/deep/.van-radio__label {
    width: 100%;
}
</style>