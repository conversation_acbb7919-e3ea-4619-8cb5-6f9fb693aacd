<template>
    <div>
        <van-sticky>
            <van-nav-bar title="用户管理" left-text="返回" left-arrow @click-left="onClickLeft" />
        </van-sticky>

        <div class="user-list">
            <div class="user-input">
                <van-search v-model="tabelAlllist.userName" shape="round" show-action placeholder="用户名" @search="onSearch">
                    <template slot="action">
                        <div @click="onSearch">搜索</div>
                    </template>
                </van-search>
            </div>
            <div class="user-btn">
                <van-button class="create-btn" type="primary" size="small" @click="CreateUser">创建用户</van-button>
                <div class="search-status">
                    <van-tabs v-model="tabelAlllist.userStatus" @click="onSearch">
                        <van-tab title="全部" name=""></van-tab>
                        <van-tab title="启用" name="0"></van-tab>
                        <van-tab title="停用" name="1"></van-tab>
                    </van-tabs>
                </div>

            </div>
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
                <van-list v-model="loading" :finished="finished" :finished-text="`总共${total}条数据,没有更多了`" @load="onLoad">
                    <div v-for="item in list" :key="item.userId">
                        <van-swipe-cell :right-width="300" :left-width="isDateState == 1 ? 120 : 0">
                            <div class="item-content">
                                <div class="user-item">
                                    <div class="user-name">{{ item.userName }}</div>
                                    <div class="user-company">{{ item.company }}</div>
                                </div>
                                <div class="user-item">
                                    <div class="user-name">{{ item.createName }}</div>
                                    <div>{{ item.createTime }}</div>

                                </div>
                                <div class="user-status">
                                    <van-tag size="large" v-if="item.userStatus == 0" type="success"
                                        @click="Enable(item)">启用</van-tag>
                                    <van-tag size="large" v-else type="danger" @click="Enable(item)">停用</van-tag>
                                </div>
                                <div @click="detailUser(item)">
                                    <van-tag class="close-btn" size="medium" :color="tagColor[0]">{{
                                        item.balanceList[0].name }} : {{
        item.balanceList[0].num }}</van-tag>
                                </div>
                            </div>
                            <van-button square slot="right" type="warning" size="normal" text="编辑"
                                @click="editUser(item.userId)" />
                            <van-button square slot="right" type="danger" size="normal" text="删除" @click="delAllS(item)" />
                            <van-button square slot="right" type="info" size="normal" text="修改密码"
                                @click="editUserPwd(item.userId)">修改密码</van-button>
                            <van-button square slot="right" type="warning" size="normal" text="添加手机号"
                                @click="addPhone(item.userId)">添加手机号</van-button>
                            <van-button v-if="item.isDateState == 2 && isDateState == 1" square slot="left" type="info"
                                size="normal" text="数据开启" @click="DataOn(item)">数据开启</van-button>
                            <van-button v-if="item.isDateState == 1 && isDateState == 1" square slot="left" type="danger"
                                size="normal" text="数据关闭" @click="DataOn(item)">数据关闭</van-button>
                        </van-swipe-cell>
                    </div>
                </van-list>
            </van-pull-refresh>
            <van-action-sheet v-model="delShow" title="此操作将永久删除该数据">
                <div class="van-action-sheet__content">
                    <van-button round block type="default" @click="delShow = false">取消</van-button>
                    <van-button style="margin-left: 10px;" round block type="danger" @click="subimtDel">删除</van-button>
                </div>
            </van-action-sheet>
            <van-popup v-model="detailShow" closeable position="bottom" :style="{ height: '30%' }" @close="handleClose">
                <h3 class="van-popup__title">余额明细 ：{{ detailList.userName }}</h3>
                <h4 class="van-popup__company">公司名称：{{ detailList.company }}</h4>
                <div class="balanceLists">
                    <van-tag style="margin: 6px;" size="large" v-for="(item, index) in detailList.balanceList"
                        :key="index + 'a'" :color="tagColor[index]">{{ item.name }} : {{ item.num }}</van-tag>
                </div>
            </van-popup>
        </div>
    </div>
</template>

<script>
import { Dialog } from 'vant';
import { tagColor } from '../../utils/tagColor';
export default {
    components: {
        [Dialog.Component.name]: Dialog.Component,
    },

    data() {
        return {
            list: [],//列表数据
            delShow: false,//删除弹窗
            detailShow: false,//详情弹窗
            finished: false, //是否已加载完成，加载完成后不再触发load事件
            loading: false,//是否正在加载
            refreshing: false,//是否正在刷新
            isDateState: null,//是否开启数据
            total: 0,//总条数
            userName: "",//搜索用户名
            tabelAlllist: {
                userName: "",
                userStatus: "0",
                currentPage: 0,
                pageSize: 10,
            },
            detailList: {},//详情弹窗数据
            tagColor: [],
        }
    },
    created() {
        this.tagColor = tagColor;
        this.getInfo();
    },
    methods: {
        onClickLeft() {
            this.$router.push('/glsHome');
        },
        //获取用户信息
        getInfo() {
            this.$api.get(
                this.API.cpus + "consumerclientinfo/getClientInfo",
                {},
                (res) => {
                    if (res.code === 200) {
                        this.isDateState = res.data.isDateState;
                        // this.userInfo = res.data;
                    } else {
                        this.$toast.fail(res.msg);
                    }
                }
            );
        },
        //搜索
        onSearch() {
            this.tabelAlllist.currentPage = 1
            this.loading = true;
            this.finished = false
            this.getUserList()
        },
        //获取用户列表
        getUserList() {
            this.$api.post(
                this.API.cpus + "v3/consumer/manager/user/list",
                this.tabelAlllist,
                (res) => {
                    if (res.code == 200) {
                        this.total = res.data.total;
                        if (res.data.records.length == 0) { //没有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.finished = true; //加载完成
                            } else { //是第一页，下拉刷新
                                this.list = []; //刷新列表
                                this.finished = false;
                                this.$toast.fail('暂无数据');
                            }
                        } else { //有数据
                            if (this.tabelAlllist.currentPage > 1) { //不是第一页，上拉加载
                                this.list = [...this.list, ...res.data.records] //数组合并
                            } else { //是第一页，下拉刷新
                                this.list = res.data.records //刷新列表
                                this.refreshing = false; //刷新完成
                            }
                            if (this.list.length == res.data.total) { //没有更多数据
                                this.finished = true; //加载完成
                            }
                            this.loading = false; //加载完成
                        }
                    }
                }
            );
        },
        //上拉加载
        onLoad() {
            this.loading = true;
            this.tabelAlllist.currentPage++
            this.getUserList()
        },
        //下拉刷新
        onRefresh() {
            this.tabelAlllist.currentPage = 1
            this.refreshing = false;
            this.getUserList()
        },
        editUser(id) {
            this.$router.push({
                path: '/createUser',
                query: {
                    userId: id
                }
            });
        },
        addPhone(id) {
            this.$router.push({
                path: '/addPhone',
                query: {
                    userId: id
                }
            });
        },
        CreateUser() {
            this.$router.push('/createUser');
        },
        delAllS(row) {
            this.delShow = true;
            this.userName = row.userName;
        },
        //确认删除
        subimtDel() {
            this.$api.delete(
                this.API.cpus + 'v3/consumer/manager/user/del/' + this.userName,
                {},
                (res) => {
                    if (res.code == 200) {
                        this.$toast.success('删除成功');
                        this.delShow = false;
                        this.onSearch();
                    } else {
                        this.$toast.fail('删除失败');
                    }
                })
        },
        editUserPwd(id) {
            this.$router.push({
                path: '/editUserPwd',
                query: {
                    userId: id
                }
            });
        },
        //启用/停用
        Enable(row) {
            Dialog.confirm({
                title: row.userStatus == 0 ? "停用" : "启用",
                message: '是否确认执行此操作？',
            })
                .then(() => {
                    this.$api.post(
                        this.API.cpus + "v3/consumer/manager/user/status",
                        {
                            userIds: [row.userId],
                            statusType: row.userStatus == 0 ? "1" : "0",
                        },
                        (res) => {
                            if (res.code == 200) {
                                this.$toast.success('操作成功');
                                this.onSearch();
                            } else {
                                this.$toast.fail('操作失败');
                            }
                        }
                    );
                })
                .catch(() => {
                });


        },
        //数据开启/关闭
        DataOn(row) {
            this.$api.post(
                this.API.cpus + "v3/consumer/manager/user/isState",
                {
                    smsId: row.smsId,
                    isDateState: row.isDateState == 2 ? "1" : "2",
                },
                (res) => {
                    this.onSearch();
                }
            );
        },
        detailUser(row) {
            this.detailList = row;
            this.detailShow = true;
        },
        handleClose() {
            this.detailShow = false;
            this.detailList = {};
        }
    },

}
</script>

<style lang="less" scoped>
.user-list {
    margin: 18px;
}

.item-content {
    display: flex;
    // height: 50px;
    border-bottom: 1px solid #eee;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    margin: 10px 0;
}

.user-item {
    width: 95px;
    line-height: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.close-btn {
    width: 55px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-name {
    font-size: 16px;
    font-weight: bold;
}

.user-company {
    font-size: 12px;
}

.user-btn {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.create-btn {
    width: 80px;
}

.search-status {
    flex: 1;
}

.van-action-sheet__content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 18px 18px;
}

.balanceLists {
    padding: 0 18px;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    overflow: auto;
}

.van-popup__title {
    padding: 18px;
    margin-left: 10px;
}

.van-popup__company {
    padding: 0 18px;
    margin-left: 10px;
}

// .user-input {
//     display: flex;
//     align-items: center;
// }

// .search-btn {
//     width: 100px;
//     margin-left: 10px;
// }</style>