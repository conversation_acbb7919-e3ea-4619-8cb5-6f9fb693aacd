<template>
    <div class="h5-container home-container">
        <div class="banner-container">
            <img class="banner-img"  src="../../../../assets/images/banner-2.png" alt="">
        </div>
        <div class="h5-home-header">
            <div class="h5-title">快捷操作</div>
            <div class="action-container">
                <div v-for="(item,index) in actionList" :key="index + 'l'" class="icon-container" @click="$router.push(item.path)">
                    <i v-if="item.icon" :class="'iconfont' + ' ' + item.icon" style="font-size: 32px; color: #fc3d36;"></i>
                    <img v-if="item.img" class="icon-container-img" :src="item.img" alt="">
                    <div class="icon-container-text" style="font-size: 14px;color: #171A1D;">{{item.title}}</div>
                </div>
            </div>

        </div>
        <div class="sms-container">
            <div class="h5-title">我的数据</div>

            <div class="sms-container-items">
                <div class="sms-item" v-for="(item, index) in balanceList" :key="index">
                    <div>
                        <img class="sms-img" v-if="item.productId == 1" :src="icon4" alt="" @click="$router.push('/mcSmsStatistics')">
                        <!-- <img class="sms-img" v-if="item.productId == 2" :src="icon5" alt="" @click="$router.push('/quickActions/multimediaMessage')"> -->
                        <img class="sms-img" v-if="item.productId == 3" :src="icon6" alt="" @click="$router.push('/mcVideoStatistics')"> <!-- @click="$router.push('/quickActions/videoMessage')" -->
                        <img class="sms-img" v-if="item.productId == 4" :src="icon7" alt="" @click="$router.push('/mcImsStatistics')"> <!--@click="$router.push('/quickActions/internationalMessage')" -->
                        <img class="sms-img" v-if="item.productId == 5" :src="icon8" alt="" @click="$toast('该功能暂未开通')">
                        <img class="sms-img" v-if="item.productId == 6" :src="icon9" alt="" @click="$router.push('/mcVoiceCodeSendRecord')">
                        <img class="sms-img" v-if="item.productId == 7" :src="icon10" alt="" @click="$router.push('/mcVoiceNoticeSendRecord')">
                        <img class="sms-img" v-if="item.productId == 8" :src="icon11" alt="" @click="$toast('该功能暂未开通')">
                        <img class="sms-img" v-if="item.productId == 9" :src="icon12" alt="" @click="$toast('该功能暂未开通')">
                        <img class="sms-img" v-if="item.productId == 10" :src="icon13" alt="" @click="$toast('该功能暂未开通')">
                    </div>
                    <div v-if="item.productId != 2" class="sms-item-text">
                        <div class="sms-item-text-num">{{item.num}}</div>
                        <div>
                            <span>{{item.name}}剩余/</span>
                            <span v-if="item.productId ==4">元</span>
                            <span v-else>条</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
  </div>
</template>

<script>
import {
  icon1,
  icon2,
  icon3,
  icon4,
  icon5,
  icon6,
  icon7,
  icon8,
  icon9,
  icon10,
  icon11,
  icon12,
  icon13,
} from "@/components/page/h5/imgs.js";

export default {
  name: "Homeindex",
  data() {
    return {
        loading: false,
      actionList: [
        {
          title: "余额提醒设置",
          path: "/mcBalanceReminder",
          img: icon1,
        },
        {
          title: "通知预警设置",
          path: "/mcNotificationWarning",
          icon: "icon-gaojingshijian",
        },
      ],
      balanceList: [],
      icon4,
      icon5,
      icon6,
      icon7,
      icon8,
      icon9,
      icon10,
      icon11,
      icon12,
      icon13,
    };
  },
  created() {
    this.loading = true;
    setTimeout(() => {
      let data = JSON.parse(localStorage.getItem("userInfo"));
      if (data) {
        this.balanceList = data.balanceList.filter(
          (item) => item.productId !== 2
        );
        this.loading = false;
        localStorage.setItem("balanceList", JSON.stringify(this.balanceList));
      }
    }, 100);

    // this.$api.get(this.API.cpus + 'consumerclientinfo/getClientInfo', null, res => {
    //     if (res.data.balanceList) {
    //         this.balanceList = res.data.balanceList.filter(item => item.productId !== 2)
    //         // console.log(this.balanceList,'this.balanceList')
    //         localStorage.setItem('balanceList', JSON.stringify(this.balanceList)) // 存储到本地缓存中，下次打开页面可以直接取出('balanceList'，)
    //     }

    // })
  },
};
</script>

<style lang="less" scoped>
.home-container {
  display: flex;
  flex-direction: column;
  &::-webkit-scrollbar {
    display: none;
  }
}

.h5-home-header {
  background-color: #fff;
  border-radius: 10px;

  margin-top: 6px;
  // box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 8px 12px 16px;
  box-sizing: border-box;
  // margin-top: 18px;
}

.h5-title {
  font-size: 16px;
  font-weight: bolder;
  color: #171a1d;
}

.action-container {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  // padding: 10px;
  flex-shrink: 0;
  flex-wrap: wrap;
  margin-top: 10px;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 0 15px;
  cursor: pointer;
  // margin: 10px;
  // margin-right: 43px;
  &:first-child {
    margin-left: 0;
  }
  &:last-child {
    margin-right: 0;
  }

  .icon-container-img {
    width: 34px;
    height: 34px;
    margin-bottom: 4px;
  }

  .icon-container-text {
    font-size: 14px;
    color: #171a1d;
  }
}

.sms-container {
  margin-bottom: 60px;
  margin-top: 8px;
  background-color: #fff;
  border-radius: 10px;
  // box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 8px 12px 15px;

  .sms-container-items {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // flex-shrink: 1;
    flex-wrap: wrap;

    .sms-item {
      width: 32%;
      height: 85px;
      // margin: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 20px 0 10px;
      cursor: pointer;

      .sms-item-text {
        text-align: center;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        color: #171a1d;
        .sms-item-text-num {
          margin: 4px 0 6px;
          font-size: 14px;
          // font-weight: 500;
        }
      }
    }

    .sms-img {
      width: 40px;
    }
  }
}

.banner-container {
  // margin: 5px;
  // border-radius: 10px;
}
.banner-img {
  width: 100%;
  border-radius: 10px;
}
</style>