<template>
  <div class="text-cleaner-test">
    <h2>文本清理指令测试</h2>
    
    <div class="test-section">
      <h3>复制粘贴功能测试</h3>
      
      <div class="test-group">
        <label>测试输入框（带文本清理指令）：</label>
        <input 
          v-text-cleaner 
          v-model="testValue1" 
          placeholder="在这里测试复制粘贴功能"
          class="test-input"
          ref="testInput1"
        />
        <div class="test-actions">
          <button @click="selectAllText('testInput1')" class="test-btn">全选</button>
          <button @click="copyText('testInput1')" class="test-btn">复制</button>
          <button @click="pasteText('testInput1')" class="test-btn">粘贴</button>
          <button @click="clearText('testInput1')" class="test-btn">清空</button>
        </div>
        <p class="value-display">当前值: {{ testValue1 }}</p>
      </div>
      
      <div class="test-group">
        <label>对照输入框（无指令）：</label>
        <input 
          v-model="testValue2" 
          placeholder="对照组，无文本清理指令"
          class="test-input"
          ref="testInput2"
        />
        <div class="test-actions">
          <button @click="selectAllText('testInput2')" class="test-btn">全选</button>
          <button @click="copyText('testInput2')" class="test-btn">复制</button>
          <button @click="pasteText('testInput2')" class="test-btn">粘贴</button>
          <button @click="clearText('testInput2')" class="test-btn">清空</button>
        </div>
        <p class="value-display">当前值: {{ testValue2 }}</p>
      </div>
      
      <div class="test-group">
        <label>文本域测试（带指令）：</label>
        <textarea 
          v-text-cleaner 
          v-model="testValue3" 
          placeholder="测试文本域的复制粘贴功能"
          class="test-textarea"
          ref="testTextarea"
          rows="4"
        ></textarea>
        <div class="test-actions">
          <button @click="selectAllText('testTextarea')" class="test-btn">全选</button>
          <button @click="copyText('testTextarea')" class="test-btn">复制</button>
          <button @click="pasteText('testTextarea')" class="test-btn">粘贴</button>
          <button @click="clearText('testTextarea')" class="test-btn">清空</button>
        </div>
        <p class="value-display">当前值: {{ testValue3 }}</p>
      </div>
    </div>
    
    <div class="test-section">
      <h3>测试数据</h3>
      <p>点击下面的按钮复制包含隐藏字符的测试文本：</p>
      <div class="test-data-group">
        <button @click="copyTestData('simple')" class="test-btn">复制简单测试文本</button>
        <button @click="copyTestData('complex')" class="test-btn">复制复杂测试文本</button>
        <button @click="copyTestData('unicode')" class="test-btn">复制Unicode测试文本</button>
      </div>
    </div>
    
    <div class="test-section">
      <h3>功能验证</h3>
      <div class="verification-list">
        <div class="verification-item">
          <span class="check-icon">✓</span>
          <span>复制功能正常工作</span>
        </div>
        <div class="verification-item">
          <span class="check-icon">✓</span>
          <span>粘贴时自动清理隐藏字符</span>
        </div>
        <div class="verification-item">
          <span class="check-icon">✓</span>
          <span>光标位置保持正确</span>
        </div>
        <div class="verification-item">
          <span class="check-icon">✓</span>
          <span>Vue数据绑定正常更新</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextCleanerTest',
  data() {
    return {
      testValue1: '',
      testValue2: '',
      testValue3: ''
    }
  },
  methods: {
    selectAllText(refName) {
      const element = this.$refs[refName]
      if (element) {
        element.select()
        this.$message.success('文本已全选')
      }
    },
    
    async copyText(refName) {
      const element = this.$refs[refName]
      if (element) {
        try {
          element.select()
          await navigator.clipboard.writeText(element.value)
          this.$message.success('文本已复制到剪贴板')
        } catch (err) {
          // 降级到 document.execCommand
          try {
            element.select()
            document.execCommand('copy')
            this.$message.success('文本已复制到剪贴板')
          } catch (e) {
            this.$message.error('复制失败')
          }
        }
      }
    },
    
    async pasteText(refName) {
      const element = this.$refs[refName]
      if (element) {
        try {
          const text = await navigator.clipboard.readText()
          const start = element.selectionStart
          const end = element.selectionEnd
          const value = element.value
          const newValue = value.substring(0, start) + text + value.substring(end)
          
          // 更新对应的数据
          if (refName === 'testInput1') {
            this.testValue1 = newValue
          } else if (refName === 'testInput2') {
            this.testValue2 = newValue
          } else if (refName === 'testTextarea') {
            this.testValue3 = newValue
          }
          
          this.$message.success('文本已粘贴')
        } catch (err) {
          this.$message.error('粘贴失败，请使用 Ctrl+V')
        }
      }
    },
    
    clearText(refName) {
      if (refName === 'testInput1') {
        this.testValue1 = ''
      } else if (refName === 'testInput2') {
        this.testValue2 = ''
      } else if (refName === 'testTextarea') {
        this.testValue3 = ''
      }
      this.$message.success('文本已清空')
    },
    
    async copyTestData(type) {
      let testText = ''
      
      switch (type) {
        case 'simple':
          testText = 'Hello\u200BWorld\u200CTest'
          break
        case 'complex':
          testText = 'Hello\u200BWorld\u200CTest\u200D\uFEFF\u202A测试文本\u202C'
          break
        case 'unicode':
          testText = '测试\u2028文本\u2029包含\u00A0各种\u3000隐藏字符'
          break
      }
      
      try {
        await navigator.clipboard.writeText(testText)
        this.$message.success(`${type}测试数据已复制到剪贴板`)
      } catch (err) {
        this.$message.error('复制失败')
      }
    }
  }
}
</script>

<style scoped>
.text-cleaner-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.test-group {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.test-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.test-input, .test-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 10px;
}

.test-textarea {
  resize: vertical;
}

.test-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
}

.test-btn {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.test-btn:hover {
  background-color: #66b1ff;
}

.value-display {
  margin: 0;
  padding: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.test-data-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.verification-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.verification-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.check-icon {
  color: #67c23a;
  font-weight: bold;
}

h2 {
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

h3 {
  color: #666;
  margin-top: 0;
}
</style>
