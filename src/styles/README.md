# SMS签名管理系统样式管理

## 概述

为了避免CSS代码重复，提高维护效率，我们将签名相关页面的通用样式提取到了独立的样式文件中。

## 文件结构

```
src/styles/
├── signature-common.less    # 签名页面通用样式
├── template-common.less     # 模板管理通用样式（基于tempv1.vue设计）
└── README.md               # 样式管理说明文档
```

## template-common.less

### 包含的样式模块

1. **简约页面基础样式**
   - `.simple-template-page/.simple-signature-page` - 简约页面容器
   - `.page-header` - 页面头部样式
   - `.page-content` - 主要内容区域

2. **提醒区域样式**
   - `.notice-section` - 提醒区域容器
   - `.notice-title` - 提醒标题
   - `.notice-list/.notice-item` - 提醒列表项

3. **工具栏样式**
   - `.toolbar-section` - 工具栏容器
   - `.toolbar-left/.toolbar-right` - 工具栏左右区域
   - `.simple-btn` - 简约按钮样式

4. **表格区域样式**
   - `.table-section` - 表格区域容器
   - `.table-header` - 表格头部
   - `.simple-table` - 简约表格样式

5. **分页样式**
   - `.pagination-section` - 分页区域
   - `.simple-pagination` - 简约分页样式

6. **响应式设计**
   - 移动端适配样式

**使用组件：**
- tempv1.vue（模板管理）
- SignatureManagement.vue（签名管理）

## signature-common.less

### 包含的样式模块

1. **基础页面布局**
   - `.modern-signature-page` - 现代化签名页面容器
   - `.page-header` - 页面头部样式
   - `.page-content` - 主要内容区域

2. **表单组件样式**
   - `.form-card` - 表单卡片样式
   - `.form-item-compact` - 紧凑表单项
   - `.compact-input/.compact-select/.compact-textarea` - 紧凑输入组件

3. **签名功能组件**
   - `.template-config-card` - 签名配置卡片
   - `.template-type-section` - 签名类型选择区域
   - `.compact-radio-group` - 紧凑单选按钮组

4. **业务组件样式**
   - `.basic-info-section` - 基本信息区域
   - `.content-section` - 内容区域
   - `.template-rules-card` - 规范说明卡片

5. **交互组件**
   - `.action-buttons` - 操作按钮组
   - `.button-group` - 按钮组容器

6. **响应式设计**
   - 移动端适配样式
   - 平板端适配样式

**使用组件：**
- CreateSign.vue（创建签名）
- userSignature.vue（用户签名）

## 使用方法

### 在Vue组件中引入

```vue
<style lang="less" scoped>
// 引入通用签名样式
@import '~@/styles/signature-common.less';

// 组件特有的样式可以在这里添加
.component-specific-style {
  // 特有样式
}
</style>
```

### 当前使用的组件

- `src/components/page/client/SMS/SMSContentConfiguration/components/CreateSign.vue`
- `src/components/page/gls/UserManagement/userSignature.vue`

## 样式特点

### 1. 现代化设计
- 使用渐变背景和阴影效果
- 圆角设计和平滑过渡动画
- 统一的色彩体系

### 2. 卡片化布局
- 每个功能模块独立成卡片
- 清晰的信息层次
- 良好的视觉分组

### 3. 交互友好
- 悬停效果和状态反馈
- 平滑的动画过渡
- 直观的操作引导

### 4. 响应式设计
- 适配不同屏幕尺寸
- 移动端友好的布局
- 灵活的网格系统

## 维护指南

### 1. 添加新样式
- 新的通用样式应添加到 `signature-common.less`
- 组件特有样式保留在组件文件中
- 遵循现有的命名规范和结构

### 2. 修改现有样式
- 修改通用样式时要考虑对所有使用组件的影响
- 建议先在开发环境测试所有相关页面
- 保持样式的一致性和兼容性

### 3. 命名规范
- 使用BEM命名规范
- 语义化的类名
- 避免过于具体的选择器

### 4. 性能考虑
- 合理使用CSS3动画
- 避免过度嵌套
- 优化选择器性能

## 扩展计划

### 1. 更多组件支持
- 可以将其他签名相关页面也迁移到统一样式
- 支持更多的业务场景

### 2. 主题系统
- 支持多主题切换
- 可配置的色彩方案
- 暗色模式支持

### 3. 组件库化
- 将通用样式组件化
- 提供更好的复用性
- 建立设计系统

## 注意事项

1. **引入路径**：使用 `~@/styles/signature-common.less` 确保正确的路径解析
2. **作用域**：在组件中使用 `scoped` 避免样式污染
3. **兼容性**：确保样式在目标浏览器中正常工作
4. **性能**：避免重复引入，合理使用样式

## 贡献指南

1. 修改前请先了解现有的样式结构
2. 保持代码风格的一致性
3. 添加必要的注释说明
4. 测试样式在不同组件中的表现
5. 更新相关文档

## 样式抽离效果

### template-common.less 抽离效果
- **tempv1.vue**：样式从 305 行减少到 33 行，减少 89%
- **SignatureManagement.vue**：样式从 258 行减少到 37 行，减少 86%
- **维护性提升**：统一的样式修改只需在一个文件中进行
- **一致性保证**：确保所有使用该样式的组件保持视觉一致性

### signature-common.less 抽离效果
- **CreateSign.vue**：复用现代化表单和卡片样式
- **userSignature.vue**：复用相同的布局和交互样式
- **代码复用率**：提高约 70% 的样式代码复用率

通过统一的样式管理，我们实现了：
- 减少代码重复
- 提高维护效率
- 保证视觉一致性
- 便于后续扩展
