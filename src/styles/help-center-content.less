/* 帮助中心内容组件通用样式 */

// 颜色变量
@primary-color: #667eea;
@secondary-color: #764ba2;
@success-color: #67c23a;
@warning-color: #e6a23c;
@danger-color: #f56c6c;
@info-color: #909399;
@text-primary: #303133;
@text-regular: #606266;
@text-secondary: #909399;
@text-placeholder: #c0c4cc;
@border-base: #dcdfe6;
@border-light: #e4e7ed;
@border-lighter: #ebeef5;
@background-color: #f5f7fa;

// 文档容器
.docs-container {
    animation: fadeIn 0.5s ease-in-out;
}

// 文档块
.docsBox {
    margin-bottom: 40px;
    padding-bottom: 40px;
    border-bottom: 1px solid @border-lighter;
    
    &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
}

// 主标题
.main-title {
    font-size: 28px;
    font-weight: 600;
    color: @text-primary;
    margin: 0 0 40px 0;
    position: relative;
    padding-left: 20px;
    
    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 24px;
        background: linear-gradient(135deg, @primary-color 0%, @secondary-color 100%);
        border-radius: 2px;
    }
}

// 分类标题
.section-title {
    font-size: 20px;
    font-weight: 500;
    color: @primary-color;
    margin: 0 0 20px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid @primary-color;
    display: inline-block;
}

// 问答容器
.qa-container {
    background: #fff;
    border: 1px solid @border-lighter;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
    }
}

// 问题项
.question-item {
    margin-bottom: 24px;
    
    &:last-child {
        margin-bottom: 0;
    }
}

// 问题标题
.question-title {
    font-size: 16px;
    font-weight: 500;
    color: @text-primary;
    margin-bottom: 12px;
    padding-left: 24px;
    position: relative;
    line-height: 1.6;
    
    &::before {
        content: 'Q';
        position: absolute;
        left: 0;
        top: 0;
        width: 20px;
        height: 20px;
        background: @primary-color;
        color: #fff;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        line-height: 20px;
        border-radius: 50%;
    }
}

// 答案内容
.answer-content {
    font-size: 14px;
    color: @text-regular;
    line-height: 1.8;
    padding-left: 24px;
    position: relative;
    
    &::before {
        content: 'A';
        position: absolute;
        left: 0;
        top: 2px;
        width: 20px;
        height: 20px;
        background: @success-color;
        color: #fff;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        line-height: 20px;
        border-radius: 50%;
    }
    
    // 高亮文本
    .highlight {
        color: @primary-color;
        font-weight: 500;
        padding: 0 4px;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 3px;
    }
    
    // 代码块
    code {
        background: #f4f4f5;
        padding: 2px 6px;
        margin: 0 4px;
        border-radius: 3px;
        font-family: 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
        font-size: 13px;
        color: @danger-color;
    }
}

// 搜索高亮
.search-highlight {
    background: #fef0c7;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
}

// 折叠面板样式
.faq-collapse {
    .el-collapse-item__header {
        font-size: 16px;
        font-weight: 500;
        color: @text-primary;
        height: 56px;
        line-height: 56px;
        padding-left: 20px;
        
        &:hover {
            color: @primary-color;
        }
    }
    
    .el-collapse-item__content {
        padding: 20px 20px 20px 44px;
        color: @text-regular;
        line-height: 1.8;
    }
    
    .el-collapse-item.is-active {
        .el-collapse-item__header {
            color: @primary-color;
        }
    }
}

// 提示框样式
.tip-box {
    background: #ecf5ff;
    border: 1px solid #d9ecff;
    border-radius: 4px;
    padding: 16px;
    margin: 20px 0;
    position: relative;
    padding-left: 44px;
    
    &::before {
        content: '\e7c3'; // el-icon-info
        font-family: 'element-icons';
        position: absolute;
        left: 16px;
        top: 16px;
        font-size: 20px;
        color: @primary-color;
    }
    
    &.warning {
        background: #fdf6ec;
        border-color: #faecd8;
        
        &::before {
            content: '\e7c3'; // el-icon-warning
            color: @warning-color;
        }
    }
    
    &.error {
        background: #fef0f0;
        border-color: #fde2e2;
        
        &::before {
            content: '\e79d'; // el-icon-circle-close
            color: @danger-color;
        }
    }
    
    &.success {
        background: #f0f9ff;
        border-color: #d1edc4;
        
        &::before {
            content: '\e7a1'; // el-icon-circle-check
            color: @success-color;
        }
    }
}

// 动画效果
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .main-title {
        font-size: 24px;
    }
    
    .section-title {
        font-size: 18px;
    }
    
    .qa-container {
        padding: 16px;
    }
    
    .question-title,
    .answer-content {
        padding-left: 28px;
    }
} 