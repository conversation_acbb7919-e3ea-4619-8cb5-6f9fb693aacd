/**
 * AI模板生成相关枚举数据
 * 统一管理所有下拉选择框的选项数据
 */

// 验证码使用场景
export const VERIFICATION_SCENARIOS = [
  { label: '注册', value: '注册' },
  { label: '登录', value: '登录' },
  { label: '修改密码', value: '修改密码' },
  { label: '修改手机号', value: '修改手机号' },
  { label: '找回密码', value: '找回密码' },
  { label: '身份验证', value: '身份验证' },
  { label: '绑定账号', value: '绑定账号' },
  { label: '其他', value: '其他' }
];

// 通知模板行业选项
export const NOTIFICATION_INDUSTRIES = [
  { label: '教育培训', value: '教育培训' },
  { label: '医疗健康', value: '医疗健康' },
  { label: '政府/事业单位', value: '政府事业单位' },
  { label: '金融保险', value: '金融保险' },
  { label: '物流快递', value: '物流快递' },
  { label: '电商零售', value: '电商零售' },
  { label: '生活服务', value: '生活服务' },
  { label: '其他', value: '其他' }
];

// 营销模板行业选项
export const MARKETING_INDUSTRIES = [
  { label: '电商零售', value: '电商零售' },
  { label: '餐饮美食', value: '餐饮美食' },
  { label: '美容美发', value: '美容美发' },
  { label: '健身运动', value: '健身运动' },
  { label: '教育培训', value: '教育培训' },
  { label: '旅游出行', value: '旅游出行' },
  { label: '房产家居', value: '房产家居' },
  { label: '汽车服务', value: '汽车服务' },
  { label: '其他', value: '其他' }
];

// 通知场景映射（根据行业）
export const NOTIFICATION_SCENARIO_MAP = {
  '教育培训': [
    { label: '报名通知', value: '报名通知' },
    { label: '课程提醒', value: '课程提醒' },
    { label: '考试通知', value: '考试通知' },
    { label: '成绩查询', value: '成绩查询' },
    { label: '缴费通知', value: '缴费通知' }
  ],
  '医疗健康': [
    { label: '预约提醒', value: '预约提醒' },
    { label: '体检通知', value: '体检通知' },
    { label: '报告查询', value: '报告查询' },
    { label: '就诊提醒', value: '就诊提醒' },
    { label: '健康提醒', value: '健康提醒' }
  ],
  '政府事业单位': [
    { label: '缴费通知', value: '缴费通知' },
    { label: '办事提醒', value: '办事提醒' },
    { label: '政策通知', value: '政策通知' },
    { label: '预约确认', value: '预约确认' },
    { label: '审核结果', value: '审核结果' }
  ],
  '金融保险': [
    { label: '账单通知', value: '账单通知' },
    { label: '还款提醒', value: '还款提醒' },
    { label: '到期提醒', value: '到期提醒' },
    { label: '交易通知', value: '交易通知' },
    { label: '风险提醒', value: '风险提醒' }
  ],
  '物流快递': [
    { label: '取件通知', value: '取件通知' },
    { label: '派送提醒', value: '派送提醒' },
    { label: '签收确认', value: '签收确认' },
    { label: '异常处理', value: '异常处理' },
    { label: '状态更新', value: '状态更新' }
  ],
  '电商零售': [
    { label: '订单确认', value: '订单确认' },
    { label: '发货通知', value: '发货通知' },
    { label: '到货提醒', value: '到货提醒' },
    { label: '退换货', value: '退换货' },
    { label: '会员通知', value: '会员通知' }
  ],
  '生活服务': [
    { label: '预约确认', value: '预约确认' },
    { label: '服务提醒', value: '服务提醒' },
    { label: '完成通知', value: '完成通知' },
    { label: '评价邀请', value: '评价邀请' },
    { label: '维护通知', value: '维护通知' }
  ],
  '其他': [
    { label: '通用通知', value: '通用通知' },
    { label: '状态更新', value: '状态更新' },
    { label: '提醒通知', value: '提醒通知' }
  ]
};

// 营销类型映射（根据行业）
export const MARKETING_TYPE_MAP = {
  '电商零售': [
    { label: '新品推广', value: '新品推广' },
    { label: '促销活动', value: '促销活动' },
    { label: '会员专享', value: '会员专享' },
    { label: '清仓特卖', value: '清仓特卖' },
    { label: '节日营销', value: '节日营销' }
  ],
  '餐饮美食': [
    { label: '新品上市', value: '新品上市' },
    { label: '优惠套餐', value: '优惠套餐' },
    { label: '会员福利', value: '会员福利' },
    { label: '节日特惠', value: '节日特惠' },
    { label: '外卖推广', value: '外卖推广' }
  ],
  '美容美发': [
    { label: '新项目推广', value: '新项目推广' },
    { label: '体验活动', value: '体验活动' },
    { label: '会员特惠', value: '会员特惠' },
    { label: '季节护理', value: '季节护理' },
    { label: '套餐推荐', value: '套餐推荐' }
  ],
  '健身运动': [
    { label: '课程推广', value: '课程推广' },
    { label: '会员招募', value: '会员招募' },
    { label: '体验活动', value: '体验活动' },
    { label: '私教推广', value: '私教推广' },
    { label: '健身挑战', value: '健身挑战' }
  ],
  '教育培训': [
    { label: '课程推广', value: '课程推广' },
    { label: '试听邀请', value: '试听邀请' },
    { label: '优惠活动', value: '优惠活动' },
    { label: '新班开课', value: '新班开课' },
    { label: '学习资料', value: '学习资料' }
  ],
  '旅游出行': [
    { label: '线路推广', value: '线路推广' },
    { label: '特价活动', value: '特价活动' },
    { label: '会员专享', value: '会员专享' },
    { label: '节日出游', value: '节日出游' },
    { label: '定制服务', value: '定制服务' }
  ],
  '房产家居': [
    { label: '新盘推广', value: '新盘推广' },
    { label: '看房邀请', value: '看房邀请' },
    { label: '优惠政策', value: '优惠政策' },
    { label: '投资机会', value: '投资机会' },
    { label: '二手房源', value: '二手房源' }
  ],
  '汽车服务': [
    { label: '新车上市', value: '新车上市' },
    { label: '试驾邀请', value: '试驾邀请' },
    { label: '保养提醒', value: '保养提醒' },
    { label: '优惠活动', value: '优惠活动' },
    { label: '二手车推荐', value: '二手车推荐' }
  ],
  '其他': [
    { label: '产品推广', value: '产品推广' },
    { label: '活动营销', value: '活动营销' },
    { label: '会员营销', value: '会员营销' }
  ]
};

/**
 * 根据行业获取通知场景选项
 * @param {string} industry 行业值
 * @returns {Array} 场景选项数组
 */
export function getNotificationScenarios(industry) {
  return NOTIFICATION_SCENARIO_MAP[industry] || NOTIFICATION_SCENARIO_MAP['其他'];
}

/**
 * 根据行业获取营销类型选项
 * @param {string} industry 行业值
 * @returns {Array} 营销类型选项数组
 */
export function getMarketingTypes(industry) {
  return MARKETING_TYPE_MAP[industry] || MARKETING_TYPE_MAP['其他'];
}

/**
 * 获取所有枚举数据（用于调试或其他用途）
 */
export const AI_TEMPLATE_ENUMS = {
  VERIFICATION_SCENARIOS,
  NOTIFICATION_INDUSTRIES,
  MARKETING_INDUSTRIES,
  NOTIFICATION_SCENARIO_MAP,
  MARKETING_TYPE_MAP
};
