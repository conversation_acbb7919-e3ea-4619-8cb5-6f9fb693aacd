import Vue from 'vue'
// import './plugins/axios'
import App from './App.vue'
import router from './router/router'
import store from './store/store'
import './plugins/element.js'
import './plugins/vant.js'
import '@/style/normalize.css'
import '@/style/icon.css';
import '@/style/permission.css'
// import echarts from 'vue-echarts-directive';
// import '@/assets/theme/index.css'
import '@/assets/styles/iconfont/iconfont.css'
import common from './assets/js/common.js'
import validates from './assets/js/validationRule.js'
import * as filters from './assets/js/filters.js'
import Moment from 'moment'
// 权限控制
import { permission } from './directives/permission'
// 文本清理指令
import textCleaner from './directives/textCleaner'
//二次弹框
import confirms from './assets/js/confirm.js'
Vue.prototype.$confirms = confirms
Vue.prototype.$common = common
import api from './plugins/axios'
import Files from './plugins/exportFile.js'
Vue.prototype.$File = Files
// 绑定 axios 到全局（vue对象原型方法）
Vue.prototype.$api = api
//接口路径配置
import globalPath from '@/assets/js/globalPath.vue'
import { formatDate } from '@/assets/js/date.js'
Vue.prototype.API = globalPath.globalPath //接口路径配置
Vue.use(validates)
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})
Vue.filter('fmtDate', function (time) {
  let date = new Date(time * 1);
  return formatDate(date, "yyyy-MM-dd hh:mm:ss");
})
Vue.prototype.moment = Moment

// 注册权限指令
Vue.directive('permission', permission)
// 注册文本清理指令
Vue.directive('text-cleaner', textCleaner)
// import '@/style/base.styl'
// import '@/style/utility.less'

// import './plugins/element.js'

router.beforeEach((to, from, next) => {
  if (to.matched.some(r => r.meta.requireAuth)) {
    const isLogin = getCookie('ZTGlS_TOKEN')
    if (isLogin) {
      next()
    } else {
      if (to.path === '/login') { //跳出循环
        next()
      } else {
        next({ path: "login" })
      }
    }
  } else {
    next();
  }
})
//捕捉懒加载报错重新加载页面
router.onError((error) => {
  const pattern = /Loading chunk (\d)+ failed/g;
  const isChunkLoadFailed = error.message.match(pattern);
  const targetPath = router.history.pending.fullPath;
  if (isChunkLoadFailed) {
    router.replace(targetPath);
  }
})
function getCookie(name) {
  var strcookie = document.cookie;//获取cookie字符串
  var arrcookie = strcookie.split("; ");//分割
  //遍历匹配
  for (var i = 0; i < arrcookie.length; i++) {
    var arr = arrcookie[i].split("=");
    if (arr[0] == name) {
      return arr[1];
    }
  }
  return "";
}


// window.Vue.config.productionTip = false
// new Vue({
//   router,
//   store,
//   render: h => h(App)
// }).$mount('#app')
window.Vue = new Vue({
  el: '#app',
  router,
  store,
  // echarts,
  components: { App },
  template: '<App/>',
  async created() {
    // 如果已登录，获取权限信息
    if (this.$common.getCookie('ZTGlS_TOKEN')) {
      try {
        await this.$store.dispatch('permissions/fetchUserPermissions', { api: this.$api })
      } catch (error) {
        console.error('权限初始化失败:', error)
      }
    }
  }
})
