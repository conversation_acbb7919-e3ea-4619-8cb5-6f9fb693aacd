/* 权限控制样式 */

/* 权限不足时的通用样式 */
.permission-disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

.permission-disabled:hover {
  opacity: 0.5 !important;
}

/* Element UI 按钮权限样式 */
.el-button.permission-disabled {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

.el-button.permission-disabled:hover {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

/* Element UI 主色调按钮权限样式 */
.el-button--primary.permission-disabled {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

.el-button--primary.permission-disabled:hover {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

/* Element UI 危险按钮权限样式 */
.el-button--danger.permission-disabled {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

.el-button--danger.permission-disabled:hover {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

/* 输入框权限样式 */
.el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

/* 文本域权限样式 */
.el-textarea.is-disabled .el-textarea__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

/* 选择器权限样式 */
.el-select.is-disabled .el-input__inner {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
}

/* 开关权限样式 */
.el-switch.is-disabled {
  opacity: 0.6 !important;
}

/* 复选框权限样式 */
.el-checkbox.is-disabled {
  opacity: 0.6 !important;
}

/* 单选框权限样式 */
.el-radio.is-disabled {
  opacity: 0.6 !important;
}

/* 权限状态标签样式 */
.permission-status {
  margin-bottom: 16px;
  text-align: right;
}

.permission-status .el-tag {
  font-size: 12px;
}

/* 权限提示框样式 */
.permission-tooltip {
  font-size: 12px;
  line-height: 1.5;
  color: #606266;
}

/* 表格操作列权限样式 */
.table-actions .permission-disabled {
  margin-right: 8px;
}

/* 工具栏权限样式 */
.toolbar .permission-disabled {
  margin-right: 10px;
}

/* 表单权限样式 */
.form-permission-disabled .el-form-item__content > * {
  pointer-events: none;
  opacity: 0.6;
}

/* 权限加载状态 */
.permission-loading {
  opacity: 0.8;
  transition: opacity 0.3s;
}

/* 权限状态指示器 */
.permission-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.permission-indicator.read-write {
  background-color: #67c23a;
}

.permission-indicator.read-only {
  background-color: #e6a23c;
}

.permission-indicator.loading {
  background-color: #909399;
  animation: permission-pulse 1.5s infinite;
}

@keyframes permission-pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
} 