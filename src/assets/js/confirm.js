// 二次确认弹窗封装
function deleRows(type,prompt,url,params,callback){
    var _this=window.Vue
    _this.$confirm(prompt, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal:false,
        type: 'warning',
        beforeClose:(action, instance, done)=>{
            done()
        }
      }).then(() => {
        if(type=="post"){
        _this.$api.post(url,params,res=>{
                callback(res)
                if(res.code){
                    if(res.code==200){
                        _this.$message({
                            type: 'success',
                            duration:'2000',
                            message:'操作成功!'
                        });
                    }else if(res.code ==4100001){
                        _this.$message({
                            type: 'error',
                            duration:'3000',
                            message:'该链接不在白名单内，请到“短链白名单”进行申请、或联系客服处理'
                        });
                    }else{
                        _this.$message({
                            type: 'error',
                            duration:'2000',
                            message:res.msg
                        });
                    }
                }else{
                    _this.$message({
                        type: 'success',
                        duration:'2000',
                        message:'操作成功!'
                    });
                }
        })
        }else if(type=="put"){
        _this.$api.put(url,params,res=>{
                callback(res)
                if(res.code){
                    if(res.code==200){
                        _this.$message({
                            type: 'success',
                            duration:'2000',
                            message:'操作成功!'
                        });
                    }else{
                        _this.$message({
                            type: 'error',
                            duration:'2000',
                            message:res.msg
                        });
                    }
                }else{
                    _this.$message({
                        type: 'success',
                        duration:'2000',
                        message:'操作成功!'
                    });
                }
        })
        }else if(type=="delete"){
        _this.$api.delete(url,params,res=>{
                callback(res)
                if(res.code){
                    if(res.code==200){
                        _this.$message({
                            type: 'success',
                            duration:'2000',
                            message:'操作成功!'
                        });
                    }else{
                        _this.$message({
                            type: 'error',
                            duration:'2000',
                            message:res.msg
                        });
                    }
                }else{
                    _this.$message({
                        type: 'success',
                        duration:'2000',
                        message:'操作成功!'
                    });
                }
        })
        }else if(type=="get"){
            _this.$api.get(url,params,res=>{
                    callback(res)
                    if(res.code){
                        if(res.code==200){
                            _this.$message({
                                type: 'success',
                                duration:'2000',
                                message:'操作成功!'
                            });
                        }else{
                            _this.$message({
                                type: 'error',
                                duration:'2000',
                                message:res.msg
                            });
                        }
                    }else{
                        _this.$message({
                            type: 'success',
                            duration:'2000',
                            message:'操作成功!'
                        });
                    }
            })
            }
      }).catch(() => {
        _this.$message({
          type: 'info',
          message: '已取消操作!'
        });
      });
}
export default {
    confirmation:function(type,prompt,url,params,callback){
        return deleRows(type,prompt,url,params,callback)
    }
}