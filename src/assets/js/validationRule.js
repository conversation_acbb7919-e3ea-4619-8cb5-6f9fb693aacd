// exports.install = function (Vue, options) {
//     /*验证手机号*/
//     const validateMobile = (rule, value, callback) => {
//         if(value != ''){
//             // /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
//             let reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
//             if (reg.test(value)) {
//                 callback();
//             } else {
//                 return callback(new Error('请输入正确的手机号'));
//             }
//         }else{
//             callback();
//         }
//     }
//     /** 验证5个手机号，逗号隔开*/
//     const fiveMobiles = (rule, value, callback) => {
//         if(value != ''){
//             let reg = /^(1[3|4|5|6|7|8|9][0-9]\d{8},){0,1000}1[3|4|5|6|7|8|9][0-9]\d{8}$/;
//             if (reg.test(value)) {
//                 var arr = value.split(',');
//                 arr2 = [];
//                 for(var i = 0;i< arr.length;i++){
//                     if(arr2.indexOf(arr[i]) <0){
//                         arr2.push(arr[i])
//                     }
//                 }
//                 if(arr2.length!=arr.length){
//                     return callback(new Error('不允许重复'))
//                 }
//                 callback();
//             } 
//             else {
//                 return callback(new Error('请输入正确的手机号,多个用逗号(,)隔开'));
//             }
//         }else{
//             callback();
//         }
//     }
//     /*含有非法字符(只能输入字母、汉字)*/
//     const isvalidateRegexn= (rule, value, callback) => {  
//         if(value != ''){
//             let reg = new RegExp("^[A-Za-z\u4e00-\u9fa5]+$");      
//             if(reg.test(value)) {
//                 callback()
//             } else {
//                 callback(new Error('含有非法字符(只能输入字母、汉字)!'))
//             }
//         }else{
//             callback();
//         }
//     }
//     /* 密码验证 8-16位*/
//     const passwords = (rule, value, callback) => { 
//         if(value != ''){ 
//             let reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]*$/;      
//             if(reg.test(value)) {
//                 callback()
//             } else {
//                 callback(new Error('必须包含数字、大小写字母,不能包含空格和汉字!'))
//             }
//         }else{
//             callback();
//         }
//     }
//     /* 用户名 6-15位*/ 
//     const userName = (rule, value, callback) => { 
//         if(value != ''){  
//             let reg = /^[a-zA-Z][a-zA-Z0-9]*$/;      
//             if(reg.test(value)) {
//                 callback()
//             } else {
//                 callback(new Error('由大小写字母开头，数字、大小写字母组成!'))
//             }
//         }else{
//             callback();
//         }
//     }
//     /**多个IP */
//     const muchIPs= (rule, value, callback) => {  
//         if(value != ''){  
//             let reg = /^(\d{1,3}|\*)(\.(\d{1,3}|\*)){3}(,(\d{1,3}|\*)(\.(\d{1,3}|\*)){3})*$/; 
//             let reg1= value.split(',');   
//             if (reg1.length>=21){
//                 return callback(new Error('只能输入20个ip地址'))
//             }
//             else if(reg.test(value)) {
//                 var arr = value.split(',');
//                 arr2 = [];
//                 for(var i = 0;i< arr.length;i++){
//                     if(arr2.indexOf(arr[i]) <0){
//                         arr2.push(arr[i])
//                     }
//                 }
//                 if(arr2.length!=arr.length){
//                     return callback(new Error('不允许重复'))
//                 }
//                 callback();
//             }else if(value.lastIndexOf(",")!=-1){
//                 callback(new Error("最多输入20个ip地址"))
//             } 
//             else {
//                 callback(new Error('请输入正确的ip地址'))
//             }
//         }else{
//             callback();
//         }
//     }

//     /* IP*/ 
//     const IP= (rule, value, callback) => {  
//         if(value != ''){  
//             let reg = /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}$/;      
//             if(reg.test(value)) {
//                 callback()
//             } else {
//                 callback(new Error('Ip格式错误!'))
//             }
//         }else{
//             callback();
//         }
//     }
//     /* 验证1-5个手机号*/
//     const Mobiles = (rule, value, callback) => {
//         let reg = /^(1[3|4|5|6|7|8|9][0-9]\d{8})|(1[3|4|5|6|7|8|9][0-9]\d{8}){}$/;
//         if (reg.test(value)) {
//             callback();
//         } else {
//             return callback(new Error('请输入正确的手机号'));
//         }
//     }
//     /* 验证邮箱*/
//     const email = (rule, value, callback) => {
//         if(value != ''){  
//             let reg = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
//             if (reg.test(value)) {
//                 callback();
//             } else {
//                 return callback(new Error('输入正确格式的邮箱！'));
//             }
//         }else{
//             callback();
//         }
//     }
//     /* 验证用户单价*/
//     const unitPrice = (rule, value, callback) => {
//         if(value != ''&&value != '0'){  
//             let reg = /^(([1-9][0-9]{0,1})|(([0]\.\d{1,5}|[1-9][0-9]{0,1}\.\d{1,5})))$/;
//             let reg1 = /^0(\.?0+)?$/;
//             if (!reg.test(value)) {
//                 return callback(new Error('单价最多2位整数和5位小数！'));
//             } else {
//                 if(!reg1.test(value)){
//                     callback();
//                 }
//                 else{
//                     callback();
//                 }
//             }
//         }else{
//             callback();
//         }    
//     }

//     Vue.prototype.filter_rules = function (item){
//         let rules = [];
//         if(item.required && item.message){
//             rules.push({ required:item.required, message: item.message, trigger: 'blur' });
//          }
//         if(item.maxLength){
//            rules.push({ min:1,max:item.maxLength, message: '最多输入'+item.maxLength+'个字符!', trigger: 'blur' })
//         }
//         if(item.min&&item.max){       
//            rules.push({ min:item.min,max:item.max, message: '字符长度在'+item.min+'至'+item.max+'之间!', trigger: 'blur' })
//         }
//         if(item.type){
//             let type = item.type;
//             switch(type) {
//                 case 'mobile':
//                     rules.push({ validator:validateMobile, trigger: ['blur', 'change']});
//                     break;
//                 case 'isvalidateRegexn':
//                     rules.push({ validator:isvalidateRegexn, trigger: ['blur', 'change']});
//                     break;
//                 case 'passwords':
//                     rules.push({ validator:passwords, trigger: ['blur', 'change']});
//                     break;
//                 case 'userName':
//                     rules.push({ validator:userName, trigger: ['blur', 'change']});
//                     break;
//                 case 'IP':
//                     rules.push({ validator:IP, trigger: ['blur', 'change']});
//                     break;
//                 case 'email':
//                     rules.push({ validator:email, trigger: ['blur', 'change']});
//                     break;
//                 case 'unitPrice':
//                     rules.push({ validator:unitPrice, trigger: ['blur', 'change']});
//                     break;
//                 case 'muchIPs':
//                     rules.push({ validator:muchIPs, trigger: ['blur', 'change']});
//                     break;     
//                 case 'fiveMobiles':
//                     rules.push({ validator:fiveMobiles, trigger: ['blur', 'change']});
//                     break;    
//                 default:
//                     rule.push({});
//                     break;
//             }
//         }
//         return rules;
//     };
// };
const base = {}
base.install = function(Vue, options){
    const validateMobile = (rule, value, callback) => {
        if(value != ''){
            // /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
            let reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
            if (reg.test(value)) {
                callback();
            } else {
                return callback(new Error('请输入正确的手机号'));
            }
        }else{
            callback();
        }
    }
    /** 验证5个手机号，逗号隔开*/
    const fiveMobiles = (rule, value, callback) => {
        if(value != ''){
            let reg = /^(1[3|4|5|6|7|8|9][0-9]\d{8},){0,1000}1[3|4|5|6|7|8|9][0-9]\d{8}$/;
            if (reg.test(value)) {
                var arr = value.split(',');
                var arr2 = [];
                for(var i = 0;i< arr.length;i++){
                    if(arr2.indexOf(arr[i]) <0){
                        arr2.push(arr[i])
                    }
                }
                if(arr2.length!=arr.length){
                    return callback(new Error('不允许重复'))
                }
                callback();
            } 
            else {
                return callback(new Error('请输入正确的手机号,多个用逗号(,)隔开'));
            }
        }else{
            callback();
        }
    }
    /*含有非法字符(只能输入字母、汉字)*/
    const isvalidateRegexn= (rule, value, callback) => {  
        if(value != ''){
            let reg = new RegExp("^[A-Za-z\u4e00-\u9fa5]+$");      
            if(reg.test(value)) {
                callback()
            } else {
                callback(new Error('含有非法字符(只能输入字母、汉字)!'))
            }
        }else{
            callback();
        }
    }
    /* 密码验证 8-16位*/
    const passwords = (rule, value, callback) => { 
        if(value != ''){ 
            let reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]*$/;      
            if(reg.test(value)) {
                callback()
            } else {
                callback(new Error('必须包含数字、大小写字母,不能包含空格和汉字!'))
            }
        }else{
            callback();
        }
    }
    /* 用户名 6-15位*/ 
    const userName = (rule, value, callback) => { 
        if(value != ''){  
            let reg = /^[a-zA-Z][a-zA-Z0-9]*$/;      
            if(reg.test(value)) {
                callback()
            } else {
                callback(new Error('由大小写字母开头，数字、大小写字母组成!'))
            }
        }else{
            callback();
        }
    }
    /**多个IP */
    const muchIPs= (rule, value, callback) => {  
        if(value != ''){  
            let reg = /^(\d{1,3}|\*)(\.(\d{1,3}|\*)){3}(,(\d{1,3}|\*)(\.(\d{1,3}|\*)){3})*$/; 
            let reg1= value.split(',');   
            if (reg1.length>=21){
                return callback(new Error('只能输入20个ip地址'))
            }
            else if(reg.test(value)) {
                var arr = value.split(',');
                arr2 = [];
                for(var i = 0;i< arr.length;i++){
                    if(arr2.indexOf(arr[i]) <0){
                        arr2.push(arr[i])
                    }
                }
                if(arr2.length!=arr.length){
                    return callback(new Error('不允许重复'))
                }
                callback();
            }else if(value.lastIndexOf(",")!=-1){
                callback(new Error("最多输入20个ip地址"))
            } 
            else {
                callback(new Error('请输入正确的ip地址'))
            }
        }else{
            callback();
        }
    }

    /* IP*/ 
    const IP= (rule, value, callback) => {  
        if(value != ''){  
            let reg = /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}$/;      
            if(reg.test(value)) {
                callback()
            } else {
                callback(new Error('Ip格式错误!'))
            }
        }else{
            callback();
        }
    }
    /* 验证1-5个手机号*/
    const Mobiles = (rule, value, callback) => {
        let reg = /^(1[3|4|5|6|7|8|9][0-9]\d{8})|(1[3|4|5|6|7|8|9][0-9]\d{8}){}$/;
        if (reg.test(value)) {
            callback();
        } else {
            return callback(new Error('请输入正确的手机号'));
        }
    }
    /* 验证邮箱*/
    const email = (rule, value, callback) => {
        if(value != ''){
            // /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/
            let reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
            if (reg.test(value)) {
                callback();
            } else {
                return callback(new Error('输入正确格式的邮箱！'));
            }
        }else{
            callback();
        }
    }
    /* 验证用户单价*/
    const unitPrice = (rule, value, callback) => {
        if(value != ''&&value != '0'){  
            let reg = /^(([1-9][0-9]{0,1})|(([0]\.\d{1,5}|[1-9][0-9]{0,1}\.\d{1,5})))$/;
            let reg1 = /^0(\.?0+)?$/;
            if (!reg.test(value)) {
                return callback(new Error('单价最多2位整数和5位小数！'));
            } else {
                if(!reg1.test(value)){
                    callback();
                }
                else{
                    callback();
                }
            }
        }else{
            callback();
        }    
    }

    Vue.prototype.filter_rules = function (item){
        let rules = [];
        if(item.required && item.message){
            rules.push({ required:item.required, message: item.message, trigger: 'blur' });
         }
        if(item.maxLength){
           rules.push({ min:1,max:item.maxLength, message: '最多输入'+item.maxLength+'个字符!', trigger: 'blur' })
        }
        if(item.min&&item.max){       
           rules.push({ min:item.min,max:item.max, message: '字符长度在'+item.min+'至'+item.max+'之间!', trigger: 'blur' })
        }
        if(item.type){
            let type = item.type;
            switch(type) {
                case 'mobile':
                    rules.push({ validator:validateMobile, trigger: ['blur', 'change']});
                    break;
                case 'isvalidateRegexn':
                    rules.push({ validator:isvalidateRegexn, trigger: ['blur', 'change']});
                    break;
                case 'passwords':
                    rules.push({ validator:passwords, trigger: ['blur', 'change']});
                    break;
                case 'userName':
                    rules.push({ validator:userName, trigger: ['blur', 'change']});
                    break;
                case 'IP':
                    rules.push({ validator:IP, trigger: ['blur', 'change']});
                    break;
                case 'email':
                    rules.push({ validator:email, trigger: ['blur', 'change']});
                    break;
                case 'unitPrice':
                    rules.push({ validator:unitPrice, trigger: ['blur', 'change']});
                    break;
                case 'muchIPs':
                    rules.push({ validator:muchIPs, trigger: ['blur', 'change']});
                    break;     
                case 'fiveMobiles':
                    rules.push({ validator:fiveMobiles, trigger: ['blur', 'change']});
                    break;    
                default:
                    rules.push({});
                    break;
            }
        }
        return rules;
    };
}

export default base;