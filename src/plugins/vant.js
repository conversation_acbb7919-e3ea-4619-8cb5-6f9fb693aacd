import Vue from 'vue';
import { Tabbar, TabbarItem, NavBar, Icon, Skeleton, Toast, Button, ActionSheet, NoticeBar, Cell, CellGroup, SwipeCell, Step, Steps, RadioGroup, Radio, NumberKeyboard, Field,Form,PullRefresh,List,Search,Tab,Tabs,Tag,Popup,Picker,Sticky,Empty,Dialog,TreeSelect,DatetimePicker,Loading } from 'vant';
Vue.use(Tabbar);
Vue.use(TabbarItem);
Vue.use(NavBar);
Vue.use(Icon);
Vue.use(Skeleton);
Vue.use(Toast);
Vue.use(Button);
Vue.use(ActionSheet);
Vue.use(NoticeBar);
Vue.use(Cell);
Vue.use(CellGroup);
Vue.use(SwipeCell);
Vue.use(Step);
Vue.use(Steps);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(NumberKeyboard);
Vue.use(Field);
Vue.use(Form);
Vue.use(PullRefresh);
Vue.use(List);
Vue.use(Search);
Vue.use(Tab);
Vue.use(Tabs);
Vue.use(Tag);
Vue.use(Popup);
Vue.use(Picker);
Vue.use(Sticky);
Vue.use(Empty);
Vue.use(Dialog);
Vue.use(TreeSelect);
Vue.use(DatetimePicker);
Vue.use(Loading);

