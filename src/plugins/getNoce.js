import axios from 'axios';

const NONCE_STORAGE_KEY = 'nonce_list';
const NONCE_EXPIRY_KEY = 'nonce_expiry';
const MIN_NONCE_COUNT = 5; // 最少剩余 Nonce 数量
const NONCE_EXPIRY_TIME = 6 * 60 * 60 * 1000; // 6小时有效期（毫秒）
let isFetchingNonces = false; // 防止重复并发获取 Nonces
let fetchNonceQueue = []; // 队列管理并发请求

/**
 * 获取新的 Nonce 的接口方法
 * @returns {Promise<string[]>} 返回新的 Nonce 数组
 */
async function fetchNonces() {
    try {
        axios.defaults.baseURL = process.env.VUE_APP_URL

        const response = await axios({
            method: 'get',
            url: '/gateway/common/once',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        if (response.status !== 200 || response.data.code !== 200) {
            throw new Error('Failed to fetch Nonces');
        }
        // 确保返回的是数组
        const data = response.data.data || [];
        return Array.isArray(data) ? data : [data];
    } catch (error) {
        console.info('Error fetching Nonces:', error);
        throw error;
    }
}

/**
 * 从 localStorage 获取当前的 Nonce 列表
 * @returns {string[]} 当前存储的 Nonce 数组
 */
function getStoredNonces() {
    const nonces = localStorage.getItem(NONCE_STORAGE_KEY);
    return nonces ? JSON.parse(nonces) : [];
}

/**
 * 将 Nonce 列表存储到 localStorage
 * @param {string[]} nonces 要存储的 Nonce 数组
 */
function storeNonces(nonces) {
    localStorage.setItem(NONCE_STORAGE_KEY, JSON.stringify(nonces));
    localStorage.setItem(NONCE_EXPIRY_KEY, Date.now() + NONCE_EXPIRY_TIME);

}
/**
 * 检查 Nonce 是否过期
 * @returns {boolean} 返回 Nonce 是否过期
 */
function isNonceExpired() {
    const expiry = localStorage.getItem(NONCE_EXPIRY_KEY);
    return !expiry || Date.now() > expiry;
}
/**
 * 安全获取新的 Nonces，防止重复并发
 * @returns {Promise<void>} 无返回值，队列完成后存储 Nonces
 */
async function safeFetchNonces() {
    if (isFetchingNonces) {// 防止重复并发
        return new Promise((resolve, reject) => {
            // 加入队列
            fetchNonceQueue.push({ resolve, reject });
        });
    }

    isFetchingNonces = true;

    try {
        const newNonces = await fetchNonces();// 获取新的 Nonces
        if (!Array.isArray(newNonces)) {
            throw new Error('Nonces data is not an array');
        }
        const currentNonces = getStoredNonces();// 获取当前的 Nonces
        storeNonces([...currentNonces, ...newNonces]);// 合并新旧 Nonces
        fetchNonceQueue.forEach(({ resolve }) => resolve());// 通知队列完成
        fetchNonceQueue = [];// 清空队列
    } catch (error) {
        fetchNonceQueue.forEach(({ reject }) => reject(error));// 通知队列失败
        fetchNonceQueue = [];// 清空队列
        throw error;// 抛出错误
    } finally {
        isFetchingNonces = false;// 标记结束
    }
}

/**
 * 获取可用的 Nonce，如果不足则自动获取新的 Nonces
 * @returns {Promise<string>} 返回一个可用的 Nonce
 */
async function useNonce() {
    let nonces = getStoredNonces();

    if (nonces.length === 0 || isNonceExpired()) {
        await safeFetchNonces();
        nonces = getStoredNonces();
    }

    // 使用第一个 Nonce
    const nonce = nonces.shift();
    storeNonces(nonces);

    // 检查剩余 Nonce 数量是否不足，提前获取新的 Nonce
    if (nonces.length < MIN_NONCE_COUNT) {
        safeFetchNonces().catch(error => console.info('Error prefetching Nonces:', error));
    }

    return nonce;
}
export default {
    useNonce
}
