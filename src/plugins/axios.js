// 配置API接口地址
// var root = 'http://103.36.168.88/gateway'
// 引用axios
var axios = require('axios')
import getNoce from './getNoce';
axios.defaults.baseURL = process.env.VUE_APP_URL
// console.log(axios,'axios');
// console.log('window',window.Vue)
// 自定义判断元素类型JS
function toType(obj) {
  return ({}).toString.call(obj).match(/\s([a-zA-Z]+)/)[1].toLowerCase()
}
// 参数过滤函数
function filterNull(o) {
  for (var key in o) {
    if (o[key] === null) {
      delete o[key]
    }
    if (toType(o[key]) === 'string') {
      o[key] = o[key].trim()
    } else if (toType(o[key]) === 'object') {
      o[key] = filterNull(o[key])
    } else if (toType(o[key]) === 'array') {
      o[key] = filterNull(o[key])
    }
  }
  return o
}
function getCookie(name) {
  var strcookie = document.cookie //获取cookie字符串
  var arrcookie = strcookie.split('; ') //分割 //遍历匹配
  for (var i = 0; i < arrcookie.length; i++) {
    var arr = arrcookie[i].split('=')
    if (arr[0] == name) {
      return arr[1]
    }
  }
  return ''
}
async function apiAxios(method, url, params, success, failure) {
  if (params) {
    params = filterNull(params)
  }
  let CancelToken = axios.CancelToken
  const nonce = await getNoce.useNonce();
  axios({
    method: method,
    url: url,
    data: method === 'POST' || method === 'PUT' ? params : null,
    params: method === 'GET' || method === 'DELETE' ? params : null,
    headers: {
      'Once': nonce,
    },
    // baseURL: root,
    withCredentials: false,
    cancelToken: new CancelToken(function executor(c) {
      window.Vue.cancel = c
      // 这个参数 c 就是CancelToken构造函数里面自带的取消请求的函数，这里把该函数当参数用
    })
  })
    .then(function (res) {
      success(res.data)
      // console.log(res.data,'resdata');
    })
    .catch(function (err) {
      let res = err
      if (err) {
        // console.log(err,'err');
        // window.alert('api error, HTTP CODE: ' + res)
      }
    })
}

axios.interceptors.request.use(
  config => {
    if (window.Vue.$common.getCookie('ZTGlS_TOKEN') && window.Vue.$route.path !== '/login') {  // 每次发送请求之前判断是否存在token，如果存在，则统一在http请求的header都加上token，不用每次请求都手动添加了
      // 每次发请求重置token
      let d = new Date();
      d.setTime(d.getTime() + 1000 * 60 * 120);
      let expires = "expires=" + d.toUTCString();
      // if(Object.prototype.toString.call(window.Vue.$common.getDomain())=='[object Array]'){
      //   document.cookie = "token="+ res.data.access_token +";path=/;domain=127.0.0.1;expires="+expires;
      // }else{
      document.cookie = "ZTGlS_TOKEN=" + window.Vue.$common.getCookie('ZTGlS_TOKEN') + ";path=/;domain=." + window.localStorage.getItem("domainName") + ";expires=" + expires; +"secure";
      // }
      config.headers.Authorization = "Bearer " + window.Vue.$common.getCookie('ZTGlS_TOKEN');
    }
    return config;
  },
  err => {
    return Promise.reject(err);
  });

// 错误提示
axios.interceptors.response.use(
  response => {
    // console.log('respobnse',response)
    return response;
  },
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 402:
          window.Vue.$message({
            type: 'warning',
            duration: '2000',
            message: "数据大于6万条，不可导出， 请联系客服。"
          });
          break;
        case 401:
          //   window.Vue.$message({
          //     type: 'error',
          //     duration:'2000',
          //     message:"账号以停用，请联系客服。"
          // });
          localStorage.removeItem("nonce_list");
          window.Vue.$router.push("login")
          break;
        case 403:
          const isLogin = getCookie('ZTGlS_TOKEN')
          if (isLogin) {
            window.Vue.$message({
              type: 'error',
              duration: '2000',
              message: error.response.data.msg || error.response.data.message
            });
          } else {
            window.Vue.$router.push("login")
          }
          localStorage.removeItem("nonce_list");
          break;
        case 417:
          // if (error.response.data.error == "unauthorized") {
          //   window.Vue.$message({
          //     message: "账号暂无权限！",
          //     type: "error",
          //   });
          // } else {
          //   window.Vue.$message({
          //     type: 'error',
          //     duration: '2000',
          //     // message:error.response.data.error_description
          //     message: "验证码不正确"
          //   });
          // }
          window.Vue.$message({
            type: 'error',
            duration: '2000',
            // message:error.response.data.error_description
            message: "验证码不正确"
          });
          break;
        case 426:
          window.Vue.$message({
            type: 'error',
            duration: '2000',
            message: "用户名密码无效"
          });
          break;
        case 428:
          window.Vue.$message({
            type: 'error',
            duration: '2000',
            message: "验证码无效"
          });
          break;
        case 400:
          window.Vue.$message({
            type: 'error',
            duration: '2000',
            message: "请求错误(400)"
          });
          break;
        case 500:
          window.Vue.$message({
            type: 'error',
            duration: '2000',
            message: '服务器错误(500)'
          });
          break;
      }
    }
    return Promise.reject(error.response)
  });

// 返回在vue模板中的调用接口
export default {
  get: function (url, params, success, failure) {
    return apiAxios('GET', url, params, success, failure)
  },
  post: function (url, params, success, failure) {
    return apiAxios('POST', url, params, success, failure)
  },
  put: function (url, params, success, failure) {
    return apiAxios('PUT', url, params, success, failure)
  },
  delete: function (url, params, success, failure) {
    return apiAxios('DELETE', url, params, success, failure)
  }
}