import { setupInputCleaner, removeHiddenCharacters } from '@/utils/textCleaner'

/**
 * v-text-cleaner 指令
 * 自动清理输入框中的隐藏特殊字符
 * 
 * 使用方式:
 * 基础用法：<input v-text-cleaner v-model="inputValue" />
 * 配置选项：<input v-text-cleaner="{ onInput: true, onPaste: true }" v-model="inputValue" />
 */
export default {
  bind(el, binding, vnode) {
    // 默认配置
    const defaultOptions = {
      onPaste: true,
      onInput: false,
      callback: null
    }

    // 合并用户配置
    const options = Object.assign(defaultOptions, binding.value || {})

    // 添加回调函数来更新Vue数据
    options.callback = (cleanedValue) => {
      // 更新Vue组件的数据
      if (vnode.componentInstance) {
        vnode.componentInstance.$emit('input', cleanedValue)
      } else {
        // 对于普通元素，触发input事件
        el.dispatchEvent(new Event('input', { bubbles: true }))
      }
    }

    // 设置输入框清理功能
    setupInputCleaner(el, options)

    // 将配置存储到元素上，以便在unbind时清理
    el._textCleanerOptions = options
  },

  update(el, binding) {
    // 如果绑定值发生变化，重新设置
    if (binding.value !== binding.oldValue) {
      this.unbind(el)
      this.bind(el, binding)
    }
  },

  unbind(el) {
    // 清理事件监听器
    if (el._textCleanerOptions) {
      // 这里可以添加清理逻辑，如果需要的话
      delete el._textCleanerOptions
    }
  }
} 