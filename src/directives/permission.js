import store from '@/store/store'

// 权限控制指令
export const permission = {
  bind(el, binding, vnode) {
    checkAndUpdatePermission(el, binding)
  },
  
  update(el, binding) {
    checkAndUpdatePermission(el, binding)
  }
}

// 检查并更新元素权限状态
function checkAndUpdatePermission(el, binding) {
  const { value } = binding
  
  // 如果没有传值或传值为true，表示需要写权限
  const needWritePermission = value === undefined || value === true
  
  if (needWritePermission) {
    const hasPermission = checkWritePermission()
    
    if (hasPermission) {
      // 有权限：恢复正常状态
      el.disabled = false
      el.classList.remove('permission-disabled')
      el.removeAttribute('title')
    } else {
      // 无权限：禁用状态
      el.disabled = true
      el.classList.add('permission-disabled')
      el.setAttribute('title', '您当前为只读权限，无法执行此操作')
    }
  }
}

// 检查写权限
function checkWritePermission() {
  const permissionsLoaded = store.getters['permissions/isPermissionsLoaded']
  const hasWritePermission = store.getters['permissions/hasWritePermission']
  
  // 如果权限还未加载，默认允许（避免页面闪烁）
  if (!permissionsLoaded) {
    return true
  }
  
  return hasWritePermission
}

// 权限检查函数导出，供组件内使用
export const hasWritePermission = checkWritePermission 