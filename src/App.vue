<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>

export default {
  name: 'app',
  components: {
  },

  mounted() {
    if (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i)) {
      if (localStorage.getItem('userInfo')) {
        this.$router.push('/h5Index');
        // let userInfo = JSON.parse(localStorage.getItem('userInfo'));
        // if (userInfo.roleId == 12) {
        //   this.$router.push('/h5Index');
        // }
      }
    }

  }
}
</script>

<style>
@import "./style/main.css";
@import "./style/color-dark.css";
/* 深色主题
/* #app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
} */

/* 网站变灰 */
/* body *{
-webkit-filter: grayscale(100%); 
-moz-filter: grayscale(100%); 
-ms-filter: grayscale(100%);
-o-filter: grayscale(100%); 
filter: grayscale(100%);
filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=1); 
filter:gray; 
} */
.el-icon-upload {
  font-size: 12px !important;
  margin: 0 !important;
  line-height: 0 !important;
}

.el-upload--text {
  /* width: 97px;
    height: 32px; */
  border: none;
}
</style>
