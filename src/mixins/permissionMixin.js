import { mapGetters } from 'vuex'

export const permissionMixin = {
  computed: {
    ...mapGetters('permissions', [
      'hasWritePermission',
      'isPermissionsLoaded',
      'permissionStatusText'
    ])
  },
  
  methods: {
    // 检查写权限
    checkWritePermission() {
      return this.hasWritePermission
    },
    
    // 显示权限不足提示
    showPermissionDenied() {
      this.$message.warning('您当前为只读权限，无法执行此操作')
    },

    // 权限检查并执行操作
    executeWithPermission(callback, errorCallback = null) {
      if (this.checkWritePermission()) {
        if (typeof callback === 'function') {
          callback()
        }
      } else {
        if (typeof errorCallback === 'function') {
          errorCallback()
        } else {
          this.showPermissionDenied()
        }
      }
    }
  }
} 