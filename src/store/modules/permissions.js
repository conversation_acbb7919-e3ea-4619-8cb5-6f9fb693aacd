const state = {
  writePermission: false,          // 写权限标识
  permissionsLoaded: false         // 权限是否已加载
}

const mutations = {
  SET_WRITE_PERMISSION(state, hasPermission) {
    state.writePermission = !!hasPermission
    state.permissionsLoaded = true
  },
  CLEAR_PERMISSIONS(state) {
    state.writePermission = false
    state.permissionsLoaded = false
  }
}

const actions = {
  // 获取用户权限
  fetchUserPermissions({ commit }, { api }) {
    return new Promise((resolve, reject) => {
      try {
        // 使用传入的api实例或者全局Vue实例的api
        const apiInstance = api || (window.Vue && window.Vue.$api)
        if (!apiInstance) {
          throw new Error('API实例不可用')
        }
        
        // 使用回调函数式API调用
        apiInstance.get(
          'gateway/client-cpus/consumerclientinfo/getClientInfo',
          {}, // 参数对象
          (response) => {
            // 成功回调
            console.log(response, 'response');
            
            if (response.code == 200) {
              // 优化权限控制逻辑：null和1为开启，0为关闭
              const writePermission = response.data.writePermission
              // const writePermission = 1
              const isEnabled = writePermission == null || writePermission == 1
              console.log('原始权限值:', writePermission, '是否开启:', isEnabled);
              
              commit('SET_WRITE_PERMISSION', isEnabled)
              resolve(isEnabled)
            } else {
              console.error('获取权限失败:', response.msg)
              commit('CLEAR_PERMISSIONS')
              resolve(false)
            }
          },
          (error) => {
            // 失败回调
            console.error('获取用户权限失败:', error)
            commit('CLEAR_PERMISSIONS')
            reject(error)
          }
        )
      } catch (error) {
        console.error('获取用户权限失败:', error)
        commit('CLEAR_PERMISSIONS')
        reject(error)
      }
    })
  },
  
  // 清除权限（退出登录时）
  clearPermissions({ commit }) {
    commit('CLEAR_PERMISSIONS')
  },

  // 刷新权限
  async refreshPermissions({ dispatch }) {
    return await dispatch('fetchUserPermissions')
  }
}

const getters = {
  // 检查是否有写权限
  hasWritePermission: (state) => {
    return state.writePermission && state.permissionsLoaded
  },
  
  // 检查权限是否已加载
  isPermissionsLoaded: (state) => {
    return state.permissionsLoaded
  },

  // 获取权限状态文本
  permissionStatusText: (state) => {
    if (!state.permissionsLoaded) return '权限加载中...'
    return state.writePermission ? '读写权限' : '只读权限'
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 