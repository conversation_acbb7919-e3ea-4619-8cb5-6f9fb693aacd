import * as types from './mutation-types.js';
export default{
    [types.SET_USERNAME](state,msg){
        state.userName=msg
    },
    [types.SET_USERID](state, msg) {
        state.userId = msg;
    } ,
    [types.SET_ROLEID](state, msg) {
        state.roleId = msg;
    } ,
    [types.SET_PHONE](state, msg) {
        state.phone = msg;
    } , 
    [types.SET_CREATETIME](state, msg) {
        state.createLocalDateTime = msg;
    } ,
    [types.SET_ROLEDESC](state, msg) {
        state.roleDesc = msg;
    } ,
    [types.SET_PASSWORD](state, msg) {
        state.password = msg;
    } ,
    [types.SET_COMPNAME](state, msg) {
        state.compName = msg;
    },
    [types.SET_PORTRAITURL](state, msg) {
        state.portraitUrl = msg;
    },
    [types.SET_LOGURL](state, msg) {
        state.logUrl = msg;
    },
    [types.SET_LOGURLS](state, msg) {
        state.logUrls = msg;
    },
    [types.SET_SHOWNAME](state, msg) {
        state.showName = msg;
    },
    [types.SET_CUSTOM](state, msg) {
        state.custom = msg;
    },
    [types.SET_WARNINGREMIND](state, msg) {
        state.warningRemind = msg;
    },
    [types.SET_LOGODATA](state, msg) {
        state.logoData = msg;
    },
    [types.SET_ISDATESTATE](state, msg) {
        state.isDateState = msg;
    },
    [types.SET_GETMENU](state,msg){
        state.getMenu=msg
    },
    [types.SET_CONTENTMMS](state,msg){
        state.contentMMS=msg
    },
}