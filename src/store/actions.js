import * as types from './mutation-types'


// export const add =  async ({ commit, state }, { value }) => {
//     commit(types.TEST, value)
// }
export const saveInfo =  async ({commit}, {userName, userId,roleId,phone,createLocalDateTime,roleDesc,password,compName,custom,warningRemind,isDateState}) => {
    // console.log(roleId,'roleId');
    commit(types.SET_USERNAME, userName);
    commit(types.SET_USERID, userId);
    commit(types.SET_ROLEID, roleId); 
    commit(types.SET_PHONE, phone);
    commit(types.SET_CREATETIME, createLocalDateTime);
    commit(types.SET_ROLEDESC, roleDesc);
    commit(types.SET_PASSWORD, password);
    commit(types.SET_COMPNAME, compName);
    commit(types.SET_CUSTOM, custom);
    commit(types.SET_WARNINGREMIND, warningRemind); 
    commit(types.SET_ISDATESTATE, isDateState); 
}
export const saveMenu =  async ({commit}, {getMenu}) => {
    commit(types.SET_GETMENU, getMenu);
}
export const saveImg = async ({commit}, {portraitUrl}) => {
    commit(types.SET_PORTRAITURL, portraitUrl);
}
export const saveUrl=  async ({commit}, {logUrl}) => {
    commit(types.SET_LOGURL, logUrl);
}
export const saveUrls = async ({commit}, {logUrls}) => {
    commit(types.SET_LOGURLS, logUrls);
}
export const saveShow = async ({commit}, {showName}) => {
    commit(types.SET_SHOWNAME, showName);
}
export const savelogoData = async ({commit}, {logoData}) => {
    commit(types.SET_LOGODATA, logoData);
}
export const saveMMS = async ({commit}, {contentMMS}) => {
    commit(types.SET_CONTENTMMS, contentMMS);
}
//重置state
export const resetStates = async({commit}) => {
    commit(types.SET_USERNAME, '');
    commit(types.SET_USERID, '');
    commit(types.SET_ROLEID, ''); 
    commit(types.SET_PHONE, '');
    commit(types.SET_CREATETIME, '');
    commit(types.SET_ROLEDESC,'');
    commit(types.SET_PASSWORD, '');
    commit(types.SET_COMPNAME, '');
    commit(types.SET_PORTRAITURL, '');
    commit(types.SET_LOGURL, '');
    commit(types.SET_CUSTOM, '');
    commit(types.SET_WARNINGREMIND, '');
}