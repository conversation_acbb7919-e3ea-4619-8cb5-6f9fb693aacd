import store from '@/store/store'

/**
 * 检查是否有写权限
 * @returns {boolean}
 */
export function hasWritePermission() {
  return store.getters['permissions/hasWritePermission']
}

/**
 * 检查权限是否已加载
 * @returns {boolean}
 */
export function isPermissionsLoaded() {
  return store.getters['permissions/isPermissionsLoaded']
}

/**
 * 获取权限状态文本
 * @returns {string}
 */
export function getPermissionStatusText() {
  return store.getters['permissions/permissionStatusText']
}

/**
 * 执行需要权限的操作
 * @param {Function} callback - 有权限时执行的回调
 * @param {Function} errorCallback - 无权限时执行的回调
 */
export function executeWithPermission(callback, errorCallback = null) {
  if (hasWritePermission()) {
    if (typeof callback === 'function') {
      callback()
    }
  } else {
    if (typeof errorCallback === 'function') {
      errorCallback()
    } else {
      // 默认提示
      if (window.Vue && window.Vue.$message) {
        window.Vue.$message.warning('您当前为只读权限，无法执行此操作')
      }
    }
  }
} 