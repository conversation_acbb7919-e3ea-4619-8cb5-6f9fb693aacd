/**
 * 文本清理工具方法
 */

/**
 * 去除文本中的隐藏特殊字符
 * @param {string} text - 需要清理的文本
 * @returns {string} - 清理后的文本
 */
export function removeHiddenCharacters(text) {
  if (!text || typeof text !== 'string') {
    return ''
  }

  return text
    // 去除零宽字符
    .replace(/[\u200B-\u200D\uFEFF]/g, '')
    // 去除零宽连接符和零宽非连接符
    .replace(/[\u200C\u200D]/g, '')
    // 去除从右到左标记和从左到右标记
    .replace(/[\u202A-\u202E]/g, '')
    // 去除其他方向控制字符
    .replace(/[\u2066-\u2069]/g, '')
    // 去除对象替换字符
    .replace(/\uFFFC/g, '')
    // 去除字节顺序标记 (BOM)
    .replace(/\uFEFF/g, '')
    // 去除控制字符（保留常用的换行符、制表符等）
    .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]/g, '')
    // 去除其他不可见字符
    .replace(/[\u2028\u2029]/g, '')
    // 去除多余的空白字符，保留单个空格、换行符和制表符
    .replace(/[\u00A0\u1680\u2000-\u200A\u202F\u205F\u3000]/g, ' ')
    // 清理多个连续空格为单个空格（可选）
    .replace(/\s+/g, ' ')
    // 去除首尾空白
    .trim()
}

/**
 * 清理粘贴的文本内容
 * @param {string} text - 粘贴的文本
 * @returns {string} - 清理后的文本
 */
export function cleanPastedText(text) {
  return removeHiddenCharacters(text)
}

/**
 * 为输入框添加自动清理功能的指令
 * @param {HTMLElement} el - 输入框元素
 * @param {Object} options - 配置选项
 */
export function setupInputCleaner(el, options = {}) {
  const {
    onPaste = true,
    onInput = false,
    callback = null
  } = options

  // 处理粘贴事件
  if (onPaste) {
    el.addEventListener('paste', (e) => {
      setTimeout(() => {
        const cleanedValue = removeHiddenCharacters(el.value)
        if (cleanedValue !== el.value) {
          el.value = cleanedValue
          // 触发input事件以更新Vue的数据绑定
          el.dispatchEvent(new Event('input', { bubbles: true }))
          callback && callback(cleanedValue)
        }
      }, 0)
    })
  }

  // 处理输入事件
  if (onInput) {
    el.addEventListener('input', (e) => {
      const cleanedValue = removeHiddenCharacters(e.target.value)
      if (cleanedValue !== e.target.value) {
        e.target.value = cleanedValue
        callback && callback(cleanedValue)
      }
    })
  }
}

export default {
  removeHiddenCharacters,
  cleanPastedText,
  setupInputCleaner
} 