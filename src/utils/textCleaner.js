/**
 * 文本清理工具方法
 */

/**
 * 去除文本中的隐藏特殊字符
 * @param {string} text - 需要清理的文本
 * @returns {string} - 清理后的文本
 */
export function removeHiddenCharacters(text) {
  if (!text || typeof text !== 'string') {
    return ''
  }

  return text
    // 去除零宽字符
    .replace(/[\u200B-\u200D\uFEFF]/g, '')
    // 去除零宽连接符和零宽非连接符
    .replace(/[\u200C\u200D]/g, '')
    // 去除从右到左标记和从左到右标记
    .replace(/[\u202A-\u202E]/g, '')
    // 去除其他方向控制字符
    .replace(/[\u2066-\u2069]/g, '')
    // 去除对象替换字符
    .replace(/\uFFFC/g, '')
    // 去除字节顺序标记 (BOM)
    .replace(/\uFEFF/g, '')
    // 去除控制字符（保留常用的换行符、制表符等）
    .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]/g, '')
    // 去除其他不可见字符
    .replace(/[\u2028\u2029]/g, '')
    // 去除多余的空白字符，保留单个空格、换行符和制表符
    .replace(/[\u00A0\u1680\u2000-\u200A\u202F\u205F\u3000]/g, ' ')
    // 清理多个连续空格为单个空格（可选）
    .replace(/\s+/g, ' ')
    // 去除首尾空白
    .trim()
}

/**
 * 清理粘贴的文本内容
 * @param {string} text - 粘贴的文本
 * @returns {string} - 清理后的文本
 */
export function cleanPastedText(text) {
  return removeHiddenCharacters(text)
}

/**
 * 为输入框添加自动清理功能的指令
 * @param {HTMLElement} el - 输入框元素
 * @param {Object} options - 配置选项
 */
export function setupInputCleaner(el, options = {}) {
  const {
    onPaste = true,
    onInput = false,
    callback = null
  } = options

  // 存储原始的选择状态
  let selectionStart = 0
  let selectionEnd = 0

  // 处理粘贴事件
  if (onPaste) {
    const pasteHandler = (e) => {
      // 不阻止默认粘贴行为，让浏览器正常处理
      // 保存当前光标位置
      selectionStart = el.selectionStart || 0
      selectionEnd = el.selectionEnd || 0

      // 使用 setTimeout 确保粘贴操作完成后再清理
      setTimeout(() => {
        const originalValue = el.value
        const cleanedValue = removeHiddenCharacters(originalValue)

        if (cleanedValue !== originalValue) {
          // 计算清理后的光标位置
          const lengthDiff = originalValue.length - cleanedValue.length
          const newSelectionStart = Math.max(0, selectionStart - lengthDiff)
          const newSelectionEnd = Math.max(0, selectionEnd - lengthDiff)

          el.value = cleanedValue

          // 恢复光标位置
          try {
            el.setSelectionRange(newSelectionStart, newSelectionEnd)
          } catch (err) {
            // 某些输入类型不支持 setSelectionRange，忽略错误
            console.debug('setSelectionRange not supported:', err)
          }

          // 触发input事件以更新Vue的数据绑定
          const inputEvent = new Event('input', { bubbles: true })
          el.dispatchEvent(inputEvent)

          // 触发change事件以确保所有监听器都能收到通知
          const changeEvent = new Event('change', { bubbles: true })
          el.dispatchEvent(changeEvent)

          callback && callback(cleanedValue)
        }
      }, 0)
    }

    el.addEventListener('paste', pasteHandler, { passive: true })

    // 存储事件处理器引用，用于清理
    el._textCleanerPasteHandler = pasteHandler
  }

  // 确保复制功能不受影响 - 添加复制事件的透明处理
  const copyHandler = (e) => {
    // 不做任何处理，让浏览器正常处理复制事件
    // 这里只是确保我们的指令不会意外干扰复制功能
  }

  el.addEventListener('copy', copyHandler, { passive: true })
  el._textCleanerCopyHandler = copyHandler

  // 同样处理剪切事件
  const cutHandler = (e) => {
    // 不做任何处理，让浏览器正常处理剪切事件
  }

  el.addEventListener('cut', cutHandler, { passive: true })
  el._textCleanerCutHandler = cutHandler

  // 处理输入事件
  if (onInput) {
    const inputHandler = (e) => {
      // 保存当前光标位置
      const currentSelectionStart = e.target.selectionStart || 0
      const currentSelectionEnd = e.target.selectionEnd || 0

      const originalValue = e.target.value
      const cleanedValue = removeHiddenCharacters(originalValue)

      if (cleanedValue !== originalValue) {
        // 计算清理后的光标位置
        const lengthDiff = originalValue.length - cleanedValue.length
        const newSelectionStart = Math.max(0, currentSelectionStart - lengthDiff)
        const newSelectionEnd = Math.max(0, currentSelectionEnd - lengthDiff)

        e.target.value = cleanedValue

        // 恢复光标位置
        try {
          e.target.setSelectionRange(newSelectionStart, newSelectionEnd)
        } catch (err) {
          // 某些输入类型不支持 setSelectionRange，忽略错误
        }

        callback && callback(cleanedValue)
      }
    }

    el.addEventListener('input', inputHandler, { passive: true })

    // 存储事件处理器引用，用于清理
    el._textCleanerInputHandler = inputHandler
  }
}

/**
 * 清理输入框的事件监听器
 * @param {HTMLElement} el - 输入框元素
 */
export function cleanupInputCleaner(el) {
  if (el._textCleanerPasteHandler) {
    el.removeEventListener('paste', el._textCleanerPasteHandler)
    delete el._textCleanerPasteHandler
  }

  if (el._textCleanerInputHandler) {
    el.removeEventListener('input', el._textCleanerInputHandler)
    delete el._textCleanerInputHandler
  }

  if (el._textCleanerCopyHandler) {
    el.removeEventListener('copy', el._textCleanerCopyHandler)
    delete el._textCleanerCopyHandler
  }

  if (el._textCleanerCutHandler) {
    el.removeEventListener('cut', el._textCleanerCutHandler)
    delete el._textCleanerCutHandler
  }
}

export default {
  removeHiddenCharacters,
  cleanPastedText,
  setupInputCleaner,
  cleanupInputCleaner
}