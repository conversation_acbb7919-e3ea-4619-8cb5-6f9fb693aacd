# SMS短信管理系统接口汇总文档

## 项目概述
- **项目名称**: SMS MixCloud Client 2.0
- **技术栈**: Vue 2.x + Element UI + Axios
- **接口总数**: 约361个
- **基础网关**: `/gateway/`

## 接口基础路径配置

| 服务模块 | 基础路径 | 说明 |
|---------|----------|------|
| api | `/gateway/` | 基础网关路径 |
| cpas | `/gateway/client-cpas/` | 账户信息、帮助中心、业务概览等 |
| oscs | `/gateway/operator-oscs/` | 运营商服务 |
| cpus | `/gateway/client-cpus/` | 客户端主要服务 |
| slms | `/gateway/usls-slms/` | 短链接服务 |
| smss | `/gateway/client-smss/` | 短信发送服务 |
| upms | `/gateway/smcp-upms/` | 用户权限管理服务 |
| wmcs | `/gateway/mob-wmcs/` | 号码服务 |
| smcs | `/gateway/operator-smcs/` | 运营商短信服务 |
| omcs | `/gateway/operator-omcs/` | 运营商管理服务 |
| recharge | `/gateway/consumer-recharge/` | 充值服务 |
| auth | `/gateway/auth/` | 认证服务 |
| shops | `/gateway/shop-client/` | 商城服务 |
| contact | `/gateway/contact/` | 联系人服务 |
| fiveWeb | `/gateway/5g-web` | 5G服务 |

## 按功能模块分类的接口列表

### 1. 认证与登录模块 (`/auth`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/auth/oauth/token` | POST | 账号密码登录 |
| `/auth/mobile/token` | POST | 手机号登录 |
| `/auth/loginOut` | POST | 退出登录 |

### 2. 用户权限管理模块 (`/upms`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/upms/menu/allTree` | POST | 获取菜单树 |
| `/upms/user/info` | GET | 获取用户信息 |
| `/upms/online/existUser/{username}` | GET | 检查用户是否存在 |
| `/upms/online/register` | POST | 用户注册 |
| `/upms/code/register/smsCode` | POST | 发送注册验证码 |
| `/upms/generatekey/decryptMobile` | POST | 解密手机号 |

### 3. 短信发送模块 (`/cpus`)

#### 3.1 短信发送相关

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumersmsinfo/submitSms` | POST | 提交短信（验证） |
| `/cpus/consumersmsinfo/customSendSms` | POST | 自定义内容发送短信 |
| `/cpus/consumersmsinfo/templateSendSms` | POST | 模板发送短信 |
| `/cpus/v3/consumersms/send/customize` | POST | 自定义短信发送V3 |
| `/cpus/v3/consumersms/send/template` | POST | 模板短信发送V3 |
| `/cpus/v3/consumersms/send/customize/file` | POST | 文件方式自定义短信发送 |
| `/cpus/v3/consumersms/hasVariable` | POST | 检查是否包含变量 |

#### 3.2 短信任务管理

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumertasksms/page` | POST | 获取短信任务列表 |
| `/cpus/v3/selectConsumerWebTaskPage` | POST | 获取Web任务列表V3 |
| `/cpus/v3/queryConsumerWebTaskById/{id}` | GET | 根据ID查询任务 |
| `/cpus/v3/updateConsumerWebTask` | POST | 更新任务 |
| `/cpus/v3/web-task/cancel` | POST | 取消任务 |
| `/cpus/v3/web-task/info/{id}` | GET | 获取任务详情 |

#### 3.3 定时短信管理

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumertimingsms/page` | POST | 获取定时短信列表 |
| `/cpus/consumertimingsms/batchCancel/{ids}` | GET | 批量取消定时短信 |

### 4. 短信模板管理模块 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/v3/consumersmstemplate/page` | POST | 获取模板列表（分页） |
| `/cpus/v3/consumersmstemplate/selectClientTemplate` | POST | 获取客户模板列表 |
| `/cpus/consumersmstemplate/get/{id}` | GET | 获取模板详情 |
| `/cpus/v3/consumersmstemplate/template/add` | POST | 添加模板 |
| `/cpus/v3/consumersmstemplate/template/update` | POST | 更新模板 |
| `/cpus/v3/consumersmstemplate/template/copy` | POST | 复制模板 |
| `/cpus/v3/consumersmstemplate/delete/{id}` | DELETE | 删除模板 |
| `/cpus/v3/consumersmstemplate/validateUserTempExist/` | POST | 验证模板是否存在 |
| `/cpus/v3/consumersmstemplate/upload` | POST | 批量上传模板 |
| `/cpus/v3/consumersmstemplate/longUrl` | POST | 获取长链接 |

### 5. 签名管理模块 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/signature/signatureList` | POST | 获取签名列表 |
| `/cpus/signature/saveInfo` | POST | 保存签名信息 |
| `/cpus/signature/update` | PUT | 更新签名 |
| `/cpus/signature/delete` | DELETE | 删除签名 |
| `/cpus/signature/findModelBySignature` | GET | 根据签名查找模型 |
| `/cpus/signature/findModel/realName` | GET | 查找实名信息 |
| `/cpus/signature/realName/edit` | POST | 编辑实名信息 |
| `/cpus/signature/realName/export` | POST | 导出实名信息 |
| `/cpus/signature/realName/import` | POST | 导入实名信息 |
| `/cpus/signature/upload` | POST | 上传签名文件 |
| `/cpus/signature/batchUploadAttachment` | POST | 批量上传附件 |

### 6. 短信记录与备案管理模块 (`/cpus`)

#### 6.1 号码备案管理

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumersmstemplatereportmobile/page` | POST | 获取号码备案列表 |
| `/cpus/consumersmstemplatereportmobile` | POST | 新增号码备案 |
| `/cpus/consumersmstemplatereportmobile` | PUT | 更新号码备案 |
| `/cpus/consumersmstemplatereportmobile/{id}` | DELETE | 删除号码备案 |
| `/cpus/consumersmstemplatereportmobile/batchUpload` | POST | 批量上传号码备案 |

#### 6.2 链接备案管理

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumersmstemplatereportlink/page` | POST | 获取链接备案列表 |
| `/cpus/consumersmstemplatereportlink` | POST | 新增链接备案 |
| `/cpus/consumersmstemplatereportlink` | PUT | 更新链接备案 |
| `/cpus/consumersmstemplatereportlink/{id}` | DELETE | 删除链接备案 |
| `/cpus/consumersmstemplatereportlink/extractDomain` | GET | 提取域名 |

#### 6.3 链接扩展管理

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumersmstemplatereportlinkextension/page` | POST | 获取链接扩展列表 |
| `/cpus/consumersmstemplatereportlinkextension` | POST | 新增链接扩展 |
| `/cpus/consumersmstemplatereportlinkextension` | PUT | 更新链接扩展 |
| `/cpus/consumersmstemplatereportlinkextension/{id}` | DELETE | 删除链接扩展 |

#### 6.4 链接权威管理

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumersmstemplatereportlinkproof/page` | POST | 获取链接权威列表 |
| `/cpus/consumersmstemplatereportlinkproof` | POST | 新增链接权威 |
| `/cpus/consumersmstemplatereportlinkproof` | PUT | 更新链接权威 |
| `/cpus/consumersmstemplatereportlinkproof/{id}` | DELETE | 删除链接权威 |

### 7. 统计分析模块 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/statistics/page` | POST | 获取统计数据（分页） |
| `/cpus/statistics/smsPage` | POST | 获取短信统计数据 |
| `/cpus/statistics/esSmsPage` | POST | 获取ES短信统计数据 |
| `/cpus/statistics/replyPage` | POST | 获取回复统计数据 |
| `/cpus/statistics/sendCharts` | GET | 获取发送图表数据 |
| `/cpus/statistics/statusReport` | GET | 获取状态报告 |
| `/cpus/statistics/export` | POST | 导出统计数据 |
| `/cpus/statistics/exportData` | POST | 导出数据 |
| `/cpus/statistics/exportDecode/check` | GET | 检查导出解密 |
| `/cpus/consumerdataoverviewday/businessOverview` | GET | 获取业务概览 |

### 8. 客户信息管理模块 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerclientinfo/getClientInfo` | GET | 获取客户信息 |
| `/cpus/consumerclientinfo/info` | GET | 获取详细信息 |
| `/cpus/consumerclientinfo/setting` | POST | 设置客户信息 |
| `/cpus/consumerclientinfo/checkRealStatus` | GET | 检查实名状态 |
| `/cpus/consumerclientinfo/validatePassword/{password}` | GET | 验证密码 |
| `/cpus/consumerclientinfo/updateLoginPassword` | GET | 更新登录密码 |
| `/cpus/consumerclientinfo/updPasswordV2` | PUT | 更新接口密码V2 |
| `/cpus/consumerclientinfo/saltV2` | PUT | 更新加密盐V2 |
| `/cpus/consumerclientinfo/generatePassword` | GET | 生成密码 |
| `/cpus/consumerclientinfo/loginTelephoneManager/list` | POST | 获取登录手机列表 |
| `/cpus/consumerclientinfo/homePage` | POST | 获取首页数据 |
| `/cpus/consumerclientsms/overrun` | GET | 获取超限信息 |

### 9. 彩信管理模块 (`/cpus`)

#### 9.1 彩信发送

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/v1/mms/consumerWebTask` | POST | 彩信Web任务 |
| `/cpus/v1/mms/cancel` | POST | 取消彩信发送 |

#### 9.2 彩信模板

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumermmstemplate/page` | POST | 获取彩信模板列表 |
| `/cpus/consumermmstemplate` | POST | 新增彩信模板 |
| `/cpus/consumermmstemplate` | PUT | 更新彩信模板 |
| `/cpus/consumermmstemplate/{id}` | GET | 获取彩信模板详情 |
| `/cpus/consumermmstemplate/{id}` | DELETE | 删除彩信模板 |

#### 9.3 彩信草稿

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/v1/consumermms/list` | POST | 获取彩信草稿列表 |

#### 9.4 彩信定时

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumertimingmms/selectTimingPage` | POST | 获取定时彩信列表 |
| `/cpus/consumertimingmms/cancelTimingMms` | POST | 取消定时彩信 |

#### 9.5 彩信回复

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/v1/consumermms/replyPage` | POST | 获取彩信回复列表 |

### 10. 视频短信管理模块 (`/cpus`)

#### 10.1 视频短信发送

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/v1/video/consumerWebTask` | POST | 视频短信Web任务 |
| `/cpus/v1/video/cancel` | POST | 取消视频短信 |

#### 10.2 视频短信模板

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/v1/consumervideo` | POST | 获取视频模板列表 |
| `/cpus/v1/consumervideo/{id}` | DELETE | 删除视频模板 |
| `/cpus/v1/consumervideo/copy` | POST | 复制视频模板 |

#### 10.3 视频短信定时

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumertimingvideo/selectTimingPage` | POST | 获取定时视频短信列表 |
| `/cpus/consumertimingvideo/cancelTimingMms` | POST | 取消定时视频短信 |

### 11. 语音服务模块 (`/cpus`)

#### 11.1 语音定时

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumertimingvoice/selectTimingPage` | POST | 获取定时语音列表 |
| `/cpus/consumertimingvoice/cancelTimingVoice` | POST | 取消定时语音 |

#### 11.2 语音消息

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumervoicemessage/messages` | POST | 获取语音消息列表 |

### 12. 国际短信管理模块 (`/cpus`)

#### 12.1 国际短信发送

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerimsmessage/messages` | POST | 获取国际短信消息 |

#### 12.2 国际短信定时

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumertimingims/selectTimingPage` | POST | 获取定时国际短信列表 |
| `/cpus/consumertimingims/cancelTimingIms` | POST | 取消定时国际短信 |

### 13. 闪信服务模块 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerflashhourstatistics/list` | POST | 获取闪信统计列表 |
| `/cpus/consumeronelogin/page` | POST | 获取一键登录列表 |
| `/cpus/consumeronelogin` | POST | 新增一键登录 |
| `/cpus/consumeronelogin` | PUT | 更新一键登录 |
| `/cpus/consumeronelogin/{id}` | DELETE | 删除一键登录 |
| `/cpus/consumeronelogin/app/all` | GET | 获取所有应用 |

### 14. 充值与费用中心模块 (`/recharge`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/recharge/client/recharge/page` | GET | 获取充值记录列表 |
| `/recharge/client/balance/{productId}` | GET | 获取产品余额 |
| `/recharge/client/balance/notice/info` | GET | 获取余额提醒信息 |
| `/recharge/client/balance/notice/open` | POST | 开启余额提醒 |
| `/recharge/client/balance/notice/close` | POST | 关闭余额提醒 |
| `/recharge/products` | GET | 获取产品列表 |
| `/recharge/client/gjPrice` | GET | 获取国际价格 |

### 15. 账户管理模块 (`/cpus`)

#### 15.1 登录手机管理

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/userLoginAdmin/loginPhoneInfo` | GET | 获取登录手机信息 |
| `/cpus/userLoginAdmin/sendVerificationCode` | GET | 发送验证码 |
| `/cpus/userLoginAdmin/phoneDecryptUnfreeze` | POST | 手机解密解冻 |
| `/cpus/userLoginAdmin/addLoginPhoneV2` | POST | 添加登录手机V2 |
| `/cpus/userLoginAdmin/deleteLoginPhoneV2` | DELETE | 删除登录手机V2 |
| `/cpus/userLoginAdmin/updateLoginPhone` | PUT | 更新登录手机 |
| `/cpus/userLoginAdmin/transferAdmin` | POST | 转移管理员 |
| `/cpus/userLoginAdmin/verifiedStatus` | GET | 获取验证状态 |

#### 15.2 验证码管理

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/code/checkVerificationCode` | GET | 检查验证码 |
| `/cpus/code/sendVerificationCode` | GET | 发送验证码 |

### 16. 黑白名单管理模块

#### 16.1 黑名单管理 (`/omcs`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/omcs/clientBlacklist/add` | POST | 添加黑名单 |
| `/omcs/clientBlacklist/batchDelete` | POST | 批量删除黑名单 |
| `/omcs/clientBlacklist/deleteById/{id}` | GET | 根据ID删除黑名单 |

#### 16.2 白名单管理 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerClientIp` | POST | 新增IP白名单 |
| `/cpus/consumerClientIp` | PUT | 更新IP白名单 |
| `/cpus/consumerClientIp/{id}` | DELETE | 删除IP白名单 |

### 17. 用户消息管理模块 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerclientmessage/page` | POST | 获取消息列表 |
| `/cpus/consumerclientmessage/markAsRead/{ids}` | GET | 标记已读 |
| `/cpus/consumerclientmessage/delete/{ids}` | GET | 删除消息 |

### 18. 文件管理模块 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/v3/file/upload` | POST | 文件上传 |
| `/cpus/v3/file/uploadAndCompress` | POST | 上传并压缩文件 |
| `/cpus/v3/file/{fileId}` | GET | 获取文件信息 |
| `/cpus/v3/file/discernMobile` | POST | 识别手机号文件 |

### 19. 短链接管理模块 (`/slms`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/slms/v3/shortLink/add` | POST | 添加短链接 |
| `/slms/shortLink/domain/update` | POST | 更新短链域名 |
| `/slms/shortLink/domainApply` | POST | 申请短链域名 |

### 20. 商城服务模块 (`/shops`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/shops/shopgoods/bestSell` | GET | 获取热销商品 |

### 21. 联系人服务模块 (`/contact`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/contact/contact/open` | GET | 打开联系人 |

### 22. 5G服务模块 (`/fiveWeb`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/fiveWeb/compress/upload` | POST | 上传压缩文件 |
| `/fiveWeb/compress/asynVideo` | POST | 异步处理视频 |
| `/fiveWeb/compress/result` | GET | 获取压缩结果 |

### 23. 运营商管理模块 (`/omcs`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/omcs/operatingchannelprovincial/list` | GET | 获取省份通道列表 |
| `/omcs/operatingchannelinfo/operator` | GET | 获取运营商信息 |
| `/omcs/operatingchannelinfo/info` | GET | 获取通道信息 |

### 24. AI模板助手模块 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/v3/ai/template/generate` | POST | AI生成模板 |
| `/cpus/v3/ai/template/rewrite` | POST | AI改写模板 |
| `/cpus/v3/ai/template/remain/times` | GET | 查询剩余次数 |

### 25. 其他服务

#### 25.1 标签管理 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerlabel/list` | GET | 获取标签列表 |
| `/cpus/consumerlabel` | POST | 新增标签 |
| `/cpus/consumerlabel/{id}` | DELETE | 删除标签 |

#### 25.2 联系人管理 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerclientlinkman/add` | POST | 添加联系人 |
| `/cpus/consumerclientlinkman/update` | PUT | 更新联系人 |
| `/cpus/consumerpartnerlinkman/add` | POST | 添加合作伙伴联系人 |
| `/cpus/consumerpartnerlinkman/update` | PUT | 更新合作伙伴联系人 |

#### 25.3 系统日志 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerclient/sysLog/page` | POST | 获取系统日志 |

#### 25.4 证书管理 (`/cpus`)

| 接口路径 | 方法 | 说明 |
|---------|------|------|
| `/cpus/consumerCertificate` | POST | 提交证书信息 |
| `/cpus/consumerCertificate/info` | GET | 获取证书信息 |
| `/cpus/consumerCertificate/verify` | POST | 验证证书 |
| `/cpus/consumerCertificate/signContract` | POST | 签署合同 |
| `/cpus/consumerCertificate/idCardInfo` | GET | 获取身份证信息 |
| `/cpus/consumerCertificate/entLicenseInfo` | GET | 获取营业执照信息 |

## 接口调用统计

### 按服务分类统计

| 服务名称 | 接口数量 | 占比 |
|---------|----------|------|
| cpus (客户端主服务) | 约260个 | 72% |
| recharge (充值服务) | 约15个 | 4% |
| upms (权限管理) | 约10个 | 3% |
| omcs (运营商管理) | 约10个 | 3% |
| slms (短链服务) | 约5个 | 1% |
| auth (认证服务) | 约3个 | 1% |
| 其他服务 | 约58个 | 16% |

### 按功能模块统计

| 功能模块 | 接口数量 | 说明 |
|---------|----------|------|
| 短信发送管理 | 约85个 | 包括发送、模板、签名等 |
| 统计分析 | 约25个 | 各类统计报表和数据分析 |
| 账户管理 | 约45个 | 用户信息、权限、登录等 |
| 彩信管理 | 约20个 | 彩信发送和模板管理 |
| 视频短信 | 约15个 | 视频短信相关功能 |
| 语音服务 | 约10个 | 语音通知相关 |
| 国际短信 | 约10个 | 国际短信发送管理 |
| 充值费用 | 约15个 | 充值和费用管理 |
| 文件管理 | 约10个 | 文件上传下载等 |
| 其他功能 | 约126个 | 包括闪信、黑白名单等 |

## 文档更新记录

- **创建日期**: 2024-12-20
- **最后更新**: 2024-12-20
- **版本**: v1.0
- **维护者**: SMS MixCloud 开发团队

## 注意事项

1. 所有接口均需要认证，需在请求头中携带 `Authorization: Bearer {token}`
2. 接口返回统一格式：`{code: 200, msg: "success", data: {...}}`
3. 分页接口统一参数：`currentPage`（当前页）、`pageSize`（每页条数）
4. 文件上传接口需要设置 `Content-Type: multipart/form-data`
5. 部分接口有调用频率限制，请合理使用 