<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta http-equiv="pragram" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="expires" content="0">
    <link rel="shortcut icon" type="image/x-icon" href=""/>
    <link rel="stylesheet" href="https://at.alicdn.com/t/font_830376_qzecyukz0s.css">
    <script src="https://g.alicdn.com/AWSC/AWSC/awsc.js"></script>
    <title></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but jl-mis doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
<script>
  var title = document.querySelector("title")
    var link = document.querySelector("link")
  if(window.location.hostname=='partner.zthysms.com' ||window.location.hostname=='partner.yameidu.com'){
      title.innerHTML="SMS行业管理系统" 
      link.setAttribute("href",'')
  }else{
    title.innerHTML="助通融合云通信" 
    link.setAttribute("href",'./favicon.ico')
  }
  window.localStorage.setItem('domainName',getDomain()) 
  //获取主域名 
 function getDomain() {
     var hostname = window.location.hostname;
     var ip = hostname.match(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g);
     if (ip) {
         return ip;
     }
     var domain = hostname.match(/([a-z0-9][a-z0-9\-]*?\.(?:com|cn|net|org|gov|info|la|cc|co|jp)(?:\.(?:cn|jp))?)$/);
     if (domain) {
         return domain[0];
     }
     return hostname;
}
</script>
