# 签名实名状态显示规则

## 适用页面
- SignatureManagement.vue (客户端签名管理页面)
- subSignatureManagement.vue (子用户签名管理页面)

## 状态定义

### 审核状态 (auditStatus)
- 0: 编辑中
- 1: 待审核
- 2: 审核通过
- 3: 审核不通过（驳回）

### 实名信息状态
- 已完善: reportId 存在
- 未完善: reportId 不存在

## 实名状态显示逻辑

### 一、实名信息未完善 (无 reportId)
- **显示**: 未实名
- **标签类型**: info (灰色)

### 二、实名信息已完善 (有 reportId)
1. **审核状态为编辑中 (0) 或待审核 (1)**
   - **显示**: -
   - **标签类型**: 普通文本

2. **审核状态为通过 (2)**
   - **显示**: 报备中
   - **标签类型**: warning (橙色)

3. **审核状态为驳回 (3)**
   - **显示**: 待报备
   - **标签类型**: danger (红色)

## 技术实现注意事项
- auditStatus 字段会被转换为字符串进行比较，确保兼容数字和字符串类型
- 当有 AvalibleType 值时，优先显示业务类型标签，无 AvalibleType 值时才按照上述规则显示

## 备注
- signature.vue 页面保持原有逻辑不变
- 该规则适用于移动、联通、电信三个运营商列